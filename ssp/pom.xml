<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.wosai.upay</groupId>
    <artifactId>web-platforms-parent</artifactId>
    <version>1.2.2-beez-SNAPSHOT</version>
  </parent>

  <groupId>com.wosai.upay</groupId>
  <artifactId>ssp</artifactId>
  <version>1.2.2-beez-SNAPSHOT</version>
  <packaging>war</packaging>


  <dependencies>
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>web-platforms-common</artifactId>
      <version>1.2.1-beez-SNAPSHOT</version>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <version>2.3</version>
        <configuration>
          <warName>ssp</warName>
        </configuration>
      </plugin>
      <plugin>
	<groupId>org.mortbay.jetty</groupId>
	<artifactId>maven-jetty-plugin</artifactId>
	<configuration>
	  <connectors>
	    <connector implementation="org.mortbay.jetty.nio.SelectChannelConnector">
              <port>9202</port>
              <maxIdleTime>60000</maxIdleTime>
	    </connector>
	  </connectors>
	</configuration>
      </plugin>
    </plugins>
  </build>

</project>
