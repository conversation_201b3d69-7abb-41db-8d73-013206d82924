package com.wosai.upay.controller

import com.wosai.upay.rest.ast.ASTHelper
import com.wosai.upay.rest.ast.RestEndpoint
import com.wosai.upay.service.CommonLoginService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody

import javax.servlet.http.HttpServletResponse

/**
 * Created by jianfree on 30/3/16.
 */

@RestEndpoint("/api/login/common")
class CommonLoginController {
    private Logger log = LoggerFactory.getLogger(CommonLoginController.class);
    @Autowired
    private ASTHelper astHelper;

    CommonLoginService delegate;

    @RequestMapping(produces = "application/json", consumes = ["application/json", "application/json; charset=UTF-8;"], value = "/login")
    @ResponseBody
    public Map login(@RequestBody Map params, HttpServletResponse response) {
        long start = System.currentTimeMillis()
        try {
            astHelper.logMethod(log, "login", params)
            delegate.login(params, response)
            return astHelper.handleResult(log, null)
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        } finally {
            long end = System.currentTimeMillis()
            astHelper.logMethodTime(log, "login", start, end)
        }
    }

    @RequestMapping(produces = "application/json", consumes = ["application/json", "application/json; charset=UTF-8;"], value = "/updateAccountPassword")
    @ResponseBody
    public Map updateAccountPassword(@RequestBody Map params, HttpServletResponse response) {
        long start = System.currentTimeMillis()
        try {
            astHelper.logMethod(log, "updateAccountPassword", params)
            delegate.updateAccountPassword(params, response)
            return astHelper.handleResult(log, null)
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        } finally {
            long end = System.currentTimeMillis()
            astHelper.logMethodTime(log, "updateAccountPassword", start, end)
        }
    }

}
