package com.wosai.upay.controller;

import java.util.Collections;
import java.util.Map;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/helloJava")
public class HelloController 
{
    @RequestMapping(value="/say", method=RequestMethod.GET, produces="application/json")
    @ResponseBody
    public Map<String, String> say() {
        return Collections.singletonMap("message", "Hello, Java!");
    }
}
