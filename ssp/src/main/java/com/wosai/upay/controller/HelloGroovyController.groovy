package com.wosai.upay.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 一般情况下.groovy文件放在src/main/groovy目录。但是如果放在src/main/java下也可以正常编译。
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/helloGroovy")
class HelloGroovyController
{
    @RequestMapping(value="/say", method=RequestMethod.GET, produces="application/json")
    @ResponseBody
    def say() {
        [message: "Hello, Groovy!"]
    }
}
