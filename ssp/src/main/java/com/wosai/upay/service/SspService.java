package com.wosai.upay.service;


import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.helper.CorePlatformsValidated;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.validation.PropNotEmpty;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 接口定义，参数校验规则定义在接口方法上，校验失败的错误提示可以支持i18n，具体做法是在src/main/resources/ValidationMessages.properties资源文件中定义错误提示的键值对，然后在这里引用错误提示键。
 * 本地化的资源文件加后缀，例如ValidationMessages_zh_CN.properties。
 *
 * <AUTHOR>
@CorePlatformsValidated
public interface SspService {

    /**
     * 获取异常编号及描述.
     *
     * @return
     */
    Map<Integer, String> getExceptionCodesAndDesc();

    /**
     * 根据storeId解绑终端.
     *
     * @param request id                  UUID
     * @return
     */
    void unbindTerminal(Map<String, Object> request);

    /**
     * 创建门店.
     *
     * @param request name
     *                industry
     *                longitude           经度
     *                latitude            纬度
     *                province
     *                city
     *                district
     *                street_address
     *                contact_name        联系人姓名
     *                contact_phone       联系固定电话号码
     *                contact_cellphone   联系移动电话号码
     *                contact_email       联系邮箱
     *                client_sn           商户外部门店号
     *                merchant_id
     *                extra               扩展字段
     * @return
     */
    Map<String, Object> createStore(Map<String, Object> request);

    /**
     * 修改门店.
     *
     * @param request id                  UUID
     *                name
     *                industry
     *                longitude           经度
     *                latitude            纬度
     *                province
     *                city
     *                district
     *                street_address
     *                contact_name        联系人姓名
     *                contact_phone       联系固定电话号码
     *                contact_cellphone   联系移动电话号码
     *                contact_email       联系邮箱
     *                client_sn           商户外部门店号
     *                extra               扩展字段
     *                version
     * @return
     */
    Map<String, Object> updateStore(Map<String, Object> request);

    /**
     * 获取门店.
     *
     * @param request id                  UUID
     * @return
     */
    Map<String, Object> getStore(Map<String, Object> request);

    /**
     * 修改终端.
     *
     * @param request id                  UUID
     *                device_fingerprint  设备指纹
     *                name                终端名
     *                type                类型  1: SQB_APP  2: SQB_SDK   3: SQB_POS  10: SATURN
     *                sdk_version
     *                os_version
     *                longitude           经度
     *                latitude            纬度
     *                client_sn           商户外部终端号
     *                extra               扩展字段
     *                target
     *                target_type
     *                vendor_app_id
     *                version
     * @return
     */
    Map<String, Object> updateTerminal(Map<String, Object> request);

    /**
     * 获取终端.
     *
     * @param request id                  UUID
     * @return
     */
    Map<String, Object> getTerminal(Map<String, Object> request);


    /**
     * 按照条件查询商户下门店列表信息
     * @param request
     * @return
     * @author:lijunjie
     */
    ListResult queryStoreList(Map request);




    /**
     * 分页查询Terminal.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *      sn                  用户可见终端编号
     *      device_fingerprint  设备指纹
     *      name                终端名
     *      type                类型
     *      status              状态
     *      client_sn           商户外部终端号
     *      target_type         回调通知方式
     *      store_id            门店ID
     *      merchant_id         商户ID
     *      solicitor_id        推广渠道ID
     *      vendor_id           服务商ID
     *      vendor_app_id       服务商应用ID
     *      deleted             删除标志
     * @return
     */
    ListResult findTerminals(@PropNotEmpty.List({
                                @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
                             })
                             Map request);



    /**
     * 得到渠道下下所用商户列表
     * @param request
     * @return
     * @author:lijunjie
     */

    ListResult queryMerchantList(Map request);


    /**
     * 获取商户信息
     * @param request
     * merchant_id
     * @return
     */
    Map getMerchant(@PropNotEmpty.List({
                            @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
                    })
                    Map request);

    /**
     * 创建商户银行账户
     * @param request
     */
    public Map createMerchantBankAccount(Map request);


    /**
     * 查询商户银行账号
     * @param request
     * @return
     */
    public Map queryMerchantBankAccount(Map request);


    /**
     * 查询商户基本信息及照片相关信息
     * @param request
     * @return
     */
    Map queryApplicationBaseByCondition(@PropNotEmpty.List({
                                            @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
                                        })
                                        Map request);


    /**
     * 查询商户银行账户
     * @param request
     * merchant_id
     * @return
     */

    Map getMerchantBankAccount(@PropNotEmpty(value = "merchant_id",message = "{value}值不可为空")Map request);

    /**
     * 创建商户.
     *
     * @param request
     *          merchant_config
     *          merchant_bank_account
     *          store
     */
    Map createMerchant(Map request);

    /**
     * 编辑商户接口信息
     * @param request
     */
    Map updateMerchant(Map request);


    /**
     * 编辑商户基本信息及照片相关信息
     */

    Map updateApplicationBase(Map request);

    /**
     * 获取商户开发者参数
     * @param request
     * merchant_id
     * @return
     */
    Map getMerchantDeveloper(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
            })
            Map request);

    /**
     * 获取商户正式的配置参数
     * @param request
     *  merchant_id
     *  payway
     * @return
     */
    Map getMerchantConfigFormalParams(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空"),
                    @PropNotEmpty(value = MerchantConfig.PAYWAY, message = "{value} 支付方式不能为空"),
            })
            Map request);

    /**
     * 获取商户基本的交易校验信息 如商户限额
     * @param request
     * merchant_id
     * @return
     */
    Map getMerchantTradeValidateParams(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空"),
            })
            Map request);

    /**
     * 获取商户收款通道是否开通，是否正式，费率， 正式参数等信息
     * @param request
     * merchant_id
     * @return
     */
    List getAnalyzedMerchantConfigs(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
            })
            Map request);

    /**
     * 编辑收款通道 是否开通以及费率信息
     * @param request
     *   merchant_id
     *   payway
     *   b2c_formal
     *   b2c_satus
     *   b2c_fee_rate
     *   c2b_formal
     *   c2b_fee_rate
     *   c2b_status
     *   wap_formal
     *   wap_status
     *   wap_fee_rate
     */
    void updateMerchantConfigStatusAndFeeRate(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空"),
                    @PropNotEmpty(value = MerchantConfig.PAYWAY, message = "{value} 支付方式不能为空"),
            })
            Map request);

    /**
     * 创建激活码
     *
     * @param request id                  UUID
     *
     * @return Map
     */
    Map createActivationCode(Map<String, Object> request);
    /**
     * 获取可用激活码
     *
     * @param request id                  UUID
     *
     * @return Map
     */
    ListResult getActivationCodes(Map<String, Object> request);

    /**
     * 修改Solicitor.
     *
     * @param request
     */
    Map updateSolicitor(Map<String, Object> request);

    /**
     * 获取Solicitor.
     *
     * @param request 任意值
     * @return
     */
    Map getSolicitor(Map<String, Object> request);

     /**
     * 获取推广者开通商户时 是否 开通,以及费率的信息
     * @param request 任意值
     * @return
     */
    List getAnalyzedSolicitorConfigs(Map<String, Object> request);

    /**
     * 渠道的配置SolicitorConfig.
     *
     * @param request 任意值
     * @return
     */
    List getSolicitorConfigs(Map<String, Object> request);

    /**
     * 获取推广者订单列表
     * @param request
     * order_sn
     * store_sn
     * store_name
     * payway
     * sub_payway
     * status
     * min_total_amount
     * max_total_amount
     * @return
     */
    ListResult getOrderList(Map request);

    /**
     * 根据订单号获取交易明细
     * @param request
     * order_sn
     * @return
     */
    List getTransactionListByOrderSn(
            @PropNotEmpty(value = "order_sn", message = "{value} 订单号不能为空")
            Map request);

    /**
     * 导出订单列表
     * @param params 参数同getOrderList
     * @param response
     */
    void exportOrderList(Map params, HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 导出账单汇总与明细
     * @param params
     * date_start
     * date_end
     * @param response
     */
    void exportStatement(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_DATE_START, message = "{value} 起始日期不能为空"),
                    @PropNotEmpty(value = ConstantUtil.KEY_DATE_END, message = "{value} 结束日期不能为空")
            })
            Map params, HttpServletRequest request, HttpServletResponse response) throws IOException;


}
