package com.wosai.upay.service.remote;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * Created by kay on 16/6/16.
 */
public interface OrderService {
    /**
     * 查询订单
     * @param pageInfo
     * @param queryFilter
     *        storeName
     *        storeSn
     *        storeId
     *        payway
     *        subPayway
     *        status
     *        minTotalAmount
     *        maxTotalAmount
     *        orderSn
     *        clientSn
     *        merchantId
     *        merchantSn
     *        merchantName
     * @return
     */
    ListResult getOrderList(PageInfo pageInfo, Map queryFilter);

    /**
     * 查询渠道订单
     * @param channelId
     * @param queryChildren
     * @param pageInfo
     * @param queryFilter
     *        storeName
     *        wosaiStoreId
     *        payway
     *        subPayway
     *        status
     *        minTotalAmount
     *        maxTotalAmount
     *        orderSn
     *        clientSn
     * @return
     */
    ListResult getChannelOrderList(String channelId, boolean queryChildren, PageInfo pageInfo , Map queryFilter);

    /**
     * 查询交易明细
     * @param merchantId
     * @param storeId
     * @param pageInfo
     * @param queryFilter
     * @return
     */
    ListResult getTransactionList(String merchantId, String storeId, PageInfo pageInfo, Map queryFilter);

    /**
     * 获取订单详情
     * @param orderSn
     * @return
     */
    Map getOrderDetailByOrderSn(String orderSn);

    /**
     * 获取订单详情
     * @param orderId
     * @return
     */
    Map getOrderDetailByOrderId(String orderId);

    /**
     * 通过订单号获取交易流水详情
     * @param orderSn
     * @return
     */
    List getTransactionListByOrderSn(String orderSn);

    /**
     * 给原始的订单添加额外的字段
     * 商户名称，商户号，门店名称 号， 终端名称 号， 是否二清， 费率等字段
     * @param orders  订单列表或者单个订单
     */
    void addAdditionalInfo(Object orders);

}

