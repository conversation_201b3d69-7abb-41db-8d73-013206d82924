package com.wosai.upay.service;

import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.googlecode.jsonrpc4j.ProxyUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.*;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.merchant.audit.api.service.ApplicationService;
import com.wosai.upay.service.remote.OrderService;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.OrderUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wosai.upay.common.dao.PageInfoUtil.extractPageInfo;
import static com.wosai.upay.common.util.ConstantUtil.KEY_MERCHANT_ID;


public class SspServiceImpl implements SspService {
    private static final Logger logger = LoggerFactory.getLogger(SspServiceImpl.class);

    private OrderService orderService ; //backend upay项目提供的接口
    private String backendUpayUrl;
    @Autowired
    private StoreService storeService;
    @Autowired
    private TerminalService terminalService;

    @Resource
    private SolicitorService solicitorService;

    @Resource
    private MerchantService merchantService;

    @Resource
    private ApplicationService applicationService;


    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private BusinssCommonService commonService;

    @Autowired
    private UserService userService;

    public SspServiceImpl(String backendUpayUrl){
        JsonRpcHttpClient client = null;
        try {
            client = new JsonRpcHttpClient(new URL(backendUpayUrl + "rpc/upayorder"));
        } catch (MalformedURLException e) {
            throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        this.orderService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                OrderService.class,
                client);
    }


    @Override
    public Map<Integer, String> getExceptionCodesAndDesc() {
        return UpayException.CODES_DESC_MAP;
    }

    @Override
    public void unbindTerminal(Map<String, Object> request) {
        terminalService.unbindTerminal(BeanUtil.getPropString(request, DaoConstants.ID));
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> createStore(Map<String, Object> request) {
        return storeService.createStore(request);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> updateStore(Map<String, Object> request) {
        return storeService.updateStore(request);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getStore(Map<String, Object> request) {
        return storeService.getStoreByStoreId((String) request.get(DaoConstants.ID));
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> updateTerminal(Map<String, Object> request) {
        return terminalService.updateTerminal(request);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getTerminal(Map<String, Object> request) {
        return terminalService.getTerminalByTerminalId((String) request.get(DaoConstants.ID));
    }


    /**
     * 得到服务商下所用商户列表
     *
     * @param request
     * @return
     * @author:lijunjie
     */
    @Override
    public ListResult queryMerchantList(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        if(request == null){
            request = new HashMap();
        }
        request.put("solicitor_id", getSessionSolicitorId());
        return merchantService.findMerchants(pageInfo, request);
    }

    @Override
    public Map getMerchant(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        checkOwner(merchantId);
        return merchantService.getMerchant(merchantId);
    }

    @Override
    public Map createMerchantBankAccount(Map request) {
        return merchantService.bindMerchantBankAccount(request);
    }

    public Map queryMerchantBankAccount(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        return merchantService.getMerchantBankAccountByMerchantId(merchantId);
    }

    /**
     * 查询商户基本信息及照片相关信息
     *
     * @param request
     * @return
     */
    @Override
    public Map queryApplicationBaseByCondition(Map request) {
        String merchantId = BeanUtil.getPropString(request, KEY_MERCHANT_ID);
        checkOwner(merchantId);
        ListResult listResult = applicationService.findApplicationBases(extractPageInfo(request), request);
        if (listResult != null && listResult.getTotal() > 0) {
            return  (Map)(listResult.getRecords().get(0));
        }
        return null;
    }

    /**
     * 查询商户银行账户
     *
     * @param request
     * @return
     */
    @Override
    public Map getMerchantBankAccount(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        checkOwner(merchantId);
        return merchantService.getMerchantBankAccountByMerchantId(merchantId);
    }



    /**
     * 按照条件查询商户下门店列表信息
     *
     * @param request
     * @return
     * @author:lijunjie
     */
    @Override
    public ListResult queryStoreList(Map request) {
        String merchantId = BeanUtil.getPropString(request, KEY_MERCHANT_ID);
        checkOwner(merchantId);
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return storeService.getStoreListByMerchantId(merchantId, pageInfo, request);
    }



    @Override
    public ListResult findTerminals(Map request) {
        String merchantId = BeanUtil.getPropString(request, KEY_MERCHANT_ID);
        checkOwner(merchantId);
        return terminalService.findTerminals(PageInfoUtil.extractPageInfo(request), request);
    }

    @Override
    public Map createMerchant(Map request) {
        request.put(ConstantUtil.KEY_SOLICITOR_ID, getSessionSolicitorId());
        return merchantService.createMerchantComplete(request);
    }

    /**
     * 编辑商户接口信息
     *
     * @param request
     */
    @Override
    public Map updateMerchant(Map request) {
        String merchantId = BeanUtil.getPropString(request, DaoConstants.ID);
        checkOwner(merchantId);
        return merchantService.updateMerchant(request);
    }

    /**
     * 编辑商户基本信息及照片相关信息
     *
     * @param request
     */
    @Override
    public Map updateApplicationBase(Map request) {
        return applicationService.updateApplicationBase(request);
    }

    @Override
    public Map getMerchantDeveloper(Map request) {
        String merchantId  = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        checkOwner(merchantId);
        return merchantService.getMerchantDeveloperByMerchantId(merchantId);
    }

    @Override
    public Map getMerchantConfigFormalParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        checkOwner(merchantId);
        int payway = BeanUtil.getPropInt(request, MerchantConfig.PAYWAY);
        Map merchantConfig =  tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        Map result = new HashMap();
        result.put(MerchantConfig.B2C_FORMAL, BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.B2C_FORMAL, false));
        result.put(MerchantConfig.C2B_FORMAL, BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.C2B_FORMAL, false));
        result.put(MerchantConfig.WAP_FORMAL, BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.WAP_FORMAL, false));
        result.put(MerchantConfig.PARAMS, BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS));
        return result;
    }


    @Override
    public Map getMerchantTradeValidateParams( Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        checkOwner(merchantId);
        return tradeConfigService.getMerchantTradeValidateParams(merchantId);
    }

    @Override
    public List getAnalyzedMerchantConfigs(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        checkOwner(merchantId);
        return tradeConfigService.getAnalyzedMerchantConfigs(merchantId);
    }

    @Override
    public void updateMerchantConfigStatusAndFeeRate(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        checkOwner(merchantId);
        tradeConfigService.updateMerchantConfigStatusAndFeeRateObeySolicitor(merchantId, request);
    }


    @Override
    public ListResult getOrderList(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        String solicitorId = request.containsKey(ConstantUtil.KEY_SOLICITOR_ID)? BeanUtil.getPropString(request, ConstantUtil.KEY_SOLICITOR_ID):getSessionSolicitorId();
        Map queryFilter = CollectionUtil.hashMap(
                "orderSn", BeanUtil.getPropString(request, "order_sn"),
                "storeSn", BeanUtil.getPropString(request, "store_sn"),
                "storeName", BeanUtil.getPropString(request, "store_name"),
                "payway", BeanUtil.getPropString(request, "payway"),
                "subPayway", BeanUtil.getPropString(request, "sub_payway"),
                "status", BeanUtil.getPropString(request, "status"),
                "minTotalAmount", BeanUtil.getPropString(request, "min_total_amount"),
                "maxTotalAmount", BeanUtil.getPropString(request, "max_total_amount"),
                "merchantId", BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID),
                "merchantSn", BeanUtil.getPropString(request, "merchant_sn"),
                "storeId", BeanUtil.getPropString(request, ConstantUtil.KEY_STORE_ID),
                "solicitorId", solicitorId
        );
        ListResult result = orderService.getOrderList(pageInfo, queryFilter);
        return result;
    }


    @Override
    public List getTransactionListByOrderSn(Map request) {
        String orderSn = BeanUtil.getPropString(request, "order_sn");
        return orderService.getTransactionListByOrderSn(orderSn);
    }

    @Override
    public void exportOrderList(Map params, HttpServletRequest request, HttpServletResponse response) throws IOException {
        long page = BeanUtil.getPropLong(params, ConstantUtil.KEY_PAGE, 1);
        long pageSize = BeanUtil.getPropLong(params, ConstantUtil.KEY_PAGESIZE, 5000);
        String sessionSolicitorId = (String)((Map)request.getSession(true).getAttribute("ssp_account")).get("solicitor_id");
        String solicitorId = params.containsKey(ConstantUtil.KEY_SOLICITOR_ID) ? BeanUtil.getPropString(request, ConstantUtil.KEY_SOLICITOR_ID):sessionSolicitorId;
        params.put(ConstantUtil.KEY_PAGE, page);
        params.put(ConstantUtil.KEY_PAGESIZE, pageSize);
        params.put(ConstantUtil.KEY_SOLICITOR_ID, solicitorId);
        ListResult listResult = getOrderList(params);
        HSSFWorkbook workbook = OrderUtil.buildOrdersExcel(listResult.getRecords());
        String fileName =  new SimpleDateFormat("yyyy-MM-dd_HHmmss").format(new Date()) + "exportOrders.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition","attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }

    @Override
    public void exportStatement(Map params, HttpServletRequest request, HttpServletResponse response) throws IOException {
        /*String solicitorId = BeanUtil.getPropString(request, ConstantUtil.KEY_SOLICITOR_ID);
        String storeId = BeanUtil.getPropString(request, ConstantUtil.KEY_STORE_ID);
        //todo 为了方便导出对账单，暂时留个后门
        if(params.containsKey("door_solicitor_id")){
            solicitorId = BeanUtil.getPropString(params, "door_solicitor_id");
        }else{
            if(request.getSession() == null || getSessionSolicitorId() == null){
                throw  new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
            }
            solicitorId = getSessionSolicitorId();
        }
        long start = BeanUtil.getPropLong(params, "date_start");
        long end = BeanUtil.getPropLong(params, "date_end");

        Map merchant = merchantService.getMerchant(merchantId);
        Map context = CollectionUtil.hashMap(
                "merchant_id", merchantId,
                "start", new Date(start), "end", new Date(end),
                "merchant_sn", BeanUtil.getPropString(merchant, Merchant.SN),
                "merchant_name", BeanUtil.getPropString(merchant, Merchant.NAME)
        );
        PageInfo pageInfo = new PageInfo( 1, 10000, start, end);
        Map queryFilter = CollectionUtil.hashMap(
                "status",Arrays.asList(Transaction.STATUS_SUCCESS)
        );
        ListResult result = orderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet summarySheet = workbook.createSheet("账单汇总");
        StringBuffer summary = UpayStatement.buildUpayStatementSummary(context, result.getRecords());
        SheetUtil util = new SheetUtil(summarySheet);

        for(String line: summary.toString().split("\n")){
            util.appendRow(Arrays.asList(line.split("\t")));
        }
        util.mergeCell(2, 0, 2, 3);
        util.setCellAlignment(2, 0, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER);
        util.mergeCell(6, 0, 6, 10);
        util.setCellAlignment(6, 0, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER);
        StringBuffer details = UpayStatement.buildUpayStatementDetail(context, result.getRecords());
        HSSFSheet detailSheet = workbook.createSheet("账单明细");
        util = new SheetUtil(detailSheet);
        for(String line: details.toString().split("\n")){
            util.appendRow(Arrays.asList(line.split("\t")));
        }
        String fileName =  new SimpleDateFormat("yyyy-MM-dd_HHmmss").format(new Date()) + "statement.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename="+fileName);
        workbook.write(response.getOutputStream());*/
    }



    public String getSessionSolicitorId(){
        return (String)((Map )HttpRequestUtil.getSession().getAttribute("ssp_account")).get("solicitor_id");
    }
    public String getSessionSolicitorSn(){
        return (String)((Map )HttpRequestUtil.getSession().getAttribute("ssp_account")).get("solicitor_sn");
    }

    /**
     * 检查商户是否属于此推广者
     * @param merchantId
     */
    private void checkOwner(String merchantId){
        Map merchant = commonService.getMerchantMinimalInfoById(merchantId);
        if(merchant == null){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户不存在");
        }
        String merchantSolicitorId = BeanUtil.getPropString(merchant, Merchant.SOLICITOR_ID);
        String sessionSolicitorId = getSessionSolicitorId();
        if(!sessionSolicitorId.equals(merchantSolicitorId)){
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "非所属推广者， 拒绝访问");
        }
    }
    @Override
    public Map createActivationCode(Map<String, Object> request){
        String solicitor_sn = getSessionSolicitorSn();//BeanUtil.getPropString(request, "solicitor_sn");
        String code = BeanUtil.getPropString(request, "code");
        String storeSn = BeanUtil.getPropString(request, "store_sn");
        if(Empty(storeSn)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "storeSn不能为空");
        }
        String terminal_name = BeanUtil.getPropString(request, "terminal_name");
        long limit = BeanUtil.getPropLong(request,"limit");
        return terminalService.createActivationCodeV2(solicitor_sn,code,storeSn,terminal_name,limit);
    }
    @Override
    public ListResult getActivationCodes(Map<String, Object> request){
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        Map queryFilter = CollectionUtil.hashMap();
        String store_id = BeanUtil.getPropString(request, "store_id");
        String merchant_id = BeanUtil.getPropString(request, "merchant_id");
        if(Empty(store_id) || Empty(merchant_id)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户和门店不能为空");
        }
        return terminalService.getActivationCodes(merchant_id, store_id, pageInfo,queryFilter);
    }
    boolean Empty(String s){
        return s == null || s.isEmpty();
    }

    @Override
    public Map updateSolicitor(Map<String, Object> request) {
        request.put(ConstantUtil.KEY_ID, getSessionSolicitorId());
        return solicitorService.updateSolicitor(request);
    }

    @Override
    public Map getSolicitor(Map<String, Object> request) {
        return solicitorService.getSolicitor(getSessionSolicitorId());
    }

    @Override
    public List getAnalyzedSolicitorConfigs(Map<String, Object> request) {
        return tradeConfigService.getAnalyzedSolicitorConfigs(getSessionSolicitorId());
    }

    @Override
    public List getSolicitorConfigs(Map<String, Object> request) {
        return solicitorService.getSolicitorConfigs(getSessionSolicitorId());
    }



}
