package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Solicitor;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.model.user.SolicitorUser;
import com.wosai.upay.core.service.SolicitorService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.common.util.ConstantUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by jianfree on 4/4/16.
 */
@Service
public class SspUserLoginServiceImpl extends AbsUserLoginService implements SspUserLoginService {
    private static final Logger logger = LoggerFactory.getLogger(SspUserLoginService.class);

    private static final String SESSION_USER = "ssp_account"; //存储在session中的user信息


    @Autowired
    private UserService userService;
    @Autowired
    private SolicitorService solicitorService;


    @Override
    public List getUserList() {
        Object accountId = getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        if(accountId == null){
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
        }
        ListResult solicitorUsers = userService.findSolicitorUsers(new PageInfo(1, Integer.MAX_VALUE),
                CollectionUtil.hashMap(MerchantUser.ACCOUNT_ID, accountId, MerchantUser.STATUS, MerchantUser.STATUS_ENABLED));
        if(super.isExistsByUser(solicitorUsers)){
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "推广者平台无此用户或者此用户被禁用");
        }
        List<Map> records = solicitorUsers.getRecords();
        List<Map> closeRecords = new ArrayList<Map>();
        for(Map solicitorUser: records){
            String solicitorId = BeanUtil.getPropString(solicitorUser, SolicitorUser.SOLICITOR_ID);
            Map solicitor = solicitorService.getSolicitor(solicitorId);
            if (BeanUtil.getPropInt(solicitor, Solicitor.STATUS) == Solicitor.STATUS_CLOSED) {
                closeRecords.add(solicitorUser);
            }
            solicitorUser.put(ConstantUtil.KEY_SOLICITOR_SN, BeanUtil.getPropString(solicitor, Solicitor.SN));
            solicitorUser.put(ConstantUtil.KEY_SOLICITOR_NAME, BeanUtil.getPropString(solicitor, Solicitor.NAME));
            solicitorUser.put(ConstantUtil.KEY_VENDOR_ID, BeanUtil.getPropString(solicitor, DaoConstants.ID));
        }
        if (closeRecords.size() != 0) {
            for (Map map : closeRecords) {
                records.remove(map);
            }
        }
        if (super.isExistsByUser(solicitorUsers)) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "此用户可以管理的推广者已被关闭");
        }
        return solicitorUsers.getRecords();
    }

    @Override
    public void changeUserContext(Map request) {
        String userId = BeanUtil.getPropString(request, "user_id");
        Object accountId = getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        if(accountId == null){
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
        }
        Map account = userService.getAccount(accountId.toString());
        Map solicitorUser = userService.getSolicitorUser(userId);
        if(!accountId.equals(BeanUtil.getPropString(solicitorUser, SolicitorUser.ACCOUNT_ID))){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "用户不存在");
        }
        String solicitorId = BeanUtil.getPropString(solicitorUser, SolicitorUser.SOLICITOR_ID);
        Map solicitor = solicitorService.getSolicitor(solicitorId);
        if(solicitor == null){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "推广者不存在");
        }
        account.put(ConstantUtil.KEY_SOLICITOR_SN, BeanUtil.getPropString(solicitor, Solicitor.SN));
        account.put(ConstantUtil.KEY_SOLICITOR_NAME, BeanUtil.getPropString(solicitor, Solicitor.NAME));
        account.put(ConstantUtil.KEY_SOLICITOR_ID, BeanUtil.getPropString(solicitor, DaoConstants.ID));
        getSession().setAttribute(SESSION_USER, account);
    }

    @Override
    public Map getCurrentUser() {
        Map account = (Map)getSession().getAttribute(SESSION_USER);
        return BeanUtil.getPart(account, Arrays.asList(Account.USERNAME, Account.NICKNAME,
                Account.AVATAR, Account.GENDER, ConstantUtil.KEY_SOLICITOR_SN, ConstantUtil.KEY_SOLICITOR_NAME,
                ConstantUtil.KEY_SOLICITOR_ID));

    }

}
