<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="ehcache.xsd"
         updateCheck="false">
    <diskStore path="java.io.tmpdir"/>

    <!-- objects are evicted from the cache every 60 seconds -->
    <cache name="foo"
           timeToLiveSeconds="60"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>

</ehcache>
