<?xml version="1.0" encoding="UTF-8"?>
<!--
Data and Service layers
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jpa="http://www.springframework.org/schema/data/jpa"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
			   http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa.xsd
			   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
			   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
  
  <bean class="com.wosai.upay.common.helper.UpayMethodValidationPostProcessor">
    <property name="validatedAnnotationType" value="com.wosai.upay.helper.CorePlatformsValidated"/>
  </bean>

  <context:component-scan base-package="com.wosai.upay.service"/>

  <bean class="com.fasterxml.jackson.databind.ObjectMapper"/>

  <context:property-placeholder location="classpath:spring/flavor-${shouqianba.flavor:default}.properties"/>

  <import resource="dynamic-beans.xml" />
  <bean class="com.wosai.upay.service.AstHelperImpl"/>
  <bean class="com.wosai.upay.service.CommonLoginServiceImpl"/>
  <bean class="com.wosai.upay.service.CoreUserServiceImpl"/>
  <bean class="com.wosai.upay.service.SmsSendServiceImpl">
    <property name="smsHost" value="${sms.host}"></property>
  </bean>

  <bean class="com.wosai.upay.service.SspServiceImpl">
    <constructor-arg name="backendUpayUrl" value="${backend.upay.server}"></constructor-arg>
  </bean>

    <bean id="jsonRpcRequestListener" class="com.wosai.upay.helper.JsonRpcRequestListener">
        <property name="projectName" value="web-platforms-ssp"></property>
    </bean>

  <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
    <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/store"></property>
    <property name="serviceInterface" value="com.wosai.upay.core.service.StoreService"></property>
    <property name="requestListener" ref="jsonRpcRequestListener"/>
  </bean>

  <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
    <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/terminal"></property>
    <property name="serviceInterface" value="com.wosai.upay.core.service.TerminalService"></property>
    <property name="requestListener" ref="jsonRpcRequestListener"/>
  </bean>
  <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
    <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/solicitor"></property>
    <property name="serviceInterface" value="com.wosai.upay.core.service.SolicitorService"></property>
    <property name="requestListener" ref="jsonRpcRequestListener"/>
  </bean>
  <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
    <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/user"></property>
    <property name="serviceInterface" value="com.wosai.upay.user.api.service.UserService"></property>
    <property name="requestListener" ref="jsonRpcRequestListener"/>
  </bean>

  <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
    <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/application"></property>
    <property name="serviceInterface" value="com.wosai.upay.merchant.audit.api.service.ApplicationService"></property>
    <property name="requestListener" ref="jsonRpcRequestListener"/>
  </bean>


  <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
    <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/merchant"></property>
    <property name="serviceInterface" value="com.wosai.upay.core.service.MerchantService"></property>
    <property name="requestListener" ref="jsonRpcRequestListener"/>
  </bean>

  <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
    <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/tradeConfig"></property>
    <property name="serviceInterface" value="com.wosai.upay.core.service.TradeConfigService"></property>
    <property name="requestListener" ref="jsonRpcRequestListener"/>
  </bean>
  <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
    <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/common"></property>
    <property name="serviceInterface" value="com.wosai.upay.core.service.BusinssCommonService"></property>
    <property name="requestListener" ref="jsonRpcRequestListener"/>
  </bean>

  <bean class="com.wosai.upay.service.CommonServiceImpl"/>
  <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
      <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}/rpc/industry"></property>
      <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.IndustryService"></property>
    <property name="requestListener" ref="jsonRpcRequestListener"/>
  </bean>

  <!-- 定义文件上传 -->
  <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver"/>

</beans>
