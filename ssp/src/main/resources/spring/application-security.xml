<?xml version="1.0" encoding="UTF-8"?>

<beans:beans xmlns="http://www.springframework.org/schema/security"
    xmlns:beans="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
                        http://www.springframework.org/schema/security http://www.springframework.org/schema/security/spring-security.xsd">

  <!-- HTTP authentication & authorization -->
  <!--
  <http pattern="/rpc/**" security="none"/>
  -->

  <!-- Authenticaton Manager -->
  <!--
  <authentication-manager alias="authenticationManager">
    <authentication-provider ref="passwordAuthenticationProvider"/>
  </authentication-manager>

  <beans:bean id="passwordAuthenticationProvider" class="com.wosai.tesla.authentication.PasswordAuthenticationProvider"/>
  -->

  <!-- Service based authorization -->
  <!--
  <global-method-security>
    <protect-pointcut expression="execution(* com.wosai.taiji.service.remote.RemoteAccountService.verify(..))" access="IS_AUTHENTICATED_ANONYMOUSLY"/>
    <protect-pointcut expression="execution(* com.wosai.taiji.service.remote.*Service.*(..))" access="IS_AUTHENTICATED_ANONYMOUSLY"/>
    <protect-pointcut expression="execution(* com.wosai.taiji.service.AccountService.getAccount(..))" access="ROLE_WEB,ROLE_DEVICE"/>
    <protect-pointcut expression="execution(* com.wosai.taiji.service.*Service.*(..))" access="ROLE_DEVICE"/>
  </global-method-security>
  -->

</beans:beans>
