package com.wosai.upay.service

import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


@ContextConfiguration(locations = ["classpath:spring/business-config.xml"])
@RunWith(SpringJUnit4ClassRunner.class)
class ServiceTest {


    @BeforeClass
    static void setup() {

    }

    @AfterClass
    static void tearDown() {

    }

    @Test
    void testSave() {

    }
}
