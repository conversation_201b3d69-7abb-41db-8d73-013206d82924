#!/bin/sh
set -e
for arg in $@
do
  if [[ $arg == -D* ]]; then
    EXTRA_OPTS="$EXTRA_OPTS $arg"
  fi
done

if [ -z "$JETTY_PORT" ]; then
  JETTY_PORT=8080
fi

if [ -z "$JETTY_HOME" ]; then
  JETTY_HOME=/app/bin/jetty
fi

if [ -z "$JETTY_CONFIG_DIR" ]; then
  JETTY_CONFIG_DIR=$JETTY_HOME/etc
fi

if [ -z "$JETTY_LOG_DIR" ]; then
  JETTY_LOG_DIR=/app/log
fi

if [ -z "$JVM_MEM_OPTS" ]; then
  JVM_MEM_OPTS="-Xms256m -Xmx1g"
fi

if [ -z "$APP_FLAVOR" ]; then
    APP_FLAVOR=docker
fi

APP_LOG_DIR=$JETTY_LOG_DIR

APP_OPTS="-Dshouqianba.flavor=$APP_FLAVOR -DlogDir=$APP_LOG_DIR -Dlogdir=$APP_LOG_DIR"
JETTY_OPTS="-Djetty.home=$JETTY_HOME -Djetty.logs=$JETTY_LOG_DIR -Djetty.port=$JETTY_PORT"

JVM_OPTS="$JVM_MEM_OPTS $JETTY_OPTS $APP_OPTS $EXTRA_OPTS"

JETTY_CONFIGS="$JETTY_CONFIG_DIR/jetty-logging.xml $JETTY_CONFIG_DIR/jetty-requestlog.xml"

CMD="java $JVM_OPTS -jar $JETTY_HOME/start.jar $JETTY_CONFIGS"

echo running $CMD
exec $CMD
