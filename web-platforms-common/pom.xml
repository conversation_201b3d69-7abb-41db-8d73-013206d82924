<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <dependencies>
        <dependency>
            <groupId>com.wosai.oss</groupId>
            <artifactId>oss-service-pro</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>web-platforms-sdk</artifactId>
            <version>1.2.2-beez-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <parent>
        <groupId>com.wosai.upay</groupId>
        <artifactId>web-platforms-parent</artifactId>
        <version>1.2.2-beez-SNAPSHOT</version>
    </parent>

    <artifactId>web-platforms-common</artifactId>
    <version>1.2.2-beez-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <build>
        <plugins>
            <!-- we need this mojo to add extra source folder such that maven-sources-plugin functions properly -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/groovy</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.5</version>
                <executions>
                    <execution>
                        <id>copy-groovy</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.outputDirectory}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${project.build.sourceDirectory}</directory>
                                    <includes>
                                        <include>**/*.groovy</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <directory>${project.basedir}/src/main/groovy</directory>
                                    <includes>
                                        <include>**/*.groovy</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.1</version>
                    <configuration>
                        <compilerId>groovy-eclipse-compiler</compilerId>
                        <!--
                              <compilerArgument>nowarn</compilerArgument>
                        -->
                        <compilerArguments>
                            <Xlint/>
                        </compilerArguments>
                        <verbose>false</verbose>
                        <source>1.7</source>
                        <target>1.7</target>
                        <showWarnings>true</showWarnings>
                        <useIncrementalCompilation>true</useIncrementalCompilation>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.codehaus.groovy</groupId>
                            <artifactId>groovy-eclipse-compiler</artifactId>
                            <version>2.9.1-01</version>
                        </dependency>
                        <dependency>
                            <groupId>org.codehaus.groovy</groupId>
                            <artifactId>groovy-eclipse-batch</artifactId>
                            <version>2.3.7-01</version>
                        </dependency>
                        <!--  Uncomment to compile against Groovy 1.7.10 -->
                        <!-- <dependency>
                             <groupId>org.codehaus.groovy</groupId>
                             <artifactId>groovy-eclipse-batch</artifactId>
                             <version>1.7.10-02</version>
                             </dependency> -->
                    </dependencies>
                </plugin>

                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.codehaus.mojo</groupId>
                                        <artifactId>build-helper-maven-plugin</artifactId>
                                        <versionRange>[1.0.0,)</versionRange>
                                        <goals>
                                            <goal>add-source</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                        <!--
                                        <execute>
                                          <runOnIncremental>true</runOnIncremental>
                                                </execute>
                                        -->
                                    </action>
                                </pluginExecution>
                                <!--
                                    <pluginExecution>
                              <pluginExecutionFilter>
                                <groupId>org.apache.maven.plugins</groupId>
                                <artifactId>maven-resources-plugin</artifactId>
                                <versionRange>[1.0.0,)</versionRange>
                                <goals>
                                  <goal>copy-resources</goal>
                                </goals>
                              </pluginExecutionFilter>
                              <action>
                                <execute>
                                  <runOnIncremental>true</runOnIncremental>
                                        </execute>
                              </action>
                                    </pluginExecution>
                                -->
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>

            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>


</project>
