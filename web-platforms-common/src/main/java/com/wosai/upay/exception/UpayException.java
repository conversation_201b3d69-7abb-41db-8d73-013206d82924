package com.wosai.upay.exception;

import com.wosai.upay.core.exception.CoreException;
import com.wosai.upay.common.exception.RuntimeWithCodeException;

import java.util.LinkedHashMap;
import java.util.Map;

@SuppressWarnings("serial")
public class UpayException extends RuntimeWithCodeException {
    private int code;
    private String message;

    // 对外业务接口异常码以50开头
    // 处理正常
    public static final int CODE_SUCCESS = 50000;

    // 501*的为通用异常 - 前置项目通用异常和core-business项目的通用异常区分开
    public static final int CODE_INVALID_PARAMETER = 50101;
    public static final int CODE_IO_EXCEPTION = 50102;
    public static final int CODE_NET_CONNECT_ERROR = 50103;
    public static final int CODE_ACCESS_DENIED = 50104;
    public static final int CODE_UNKNOWN_ERROR = 50105;

    // 其他情况异常使用>=502*的异常编码
    // public static final int CODE_XXX_ERROR = 50201;
    public static final int CODE_NEED_LOGIN = 50201;
    public static final int CODE_AUTH_CODE_ERROR = 50202;
    public static final int CODE_NEED_AUTH_CODE = 50203;
    public static final int CODE_AUTH_CODE_SEND_FAIL = 50204;
    public static final int CODE_PASSWORD_ERROR = 50205;
    public static final int CODE_SESSION_ERROR = 50206;
    public static final int CODE_OLD_PASSWORD_ERROR_WHEN_MODIFY = 50207;
    public static final int CODE_PUSH_MESSAGE_FAIL = 50208;
    public static final int CODE_PASSWORD_RETRY_FREQUENT = 50209;
    public static final int CODE_ACCOUNT_OTHER_PLACE_LOGINED_IN = 50210;

    public static final Map<Integer, String> CODES_DESC_MAP = new LinkedHashMap<>();

    static {
        CODES_DESC_MAP.putAll(CoreException.CODES_DESC_MAP); // 将core-business的异常code存入
        CODES_DESC_MAP.put(CODE_SUCCESS, "处理成功");
        CODES_DESC_MAP.put(CODE_INVALID_PARAMETER, "参数不合法");
        CODES_DESC_MAP.put(CODE_IO_EXCEPTION, "IO异常");
        CODES_DESC_MAP.put(CODE_NET_CONNECT_ERROR, "网络链接异常");
        CODES_DESC_MAP.put(CODE_ACCESS_DENIED, "拒绝访问");
        CODES_DESC_MAP.put(CODE_UNKNOWN_ERROR, "系统错误");
        CODES_DESC_MAP.put(CODE_NEED_LOGIN, "需要登录后才能操作");
        CODES_DESC_MAP.put(CODE_AUTH_CODE_ERROR, "验证码错误");
        CODES_DESC_MAP.put(CODE_NEED_AUTH_CODE, "需要验证码验证");
        CODES_DESC_MAP.put(CODE_AUTH_CODE_SEND_FAIL, "验证码发送失败");
        CODES_DESC_MAP.put(CODE_PASSWORD_ERROR,"密码错误");
        CODES_DESC_MAP.put(CODE_SESSION_ERROR,"未在Session中取到所需的值");
        CODES_DESC_MAP.put(CODE_OLD_PASSWORD_ERROR_WHEN_MODIFY, "输入的原密码不正确");
        CODES_DESC_MAP.put(CODE_PASSWORD_RETRY_FREQUENT, "账号或密码多次错误，请1个小时后再试");
        CODES_DESC_MAP.put(CODE_ACCOUNT_OTHER_PLACE_LOGINED_IN, "您的账号已在其他位置登录，当前登录被迫下线");
    }

    public UpayException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    public UpayException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public static String getCodeDesc(int code) {
        return CODES_DESC_MAP.get(code);
    }

    public String getMessage() {
        return this.message;
    }

    public static void main(String[] args) {
        for (int i : CODES_DESC_MAP.keySet()) {
            System.out.println(i + " | " + CODES_DESC_MAP.get(i));
        }
    }

}
