package com.wosai.upay.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.exception.UpayException;
import org.apache.commons.lang.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Created by jianfree on 30/3/16.
 */
public class LoginCheckerFilter implements Filter {
    private String sessionKey = null;
    private Set<String> notCheckUrls = new HashSet();
    private ObjectMapper om = new ObjectMapper();
    private final String HTTP_METHOD_OPTIONS = "OPTIONS";

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        sessionKey = filterConfig.getInitParameter("sessionKey");
        String notCheckUrlsString = filterConfig.getInitParameter("notCheckUrls");
        if(!StringUtil.empty(notCheckUrlsString)){
            for(String url: notCheckUrlsString.split(",")){
                notCheckUrls.add(url.toLowerCase());
            }
        }
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        if(HTTP_METHOD_OPTIONS.equals(req.getMethod().toUpperCase())){
            chain.doFilter(request, response);
            return;
        }
        String path = req.getServletPath().toLowerCase();
        boolean needCheck = true;
        if (StringUtils.isBlank(path) || StringUtils.equals(path, "/")) {
            needCheck = false;
        } else {
            for(String notCheckUrl: notCheckUrls){
                if(path.endsWith(notCheckUrl)){
                    needCheck = false;
                    break;
                }
            }
        }

        if(needCheck){
            HttpSession session = req.getSession();
            if(session == null || session.getAttribute(sessionKey) == null){
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                Map map = CollectionUtil.hashMap(
                        "code", UpayException.CODE_NEED_LOGIN,
                        "msg", "请重新登录",
                        "data",null
                );
                response.getWriter().write(om.writeValueAsString(map));
                return;
            }
        }
        chain.doFilter(request, response);
    }



    @Override
    public void destroy() {

    }
}
