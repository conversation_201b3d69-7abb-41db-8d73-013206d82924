package com.wosai.upay.filter

import javax.servlet.*
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * Created by jian<PERSON> on 22/10/15.
 * 处理跨越问题
 */
class CrossDomainFilter implements Filter{

    @Override
    void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        String origin = "*"
        if(request instanceof HttpServletRequest){
            origin = request.getHeader("origin")
        }
        if(response instanceof HttpServletResponse){
            response.setHeader("Access-Control-Allow-Origin", origin)
            response.setHeader("Access-Control-Allow-Credentials", "true")
            response.setHeader("Access-Control-Allow-Headers", "Origin, X-Request-With, Content-Type, Accept,businessLog")
        }
        chain.doFilter(request, response)
    }

    @Override
    void destroy() {

    }
}
