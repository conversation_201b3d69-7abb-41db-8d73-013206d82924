package com.wosai.upay.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.sdk.support.CommonRedisOps;
import com.wosai.upay.constant.MspLoginRedisPrefix;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.service.CommonLoginServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.context.support.WebApplicationContextUtils;
import org.springframework.web.context.support.XmlWebApplicationContext;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Description Session拦截器，用于 账号只能在一处登录，jira:core-192
 * <AUTHOR>
 * @Datetime 2019-04-25 17:08
 */
public class SessionFilter implements Filter {

    private static Logger log = LoggerFactory.getLogger(SessionFilter.class);

    private ObjectMapper om = new ObjectMapper();

    private Set<String> notCheckUrls = new HashSet();

    private final String HTTP_METHOD_OPTIONS = "OPTIONS";

    private static CommonRedisOps commonRedisOps;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        String notCheckUrlsString = filterConfig.getInitParameter("notCheckUrls");
        if (!StringUtil.empty(notCheckUrlsString)) {
            for (String url : notCheckUrlsString.split(",")) {
                notCheckUrls.add(url.toLowerCase());
            }
        }
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        if(commonRedisOps == null) {
            ServletContext sc = request.getSession().getServletContext();
            XmlWebApplicationContext cxt = (XmlWebApplicationContext) WebApplicationContextUtils.getWebApplicationContext(sc);

            if(cxt != null) {
                commonRedisOps = cxt.getBean(CommonRedisOps.class);
            }
        }

        if (HTTP_METHOD_OPTIONS.equals(request.getMethod().toUpperCase())) {
            filterChain.doFilter(request, response);
            return;
        }

        String requestPath = request.getServletPath().toLowerCase();
        boolean needCheck = true;
        if (StringUtils.isBlank(requestPath) || StringUtils.equals(requestPath, "/")) {
            needCheck = false;
        } else {
            for (String notCheckUrl : notCheckUrls) {
                if (requestPath.endsWith(notCheckUrl)) {
                    needCheck = false;
                    break;
                }
            }
        }

        // 拦截非登录、非退出登录、非根目录的请求（也就是已登录状态下）
        if (needCheck) {
            String username = (String) request.getSession().getAttribute(CommonLoginServiceImpl.SESSION_USERNAME);
            String sessionId = request.getSession().getId();

            // 实际有效的session，也就是Redis存储的Session（一定存在Redis中）
            String effectiveSessionId = username == null ? "" : commonRedisOps.get(MspLoginRedisPrefix.MSP_LOGIN_ONLY_ONE_PLACE + username);

            // 当前sessionId为有效的情况下，刷新Redis中有效期
            if (sessionId.equalsIgnoreCase(effectiveSessionId)) {
                commonRedisOps.set(MspLoginRedisPrefix.MSP_LOGIN_ONLY_ONE_PLACE + username, sessionId, 1, TimeUnit.DAYS);
            } else {
                log.info("effectiveSessionId {} sessionId {}", effectiveSessionId, sessionId);
                // 否则，退出登录
                request.getSession().invalidate();

                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                Map map = CollectionUtil.hashMap(
                        "code", UpayException.CODE_ACCOUNT_OTHER_PLACE_LOGINED_IN,
                        "msg", "您的账号已在其他位置登录，当前登录被迫下线，若非本人登录，请及时更改密码。",
                        "data", null);
                response.getWriter().write(om.writeValueAsString(map));
                return;
            }
        }

        filterChain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void destroy() {

    }
}
