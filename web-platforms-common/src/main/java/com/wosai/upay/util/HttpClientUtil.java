package com.wosai.upay.util;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;

/**
 * HttpClient Util.
 *
 * <AUTHOR>
 */
public class HttpClientUtil {

    private static Logger log = LoggerFactory.getLogger(HttpClientUtil.class);

    /**
     * 编码.
     */
    public static final String ENCODING_DEFAULT = "UTF-8";

    /**
     * blank json string.
     */
    public static final String BLANK_JSON_STRING = "{}";

    /**
     * 获取响应结果.
     *
     * @param url
     * @return
     */
    public static String getResponseStringDefaultErrorValue(String url) {
        return getResponseStringWithDefaultErrorValue(url, true);
    }

    /**
     * 获取响应结果.
     *
     * @param url
     * @param isToUtf8
     * @return
     */
    public static String getResponseStringWithDefaultErrorValue(String url, boolean isToUtf8) {
        String rs = BLANK_JSON_STRING;
        try {
            rs = getResponseString(url, isToUtf8);
        } catch (Exception e) {
            log.error("Http request error", e);
        }
        return rs;
    }

    /**
     * 获取响应结果.
     *
     * @param url
     * @return
     * @throws IOException
     */
    public static String getResponseString(String url) throws IOException {
        return getResponseString(url, true);
    }

    /**
     * 获取响应结果.
     *
     * @param url
     * @param isToUtf8
     * @return
     * @throws IOException
     */
    public static String getResponseString(String url, boolean isToUtf8) throws IOException {
        CloseableHttpClient httpClient = HttpClientManagerUtil.getHttpClient();
        HttpGet httpGet = null;
        CloseableHttpResponse httpResponse = null;
        try {
            httpGet = new HttpGet(url);
            httpResponse = httpClient.execute(httpGet);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            // 正确时code为200
            log.debug("Response status code: " + statusCode);

            String rs;
            // 获取返回数据
            if (isToUtf8) {
                rs = getResponseEntityString(httpResponse);
            } else {
                HttpEntity entity = httpResponse.getEntity();
                rs = EntityUtils.toString(entity);
            }

            log.debug("Response data: " + rs);
            return rs;
        } catch (IOException e) {
            throw new IOException(e);
        } finally {
            try {
                if (httpResponse != null) {
                    httpResponse.close();
                }
            } catch (IOException e) {
                log.error("close response error", e);
            }
        }
    }

    /**
     * 获取响应结果.
     *
     * @param url
     * @param body
     * @return
     * @throws IOException
     */
    public static String getPostResponseString(String url, String body) throws IOException {
        return getPostResponseString(url, body, null);
    }

    /**
     * 获取响应结果.
     *
     * @param url
     * @param body
     * @param customHeaders
     * @return
     * @throws IOException
     */
    public static String getPostResponseString(String url, String body, List<BasicHeader> customHeaders) throws IOException {
        CloseableHttpClient httpClient = HttpClientManagerUtil.getHttpClient();
        HttpPost httpPost = null;
        CloseableHttpResponse httpResponse = null;
        HttpEntity entity = null;

        try {
            httpPost = new HttpPost(url);
            httpPost.addHeader(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
            httpPost.addHeader(new BasicHeader(HTTP.CONTENT_ENCODING, ENCODING_DEFAULT));
            if (customHeaders != null) {
                for (BasicHeader header : customHeaders) {
                    httpPost.addHeader(header);
                }
            }
            entity = new InputStreamEntity(new ByteArrayInputStream(body.getBytes(ENCODING_DEFAULT)));
            httpPost.setEntity(entity);
            httpResponse = httpClient.execute(httpPost);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            // 正确时code为200
            log.debug("Response status code: " + statusCode);

            // 获取返回数据
            String rs = getResponseEntityString(httpResponse);

            log.debug("Response data: " + rs);
            return rs;
        } catch (IOException e) {
            throw new IOException(e);
        } finally {
            try {
                if (entity != null) {
                    EntityUtils.consume(entity);
                }
                if (httpResponse != null) {
                    httpResponse.close();
                }
            } catch (IOException e) {
                log.error("close response error", e);
            }
        }
    }

    /**
     * Get response string.
     *
     * @param httpResponse
     * @return
     * @throws IOException
     */
    private static String getResponseEntityString(HttpResponse httpResponse) throws IOException {
        // 获取返回数据
        HttpEntity entity = httpResponse.getEntity();
        return getInputStreamString(entity.getContent());
    }

    public static String getInputStreamString(InputStream inputStream) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, ENCODING_DEFAULT));
        StringBuilder resSb = new StringBuilder();
        String resTemp;
        while ((resTemp = br.readLine()) != null) {
            resSb.append(resTemp);
        }
        return resSb.toString();
    }

}
