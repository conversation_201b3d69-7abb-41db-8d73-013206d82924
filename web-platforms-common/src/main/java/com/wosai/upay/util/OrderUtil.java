package com.wosai.upay.util;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.common.util.JacksonUtil;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by jianfree on 5/4/16.
 * 订单交易相关
 */
public class OrderUtil {
    private static Logger log = LoggerFactory.getLogger(OrderUtil.class);

    /**
     * es 查询源交易订单号接口
     */
    public static final String ES_SEARCH_CHANNEL_TRADE_NO_URL = "https://xzg.shouqianba.com/api/trend/platform/transactionFetch?field=channel_trade_no&value=";

    public static final String ES_SEARCH_TRADE_NO_URL = "https://xzg.shouqianba.com/api/trend/platform/transactionFetch?field=trade_no&value=";
    
    public static LinkedHashMap<String, String> providerMap;
    public static LinkedHashMap<String, String> paywayMap;
    public static LinkedHashMap<String, String> subPaywayMap;
    public static LinkedHashMap<String, String> orderStatusMap;
    public static LinkedHashMap<String, String> transactionStatusMap;
    public static LinkedHashMap<String, String> transactionTypeMap;
    public static LinkedHashMap<String, String> orderExportColumnMap;
    public static LinkedHashMap<String, String> orderExportColumnMap2;
    public static LinkedHashMap<String, String> terminalTypeMap;


    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(3);
        map.put("1001", "兴业银行");
        map.put("1002", "拉卡拉");
        providerMap = map;
    }

    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(5);
        map.put("1", "支付宝");
        map.put("2", "支付宝2.0");
        map.put("3", "微信");
        map.put("4", "百度钱包");
        map.put("5", "京东钱包");
        map.put("6", "qq钱包");
        map.put("8", "拉卡拉钱包");
        map.put("17", "银联二维码");
        paywayMap = map;
    }


    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(3);
        map.put("1", "B扫C");
        map.put("2", "C扫B");
        map.put("3", "WAP支付");
        subPaywayMap = map;
    }



    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(11);
        map.put("0", "待支付");
        map.put("1200", "已支付");
        map.put("1300", "已关闭");
        map.put("1501", "支付失败");
        map.put("2201", "已全额退款");
        map.put("2100", "退款中");
        map.put("2210", "已部分退款");
        map.put("2501", "退款失败");
        map.put("3100", "撤单中");
        map.put("3201", "已撤单");
        map.put("3501", "撤单失败");
        orderStatusMap = map;
    }


    /**
     * 新支付网关交易状态表
     */
    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(14);
        map.put("0", "未处理");
        map.put("2000", "成功");
        map.put("2001", "失败");
        map.put("2101", "失败");
        map.put("2102", "失败");
        map.put("2103", "失败");
        map.put("2104", "失败");
        map.put("2105", "失败");
        map.put("2106", "失败");
        map.put("2107", "失败");
        map.put("2108", "失败");
        map.put("2109", "失败");
        map.put("1001", "处理中");
        map.put("1002", "失败");
        transactionStatusMap = map;
    }

    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(3);
        map.put("30", "付款");
        map.put("31", "退款撤销");
        map.put("11", "退款");
        map.put("10", "撤单");
        transactionTypeMap = map;
    }

    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(6);
        map.put("10", "Android应用");
        map.put("11", "iOS应用");
        map.put("20", "Windows桌面应用");
        map.put("30", "专用设备");
        map.put("40", "门店码");
        map.put("50", "服务");
        terminalTypeMap = map;
    }

    /**
     * 导出订单字段
     */
    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
        map.put("day", "日期");
        map.put("time", "时间");
        map.put("sn", "商户订单号");
        map.put("trade_no", "收款通道订单号");
        map.put("client_sn", "商户内部订单号");
        map.put("status", "订单状态");
        map.put("original_total", "订单金额");
        map.put("discount", "优惠减免");
        map.put("net_original", "实收金额");
        map.put("terminal_name", "终端名称");
        map.put("terminal_sn", "终端编号");
        map.put("store_name", "门店名称");
        map.put("store_sn", "门店编号");
        map.put("merchant_name", "商户名称");
        map.put("merchant_sn", "商户编号");
        map.put("payway", "收款通道");
        map.put("sub_payway", "交易模式");
        map.put("buyer_login", "付款账户");
        map.put("liquidation_next_day", "是否试用");
        map.put("fee_rate", "扣率");
        map.put("fee", "手续费");
        map.put("operator", "操作员");
        map.put("provider", "支付通道");

        orderExportColumnMap = map;
    }
    /**
     * 导出订单字段
     */
    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
        map.put("day", "日期");
        map.put("time", "时间");
        map.put("sn", "商户订单号");
        map.put("trade_no", "收款通道订单号");
        map.put("client_sn", "商户内部订单号");
        map.put("status", "订单状态");
        map.put("original_total", "订单金额");
        map.put("discount", "优惠减免");
        map.put("net_original", "实收金额");
        map.put("terminal_name", "终端名称");
        map.put("terminal_sn", "终端号");
        map.put("terminal_type", "终端类型");
        map.put("terminal_device_sn", "设备号");
        map.put("store_name", "门店名称");
        map.put("store_sn", "门店号");
        map.put("merchant_name", "商户名称");
        map.put("merchant_sn", "商户号");
        map.put("payway", "收款通道");
        map.put("sub_payway", "交易模式");
        map.put("buyer_login", "付款账户");
        map.put("liquidation_next_day", "是否试用");
        map.put("fee_rate", "扣率");
        map.put("fee", "手续费");
        map.put("operator", "操作员");
        map.put("operator_name", "收银员");

        orderExportColumnMap2 = map;
    }

    public static String getProviderDesc(Object provider){
        if(provider == null){
            return null;
        }else{
            return providerMap.get(provider.toString());
        }
    }

    public static String getPaywayDesc(Object payway){
        if(payway == null){
            return null;
        }else{
            return paywayMap.get(payway.toString());
        }
    }

    public static String getSubPaywayDesc(Object subPayway){
        if(subPayway == null){
            return null;
        }else{
            return subPaywayMap.get(subPayway.toString());
        }
    }

    public static String getOrderStatusDesc(Object orderStatus){
        if(orderStatus == null){
            return null;
        }else{
            return orderStatusMap.get(orderStatus.toString());
        }
    }

    public static String getTransactionStatusDesc(Object transactionStatus){
        if(transactionStatus == null){
            return null;
        }else{
            return transactionStatusMap.get(transactionStatus.toString());
        }
    }

    public static String getTransactionTypeDesc(Object type){
        if(type == null){
            return null;
        }else{
            return transactionTypeMap.get(type.toString());
        }
    }
    public static String getTerminallTypeDesc(Object type){
        if(type == null){
            return null;
        }else{
            return terminalTypeMap.get(type.toString());
        }
    }



    /**
     * 四舍五入保留多少位小数点
     * @param money
     * @param scaleSize
     * @return
     */
    public static double formatMoney(Object money, int scaleSize) {
        if (money == null) {
            return 0.00;
        }
        return new BigDecimal(money.toString()).setScale(scaleSize, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 金额转为字符串
     * @param money
     * @param scaleSize
     * @return
     */
    public static String formatMoneyToString(Object money, int scaleSize){
        if(money == null){
            return "";
        }else{
            return new BigDecimal(money.toString()).setScale(scaleSize, BigDecimal.ROUND_HALF_UP).toString();
        }
    }


    public static HSSFWorkbook buildOrdersExcel(List<Map> orderInfos){
        Collections.sort(orderInfos,new Comparator<Map>() {
            @Override
            public int compare(Map o1, Map o2) {
                long o1Ctime =  BeanUtil.getPropLong(o1, "ctime", 0);
                long o2Ctime = BeanUtil.getPropLong(o2, "ctime", 0);
                if(o1Ctime > o2Ctime){
                    return 1;
                }else if(o1Ctime == o2Ctime){
                    return 0;
                }{
                    return -1;
                }
            }
        } );
        SimpleDateFormat daySdf =  new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat timeSdf =  new SimpleDateFormat("HH:mm:ss");
        for(Map orderInfo: orderInfos){
            Date ctime = BeanUtil.getPropString(orderInfo, DaoConstants.CTIME) != null?new Date(BeanUtil.getPropLong(orderInfo, DaoConstants.CTIME)): null;
            long netOriginal = BeanUtil.getPropLong(orderInfo, "net_original", 0);
            long netEffective = BeanUtil.getPropLong(orderInfo, "net_effective", 0);
            Double feeRate = formatMoney(BeanUtil.getPropString(orderInfo, "fee_rate", "0.0"), 2);
            Boolean liquidationNextDay;
            if(BeanUtil.getPropString(orderInfo, "liquidation_next_day").equals("true")){
                liquidationNextDay = true;
            }else {
                liquidationNextDay = BeanUtil.getPropBoolean(orderInfo, "liquidation_next_day");
            }
            long discount = netOriginal - netEffective;
            double fee = formatMoney(netOriginal * feeRate * 0.01 /100.0 * -1, 2); //负数
            orderInfo.put("day", ctime!=null?daySdf.format(ctime):null);
            orderInfo.put("time", ctime!=null?timeSdf.format(ctime):null);
            orderInfo.put("payway", getPaywayDesc(BeanUtil.getPropString(orderInfo, "payway")));
            orderInfo.put("sub_payway", getSubPaywayDesc(BeanUtil.getPropString(orderInfo, "sub_payway")));
            orderInfo.put("status", getOrderStatusDesc(BeanUtil.getPropString(orderInfo, "status")));
            orderInfo.put("discount", formatMoney(discount/100.0, 2));
            orderInfo.put("original_total", formatMoney(BeanUtil.getPropLong(orderInfo, "original_total", 0)/100.0, 2));
            orderInfo.put("net_original", formatMoney(BeanUtil.getPropLong(orderInfo, "net_original", 0)/100.0, 2));
            orderInfo.put("liquidation_next_day", liquidationNextDay == true? "是": "否");
            orderInfo.put("fee_rate", feeRate); //费率
            orderInfo.put("fee", fee); //手续费
            orderInfo.put("provider", getProviderDesc(BeanUtil.getPropString(orderInfo, "provider"))); //手续费
        }

        long count = orderInfos.size();
        double sumAmount = 0.0d;
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("订单数据");
        SheetUtil sheetUtil = new SheetUtil(sheet);

        List<String> columnArr = Arrays.asList(
                "day", "time", "sn", "status", "payway", "sub_payway",
                "original_total", "net_original", "liquidation_next_day", "fee_rate", "fee",
                "merchant_name", "merchant_sn", "store_name","store_sn", "terminal_name", "terminal_sn",
                "buyer_login", "trade_no", "operator", "client_sn", "provider"
        );
        List headers = new ArrayList();
        for (String column: columnArr){
            headers.add(orderExportColumnMap.get(column));
        }
        sheetUtil.appendRow(headers);
        for (Map orderInfo: orderInfos){
            List values = new ArrayList();
            for (String column: columnArr){
                Object value = BeanUtil.getProperty(orderInfo, column);
                values.add(value);
                if(column.equals("net_original")){
                    sumAmount = formatMoney(sumAmount + formatMoney(value, 2), 2);
                }
            }
            sheetUtil.appendRow(values);
        }
        sheetUtil.appendRow(Arrays.asList("总实收金额为："+ formatMoneyToString(sumAmount, 2)+", 总笔数为: " + count));
        sheetUtil.mergeCell(sheetUtil.getCurrentRow(), 0, sheetUtil.getCurrentRow(), 8);
        return workbook;
    }

    public static HSSFWorkbook buildOrdersExcelV2(List<Map> orderInfos){
        Collections.sort(orderInfos,new Comparator<Map>() {
            @Override
            public int compare(Map o1, Map o2) {
                long o1Ctime =  BeanUtil.getPropLong(o1, "ctime", 0);
                long o2Ctime = BeanUtil.getPropLong(o2, "ctime", 0);
                if(o1Ctime > o2Ctime){
                    return 1;
                }else if(o1Ctime == o2Ctime){
                    return 0;
                }{
                    return -1;
                }
            }
        } );
        SimpleDateFormat daySdf =  new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat timeSdf =  new SimpleDateFormat("HH:mm:ss");
        for(Map orderInfo: orderInfos){
            Date ctime = BeanUtil.getPropString(orderInfo, DaoConstants.CTIME) != null?new Date(BeanUtil.getPropLong(orderInfo, DaoConstants.CTIME)): null;
            long netOriginal = BeanUtil.getPropLong(orderInfo, "net_original", 0);
            long netEffective = BeanUtil.getPropLong(orderInfo, "net_effective", 0);
            Double feeRate = formatMoney(BeanUtil.getPropString(orderInfo, "fee_rate", "0.0"), 2);
            Boolean liquidationNextDay;
            if(BeanUtil.getPropString(orderInfo, "liquidation_next_day").equals("true")){
                liquidationNextDay = true;
            }else {
                liquidationNextDay = BeanUtil.getPropBoolean(orderInfo, "liquidation_next_day");
            }
            long discount = netOriginal - netEffective;
            double fee = formatMoney(netOriginal * feeRate * 0.01 /100.0 * -1, 2); //负数
            orderInfo.put("day", ctime!=null?daySdf.format(ctime):null);
            orderInfo.put("time", ctime!=null?timeSdf.format(ctime):null);
            orderInfo.put("payway", getPaywayDesc(BeanUtil.getPropString(orderInfo, "payway")));
            orderInfo.put("sub_payway", getSubPaywayDesc(BeanUtil.getPropString(orderInfo, "sub_payway")));
            orderInfo.put("status", getOrderStatusDesc(BeanUtil.getPropString(orderInfo, "status")));
            orderInfo.put("terminal_type", getTerminallTypeDesc(BeanUtil.getPropString(orderInfo, "terminal_type")));
            orderInfo.put("discount", formatMoney(discount/100.0, 2));
            orderInfo.put("original_total", formatMoney(BeanUtil.getPropLong(orderInfo, "original_total", 0)/100.0, 2));
            orderInfo.put("net_original", formatMoney(BeanUtil.getPropLong(orderInfo, "net_original", 0)/100.0, 2));
            orderInfo.put("liquidation_next_day", liquidationNextDay == true? "是": "否");
            orderInfo.put("fee_rate", feeRate); //费率
            orderInfo.put("fee", fee); //手续费
        }

        long count = orderInfos.size();
        double sumAmount = 0.0d;
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("订单数据");
        SheetUtil sheetUtil = new SheetUtil(sheet);

        List<String> columnArr = Arrays.asList(
                "day", "time", "sn", "status", "payway", "sub_payway",
                "original_total", "net_original", "liquidation_next_day", "fee_rate", "fee",
                "merchant_name", "merchant_sn", "store_name","store_sn",
                "terminal_name", "terminal_sn", "terminal_type", "terminal_device_sn",
                "buyer_login", "trade_no", "operator", "operator_name", "client_sn"
        );
        List headers = new ArrayList();
        for (String column: columnArr){
            headers.add(orderExportColumnMap2.get(column));
        }
        sheetUtil.appendRow(headers);
        for (Map orderInfo: orderInfos){
            List values = new ArrayList();
            for (String column: columnArr){
                Object value = BeanUtil.getProperty(orderInfo, column);
                values.add(value);
                if(column.equals("net_original")){
                    sumAmount = formatMoney(sumAmount + formatMoney(value, 2), 2);
                }
            }
            sheetUtil.appendRow(values);
        }
        sheetUtil.appendRow(Arrays.asList("总实收金额为："+ formatMoneyToString(sumAmount, 2)+", 总笔数为: " + count));
        sheetUtil.mergeCell(sheetUtil.getCurrentRow(), 0, sheetUtil.getCurrentRow(), 8);
        return workbook;
    }


    /**
     * 根据 源交易订单号  查询订单sn
     * @param channelTradeNo
     * @return
     */
    public static String getOrderSnByChannelTradeNo(String channelTradeNo){
        try {
            String result =HttpClientUtil.getResponseString(ES_SEARCH_CHANNEL_TRADE_NO_URL + channelTradeNo);
            Map map = JacksonUtil.toBean(result, Map.class);
            if(BeanUtil.getPropBoolean(map, "success")){
                Map data = (Map) BeanUtil.getProperty(map, "data");
                if (data != null && !data.isEmpty()){
                    return BeanUtil.getPropString(data, "order_sn");
                }
            }
        } catch (Exception e) {
            log.error("params channelTradeNo[{}]", channelTradeNo, e);
        }
        return "";
    }

    /**
     * 根据 收款通道订单号  查询订单sn
     * @param tradeNo
     * @return
     */
    public static String getOrderSnByTradeNo(String tradeNo){
        try {
            String result =HttpClientUtil.getResponseString(ES_SEARCH_TRADE_NO_URL + tradeNo);
            Map map = JacksonUtil.toBean(result, Map.class);
            if(BeanUtil.getPropBoolean(map, "success")){
                Map data = (Map) BeanUtil.getProperty(map, "data");
                if (data != null && !data.isEmpty()){
                    return BeanUtil.getPropString(data, "order_sn");
                }
            }
        } catch (Exception e) {
            log.error("params tradeNo[{}]", tradeNo, e);
        }
        return "";
    }
    
    public static void main(String[] args) {
        System.out.println(getOrderSnByChannelTradeNo("4006222001201709111461354032"));
    }
}
