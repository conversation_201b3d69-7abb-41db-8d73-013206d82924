package com.wosai.upay.util;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.shouqianba.withdrawservice.model.NoticeRule;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.upay.Withdraw;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.util.*;

/**
 * Created by kay on 16/11/3.
 */
public class WithdrawUtil {

    public static LinkedHashMap<String, String> withdrawOpCheckStatusMap;
    public static LinkedHashMap<String, String> withdrawModeMap;
    public static List<Map> NOTICE_RULE_CODES_DESC;
    public static List<String> NOTICE_RULE_CODES;

    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(11);
        map.put("1", "提现审核中");
        map.put("2", "提现驳回");
        map.put("3", "提现冻结");
        map.put("4", "运营审核通过，待打款");
        map.put("5", "等待打款结果");
        map.put("6", "打款成功");
        map.put("7", "打款失败");
        map.put("8", "已人工打款");
        map.put("9", "打款异常");
        withdrawOpCheckStatusMap = map;
    }
    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(11);
        map.put("1", "标准提现");
        map.put("2", "智能提现");
        withdrawModeMap = map;
    }
    static {
        List<Map> list = new ArrayList<Map>();
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1001",
                NoticeRule.SCENE_DESC, "商户加入D0白名单"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1002",
                NoticeRule.SCENE_DESC, "商户移出白名单"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1003",
                NoticeRule.SCENE_DESC, "D0提现打款超时，批量勾选赔付"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1004",
                NoticeRule.SCENE_DESC, "通用规则》禁用提现"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1005",
                NoticeRule.SCENE_DESC, "通用规则》启用提现"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1006",
                NoticeRule.SCENE_DESC, "通过D0白名单管理》批量禁用商户D0"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1009",
                NoticeRule.SCENE_DESC, "通过D0白名单管理》批量启用商户D0"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1010",
                NoticeRule.SCENE_DESC, "D1打款》"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1011",
                NoticeRule.SCENE_DESC, "D0打款》"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1012",
                NoticeRule.SCENE_DESC, "存量商户补信息》"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1013",
                NoticeRule.SCENE_DESC, "真实性审核"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "1014",
                NoticeRule.SCENE_DESC, "实名认证》营业执照"
        ));

        // D0卡券
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "2001",
                NoticeRule.SCENE_DESC, "用户有新增的卡券"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "2002",
                NoticeRule.SCENE_DESC, "用户有卡券即将到期还未启用"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "2003",
                NoticeRule.SCENE_DESC, "用户有卡券未使用完毕额度过半"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "2004",
                NoticeRule.SCENE_DESC, "用户有卡券核销完毕"
        ));


        //理财相关
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "3001",
                NoticeRule.SCENE_DESC, "理财申购相关"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "3002",
                NoticeRule.SCENE_DESC, "理财收益相关"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "3003",
                NoticeRule.SCENE_DESC, "理财名单相关"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "3004",
                NoticeRule.SCENE_DESC, "理财开户关闭智能提现"
        ));
        list.add(CollectionUtil.hashMap(
                NoticeRule.CODE, "4001",
                NoticeRule.SCENE_DESC, "保险相关"
        ));

        NOTICE_RULE_CODES_DESC = list;

        List<String> codes = new ArrayList<>();
        for(Map map : list){
            codes.add(BeanUtil.getPropString(map, NoticeRule.CODE));
        }
        NOTICE_RULE_CODES = codes;
    }


    /**
     * 提现流水导出
     * @param withdraws
     * @return
     */
    public static HSSFWorkbook buildWithdrawsExcel(List<Map> withdraws) {
        //pre format data
        for(Map withdraw: withdraws){
            withdraw.put("op_check_status", withdrawOpCheckStatusMap.get(BeanUtil.getPropString(withdraw, "op_check_status")));
            withdraw.put(Merchant.WITHDRAW_MODE, BeanUtil.getPropString(withdrawModeMap, BeanUtil.getPropString(withdraw, Merchant.WITHDRAW_MODE)));
        }
        long count = withdraws.size();
        double sumAmount = 0.0d;
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("提现数据");
        SheetUtil sheetUtil = new SheetUtil(sheet);
        List<String> headerArr = Arrays.asList(
                "日期", "时间", "商户名称", "提现状态", "提现金额", "收款人",
                "收款账户", "开户行", "联系人", "联系电话", "提现方式", "备注", "拉卡拉商户号"
        );
        sheetUtil.appendRow(headerArr);

        List<String> columnArr = Arrays.asList(
                "date", "time", ConstantUtil.KEY_MERCHANT_NAME, "op_check_status", Withdraw.AMOUNT, "account_name",
                "bankcard_no", "bank_name", "contact_name", "contact_cellphone", Merchant.WITHDRAW_MODE, "remark", "provider_mchid"
        );

        for(Map withdraw: withdraws){
            List values = new ArrayList();
            for(String column: columnArr){
                values.add(BeanUtil.getPropString(withdraw, column));
                if (column == Withdraw.AMOUNT) {
                    sumAmount += OrderUtil.formatMoney(BeanUtil.getPropString(withdraw, column), 2);
                }
            }
            sheetUtil.appendRow(values);
        }
        sheetUtil.appendRow(Arrays.asList("总实收金额为："+ OrderUtil.formatMoneyToString(sumAmount, 2)+", 总笔数为: " + count));
        sheetUtil.mergeCell(sheetUtil.getCurrentRow(), 0, sheetUtil.getCurrentRow(), 8);
        return workbook;
    }
}
