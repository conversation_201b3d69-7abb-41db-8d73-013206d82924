package com.wosai.upay.util;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2017/7/13.
 */
public class TaskApplyLogUtil {
    /**
     * 任务状态-申请
     */
    public static final int APPLY_STATUS_APPLY = 0;
    /**
     * 任务状态-执行中
     */
    public static final int APPLY_STATUS_IN_EXCUTE = 1;
    /**
     * 任务状态-执行成功
     */
    public static final int APPLY_STATUS_EXCUTE_SUCCESS = 2;
    /**
     * 任务状态-执行失败
     */
    public static final int APPLY_STATUS_EXCUTE_FAILURE = 3;


    /**
     * 操作系统-未知
     */
    public static final int APPLY_SYSTEM_UNKNOW = 1;
    /**
     * 操作系统-运营服务平台
     */
    public static final int APPLY_SYSTEM_SP = 2;
    /**
     * 操作系统-服务商
     */
    public static final int APPLY_SYSTEM_VSP = 3;
    /**
     * 操作系统-商户服务平台
     */
    public static final int APPLY_SYSTEM_MSP = 4;
    /**
     * 操作系统-推广者
     */
    public static final int APPLY_SYSTEM_SSP = 5;


    /**
     * 任务类型-对账单下载
     */
    public static final int TYPE_STATEMENT_DOWNLOAD = 1;
    /**
     * 任务类型-订单下载
     */
    public static final int TYPE_ORDER_DOWNLOAD = 2;
    /**
     * 任务类型-渠道分润报表下载
     */
    public static final int TYPE_CHANEL_SHARE_BENEFIT_DOWNLOAD = 3;
    /**
     * 任务类型-导入D0白名单
     */
    public static final int TYPE_IMPORT_D0 = 201;
    /**
     * 任务类型-批量强制结算
     */
    public static final int TYPE_INMORT_FORCE_CLEAR = 202;
    /**
     * 任务类型-批量修改D0免费次数
     */
    public static final int TYPE_BATCH_UPDATE_D0_FREECOUNT = 203;
    /**
     * 任务类型-批量修改D0规则
     */
    public static final int TYPE_BATCH_UPDATE_D0_RULE = 204;
    /**
     * 任务类型-批量禁用D0状态
     */
    public static final int TYPE_BATCH_DISABLE_D0_STATUS = 205;
    /**
     * 任务类型-批量修改推广者组织D0规则
     */
    public static final int TYPE_BATCH_UPDATE_ORINIZITION_D0_RULE = 206;
    /**
     * 任务类型-关闭提现系统发送通知
     */
    public static final int TYPE_WITHDRAW_DISABLE_SYSTEM_NOTICE = 207;
    /**
     * 任务类型-批量启用用D0状态
     */
    public static final int TYPE_BATCH_ENABLE_D0_STATUS = 208;
    /**
     * 任务类型-批量移除D0白名单
     */
    public static final int TYPE_BATCH_REMOVE_D0 = 209;
    /**
     * 任务类型-批量超时赔付
     */
    public static final int TYPE_BATCH_COMPENSATION = 210;
    /**
     * 任务类型-批量修改商户交易配置
     */
    public static final int TYPE_BATCH_CHANGE_MERCHANT_CONFIG = 211;
    /**
     * 任务类型-修改D0白名单
     */
    public static final int TYPE_BATCH_CHANGE_MERCHANT_D0_CONFIG = 212;
    /**
     * 任务类型-批量导入D0卡券白名单
     */
    public static final int TYPE_BATCH_IMPORT_D0_CARDS = 213;
    /**
     * 任务类型-批量导入D1优先批次商户白名单
     */
    public static final int TYPE_BATCH_IMPORT_D1_PRIOR_MERCHANTS = 214;
    /**
     * 任务类型-清空原有D1批次白名单
     */
    public static final int TYPE_BATCH_DELETE_OLD_D1_PRIOR_MERCHANTS = 215;

    /**
     * 任务类型-批量导入理财白名单
     */
    public static final int TYPE_IMPORT_FINANCE_WHITELIST = 216;
    /**
     * 任务类型-批量修改理财快赎免费次数
     */
    public static final int TYPE_BATCH_UPDATE_FINANCE_FREECOUNT = 217;
    /**
     * 任务类型-批量启用理财账户状态
     */
    public static final int TYPE_BATCH_ENABLE_FINANCE_STATUS = 218;
    /**
     * 任务类型-批量禁用理财账户状态
     */
    public static final int TYPE_BATCH_DISABLE_FINANCE_STATUS = 219;
    /**
     * 任务类型-批量关闭理财账户状态
     */
    public static final int TYPE_BATCH_CLOSE_FINANCE_STATUS = 220;
    /**
     * 任务类型-批量重新启用理财账户状态
     */
    public static final int TYPE_BATCH_REOPEN_FINANCE_STATUS = 221;
    /**
     * 任务类型-批量启用理财账户在线客服
     */
    public static final int TYPE_BATCH_OPEN_ONLINE_SERVICE_STATUS = 222;

    /**
     * 任务类型-批量导入保险白名单
     */
    public static final int TYPE_BATCH_IMPORT_INSURE_WHITELIST = 223;
    /**
     * 任务类型-批量导出保险白名单
     */
    public static final int TYPE_BATCH_EXPORT_INSURE_WHITELIST = 224;

    /**
     * 提现记录导出
     */
    public static final int TYPE_WITHDRAW_LIST_DOWNLOAD = 225;
    /**
     * 任务类型-批量设置贷款用户分组
     * 后一个batch是分组的意思。
     */
    public static final int TYPE_BATCH_SET_LOAN_BATCH = 228;

    /**
     * 任务类型-批量导出活动记录
     */
    public static final int TYPE_BATCH_EXPORT_ACTIVITY_RECORDS = 226;
    /**
     * 任务类型-批量导出红包发放记录
     */
    public static final int TYPE_BATCH_EXPORT_GRANT_REDPACKET_RECORDS = 227;
    /**
     * 任务类型-批量查询健康分
     */
    public static final int TYPE_BATCH_IMPORT_GET_HEALTH_POINT = 300;
    /**
     * 任务类型-批量开关余额提现理财转入入口
     */
    public static final int TYPE_BATCH_FINANCE_PURCHASE_IN_WALLET_WHITELIST = 301;
    /**
     * 任务类型-批量删除商户d0禁用原因
     */
    public static final int TYPE_BATCH_DELETE_MERCHANT_D0_DISABLE_REASON = 302;

    /**
     * 任务类型-批量报备
     */
    public static final int TYPE_BATCH_CONTRACT = 400;
    /**
     * 任务类型-批量配置交易参数
     */
    public static final int TYPE_BATCH_CHANGE = 401;
    /**
     * 任务类型-批量配置交易参数按规则
     */
    public static final int TYPE_BATCH_CHANGE_RULE = 402;
    /**
     * 任务类型-批量修改外部商户号
     */
    public static final int TYPE_BATCH_CHANGE_OUT_SN = 420;
    /**
     * 任务类型-批量修改外部商户号
     */
    public static final int TYPE_BATCH_ALLOW_ORDER_REPEAT = 421;

    /**
     * 任务类型-批量创建用户
     */
    public static final int TYPE_BATCH_CREATE_USER = 422;
    /**
     * 任务类型-上游账单拆分明细列表
     */
    public static final int TYPE_BATCH_SOURCE_BILL_SPLITTING = 423;

    /**
     * 自定义字段
     * 描述-类型描述
     */
    public static final String DESC_TYPE = "type_desc";
    /**
     * 自定义字段
     * 描述-操作平台描述
     */
    public static final String DESC_APPLY_SYSTEM = "apply_system_desc";
    /**
     * 自定义字段
     * 描述-任务状态描述
     */
    public static final String DESC_APPLY_STATUS = "apply_status_desc";

    /**
     * 类型描述
     */
    public static Map TYPE_DESC = new HashMap<>();
    /**
     * 操作系统描述
     */
    public static Map APPLY_SYSTEM_DESC = new HashMap<>();
    /**
     * 状态描述
     */
    public static Map APPLY_STATUS_DESC = new HashMap<>();

    static {
        TYPE_DESC.put(TYPE_STATEMENT_DOWNLOAD, "对账单下载");
        TYPE_DESC.put(TYPE_ORDER_DOWNLOAD, "订单下载");
        TYPE_DESC.put(TYPE_CHANEL_SHARE_BENEFIT_DOWNLOAD, "渠道分润报表下载");
        TYPE_DESC.put(TYPE_IMPORT_D0, "导入D0白名单");
        TYPE_DESC.put(TYPE_BATCH_REMOVE_D0, "批量移除D0白名单");
        TYPE_DESC.put(TYPE_INMORT_FORCE_CLEAR, "批量强制结算");
        TYPE_DESC.put(TYPE_BATCH_UPDATE_D0_FREECOUNT, "批量修改D0免费次数");
        TYPE_DESC.put(TYPE_BATCH_UPDATE_D0_RULE, "批量修改D0规则");
        TYPE_DESC.put(TYPE_BATCH_DISABLE_D0_STATUS, "批量禁用D0状态");
        TYPE_DESC.put(TYPE_BATCH_ENABLE_D0_STATUS, "批量启用D0状态");
        TYPE_DESC.put(TYPE_BATCH_UPDATE_ORINIZITION_D0_RULE, "批量修改推广者组织D0规则");
        TYPE_DESC.put(TYPE_STATEMENT_DOWNLOAD, "对账单下载");
        TYPE_DESC.put(TYPE_WITHDRAW_DISABLE_SYSTEM_NOTICE, "关闭提现系统发送通知");
        TYPE_DESC.put(TYPE_BATCH_COMPENSATION, "批量超时赔付");
        TYPE_DESC.put(TYPE_BATCH_CHANGE_MERCHANT_CONFIG, "批量修改商户交易配置");
        TYPE_DESC.put(TYPE_BATCH_CHANGE_MERCHANT_D0_CONFIG, "批量修改商户D0配置");
        TYPE_DESC.put(TYPE_BATCH_IMPORT_D0_CARDS, "批量导入D0卡券白名单");
        TYPE_DESC.put(TYPE_BATCH_IMPORT_D1_PRIOR_MERCHANTS, "批量导入D1优先批次白名单");
        TYPE_DESC.put(TYPE_IMPORT_FINANCE_WHITELIST, "批量导入理财白名单");
        TYPE_DESC.put(TYPE_BATCH_DISABLE_FINANCE_STATUS, "批量禁用理财账户");
        TYPE_DESC.put(TYPE_BATCH_ENABLE_FINANCE_STATUS, "批量启用理财账户");
        TYPE_DESC.put(TYPE_BATCH_UPDATE_FINANCE_FREECOUNT, "批量更新理财账户快赎次数");
        TYPE_DESC.put(TYPE_BATCH_CLOSE_FINANCE_STATUS, "批量关闭理财账户");
        TYPE_DESC.put(TYPE_BATCH_REOPEN_FINANCE_STATUS, "批量重开理财账户");
        TYPE_DESC.put(TYPE_BATCH_OPEN_ONLINE_SERVICE_STATUS, "批量开启理财账户在线服务");
        TYPE_DESC.put(TYPE_BATCH_IMPORT_INSURE_WHITELIST, "批量导入保险白名单");
        TYPE_DESC.put(TYPE_BATCH_EXPORT_INSURE_WHITELIST, "批量导出保险白名单");
        TYPE_DESC.put(TYPE_WITHDRAW_LIST_DOWNLOAD, "提现记录下载");
        TYPE_DESC.put(TYPE_BATCH_EXPORT_ACTIVITY_RECORDS, "批量导出活动记录");
        TYPE_DESC.put(TYPE_BATCH_EXPORT_GRANT_REDPACKET_RECORDS, "批量导出红包发放记录");
        TYPE_DESC.put(TYPE_BATCH_SET_LOAN_BATCH, "设置贷款商户分组");
        TYPE_DESC.put(TYPE_BATCH_FINANCE_PURCHASE_IN_WALLET_WHITELIST, "批量开关余额提现页面理财转入入口");
        TYPE_DESC.put(TYPE_BATCH_CONTRACT, "批量报备");
        TYPE_DESC.put(TYPE_BATCH_CHANGE, "批量通过名单导入配置交易参数");
        TYPE_DESC.put(TYPE_BATCH_CHANGE_RULE, "批量通过条件筛选配置交易参数");
        TYPE_DESC.put(TYPE_BATCH_CHANGE_OUT_SN, "批量修改外部商户号");
        TYPE_DESC.put(TYPE_BATCH_ALLOW_ORDER_REPEAT,  "批量允许商户订单号重复配置");
        TYPE_DESC.put(TYPE_BATCH_CREATE_USER,  "批量创建用户");
        TYPE_DESC.put(TYPE_BATCH_SOURCE_BILL_SPLITTING,  "上游账单拆分明细列表");

        APPLY_SYSTEM_DESC.put(APPLY_SYSTEM_UNKNOW, "未知系统");
        APPLY_SYSTEM_DESC.put(APPLY_SYSTEM_SP, "运营服务平台");
        APPLY_SYSTEM_DESC.put(APPLY_SYSTEM_VSP, "服务商平台");
        APPLY_SYSTEM_DESC.put(APPLY_SYSTEM_MSP, "商户服务平台");
        APPLY_SYSTEM_DESC.put(APPLY_SYSTEM_SSP, "推广者平台");

        APPLY_STATUS_DESC.put(APPLY_STATUS_APPLY, "新申请");
        APPLY_STATUS_DESC.put(APPLY_STATUS_IN_EXCUTE, "执行中");
        APPLY_STATUS_DESC.put(APPLY_STATUS_EXCUTE_SUCCESS, "执行成功");
        APPLY_STATUS_DESC.put(APPLY_STATUS_EXCUTE_FAILURE, "执行失败");
    }
}
