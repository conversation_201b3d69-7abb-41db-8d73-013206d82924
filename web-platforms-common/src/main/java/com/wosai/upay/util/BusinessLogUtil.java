package com.wosai.upay.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.service.CommonLoginService;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.net.URLDecoder;
import java.util.*;

/**
 * Created by Administrator on 2017/5/31.
 */
public class BusinessLogUtil {
    public static final Logger logger = LoggerFactory.getLogger(BusinessLogUtil.class);

    /**
     * 不用保存记录的字段
     */
    public static List<String> UN_SAVE_FIELDS = Arrays.asList("version","mtime");

    /**
     * 业务日志操作平台
     */
    public static final String BUSINESS_SYSTEM_CODE_SP = "SP";
    /**
     * 业务日志操作版本
     */
    public static final String BUSINESS_SYSTEM_VERSION_SP = "2.0";

    /**
     * 业务日志操作平台
     */
    public static final String BUSINESS_SYSTEM_CODE_MSP = "MSP";
    /**
     * 业务日志操作版本
     */
    public static final String BUSINESS_SYSTEM_VERSION_MSP = "2.0";

    /**
     * 变更前map
     */
    public static final String LOG_PARAM_CHANGE_BEFORE = "change_before";
    /**
     * 变更后map
     */
    public static final String LOG_PARAM_CHANGE_AFTER = "change_after";
    /**
     * 操作参数
     */
    public static final String LOG_PARAM_OP_PARAMS = "op_params";
    /**
     * httpServeletRequest
     */
    public static final String LOG_PARAM_REQUEST = "request";

    /**
     * 业务对象字段集合
     */
    public static final String LOG_PARAM_OP_PARAMS_COLUMNS = "object_columns";

    /**
     * 表名
     */
    public static final String LOG_PARAM_OP_PARAMS_TABLENAME = "tableName";
    /**
     * 对象主键
     */
    public static final String LOG_PARAM_OP_PARAMS_OBJECT_ID = "object_id";

    /**
     * 业务对象code对应opearation_field
     * 格式：业务对象code,<字段,>
     */
    public static Map<String,String> BUSINESS_OGJECT_OPERATION_FIELDS = new HashMap<String,String>(){{
        put("merchant","merchant#sn");
        put("store","store#sn");
        put("terminal","terminal#sn");
        put("merchant_user","account#username");
        put("osp_user","account#phone");
        put("group","group#sn");
        put("vendor","vendor#sn");
        put("solicitor","solicitor#sn");
        put("general_rule","general_rule#type");
        put("organizations  ","organizations#name");
        put("special_auth_whitelist  ","special_auth_whitelist#obejct_sn");
        put("transaction  ","transaction#order_sn");
    }};

    /**
     * 表下业务字段没有变更也许传入的字段
     */
    public static Map<String,List<String>> BUSINESS_OBJECT_COLUMN_NEED =  new HashedMap(){{
        put("merchant_config", Arrays.asList("merchant_config#payway"));
        put("general_rule", Arrays.asList("general_rule#type"));
        put("solicitor_config", Arrays.asList("solicitor_config#payway"));
        put("terminal",Arrays.asList("merchant_id","store_id"));
        put("terminal_config",Arrays.asList("merchant_id","store_id"));
        put("merchant_user",Arrays.asList("merchant_id"));
        put("account",Arrays.asList("merchant_id"));
        put("general_rule",Arrays.asList("rule.organizationId"));
        put("organizations",Arrays.asList("organizations#name", "organizations#path"));
        put("special_auth_whitelist",Arrays.asList("special_auth_whitelist#object_id", "special_auth_whitelist#object_type", "special_auth_whitelist#object_name", "special_auth_whitelist#auth_type"));
        put("transaction",Arrays.asList("merchant_id", "store_id", "terminal_id", "transaction#order_id", "transaction#order_sn", "transaction#type", "transaction#id"));
        put("merchant_d1_withdraw_batch",Arrays.asList("merchant_id", "batch_id"));
        put("merchant_gray",Arrays.asList("merchant_gray#status"));
    }};

    /**
     * SP创建业务日志字符串
     * @param params
     * @return
     */
    public static String buildBusinessLogSpJsonStr(Map<String,Object> params){
        List<Map> results = buildBusinessLog(params,BUSINESS_SYSTEM_CODE_SP,BUSINESS_SYSTEM_VERSION_SP);
        return results == null? "":JacksonUtil.toJsonString(results);
    }

    /**
     * MSP创建业务日志字符串
     * @param params
     * @return
     */
    public static String buildBusinessLogMspJsonStr(Map<String,Object> params){
        List<Map> results = buildBusinessLog(params,BUSINESS_SYSTEM_CODE_MSP,BUSINESS_SYSTEM_VERSION_MSP);
        return results == null? "":JacksonUtil.toJsonString(results);
    }

    /**
     * SP创建业务日志字符串
     * @param params
     * @return
     */
    public static List<Map> buildBusinessLogSp(Map<String,Object> params){
        return buildBusinessLog(params,BUSINESS_SYSTEM_CODE_SP,BUSINESS_SYSTEM_VERSION_SP);
    }

    /**
     * MSP创建业务日志字符串
     * @param params
     * @return
     */
    public static List<Map> buildBusinessLogMsp(Map<String,Object> params){
        return buildBusinessLog(params,BUSINESS_SYSTEM_CODE_MSP,BUSINESS_SYSTEM_VERSION_MSP);
    }

    public static List<Map> buildBusinessLog(Map<String,Object> params,String system_code,String system_version) {
        Map change_before = (Map) BeanUtil.makeCopy(params.get(LOG_PARAM_CHANGE_BEFORE));
        Map change_after = (Map) BeanUtil.makeCopy(params.get(LOG_PARAM_CHANGE_AFTER));
        Map op_params = (Map) params.get(LOG_PARAM_OP_PARAMS);
        Map request = (Map) params.get(LOG_PARAM_REQUEST);
        List<String> business_object_colums = (List<String>) params.get(LOG_PARAM_OP_PARAMS_COLUMNS);

        if (op_params == null){
            logger.info("业务日志参数或其他参数为空[{}]",params);
            return null;
        }

        //清除无需保存的字段
        removeUnSaveField(change_before);
        removeUnSaveField(change_after);

        //业务对象code
        String business_object_code = BeanUtil.getPropString(op_params, BizOpLog.BUSINESS_OBJECT_CODE,"");
        String tableName = BeanUtil.getPropString(op_params,LOG_PARAM_OP_PARAMS_TABLENAME,"");
        //拿到表名后去掉tableName属性
        op_params.remove(LOG_PARAM_OP_PARAMS_TABLENAME);

        //业务对象主键id
        String op_object_id = tableName + "#" + BeanUtil.getPropString(op_params,LOG_PARAM_OP_PARAMS_OBJECT_ID,"id");
        if (op_params.containsKey(LOG_PARAM_OP_PARAMS_OBJECT_ID)){
            op_params.remove(LOG_PARAM_OP_PARAMS_OBJECT_ID);
        }

        //日志集合
        List<Map> opLogs = new ArrayList<>();

        //从头部拿到map
        Map<String,String> headerParams = getHeaderParams(request);

        //批次获取优先级:1.从头部获取  2.从op_params获取  3.现场生产
        if (StringUtil.empty(BeanUtil.getPropString(headerParams,BizOpLog.OP_ID))){
            if (StringUtil.empty(BeanUtil.getPropString(op_params,BizOpLog.OP_ID))){
                op_params.put(BizOpLog.OP_ID,uuid());
            }
        }else{
            op_params.put(BizOpLog.OP_ID,BeanUtil.getPropString(headerParams,BizOpLog.OP_ID));
        }

        //操作类型取优先级:1.从头部获取
        if (!StringUtil.empty(BeanUtil.getPropString(headerParams,BizOpLog.OP_TYPE))){
            op_params.put(BizOpLog.OP_TYPE,BeanUtil.getPropString(headerParams,BizOpLog.OP_TYPE));
        }

        //功能code,优先级： 1.头部  2.op_params
        if (StringUtil.empty(BeanUtil.getPropString(headerParams,BizOpLog.BUSINESS_FUNCTION_CODE))){
            if (StringUtil.empty(BeanUtil.getPropString(op_params,BizOpLog.BUSINESS_FUNCTION_CODE))){
                logger.info("功能code为空,params[{}]",params);
                return null;
            }
        }else {
            op_params.put(BizOpLog.BUSINESS_FUNCTION_CODE,BeanUtil.getPropString(headerParams,BizOpLog.BUSINESS_FUNCTION_CODE));
        }
        //生成时间 优先级:1.从头部获取  2.从op_params获取  3.现场生产
        if (StringUtil.empty(BeanUtil.getPropString(headerParams,BizOpLog.OP_TIME,""))){
            if (StringUtil.empty(BeanUtil.getPropString(op_params,BizOpLog.OP_TIME,""))){
                op_params.put(BizOpLog.OP_TIME,new Date().getTime());
            }
        }else{
            op_params.put(BizOpLog.OP_TIME,BeanUtil.getPropLong(headerParams,BizOpLog.OP_TIME));
        }
        //记录操作人
        op_params.put(BizOpLog.OP_USER_ID,getUserId(request));
        op_params.put(BizOpLog.OP_USER_NAME,getUserName(request));
        //记录备注 优先级： 1.头部  2.op_params
        if (!StringUtil.empty(BeanUtil.getPropString(headerParams,BizOpLog.REMARK))){
            op_params.put(BizOpLog.REMARK,BeanUtil.getPropString(headerParams,BizOpLog.REMARK));
        }

        //操作平台
        op_params.put(BizOpLog.BUSINESS_SYSTEM_CODE,system_code);
        //操作版本
        op_params.put(BizOpLog.BUSINESS_SYSTEM_VERSION,system_version);


        //判空
        change_after = change_after == null? new HashedMap():change_after;
        change_before = change_before == null? new HashedMap():change_before;

        Map change_after_old = change_after;
        Map change_before_old = change_before;
        //补全字段表明前缀
        change_after = addPre(change_after,tableName);
        change_before = addPre(change_before,tableName);

        //生成op_object_id op_object_operation_flag

        //表id
        op_params.put(BizOpLog.OP_OBJECT_ID,BeanUtil.getPropString(change_after, op_object_id,""));
        //运营id
        op_params.put(BizOpLog.OP_OBJECT_OPERATION_FLAG,BeanUtil.getPropString(change_after, BUSINESS_OGJECT_OPERATION_FIELDS.get(business_object_code),""));
        if(StringUtil.empty(BeanUtil.getPropString(op_params,BizOpLog.OP_OBJECT_OPERATION_FLAG))){
            op_params.put(BizOpLog.OP_OBJECT_OPERATION_FLAG,BeanUtil.getPropString(change_after_old, BUSINESS_OGJECT_OPERATION_FIELDS.get(business_object_code),""));
        }
        if(StringUtil.empty(getColumValue(op_params,BizOpLog.OP_OBJECT_ID))){
            op_params.put(BizOpLog.OP_OBJECT_ID,BeanUtil.getPropString(change_before, op_object_id,""));
        }
        if(StringUtil.empty(getColumValue(op_params,BizOpLog.OP_OBJECT_ID))){
            op_params.put(BizOpLog.OP_OBJECT_OPERATION_FLAG,BeanUtil.getPropString(change_before, BUSINESS_OGJECT_OPERATION_FIELDS.get(business_object_code),""));
        }

        if(StringUtil.empty(BeanUtil.getPropString(op_params,BizOpLog.OP_OBJECT_OPERATION_FLAG))){
            op_params.put(BizOpLog.OP_OBJECT_OPERATION_FLAG,BeanUtil.getPropString(change_before_old, BUSINESS_OGJECT_OPERATION_FIELDS.get(business_object_code),""));
        }

        //标志位，记录是否需要增加必须记录的字段
        //变更字段集合
        if (!change_after.isEmpty() || !change_before.isEmpty()){
            //记录变更了的字段
            Map changedMap = new HashedMap();
            for(String key : business_object_colums){
                Map op_params_copy = copyMap(op_params);
                String key_pre = key.contains("#")? key:tableName+"#"+key;
                String before_value = getColumValue(change_before,key_pre);
                String after_value = getColumValue(change_after,key_pre);
                if (!before_value.equals(after_value)){
                    op_params_copy.put(BizOpLog.BUSINESS_OBJECT_COLUMN_CODE,key);
                    op_params_copy.put(BizOpLog.OP_COLUMN_VALUE_BEFORE,before_value);
                    op_params_copy.put(BizOpLog.OP_COLUMN_VALUE_AFTER, after_value);
                    opLogs.add(op_params_copy);
                    changedMap.put(key_pre,"");
                    changedMap.put(key,"");
                }
            }
            //检测数据中是否有必须记录的字段
            List<String> business_object_column_nedded =  getBusinessObjectColumnsNeeded(tableName);

            if(business_object_column_nedded != null && business_object_column_nedded.size() > 0 && changedMap.size() > 0){
                for(String needRecordKey : business_object_column_nedded){
                    //如果已经记录过，直接跳过
                    if(changedMap.containsKey(needRecordKey)){
                        continue;
                    }
                    Map op_params_copy = copyMap(op_params);
                    //否则记录必须记录的字段
                    String needValue = getColumValue(change_before,needRecordKey);
                    needValue = StringUtil.empty(needValue) ? getColumValue(change_after,needRecordKey):needValue;
                    needValue = StringUtil.empty(needValue) ? getColumValue(change_before_old,needRecordKey):needValue;
                    needValue = StringUtil.empty(needValue) ? getColumValue(change_after_old,needRecordKey):needValue;
                    if (StringUtil.empty(needValue)){
                        continue;
                    }
                    op_params_copy.put(BizOpLog.BUSINESS_OBJECT_COLUMN_CODE,needRecordKey);
                    op_params_copy.put(BizOpLog.OP_COLUMN_VALUE_BEFORE,needValue);
                    op_params_copy.put(BizOpLog.OP_COLUMN_VALUE_AFTER, needValue);
                    opLogs.add(op_params_copy);
                    changedMap.put(needRecordKey,"");
                }
            }
        }else{
            opLogs.add(op_params);
        }
        

        return opLogs;

    }

    public static List<String>  getBusinessObjectColumnsNeeded(String tableName){
        if (BUSINESS_OBJECT_COLUMN_NEED.containsKey(tableName)){
            return BUSINESS_OBJECT_COLUMN_NEED.get(tableName);
        }
        return null;
    }


    /**
     * 获取map中字段值 支持获取子字段
     * 自字段以.的形式获取
     * @param params
     * @param key
     * @return
     */
    public static String getColumValue(Map params,String key){
        if (StringUtil.empty(key) || (!StringUtil.empty(key) && !key.contains("."))){
            return BeanUtil.getPropString(params,key,"");
        }
        //表#字段.字段
        try {
            String[] keys = key.split("\\.");
            Map values = (Map) BeanUtil.getProperty(params,keys[0]);
            if (keys.length > 2){
                String _key = keys[1];
                for(int i = 2;i < keys.length ;i++){
                    _key = _key + "." + keys[i];
                }
                return getColumValue(values,_key);
            }else{
                return getColumValue(values,keys[1]);
            }
        }catch (Exception e){
        }
        return "";
    }

    public static Map copyMap(Map map){
        Map copy = new HashMap();
        if (map != null){
            Set<String> keys = map.keySet();
            for (String key:keys) {
                copy.put(key,map.get(key));
            }
        }
        return copy;
    }


    /**
     * 获取用户id，待扩充
     * @return
     */
    public static String getUserId(Map request){
        String userId = "";
        try{
            HttpSession session = (HttpSession) BeanUtil.getProperty(request,"session");
            userId = (String) session.getAttribute(CommonLoginService.SESSION_USERID);
            if (StringUtil.empty(userId)) {
                userId = (String) session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
            }
        }catch (Exception e){

        }
        return userId;
    }
    /**
     * 获取用户姓名，待扩充
     * @return
     */
    public static String getUserName(Map request) {
        String userName = "";
        try {
            HttpSession session = (HttpSession) BeanUtil.getProperty(request,"session");
            userName = (String) session.getAttribute(CommonLoginService.SESSION_USERNAME);
        }catch (Exception e){

        }
        return userName;
    }
    /**
     * 从头部获取业务日志相关数据
     * @return
     */
    public static Map<String,String> getHeaderParams(Map request) {
        Map<String,String> params = new HashedMap();
        try {
            String business_log = getColumValue(request,"businessLog");
            if (StringUtil.empty(business_log)){
                return params;
            }
            logger.info("businessLog:" + business_log);
            business_log = URLDecoder.decode(business_log,"utf-8");
            logger.info("businessLog_after_decode:" + business_log);
            JSONObject jo = JSON.parseObject(business_log);
            if (jo != null){
                if (jo.containsKey("remark")){
                    params.put(BizOpLog.REMARK,jo.getString("remark"));
                }
                if (jo.containsKey("business_function_code")){
                    params.put(BizOpLog.BUSINESS_FUNCTION_CODE,jo.getString("business_function_code"));
                }
                if (jo.containsKey("op_id")){
                    params.put(BizOpLog.OP_ID,jo.getString("op_id"));
                }
                if (jo.containsKey("op_type")){
                    params.put(BizOpLog.OP_TYPE,jo.getString("op_type"));
                }
                if (jo.containsKey("op_time")){
                    params.put(BizOpLog.OP_TIME,jo.getString("op_time"));
                }
            }
        }catch (Exception e){
            logger.error("转换header json异常,params[{}]",params,e);
        }
        return params;
    }

    /**
     * 从头部获取业务日志相关数据-备注
     * @return
     */
    public static String getHeaderParamsRemark(Map request) {
        String remark = "";
        try {
            String business_log = getColumValue(request,"businessLog");
            if (StringUtil.empty(business_log)){
                return remark;
            }
            business_log = URLDecoder.decode(business_log,"utf-8");
            JSONObject jo = JSON.parseObject(business_log);
            if (jo != null){
                if (jo.containsKey("remark")){
                    return jo.getString("remark");
                }
            }
        }catch (Exception e){
            logger.error("转换header json异常,remark[{}]",remark,e);
        }
        return remark;
    }



    /**
     * 清除无需保存的key
     * @param map
     */
    public static void removeUnSaveField(Map map){
        if (map != null && !map.isEmpty()){
            for (String field : UN_SAVE_FIELDS) {
                if (map.containsKey(field)){
                    map.remove(field);
                }
            }
        }
    }

    /**
     * 补全字段前缀
     * @param map
     */
    public static Map addPre(Map map,String preName){
        Map resultMap = new HashedMap();
        if (map != null && !map.isEmpty()){
            Set<String> keys = map.keySet();
            for (String key : keys){
                if (!StringUtil.empty(key)){
                    resultMap.put(preName + "#" + key,map.get(key));
                }
            }
        }
        return resultMap;
    }

    /**
     * 获取uuid
     * @return
     */
    public static String uuid(){
        return CrudUtil.randomUuid();
    }


}
