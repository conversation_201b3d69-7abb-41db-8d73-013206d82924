package com.wosai.upay.bean;

/**
 * Created by jianfree on 6/4/16.
 * 新支付网关流水的字段定义,从upay-gateway项目拷贝而来

 */
public class Transaction {
    public static final String TN = "transaction";

    public static final int TYPE_PAYMENT = 30;
    public static final int TYPE_REFUND_REVOKE = 31;
    public static final int TYPE_REFUND = 11;
    public static final int TYPE_CANCEL = 10;

    public static final int STATUS_CREATED = 0;

    public static final int STATUS_SUCCESS = 2000;
    public static final int STATUS_FAIL_CANCELED = 2001;

    public static final int STATUS_FAIL_PROTOCOL_1 = 2101;
    public static final int STATUS_FAIL_IO_1 = 2102;
    public static final int STATUS_FAIL_PROTOCOL_2 = 2103;
    public static final int STATUS_FAIL_IO_2 = 2104;
    public static final int STATUS_FAIL_PROTOCOL_3 = 2105;
    public static final int STATUS_FAIL_IO_3 = 2106;

    public static final int STATUS_FAIL_ERROR = 2107;
    public static final int STATUS_CANCEL_ERROR = 2108;
    public static final int STATUS_REFUND_ERROR = 2109;

    public static final int STATUS_IN_PROG = 1001;
    public static final int STATUS_ERROR_RECOVERY = 1002;

    public static final int PRODUCT_APP = 1;
    public static final int PRODUCT_SDK = 2;
    public static final int PRODUCT_POS = 3;

    public static final String TSN = "tsn";                     // 交易流水号（按规则自动生成） VARCHAR(20)
    public static final String CLIENT_TSN = "client_tsn";       // 商户流水号（商户下唯一）VARCHAR(32)

    public static final String TYPE = "type";                   // 类型 INT
    public static final String SUBJECT = "subject";             // 标题 VARCHAR(45)
    public static final String BODY = "body";                   // 详情 VARCHAR(255)
    public static final String STATUS = "status";               // 状态 INT UNSIGNED
    public static final String ORIGINAL_AMOUNT = "original_amount";     // 原始金额 BIGINT
    public static final String EFFECTIVE_AMOUNT = "effective_amount";   // 向支付通道请求的金额 BIGINT
    public static final String PAID_AMOUNT = "paid_amount";     // 消费者实际支付金额
    public static final String RECEIVED_AMOUNT = "received_amount"; // 收钱吧或正式商户在支付通道的实际收款金额
    public static final String ITEMS = "items";                 // 明细 BLOB


    public static final String BUYER_UID = "buyer_uid";         // 付款人在支付通道的用户ID VARCHAR(45)
    public static final String BUYER_LOGIN = "buyer_login";     // 付款人在支付通道的登录账户 VARCAR(45)
    public static final String MERCHANT_ID = "merchant_id";     // 商户ID VARCHAR(37)  商户记录的UUID
    public static final String STORE_ID = "store_id";           // 门店ID VARCHAR(37) 门店记录的UUID
    public static final String TERMINAL_ID = "terminal_id";     // VARCHAR(37) 终端记录的UUID
    public static final String OPERATOR = "operator";           // VARCHAR(45) 操作员姓名或其它ID

    public static final String ORDER_SN = "order_sn";           // 订单号 VARCHAR(20)
    public static final String ORDER_ID = "order_id";           // 订单ID VARCHAR(37)

    public static final String PAYWAY = "payway";               // 支付通道 INT UNSIGNED
    public static final String SUB_PAYWAY = "sub_payway";       // 支付方式 INT UNSIGNED
    public static final String TRADE_NO = "trade_no";           // 支付通道返回的交易凭证号 VARCHAR(128)

    public static final String PRODUCT_FLAG = "product_flag";   // 产品标志 INT

    public static final String EXTRA_PARAMS = "extra_params";   // 可选参数（poi, notify_url, remark, barcode, client_terminal_id, client_store_id) BLOB
    public static final String POI = "poi";                     // EXTRA_PARAMS
    public static final String LONGITUDE = "longitude";         // poi
    public static final String LATITUDE = "latitude";           // poi
    public static final String NOTIFY_URL = "notify_url";       // EXTRA_PARAMS
    public static final String REMARK = "remark";               // EXTRA_PARAMS
    public static final String BARCODE = "barcode";             // EXTRA_PARAMS
    public static final String DEVICE_ID = "device_id";         // EXTRA_PARAMS
    public static final String PAYER_UID = "payer_uid";         // EXTRA_PARAMS

    public static final String EXTRA_OUT_FIELDS = "extra_out_fields";   // 可选的交易流水的返回字段（qrcode)BLOB
    public static final String QRCODE = "qrcode";                       // EXTRA_OUT_FIELDS
    public static final String WAP_PAY_REQUEST = "wap_pay_request";     // 针对支付通道生成的wap支付请求。里面的内容由wap支付前端和支付通道之间约定。

    public static final String EXTENDED_PARAMS = "extended_params";     // 透传到支付通道的参数(goods_details)，由商户和支付通道约定，我们不做解析 BLOB

    public static final String REFLECT = "reflect";             // 商户上传的附加字段，保存在订单中。终端查询的时候原样返回。

    public static final String CONFIG_SNAPSHOT = "config_snapshot";     // 配置参数快照 BLOB

    public static final String CHANNEL_FINISH_TIME = "channel_finish_time";        // 交易完成时间（从支付通道得到）BIGINT
    public static final String FINISH_TIME = "finish_time";                         // 交易完成时间（收钱吧系统时间）BIGINT

    public static final String BIZ_ERROR_CODE = "biz_error_code";                   // 业务错误码，参考com.wosai.upay.workflow.UpayBizError
    public static final String PROVIDER_ERROR_INFO = "provider_error_info";         // 支付通道返回的错误信息 BLOB


    public static enum ProductFlag {
        APP(PRODUCT_APP),
        SDK(PRODUCT_SDK),
        POS(PRODUCT_POS);
        int code;

        ProductFlag(int code) {
            this.code = code;
        }

        public static ProductFlag fromCode(int code) {
            for (ProductFlag productFlag: values()) {
                if (productFlag.code == code) {
                    return productFlag;
                }
            }
            return APP;
        }
    }

    public static boolean notFailed(int status) {
        return status == STATUS_CREATED || status == STATUS_SUCCESS || status == STATUS_IN_PROG;
    }
}

