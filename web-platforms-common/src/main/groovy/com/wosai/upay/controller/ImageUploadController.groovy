package com.wosai.upay.controller

import com.wosai.upay.service.OssFileUploader
import org.apache.commons.io.IOUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.multipart.MultipartFile

import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

@Controller
@RequestMapping("/api/image/upload")
class ImageUploadController {
    private Logger log = LoggerFactory.getLogger(ImageUploadController.class);

    @Autowired
    OssFileUploader ossFileUploader;

    @RequestMapping(value = "{ext}", method = RequestMethod.POST)
    @ResponseBody
    public Object uploadImage(
            @PathVariable("ext") String ext,
            @RequestParam(value = "is_private", defaultValue = "false") boolean isPrivate,
            @RequestParam("file") MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        IOUtils.copy(file.getInputStream(), baos);

        byte[] content = baos.toByteArray();

        // calculate path
        String digest = toSHA1(content);
        String dirName = digest.substring(0, 2);
        String fullName = dirName + "/" + digest.substring(2) + "." + ext;

        ByteArrayInputStream bais = new ByteArrayInputStream(content);
        String url = ossFileUploader.uploadIfNotExists(OssFileUploader.IMAGE_BUCKET_NAME, fullName, bais, content.length, isPrivate);
        return StringUtils.isEmpty(url) ? fullName : url
    }

    private static String toSHA1(byte[] convertme) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("SHA-1");
        }
        catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return byteArrayToHexString(md.digest(convertme));
    }

    private static String byteArrayToHexString(byte[] b) {
        String result = "";
        for (int i = 0; i < b.length; i++) {
            result +=
                    Integer.toString((b[i] & 0xff) + 0x100, 16).substring(1);
        }
        return result;
    }

}
