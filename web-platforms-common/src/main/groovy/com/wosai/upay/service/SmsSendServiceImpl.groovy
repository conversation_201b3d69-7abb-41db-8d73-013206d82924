package com.wosai.upay.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.wosai.data.util.CollectionUtil
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * Created by jian<PERSON> on 16/2/16.
 */
class SmsSendServiceImpl implements SmsSendService {
    public static final Logger logger = LoggerFactory.getLogger(SmsSendService.class)

    private String smsHost;

    @Override
    boolean sendSmsAuthCode(String cellphone, String code, String template) {
        if (!cellphone) {
            return false
        }
        Map sms = CollectionUtil.hashMap(
                "to", cellphone,
                "template", template,
                "vars", [authcode: code]
        )
        HttpURLConnection connection = new URL(smsHost).openConnection()
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setConnectTimeout(10000)
        connection.setReadTimeout(5000)
        def out = connection.getOutputStream()
        new ObjectMapper().writeValue(out, sms)
        out.close()
        def result = connection.getInputStream().text
        connection.disconnect()
        logger.info("SendSmsAuthCodeResult:" + new ObjectMapper().writeValueAsString(sms) + " result:" + result)
        if ("{}".equals(result)) {
            logger.info("cellphone: ${cellphone} AuthCode: ${code}")
            return true
        } else {
            return false
        }

    }

    void setSmsHost(String smsHost) {
        this.smsHost = smsHost
    }

}
