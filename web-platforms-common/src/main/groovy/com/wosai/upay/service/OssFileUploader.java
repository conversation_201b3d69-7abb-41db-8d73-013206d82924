package com.wosai.upay.service;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSErrorCode;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.*;
import com.wosai.oss.OssUpload;
import com.wosai.oss.OssUrlEncrypt;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
public class OssFileUploader {
    @Autowired
    private ApplicationContext context;

    public static String ENDPOINT_URL;

    public static String IMAGE_BUCKET_NAME = "wosai-images";
    public static String STATICS_BUCKET_NAME = "wosai-statics";

    private static final String ACCESS_ID = "LTAIgdvAVQ7FnGIO";
    private static final String ACCESS_KEY = "Da99zhuALMOrvIQquyGe7q36gEmuJR";

    static {
        if("beta".equalsIgnoreCase(System.getProperty("shouqianba.flavor"))){
            ENDPOINT_URL = "https://oss-cn-hangzhou.aliyuncs.com";
        }else{
            ENDPOINT_URL = "http://oss-cn-hangzhou-internal.aliyuncs.com";
        }
    }

    private OSSClient client = new OSSClient(ENDPOINT_URL, ACCESS_ID, ACCESS_KEY);

    public OssFileUploader() {
    }

    //上传图片
    public String uploadImage(String key, InputStream input, long length) {
        return upload(IMAGE_BUCKET_NAME, key, input, length);
    }

    //上传文件
    public String uploadStaticsFile(String key, InputStream input, long length) {
        return upload(STATICS_BUCKET_NAME, key, input, length);
    }

    // 上传文件
    private String upload(String bucketName, String key, InputStream input, long length) {
        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(length);
        client.putObject(bucketName, key, input, objectMeta);
        return "";
    }

    public String uploadIfNotExists(String bucketName, String key, InputStream input, long length) {
        return upload(bucketName, key, input, length);
    }

    public String uploadIfNotExists(String bucketName, String key, InputStream input, long length, boolean isPrivate) {
        if (isPrivate && context.containsBean("OssUpload")) {
            String upload = context.getBean(OssUpload.class).upload(key, input, length, null);
            try {
                return context.getBean(OssUrlEncrypt.class).encryptUrl(upload);
            } catch (UnsupportedEncodingException e) {
            }
        }
        return uploadIfNotExists(bucketName, key, input, length);
    }

    // 下载文件
    public void downloadFile(String bucketName, String key, String filename) {
        client.getObject(new GetObjectRequest(bucketName, key),
                new File(filename));
    }

    private ObjectMetadata getObjectMetadata(String bucketName, String key) {
        return client.getObjectMetadata(bucketName, key);
    }

    private boolean exists(String bucketName, String key) {
        try {
            getObjectMetadata(bucketName, key);
            return true;
        } catch (OSSException e) {
            if (OSSErrorCode.NO_SUCH_KEY.equals(e.getErrorCode()) || OSSErrorCode.NO_SUCH_BUCKET.equals(e.getErrorCode())) {
                return false;
            } else {
                throw e;
            }
        }
    }


    private List<OSSObjectSummary> listFiles(String bucketName, String keyPrefix) {
        ObjectListing objectListing = client.listObjects(bucketName, keyPrefix);
        return objectListing.getObjectSummaries();
    }

    public List<String> listFiles(String keyPrefix) {
        List<OSSObjectSummary> sums = listFiles(STATICS_BUCKET_NAME, keyPrefix);
        if (CollectionUtils.isNotEmpty(sums)) {
            List<String> keys = new ArrayList<>();
            for (OSSObjectSummary sum : sums) {
                keys.add(sum.getKey());
            }
            return keys;
        }
        return null;
    }

    public void deleteFilesDaysBefore(String prefix, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, -1 * day);
        Date dayAgo = calendar.getTime();

        List<OSSObjectSummary> sums = listFiles(STATICS_BUCKET_NAME, prefix);
        if (CollectionUtils.isNotEmpty(sums)) {
            List<String> keys = new ArrayList<>();
            for (OSSObjectSummary sum : sums) {
                if (sum.getLastModified().getTime() <= dayAgo.getTime()) {
                    keys.add(sum.getKey());
                }
            }
            client.deleteObjects(new DeleteObjectsRequest(STATICS_BUCKET_NAME).withKeys(keys));
        }
    }


    public static void main(String[] args) throws Exception {
        OssFileUploader ossFileUploader = new OssFileUploader();

        File file = new File("/Users/<USER>/Downloads/八马终端导入.xls");
        FileInputStream input = new FileInputStream(file);
        ossFileUploader.upload(STATICS_BUCKET_NAME, "导出/八马终端导入.xls", input, file.length());

    }
}
