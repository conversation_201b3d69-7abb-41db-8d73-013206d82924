package com.wosai.upay.service;

import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.dto.V2.UpdateUcUserPasswordReq;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.sdk.support.CommonRedisOps;
import com.wosai.upay.bean.Captcha;
import com.wosai.upay.constant.MspLoginRedisPrefix;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.Digest;
import com.wosai.upay.util.EncodeUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import static com.wosai.upay.exception.UpayException.CODE_AUTH_CODE_ERROR;
import static com.wosai.upay.exception.UpayException.CODE_INVALID_PARAMETER;
import static com.wosai.upay.util.CookieUtil._10DAY_CYCLE;

public class CommonLoginServiceImpl implements CommonLoginService {
    protected static final Logger logger = LoggerFactory.getLogger(CommonLoginService.class);

    protected String SESSION_SMS_CODE = "_sms_code";
    protected static final String TEMPLDATE_NORMAL = "veqof4"; //登录验证码模板

    protected static final String DES_KEY = "2255b3b92b5011e8a6163d4e";

    /**
     * 登录密码错误重试最大次数
     */
    private static final int LOGIN_RETRY_MAX_TIMES = 10;

    @Autowired(required = false)
    private CommonRedisOps commonRedisOps;

    @Autowired
    protected SmsSendService smsSendService;

    @Autowired
    protected UserService userService;

    @Autowired
    private UcUserAccountService ucUserAccountService;

    private boolean loginNeedCellphoneVerify = true; //登录是否需要手机号码验证

    /**
     * 是否开启登录安全检查
     * jira:core-192
     */
    private boolean loginSafeCheck = false;


    @Override
    public void login(Map request, HttpServletResponse response) throws IOException {
        String cookieName = "smsCodeValidate";
        String username = BeanUtil.getPropString(request, "username");

        if(username == null){
            username = (String) getSession().getAttribute(SESSION_USERNAME);
        }

        // 密码尝试次数过多，账户锁定一定时间，这里用于校验账号是否被锁定
        if (loginSafeCheck && checkUserLoginIsLocked(username)) {
            throw new UpayException(UpayException.CODE_PASSWORD_RETRY_FREQUENT, "账号或密码多次错误，请1个小时后再试");
        }

        String password = BeanUtil.getPropString(request, "password");
        String code = BeanUtil.getPropString(request, "sms_code");
        if (!StringUtil.empty(code)) {
            this.verficatByCaptcha(response, cookieName, username, code);
        }else if(loginNeedCellphoneVerify) {
            Cookie[] cookies = getRequest().getCookies();
            boolean needSendSmsCode = false;
            if(cookies != null){
                for (Cookie n : cookies) {
                    if (cookieName.equals(n.getName())) {
                        String value = n.getValue();
                        if (value.equals(StringUtil.md5(username + "shouqianba"))) {
                            needSendSmsCode = false;
                            break;
                        }
                    }
                }
            }
            UcUserInfo ucUserInfo = ucUserAccountService.getUcUserByCellphone(username);
            logger.info("session的ucUserInfo = {}",ucUserInfo);
            if(ucUserInfo == null || StringUtil.empty(ucUserInfo.getCellphone())){
                needSendSmsCode = false;
            }
            if (needSendSmsCode) {
                throw new UpayException(UpayException.CODE_NEED_AUTH_CODE, "需要验证码验证");
            }
        }
        boolean matched = ucUserAccountService.checkPassword(username, password);
        if (!matched) {

            if (loginSafeCheck) {
                recordUserLoginFailTimes(username);
            }

            throw new UpayException(UpayException.CODE_PASSWORD_ERROR, "账号或密码错误");
        }

        if (loginSafeCheck) {
            // 用户登录需要清除已锁定的状态，比如用户重试了9次密码才成功
            commonRedisOps.delete(MspLoginRedisPrefix.MSP_LOGIN_RETRY_TIMES_PREFIX + username);
        }


        UcUserInfo ucUserInfo = ucUserAccountService.getUcUserByCellphone(username);
        logger.info("session的ucUserInfo = {}",ucUserInfo);

        if (ucUserInfo.getStatus() == Account.STATUS_DISABLED) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "用户已被禁用，暂时无法登录");
        }


        //重置SESSIONId
        getSession().invalidate();
        getSession().setAttribute(SESSION_USERNAME,username);

        String origianlString = ucUserInfo.getUc_user_id() + " " + System.currentTimeMillis();

        Cookie cookie = new Cookie("_accountId" , EncodeUtil.encode(DES_KEY.getBytes() , origianlString.getBytes()));
        logger.info("ucUserInfo id is : " + EncodeUtil.encode(DES_KEY.getBytes() , ucUserInfo.getUc_user_id().getBytes()));
        cookie.setMaxAge(1800);
        cookie.setPath("/");
        cookie.setDomain(".shouqianba.com");
        response.addCookie(cookie);

        getSession().setAttribute(SESSION_USERNAME, username);
        getSession().setAttribute(CommonLoginService.SESSION_ACCOUNT_ID, ucUserInfo.getUc_user_id());
        getSession().removeAttribute(SESSION_SMS_CODE);
        getSession().setAttribute("osp_account",ucUserInfo);

        logger.info("session的ucUserInfo = {}",getSession());
    }

    private void verficatByCaptcha(HttpServletResponse response, String cookieName, String username, String code) throws IOException {
        Captcha captcha = (Captcha) getSession().getAttribute(SESSION_SMS_CODE);
        String sessionUsername = (String) getSession().getAttribute(SESSION_USERNAME);

        if (!username.equals(sessionUsername)) {
            throw new UpayException(CODE_INVALID_PARAMETER, "请重新获取验证码");
        }

        if (captcha.getVerifyFailCount() > 3) {
            throw new UpayException(CODE_AUTH_CODE_ERROR, "请重新获取验证码");
        }

        boolean isOk = captcha.verify(code);
        getSession().setAttribute(SESSION_SMS_CODE, captcha);

        if (!isOk) {
            throw new UpayException(CODE_AUTH_CODE_ERROR, "'验证码错误'");
        }
        Cookie cookie = new Cookie(cookieName, Digest.md5((username + "shouqianba").getBytes()));
        cookie.setMaxAge(_10DAY_CYCLE);
        response.addCookie(cookie);
    }


    @Override
    public void sendSmsCode(Map request) {
        String username = BeanUtil.getPropString(request, "username");
        if (StringUtil.empty(username)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "用户名不能为空");
        }
        String cellphone = BeanUtil.getPropString(userService.getAccountByUsername(username), Account.CELLPHONE);
        if (StringUtil.empty(cellphone)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "用户不存在或者用户手机号资料不全");
        }
        String code = (new Random().nextInt(899999) + 100000) + "";
        Captcha captcha = new Captcha(code);
        getSession().setAttribute(SESSION_SMS_CODE, captcha);
        getSession().setAttribute(SESSION_USERNAME, username);
        boolean sendFlag = smsSendService.sendSmsAuthCode(cellphone, code, TEMPLDATE_NORMAL);
        if (!sendFlag) {
            throw new UpayException(UpayException.CODE_AUTH_CODE_SEND_FAIL, "验证码下发失败");
        }
    }

    @Override
    public void sendSmsCodeByCellphone(Map request) {
        String cellphone = BeanUtil.getPropString(request, "cellphone");
        if (StringUtil.empty(cellphone)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "手机号码不能为空");
        }
        String code = (new Random().nextInt(899999) + 100000) + "";
        Captcha captcha = new Captcha(code);
        getSession().setAttribute(SESSION_SMS_CODE + cellphone, captcha);
        boolean sendFlag = smsSendService.sendSmsAuthCode(cellphone, code, TEMPLDATE_NORMAL);
        if (!sendFlag) {
            throw new UpayException(UpayException.CODE_AUTH_CODE_SEND_FAIL, "验证码下发失败");
        }
    }

    @Override
    public void verifySmsCodeByCaptcha(Map request) {
        String cellphone = BeanUtil.getPropString(request, "cellphone");
        String smsCode = BeanUtil.getPropString(request, "sms_code");
        Captcha captcha = (Captcha) getSession().getAttribute(SESSION_SMS_CODE + cellphone);

        if (captcha == null) {
            throw new UpayException(CODE_AUTH_CODE_ERROR, "请先调用发送短信接口发送验证码");
        }

        if (captcha.getVerifyFailCount() > 3) {
            throw new UpayException(CODE_AUTH_CODE_ERROR, "请重新获取验证码");
        }

        boolean isOk = captcha.verify(smsCode);
        getSession().setAttribute(SESSION_SMS_CODE, captcha);

        if (!isOk) {
            throw new UpayException(CODE_AUTH_CODE_ERROR, "'验证码错误'");
        }
    }

    @Override
    public void logout(Map request) {
        getSession().invalidate();
    }


    private HttpSession getSession() {
        return HttpRequestUtil.getSession();
    }

    private HttpServletRequest getRequest() {
        return HttpRequestUtil.getRequest();
    }

    /**
     * 校验【密码尝试次数过多锁定账户一定时间】
     * @param userName 用户名称
     * @return true 表示用户当前被锁定，false表示用户当前未被锁定
     */
    private boolean checkUserLoginIsLocked(String userName) {
        String timesForString = commonRedisOps.get(MspLoginRedisPrefix.MSP_LOGIN_RETRY_TIMES_PREFIX + userName);
        if (timesForString != null && timesForString.length() > 0) {
            return Integer.valueOf(timesForString) >= LOGIN_RETRY_MAX_TIMES;
        }
        return false;
    }

    /**
     * 记录用户密码尝试失败次数
     */
    private void recordUserLoginFailTimes(String userName) {
        // 登录失败重试次数
        int times = 0;
        String timesForString = commonRedisOps.get(MspLoginRedisPrefix.MSP_LOGIN_RETRY_TIMES_PREFIX + userName);
        if (timesForString != null && timesForString.length() > 0) {
            times = Integer.parseInt(timesForString);
        }
        ++times;

        // 超时时间是1小时（具体天数咨询产品经理）
        commonRedisOps.set(MspLoginRedisPrefix.MSP_LOGIN_RETRY_TIMES_PREFIX + userName, Integer.toString(times), 1, TimeUnit.HOURS);
    }

    /**
     * 【账号只能在一处登录】将用户Session存储/更新在Redis，以便校验SessionId使用
     * @param username the username
     */
    private void storeSessionId(String username) {
        String sessionId = getRequest().getSession().getId();
        // 在web.xml中设置的session有效期是15分钟，依赖Servlet刷新有效期。而Redis中的sessionId，依赖LoginCheckerFilter#doFile主动去刷新有效期
        commonRedisOps.set(MspLoginRedisPrefix.MSP_LOGIN_ONLY_ONE_PLACE + username, sessionId, 1, TimeUnit.DAYS);
    }

    public void setLoginNeedCellphoneVerify(boolean loginNeedCellphoneVerify) {
        this.loginNeedCellphoneVerify = loginNeedCellphoneVerify;
    }

    public void setLoginSafeCheck(boolean loginSafeCheck) {
        this.loginSafeCheck = loginSafeCheck;
    }

    @Override
    public void updateAccountPassword(Map request, HttpServletResponse response) throws IOException {
        String oldPassword = BeanUtil.getPropString(request, "old_password");
        String password = BeanUtil.getPropString(request, "password");

        String username = BeanUtil.getPropString(request, "username");
        if(StringUtil.empty(username)) {
            username = (String) getSession().getAttribute(SESSION_USERNAME);
        }

        // 校验当前密码
        boolean matched = ucUserAccountService.checkPassword(username, oldPassword);
        if (!matched) {
            throw new UpayException(UpayException.CODE_OLD_PASSWORD_ERROR_WHEN_MODIFY, UpayException.getCodeDesc(UpayException.CODE_OLD_PASSWORD_ERROR_WHEN_MODIFY));
        }

        // 根据手机号查询uc-user
        UcUserInfo userInfo = ucUserAccountService.getUcUserByCellphone(username);
        String ucUserId = userInfo.getUc_user_id();

        UpdateUcUserPasswordReq updateUcUserPasswordReq = new UpdateUcUserPasswordReq();
        updateUcUserPasswordReq.setUc_user_id(ucUserId);
        updateUcUserPasswordReq.setNew_password(password);
        boolean updated = ucUserAccountService.updatePassword(updateUcUserPasswordReq);
        if(updated){
            getSession().invalidate();
        }
    }
}
