package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.bank.info.api.service.IndustryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public class CommonServiceImpl implements CommonService {
    protected static final Logger logger = LoggerFactory.getLogger(CommonService.class);

    @Autowired
    IndustryService industryService;

    @Override
    public Map getIndustry(Map request) {
        return industryService.getIndustry(BeanUtil.getPropString(request, "id"));
    }

    @Override
    public List findIndustrys(Map request) {
        return industryService.findIndustrys(request);
    }

    @Override
    public List getLevel1Industries(Map request) {
        return industryService.getLevel1Industries();
    }

    @Override
    public List getLevelsIndustries(Map request){
        return industryService.getLevelsIndustries();
    }

}
