package com.wosai.upay.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.wosai.merchant.exceptions.MerchantServiceException
import com.wosai.upay.common.exception.CommonException
import com.wosai.upay.common.log.LogstashMarkerAppendFileds
import com.wosai.upay.common.log.MethodEndLog
import com.wosai.upay.common.log.MethodStartLog
import com.wosai.upay.exception.UpayException
import com.wosai.upay.rest.ast.ASTHelper
import org.hibernate.validator.method.MethodConstraintViolation
import org.hibernate.validator.method.MethodConstraintViolationException
import org.slf4j.Logger

import javax.validation.ConstraintViolation
import javax.validation.ConstraintViolationException
import java.lang.reflect.UndeclaredThrowableException

/**
 * Created by jianfree on 28/3/16.
 */
public class AstHelperImpl implements ASTHelper {
    private static ObjectMapper om = new ObjectMapper()

    public void logMethod(Logger logger, String method, Object...args) {
        //StringBuilder sb = new StringBuilder()
        //sb.append("METHOD ").append(method).append(" PARAMS ").append(om.writeValueAsString(args))
        //logger.info(sb.toString())
        logger.trace(LogstashMarkerAppendFileds.append(new MethodStartLog(method, args)), "method start");
    }

    public Map handleResult(Logger logger, Object data) {
        def result = [code: UpayException.CODE_SUCCESS, msg: UpayException.getCodeDesc(UpayException.CODE_SUCCESS), data: data]
        logResult(logger, result)
        return result
    }

    public Map handleException(Logger logger, Throwable ex) {
        if (ex instanceof ReflectiveOperationException || ex instanceof UndeclaredThrowableException){
            ex = ex.getCause()
        }
        def result = [:]
        if (ex instanceof MethodConstraintViolationException) {
            StringBuilder sb = new StringBuilder()
            for (MethodConstraintViolation<?> violation : ex.getConstraintViolations()) {
                if (sb.length() > 0)
                    sb.append(";")
                sb.append(violation.getMessage())
            }
            result = [code: UpayException.CODE_INVALID_PARAMETER, msg: sb.toString()]
        }  else  if (ex instanceof IllegalArgumentException) {
            UpayException uex = new UpayException(UpayException.CODE_INVALID_PARAMETER, UpayException.getCodeDesc(UpayException.CODE_INVALID_PARAMETER))
            result = [code: UpayException.CODE_INVALID_PARAMETER, msg: uex.getMessage()]
        }  else if (ex instanceof CommonException){
            result = [code: ex.getCode(), msg: ex.getMessage()]
        } else if (ex instanceof UpayException) {
            result = [code: ex.getCode(), msg: ex.getMessage()]
        } else if (ex instanceof ConnectException) {
            UpayException uex = new UpayException(UpayException.CODE_NET_CONNECT_ERROR, UpayException.getCodeDesc(UpayException.CODE_NET_CONNECT_ERROR))
            result = [code: uex.getCode(), msg: uex.getMessage()]
        } else if (ex instanceof IOException) {
            UpayException uex = new UpayException(UpayException.CODE_IO_EXCEPTION, UpayException.getCodeDesc(UpayException.CODE_IO_EXCEPTION))
            result = [code: uex.getCode(), msg: uex.getMessage()]
        } else if (ex instanceof MerchantServiceException){
            result = [code: ex.getCode(), msg: ex.getMessage()]
        } else if(ex instanceof ConstraintViolationException) {
            Set<ConstraintViolation<?>> set = ex.getConstraintViolations()
            StringJoiner stringJoiner = new StringJoiner(";");
            for (ConstraintViolation constraintViolation : set) {
                stringJoiner.add(constraintViolation.getMessage());
            }
            UpayException uex = new UpayException(UpayException.CODE_INVALID_PARAMETER, stringJoiner.toString())
            result = [code: uex.getCode(), msg: uex.getMessage()]
        } else {
            UpayException uex = new UpayException(UpayException.CODE_UNKNOWN_ERROR, ex.getMessage())
            logger.error("unknown error", ex)
            result = [code: uex.getCode(), msg: uex.getMessage()]
        }
        if(ex != null){
            logThrowable(logger, ex)
        }
        logResult(logger, result)
        return result
    }

    public void logResult(Logger logger, Object result) {
        //StringBuilder sb = new StringBuilder()
        //sb.append("RESULT ").append(om.writeValueAsString(result)).append("\n")
        //logger.info(sb.toString())
        logger.info(LogstashMarkerAppendFileds.append("result", result), "result")
    }

    public void logThrowable(Logger logger, Throwable throwable) {
        logger.error(LogstashMarkerAppendFileds.append("error", throwable.getClass().getName() + ":" + throwable.getMessage()), "error");
    }

    public void logMethodTime(Logger logger, String method, Long start, Long end){
        //logger.info("cost: " + (end - start) + " ms " + method)
        logger.trace(LogstashMarkerAppendFileds.append(new MethodEndLog(method, null, (end - start), null, null)), "method end");
    }
}
