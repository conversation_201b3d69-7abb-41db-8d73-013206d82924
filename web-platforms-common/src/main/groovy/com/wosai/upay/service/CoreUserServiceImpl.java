package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.exception.UpayException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public class CoreUserServiceImpl implements CoreUserService {

    @Autowired
    UserService userService;

    @Override
    public Map createAccount(Map request) {
        return userService.createAccount(request);
    }

    @Override
    public void deleteAccount(Map request) {
        userService.deleteAccount((String) request.get("account_id"));
    }

    @Override
    public Map updateAccount(Map request) {
        return userService.updateAccount(request);
    }

    @Override
    public Map modifyMerchantSuperAdminUsername(Map request) {
        String merchantUserId = BeanUtil.getPropString(request, "merchant_user_id");
        String newUsername = BeanUtil.getPropString(request, "new_username");
        String newPassword = BeanUtil.getPropString(request, "new_password");
        if (StringUtil.empty(merchantUserId) || StringUtil.empty(newUsername) || StringUtil.empty(newPassword)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "用户ID、新账号名、新密码不能为空");
        }
        Map merchantUser = userService.getMerchantUser(merchantUserId);
        if (merchantUser == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "用户不存在");
        }
        if (!MerchantUser.ROLE_SUPER_ADMIN.equals(merchantUser.get(MerchantUser.ROLE))) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "用户非超级管理员(老板)");
        }
        Map oldAccount = userService.getAccount(BeanUtil.getPropString(merchantUser, MerchantUser.ACCOUNT_ID));
        if (oldAccount == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "用户ID对应的账号不存在");
        }
        Map account = userService.getAccountByUsername(newUsername);
        if (account == null) {
            account = userService.createAccount(CollectionUtil.hashMap(Account.USERNAME, newUsername,
                    Account.NICKNAME, newUsername,
                    Account.CELLPHONE, newUsername,
                    Account.PASSWORD, newPassword,
                    Account.AVATAR, BeanUtil.getProperty(oldAccount, Account.AVATAR),
                    Account.EMAIL, BeanUtil.getProperty(oldAccount, Account.EMAIL),
                    Account.GENDER, BeanUtil.getProperty(oldAccount, Account.GENDER)
            ));
        }
        Map newUser = userService.createMerchantUser(CollectionUtil.hashMap(
                MerchantUser.ACCOUNT_ID, BeanUtil.getProperty(account, DaoConstants.ID),
                MerchantUser.MERCHANT_ID, BeanUtil.getProperty(merchantUser, MerchantUser.MERCHANT_ID),
                MerchantUser.ROLE, MerchantUser.ROLE_SUPER_ADMIN,
                MerchantUser.NAME, newUsername,
                MerchantUser.EMAIL, BeanUtil.getProperty(merchantUser, MerchantUser.EMAIL),
                MerchantUser.REMARK, BeanUtil.getProperty(merchantUser, MerchantUser.REMARK)
        ));
        userService.deleteMerchantUser(merchantUserId);
        return newUser;
    }

    @Override
    public Map updateAccountPassword(Map request) {
        return userService.updateAccountPassword((String) request.get("username"), (String) request.get("password"), (Boolean) request.get("md5"));
    }

    @Override
    public Map getAccount(Map request) {
        return userService.getAccount((String) request.get("account_id"));
    }

    @Override
    public Map getAccountByUsername(Map request) {
        return userService.getAccountByUsername((String) request.get("username"));
    }

    @Override
    public Map getAccountByCellphone(Map request) {
        return userService.getAccountByUsername((String) request.get("cellphone"));
    }

    @Override
    public Map createVendorUser(Map request) {
        return userService.createVendorUser(request);
    }

    @Override
    public void deleteVendorUser(Map request) {
        userService.deleteVendorUser((String) request.get("vendor_user_id"));
    }

    @Override
    public Map updateVendorUser(Map request) {
        return userService.updateAccount(request);
    }

    @Override
    public Map getVendorUser(Map request) {
        return userService.getVendorUser((String) request.get("vendor_user_id"));
    }

    @Override
    public Map getVendorUserByAccountId(Map request) {
        return userService.getVendorUserByAccountId((String) request.get("account_id"));
    }

    @Override
    public void disableVendorUser(Map request) {
        userService.disableVendorUser((String) request.get("vendor_user_id"));
    }

    @Override
    public ListResult findVendorUsers(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return userService.findVendorUsers(pageInfo, request);
    }

    @Override
    public Map createMerchantUser(Map request) {
        return userService.createMerchantUser(request);
    }

    @Override
    public void deleteMerchantUser(Map request) {
        if(request!=null){
            String merchantUserId = BeanUtil.getPropString(request,"merchant_user_id");
            String accountId = BeanUtil.getPropString(request,"account_id");
            if(!StringUtil.empty(merchantUserId) && !StringUtil.empty(accountId)){
                userService.deleteMerchantUser(merchantUserId);
                userService.deleteAccountTruly(accountId);
            }
        }

    }

    @Override
    public Map updateMerchantUser(Map request) {
        return userService.updateMerchantUser(request);
    }

    @Override
    public Map getMerchantUser(Map request) {
        return userService.getMerchantUser((String) request.get("merchant_user_id"));
    }

    @Override
    public Map getFirstMerchantUser(Map request) {
        return userService.getFirstMerchantUser((String) request.get("merchant_id"));
    }

    @Override
    public Map getMerchantUserByAccountId(Map request) {
        return userService.getMerchantUserByAccountId((String) request.get("account_id"));
    }

    @Override
    public List getMerchantUserRoles(Map request) {
        return userService.getMerchantUserRoles((String) request.get("merchant_user_id"));
    }

    @Override
    public List getMerchantUserPermissions(Map request) {
        return userService.getMerchantUserPermissions((String) request.get("merchant_user_id"));
    }

    @Override
    public void disableMerchantUser(Map request) {
        userService.disableMerchantUser((String) request.get("merchant_user_id"));
    }

    @Override
    public ListResult findMerchantUsers(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return userService.findMerchantUsers(pageInfo, request);
    }

    @Override
    public Map createSolicitorUser(Map request) {
        return userService.createSolicitorUser(request);
    }

    @Override
    public void deleteSolicitorUser(Map request) {
        userService.deleteSolicitorUser((String) request.get("solicitor_user_id"));
    }

    @Override
    public Map updateSolicitorUser(Map request) {
        return userService.updateSolicitorUser(request);
    }

    @Override
    public Map getSolicitorUser(Map request) {
        return userService.getSolicitorUser((String) request.get("solicitor_user_id"));
    }

    @Override
    public Map getSolicitorUserByAccountId(Map request) {
        return userService.getSolicitorUserByAccountId((String) request.get("account_id"));
    }

    @Override
    public void disableSolicitorUser(Map request) {
        userService.disableSolicitorUser((String) request.get("solicitor_user_id"));
    }

    @Override
    public ListResult findSolicitorUsers(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return userService.findSolicitorUsers(pageInfo, request);
    }

    @Override
    public void deleteBoss(Map request) {
        if(request!=null){
            String merchant_user_id = BeanUtil.getPropString(request,"merchant_user_id");
            String account_id = BeanUtil.getPropString(request,"account_id");
            if(!StringUtil.empty(merchant_user_id) && !StringUtil.empty(account_id)){
                userService.deleteMerchantUser(merchant_user_id);
                userService.deleteAccountTruly(account_id);
            }
        }
    }
}
