package com.wosai.upay.service;


import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * Created by jianfree on 30/3/16.
 * 通用登录
 */
public interface CommonLoginService {
    String SESSION_ACCOUNT_ID = "_accountId"; //登录成功后session中存用户id的key
    String SESSION_USERID = "_userid"; //session中存用户id
    String SESSION_USERNAME = "_username"; //session中存用户名的
    String SESSION_CELLPHONE = "_cellphone";
    public static final String SUCCESS_PATH = "path";
    public static final String SUCCESS_URL = "url";

    /**
     * 登录
     *
     * @param request username
     *                password
     *                sms_code
     */
    void login(Map request, HttpServletResponse response) throws IOException;


    /**
     * send the validatecode to cellphone of the current user
     * 发送短信验证码给用户名
     *
     * @param request username
     */
    void sendSmsCode(Map request);

    /**
     * 发送短信验证码给手机
     *
     * @param request cellphone cellphone
     */
    void sendSmsCodeByCellphone(Map request);

    /**
     * 验证短信验证码
     *
     * @param request cellphone sms_code
     */
    void verifySmsCodeByCaptcha(Map request);

    /**
     * 退出登录
     *
     * @param request
     */
    void logout(Map request);

    /**
     * 修改密码
     *
     * @param request password
     *                sms_code
     */
    void updateAccountPassword(Map request, HttpServletResponse response) throws IOException;
}
