package com.wosai.upay.dummy;

import com.wosai.upay.service.MspTransactionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by xuyuanxiang on 2017/8/10.
 */
@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class MspTransactionServiceTests {
    private String mockOspUsername = "***********";
    private Map<String, Object> request;
    private MockHttpSession session;
    private MockHttpServletRequest httpServletRequest;
    @Autowired
    MspTransactionService mspTransactionService;


    @Test
    public void getTransactionListByOrderSnTest() {
        httpServletRequest = new MockHttpServletRequest();
        session = new MockHttpSession();
        httpServletRequest.setSession(session);
        session.setAttribute("username", mockOspUsername);
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(httpServletRequest));
        Map request = new HashMap<>();
        request.put("order_sn", "111");
        mspTransactionService.getTransactionListByOrderSn(request);
    }



}
