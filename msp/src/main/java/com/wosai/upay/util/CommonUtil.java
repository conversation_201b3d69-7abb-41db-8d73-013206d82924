package com.wosai.upay.util;

import avro.shaded.com.google.common.collect.Lists;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.common.exception.CommonAccessDeniedException;
import com.wosai.upay.exception.UpayException;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;


/**
  * Created by chenyu on 2018/1/22.
 */

public class CommonUtil {

    public static Map<String, Map> convert(List<Map> data, String key) {
        Map<String, Map> result = new HashMap<>();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }

        for (Map map : data) {
            result.put(BeanUtil.getPropString(map, key), map);
        }
        return result;
    }

    public static List<Map> convertToListMap(Map<String,Map> object) {
        if(CollectionUtils.isEmpty(object)){
            return Collections.EMPTY_LIST;
        }
        List<Map> result = new ArrayList<>();
        for (String key:object.keySet()){
            result.add(object.get(key));
        }
        return result;
    }

    public static List getValues(List<Map> data, String key) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.EMPTY_LIST;
        }
        List result = new ArrayList();
        for(Map map:data){
            result.add(MapUtils.getObject(map, key));
        }
        return result;
    }

    public static List convertToList(Object object) {
        if (object instanceof Collection) {
            Collection collection = (Collection) object;
            return Lists.newArrayList(collection);
        }
        return Collections.EMPTY_LIST;
    }


    public static List<String> getSessionMerchantIds() {
        List merchantIds = new ArrayList<>();
        Map account = ((Map) HttpRequestUtil.getSession().getAttribute("msp_account"));
        String sessionMerchantId = (String) account.get("merchant_id");
        if (StringUtils.isEmpty(sessionMerchantId)) {
            List<String> sessionMerchantIds = (List<String>) account.get("merchant_ids");
            if (sessionMerchantIds == null) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户不存在");
            } else {
                merchantIds = sessionMerchantIds;
            }
        } else {
            merchantIds.add(sessionMerchantId);
        }
        return merchantIds;
    }


    public static void checkJurisdiction(String merchantId) {
        Map account = ((Map) HttpRequestUtil.getSession().getAttribute("msp_account"));
        String sessionMerchantId = (String) account.get("merchant_id");
        List<String> sessionMerchantIds = (List<String>) account.get("merchant_ids");
        //普通商户
        if (WosaiStringUtils.isNotEmpty(sessionMerchantId) && (!merchantId.equalsIgnoreCase(sessionMerchantId))) {
            throw new CommonAccessDeniedException("该商户无查看权限");
        }
        //集团商户
        if (sessionMerchantIds != null && (!sessionMerchantIds.contains(merchantId))) {
            throw new CommonAccessDeniedException("该商户无查看权限");
        }

    }

}
