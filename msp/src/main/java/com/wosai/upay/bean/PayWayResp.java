package com.wosai.upay.bean;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * <AUTHOR> on 2022-07-08
 */
public class PayWayResp implements Serializable{

    private static final long serialVersionUID = -7287345566189659394L;

    @JsonProperty(value = "pay_way")
    private Integer payWay;
    @JsonProperty(value = "pay_way_name")
    private String payWayName;
    @JsonProperty(value = "pay_way_desc")
    private String payWayDesc;
    @JsonProperty(value = "i18n_key")
    private String i18nKey;
    @JsonProperty(value = "logo")
    private String logo;
    @JsonProperty(value = "grey_logo")
    private String greyLogo;
    @JsonProperty(value = "bic_code")
    private String bicCode;

    public String getI18nKey() {
        return i18nKey;
    }

    public void setI18nKey(String i18nKey) {
        this.i18nKey = i18nKey;
    }

    public Integer getPayWay() {
        return payWay;
    }

    public void setPayWay(Integer payWay) {
        this.payWay = payWay;
    }

    public String getPayWayName() {
        return payWayName;
    }

    public void setPayWayName(String payWayName) {
        this.payWayName = payWayName;
    }

    public String getPayWayDesc() {
        return payWayDesc;
    }

    public void setPayWayDesc(String payWayDesc) {
        this.payWayDesc = payWayDesc;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getGreyLogo() {
        return greyLogo;
    }

    public void setGreyLogo(String greyLogo) {
        this.greyLogo = greyLogo;
    }

    public String getBicCode() {
        return bicCode;
    }

    public void setBicCode(String bicCode) {
        this.bicCode = bicCode;
    }

    public PayWayResp() {
    }
}
