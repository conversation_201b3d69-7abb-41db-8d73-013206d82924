package com.wosai.upay.controller;

import com.wosai.data.util.CollectionUtil;
import com.wosai.ka.core.dto.request.StoreQRCodeDeactivateReq;
import com.wosai.upay.bean.PayWayResp;
import com.wosai.upay.bean.TransactionUpdateReq;
import com.wosai.upay.exception.UpayException;


import com.wosai.upay.transaction.model.UpdateTransactionReq;
import com.wosai.upay.transaction.service.TransactionServiceV2;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/transactions")
public class TransactionController {

    @Autowired
    private TransactionServiceV2 transactionServiceV2;

    @PostMapping(value="/{tsn}", produces="application/json")
    public Map<String, Object> updateTansactionInfo(
                                 @PathVariable(value = "tsn") String tsn,
                                 @RequestBody TransactionUpdateReq req) {

        try {
            UpdateTransactionReq updateTransactionReq = new UpdateTransactionReq();
            updateTransactionReq.setTsn(tsn);
            updateTransactionReq.setReflect(req.getReflect());
            Map<String, Object> result = transactionServiceV2
                    .updateTransactionInfo(updateTransactionReq);
            if (String.valueOf(result.get("code")).equals("200")) {
                return  CollectionUtil.hashMap(
                        "code", 50000,
                        "msg", "处理成功");
            } else {
                return  CollectionUtil.hashMap(
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", result.get("msg"));
            }
        } catch (Exception e) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "更新备注失败");
        }
    }

}
