package com.wosai.upay.controller;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bean.PayWayResp;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.service.IPayWayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2022-07-08
 */
@Controller
@RequestMapping("/api/v1/pay_ways")
public class PayWayController {

    @Autowired
    private IPayWayService iPayWayService;

    @RequestMapping(value="", method= RequestMethod.GET, produces="application/json")
    @ResponseBody
    public Map<String, Object> getPayWay(){
        try {
            List<PayWayResp> payWay = iPayWayService.getPayWay();
            return CollectionUtil.hashMap(
                    "code", 50000,
                    "msg", "处理成功",
                    "data", payWay
            );
        }catch (Exception e){
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "查询失败");
        }
    }

}
