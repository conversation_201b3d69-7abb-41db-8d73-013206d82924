package com.wosai.upay.controller;

import com.wosai.upay.member.bean.BMerchant;
import com.wosai.upay.member.bean.WeixinConfig;
import com.wosai.upay.service.MspWeixinService;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.weixin.open.bean.AuthorizerInfo;
import com.wosai.weixin.open.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/callback")
public class CallbackController {
    @Autowired
    private MspWeixinService mspWeixinService;

    @Autowired
    private AuthService authService;


    @RequestMapping(value = "/weixinpre", method = RequestMethod.GET)
    @ResponseBody
    public void weixinpre(@RequestParam("userRedirectUrl") String userRedirectUrl, HttpServletResponse response) throws IOException {
        HttpRequestUtil.getSession().setAttribute(MspWeixinService.WEIXIN_OPEN_AUTH_REDIRECT_URL_KEY, userRedirectUrl);
        response.sendRedirect(mspWeixinService.getWeixinOpenUrl() + "auth/pre.jsp?userRedirectUrl=" + mspWeixinService.getWebPlatformsMspServer() + "callback/weixinopen");
    }

    @RequestMapping(value = "/weixinopen", method = RequestMethod.GET)
    @ResponseBody
    public void weixinopen(@RequestParam("openAppId") String openAppId, @RequestParam("appId") String appId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String redirectUrl = (String) request.getSession().getAttribute(MspWeixinService.WEIXIN_OPEN_AUTH_REDIRECT_URL_KEY) + "?success=";
        try {
            AuthorizerInfo authorizerInfo = authService.getAuthorizerInfo(appId);
            Map bMerchant = new HashMap();
            Map weixinConfig = new HashMap();
            weixinConfig.put(WeixinConfig.OPEN_APPID, openAppId);
            weixinConfig.put(WeixinConfig.APPID, appId);
            // 授权方公众号类型，0代表订阅号，1代表由历史老帐号升级后的订阅号，2代表服务号
            weixinConfig.put(WeixinConfig.TYPE, authorizerInfo.getService_type_info() != null ? authorizerInfo.getService_type_info().get("id").toString() : "");
            weixinConfig.put(WeixinConfig.NAME, authorizerInfo.getNick_name());
            weixinConfig.put(WeixinConfig.USER_NAME, authorizerInfo.getUser_name());
            weixinConfig.put(WeixinConfig.ALIAS, authorizerInfo.getAlias());
            weixinConfig.put(WeixinConfig.HEAD_IMG, authorizerInfo.getHead_img());
            weixinConfig.put(WeixinConfig.QRCODE_URL, authorizerInfo.getQrcode_url());
            weixinConfig.put(WeixinConfig.PRINCIPAL_NAME, authorizerInfo.getPrincipal_name());
            // 授权方认证类型，-1代表未认证，0代表微信认证，1代表新浪微博认证，2代表腾讯微博认证，3代表已资质认证通过但还未通过名称认证，4代表已资质认证通过、还未通过名称认证，但通过了新浪微博认证，5代表已资质认证通过、还未通过名称认证，但通过了腾讯微博认证
            int verifyType = (authorizerInfo.getVerify_type_info() != null ? authorizerInfo.getVerify_type_info().get("id") : -1);
            weixinConfig.put(WeixinConfig.VERIFIED, verifyType == 0); //
            weixinConfig.put(WeixinConfig.VERIFIED_EXPIRE, null); // 微信没给过期时间
            bMerchant.put(BMerchant.WEIXIN_CONFIG, weixinConfig);
            mspWeixinService.createOrUpdateBMerchantWithWeixinConfig(bMerchant);
            response.sendRedirect(redirectUrl + "true");
            return;
        } catch (IOException e) {
            response.sendRedirect(redirectUrl + "false");
            return;
        }
    }
}
