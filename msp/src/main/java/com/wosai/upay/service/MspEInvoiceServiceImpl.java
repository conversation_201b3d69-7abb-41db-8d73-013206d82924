package com.wosai.upay.service;

import com.wosai.einvoice.api.EInvoiceMerchantService;
import com.wosai.einvoice.model.*;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.util.CommonConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class MspEInvoiceServiceImpl  implements MspEInvoiceService {

    @Resource
    EInvoiceMerchantService eInvoiceMerchantService;
    // ==================== EInvoiceMerchantService 商户管理相关方法实现 ====================

    @Override
    public MerchantInvoiceInfoResponse createMerchantBasicInfo(Map request) {

        MerchantBasicInfoRequest merchantBasicInfoRequest = JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(request), MerchantBasicInfoRequest.class);
        MerchantInvoiceInfoResponse merchantBasicInfo = eInvoiceMerchantService.createMerchantBasicInfo(merchantBasicInfoRequest);

        return merchantBasicInfo;
    }

    @Override
    public MerchantInvoiceInfoResponse updateMerchantBasicInfo(Map request) {
        MerchantBasicInfoRequest merchantBasicInfoRequest = JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(request), MerchantBasicInfoRequest.class);

        return eInvoiceMerchantService.updateMerchantBasicInfo(merchantBasicInfoRequest);
    }

    @Override
    public MerchantInvoiceInfoResponse getMerchantInvoiceInfo(Map request) {
        AppApiSixRequest appApiSixRequest = new AppApiSixRequest();
        appApiSixRequest.setMerchantId(MapUtil.getString(request, CommonConstant.MERCHANT_ID));
        MerchantInvoiceInfoResponse merchantInvoiceInfo = eInvoiceMerchantService.getMerchantInvoiceInfo(appApiSixRequest);

        return merchantInvoiceInfo;

    }

    @Override
    public MerchantInvoiceInfoResponse updateMerchantClientInfo(Map request) {
        MerchantClientInfoRequest merchantBasicInfoRequest = JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(request), MerchantClientInfoRequest.class);
        return eInvoiceMerchantService.updateMerchantClientInfo(merchantBasicInfoRequest);
    }

    @Override
    public Boolean validateClientInfo(Map request) {
        return eInvoiceMerchantService.validateClientInfo(MapUtil.getString(request, "clientId"), MapUtil.getString(request, "secret"));
    }
}
