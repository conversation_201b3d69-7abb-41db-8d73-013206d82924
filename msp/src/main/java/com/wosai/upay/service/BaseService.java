package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.department.Department;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.GroupUserMerchantAuth;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.model.user.MerchantUserStoreAuth;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.user.api.service.DepartmentService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.HttpRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;

/**
 * Created by lijunjie on 16/5/11.
 */
public abstract class BaseService {
    private static final Logger logger = LoggerFactory.getLogger(BaseService.class);

    @Autowired
    private UserService userService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private GroupService groupService;
    @Autowired
    private StoreService storeService;

    @Autowired
    DepartmentService departmentService;


    /**
     * 获取merchantId
     *
     * @return
     */
    protected String getSessionMerchantId() {
        return ((Map) HttpRequestUtil.getSession().getAttribute("msp_account")).get("merchant_id") + "";
    }

    protected String getSessionStoreId() {
        return (String) ((Map) HttpRequestUtil.getSession().getAttribute("msp_account")).get("store_id");
    }

    protected String getSessionUserRole() {
        return (String) ((Map) HttpRequestUtil.getSession().getAttribute("msp_account")).get(MerchantUser.ROLE);
    }

    protected String getSessionAccountUsername() {
        return (String) ((Map) HttpRequestUtil.getSession().getAttribute("msp_account")).get(Account.USERNAME);
    }

    protected String getSessionUserId(HttpServletRequest request) {
        return ((Map) request.getSession().getAttribute("msp_account")).get("user_id") + "";
    }

    protected String getSessionUserId() {
        return (String) ((Map) HttpRequestUtil.getSession().getAttribute("msp_account")).get("user_id");
    }

    protected String getSessionGroupId() {
        return (String) ((Map) HttpRequestUtil.getSession().getAttribute("msp_account")).get("group_id");
    }

    protected String getObjectId() {
        if ("merchant".equals(getSessionObjectType())) {
            return getSessionMerchantId();
        }
        return getSessionGroupId();
    }

    /**
     * 获取merchantId
     *
     * @return
     */
    protected String getSessionMerchantId(HttpServletRequest request) {
        return ((Map) request.getSession().getAttribute("msp_account")).get("merchant_id") + "";
    }

    protected String getSessionStoreId(HttpServletRequest request) {
        return (String) ((Map) request.getSession().getAttribute("msp_account")).get("store_id");
    }

    /**
     * 获取账户类型
     *
     * @return
     */
    protected String getSessionObjectType() {
        Map session_user = (Map) HttpRequestUtil.getSession().getAttribute(GroupUserLoginService.SESSION_USER);
        String group_id = BeanUtil.getPropString(session_user, "group_id", "");
        //普通商户
        if (StringUtil.empty(group_id)) {
            return "merchant";
        }
        //集团商户
        return "group";
    }

    /**
     * 增加多余参数
     * {
     * user_id: '',
     * merchant_ids: [],
     * merchant_sns: [],
     * store_ids: [store_id1 , store_id2 ....],
     * store_sns: [store_sn1 , store_sn2 ....]
     * }
     */
    protected Map getSessionInfos(Object httpRequest) {
        HttpServletRequest httpServletRequest = null;
        try {
            httpServletRequest = (HttpServletRequest) httpRequest;
        } catch (Exception e) {

        }

        //从session中拿取proxy参数
        HttpSession session = httpServletRequest == null ? HttpRequestUtil.getSession() : httpServletRequest.getSession();
        Object session_proxy_params = session.getAttribute("MERCHANT_STORE_PARAMS");
        //session中不存在重新获取 并 保存session
        if (session_proxy_params == null) {
            List<String> merchant_ids = new ArrayList<>();
            List<String> merchant_sns = new ArrayList<>();
            List<String> store_ids = new ArrayList<>();
            List<String> store_sns = new ArrayList<>();
            List<String> department_ids = new ArrayList<>();
            List<String> department_sns = new ArrayList<>();
            boolean isSuperAdmin = false;
            Map session_user = (Map) HttpRequestUtil.getSession().getAttribute(GroupUserLoginService.SESSION_USER);
            logger.info("session_user info[{}]", session_user);
            String user_id = BeanUtil.getPropString(session_user, "user_id", "");
            String group_id = BeanUtil.getPropString(session_user, "group_id", "");
            String merchant_name = "";
            String group_sn = "";
            String group_name = "";
            String type = "";
            String objectId = "";
            //普通商户
            if (StringUtil.empty(group_id)) {
                merchant_name = BeanUtil.getPropString(session_user, ConstantUtil.KEY_MERCHANT_NAME);
                String merchantId = BeanUtil.getPropString(session_user, ConstantUtil.KEY_MERCHANT_ID);
                merchant_ids.add(merchantId);
                merchant_sns.add(BeanUtil.getPropString(session_user, ConstantUtil.KEY_MERCHANT_SN));
                type = "merchant";
                objectId = merchantId;
            }
            //集团商户
            else {
                merchant_name = BeanUtil.getPropString(session_user, "group_name");
                group_name = merchant_name;
                group_sn = BeanUtil.getPropString(session_user, "group_sn");
                List<Map> group_merchants = groupService.getGroupUserMerchantAuths(CollectionUtil.hashMap(
                        GroupUserMerchantAuth.GROUP_ID, group_id,
                        GroupUserMerchantAuth.GROUP_USER_ID, user_id
                ));
                for (Map group_merchant : group_merchants) {
                    merchant_ids.add(BeanUtil.getPropString(group_merchant, ConstantUtil.KEY_MERCHANT_ID));
                    merchant_sns.add(BeanUtil.getPropString(group_merchant, ConstantUtil.KEY_MERCHANT_SN));
                }
                type = "group";
                objectId = group_id;
            }
            //门店信息
            Map user = userService.getMerchantUser(user_id);
            String merchantUserId = BeanUtil.getPropString(user, "merchant_user_id");
            boolean isStoreAuth = !BeanUtil.getPropString(user, "role", "").equals(MspUserLoginService.ROLE_SUPER_ADMIN) && BeanUtil.getPropInt(user, "store_auth") == 2;
            //集团或非授权商户显示商户下所有门店
            if (type.equals("group") || !isStoreAuth) {
                isSuperAdmin = true;
                for (String merchant_id : merchant_ids) {
                    ListResult stores = storeService.getStoreListByMerchantId(merchant_id, new PageInfo(1, 1000), null);
                    int page = 1;
                    while (stores != null && stores.getRecords() != null && stores.getRecords().size() > 0) {
                        for (Map store : stores.getRecords()) {
                            store_ids.add(BeanUtil.getPropString(store, DaoConstants.ID));
                            store_sns.add(BeanUtil.getPropString(store, Store.SN));
                        }
                        page++;
                        stores = storeService.getStoreListByMerchantId(merchant_id, new PageInfo(page, 1000), null);
                    }
                }
            }
            //授权商户只显示改授权门店
            else {
                if (BeanUtil.getPropString(user, "role", "").equals(MspUserLoginService.ROLE_DEPARTMENT_MANAGER)) {
                    List<Map> userDepartments = userService.getMerchantUserDepartmentAuths(CollectionUtil.hashMap(
                            "merchant_id", merchant_ids.size() > 0 ? merchant_ids.get(0) : "",
                            "merchant_user_id", user_id
                    ));
                    for (Map userDepartment : userDepartments) {
                        if (userDepartment != null) {
                            department_ids.add(BeanUtil.getPropString(userDepartment, DaoConstants.ID));
                            department_sns.add(BeanUtil.getPropString(userDepartment, Store.SN));
                        }
                    }
                    List<Map> departmentAuths = userService.getMerchantUserDepartmentAuths(CollectionUtil.hashMap(
                            "merchant_id", merchant_ids.size() > 0 ? merchant_ids.get(0) : "",
                            "merchant_user_id", user_id
                    ));
                    List<String> departmentIds = new ArrayList<>();
                    for (Map departmentAuth : departmentAuths) {
                        departmentIds.add(BeanUtil.getPropString(departmentAuth, Department.DEPARTMENT_ID));
                    }
                    List<Map> stores = departmentService.listStoresByDepartment(getSessionMerchantId(), departmentIds);
                    for (Map store : stores) {
                        if (store != null) {
                            store_ids.add(BeanUtil.getPropString(store, DaoConstants.ID));
                            store_sns.add(BeanUtil.getPropString(store, Store.SN));
                        }
                    }

                } else {
                    //user对应的门店
                    LinkedList storeIds = new LinkedList();
                    List<Map> userStores = userService.getMerchantUserStoreAuths(CollectionUtil.hashMap(
                            "merchant_id", merchant_ids.size() > 0 ? merchant_ids.get(0) : "",
                            "merchant_user_id", user_id
                    ));
                    for (Map userStore : userStores) {
                        Map store = storeService.getStore(BeanUtil.getPropString(userStore, ConstantUtil.KEY_STORE_ID));
                        if (store != null) {
                            store_ids.add(BeanUtil.getPropString(store, DaoConstants.ID));
                            store_sns.add(BeanUtil.getPropString(store, Store.SN));
                        }
                    }
                }
            }


            session_proxy_params = CollectionUtil.hashMap(
                    "type", type,
                    "group_sn", group_sn,
                    "group_name", group_name,
                    "merchant_name", merchant_name,
                    "user_id", user_id,
                    "merchant_ids", merchant_ids,
                    "merchant_sns", merchant_sns,
                    "store_ids", store_ids,
                    "store_sns", store_sns,
                    "department_ids", department_ids,
                    "department_sns", department_sns,
                    "isSuperAdmin", isSuperAdmin,
                    "object_id", objectId,
                    "merchant_user_id", merchantUserId
            );
            session.setAttribute("MERCHANT_STORE_PARAMS", session_proxy_params);
        }
        Map visual_proxy_params = (Map) session_proxy_params;
        return visual_proxy_params;
    }

    /**
     * 绑定账户管理员门店
     *
     * @param merchantId
     * @param storeId
     */
    protected void bindMerchantAdminUserStore(String merchantId, String storeId) {
        if (StringUtil.empty(storeId) || StringUtil.empty(merchantId)) {
            return;
        }
        try {
            PageInfo pageInfo = new PageInfo(1, 1000, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
            Map params = CollectionUtil.hashMap(
                    ConstantUtil.KEY_MERCHANT_ID, merchantId,
                    MerchantUser.ROLE, MerchantUser.ROLE_ADMIN
            );
            ListResult users = userService.findMerchantUsers(pageInfo, params);
            while (users != null && users.getRecords() != null && users.getRecords().size() > 0) {
                logger.info("bind users[{}]", users);
                for (Map user : users.getRecords()) {
                    String merchantUserId = BeanUtil.getPropString(user, DaoConstants.ID);
                    //已绑定
                    ListResult hasResult = userService.findMerchantUserStoreAuths(new PageInfo(1, 1), CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchantId,
                            MerchantUserStoreAuth.MERCHANT_USER_ID, merchantUserId,
                            MerchantUserStoreAuth.STORE_ID, storeId
                    ));
                    if (hasResult != null && hasResult.getRecords() != null && hasResult.getRecords().size() > 0) {
                        continue;
                    }

                    //执行绑定
                    //1.获取门店列表
                    //获取绑定列表
                    PageInfo pageInfoStore = new PageInfo(1, 1000, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
                    Map paramsStore = CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchantId,
                            MerchantUserStoreAuth.MERCHANT_USER_ID, merchantUserId
                    );
                    List<String> storeIds = new ArrayList<>();
                    storeIds.add(storeId);
                    ListResult storeAuths = userService.findMerchantUserStoreAuths(pageInfoStore, paramsStore);
                    logger.info("bind users stores user[{}] stores[{}]", user, storeAuths);
                    while (storeAuths != null && storeAuths.getRecords() != null && storeAuths.getRecords().size() > 0) {
                        for (Map storeAuth : storeAuths.getRecords()) {
                            String storeIdAuth = BeanUtil.getPropString(storeAuth, MerchantUserStoreAuth.STORE_ID);
                            if (!StringUtil.empty(storeId) && !storeIds.contains(storeIdAuth)) {
                                storeIds.add(storeIdAuth);
                            }
                        }
                        pageInfoStore.setPage(pageInfoStore.getPage() + 1);
                        storeAuths = userService.findMerchantUsers(pageInfoStore, paramsStore);
                    }

                    //转为，分割符号
                    String storeIdStr = storeIds.get(0);
                    for (int i = 1; i < storeIds.size(); i++) {
                        storeIdStr = storeIdStr + "," + storeIds.get(i);
                    }
                    logger.info("bind users stores save storeIdStr[{}]", storeIdStr);

                    //保存
                    userService.saveMerchantUserStoreAuth(merchantUserId, storeIdStr);

                }
                pageInfo.setPage(pageInfo.getPage() + 1);
                users = userService.findMerchantUsers(pageInfo, params);
            }
        } catch (Exception e) {
            logger.error("绑定账户管理员门店异常merchantId[{}], storeId[{}]", merchantId, storeId, e);
        }
    }
}
