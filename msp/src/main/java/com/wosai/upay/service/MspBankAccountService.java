package com.wosai.upay.service;

import com.wosai.upay.bank.model.*;
import com.wosai.upay.bank.model.verify.VerifyResp;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.helper.CorePlatformsValidated;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-12-05
 */
@CorePlatformsValidated
public interface MspBankAccountService {

    /**
     * 申请账户信息变更
     *
     * @param request
     */
    BankAccountChangeApply applyBankAccountChange(Map request);

    /**
     * 获取最新的一次申请
     *
     * @param request
     * @return
     */
    BankAccountChangeApply getLatestApply(Map request);

    /**
     * 获取申请完整信息
     *
     * @param request
     * @return
     */
    BankAccountChangeApplyDetail getApplyDetail(@PropNotEmpty.List({
            @PropNotEmpty(value = "apply_id")
    }) Map request);

    /**
     * 获取允许的结算账户变更类型
     *
     * @param request
     * @return
     */
    AllowChangeTypeVo getAllowChangeTypes(Map request);

    /**
     * 验证代付金额
     *
     * @param request
     * @return
     */
    VerifyResp verifyAmount(@PropNotEmpty.List({
            @PropNotEmpty(value = "apply_id"),
            @PropNotEmpty(value = "amount"),
    }) Map request);


    /**
     * 取消申请
     *
     * @param request
     * @return
     */
    Map cancelApply(@PropNotEmpty.List({
            @PropNotEmpty(value = "apply_id"),
    }) Map request);

    /**
     * 异名换卡前置校验
     *
     * @param request
     * @return
     */
    Map preCheckForDiffName(@PropNotEmpty.List({
            @PropNotEmpty(value = "redirect_url"),
    }) Map request);

    //====================  以下是同名换卡接口  ====================


    /**
     * 同名换卡
     *
     * @param merchantBankAccount
     * @return
     */
    Map bindMerchantBankAccount(@PropNotEmpty.List({
            @PropNotEmpty(value = Request.TYPE, message = "{value}请求类型不能为空"),
            @PropNotEmpty(value = "bank_card_image", message = "{value} 银行卡照片不能为空"),
            @PropNotEmpty(value = "bank_card_status", message = "{value} 银行卡照片审核状态不能为空"),
            @PropNotEmpty(value = "number", message = "{value} 银行卡号不能为空"),
            @PropNotEmpty(value = "bank_name", message = "{value}开户银行名称不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}备注不能为空")
    }) Map merchantBankAccount);


    /**
     * 修改不需要送拉卡拉信息
     *
     * @param merchantBankAccount
     * @return
     */
    boolean updateBankOtherMessage(@PropNotEmpty.List({
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}不能为空")
    }) Map merchantBankAccount);

    /**
     * 分页查询商户银行预存信息
     *
     * @param
     * @param request type
     *                holder
     *                id_type
     *                identity
     *                tax_payer_id
     *                number
     *                verify_status
     *                bank_name
     *                branch_name
     *                city
     *                cellphone
     * @return
     */
    ListResult findMerchantBankAccounts(Map<String, Object> request);

    /**
     * 切换现有银行卡
     *
     * @return
     */
    Map replaceMerchantBankAccount(@PropNotEmpty.List({
            @PropNotEmpty(value = "id", message = "{value}预存卡id不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}备注不能为空")
    }) Map merchantBankAccountPre);

    /**
     * 删除预存银行卡数据
     *
     * @param
     */
    void deletedMerchantBankAccountPre(@PropNotEmpty.List({
            @PropNotEmpty(value = "id", message = "{value}预存卡id不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}备注不能为空")
    }) Map<String, Object> request);

    /**
     * 修改预存卡
     *
     * @param merchantBankAccountPre
     * @return
     */
    Map updateMerchantBankAccount(@PropNotEmpty.List({
            @PropNotEmpty(value = "id", message = "{value}预存卡id不能为空"),
            @PropNotEmpty(value = Request.TYPE, message = "{value}换卡类型不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}备注不能为空")
    }) Map merchantBankAccountPre);

    /**
     * 换卡前置校验接口
     *
     * @param params
     * @return
     */
    Map checkoutAllowChangeCard(@PropNotEmpty.List({
            @PropNotEmpty(value = Request.CHANGE_TYPE, message = "{value}请求类型不能为空"),
            @PropNotEmpty(value = "number", message = "{value} 银行卡号不能为空")
    }) Map params);

    /**
     * 验证代付金额
     *
     * @param request id 预存卡id
     *                amount 金额（分）
     * @return
     */
    Map verifyAmountForSameName(@PropNotEmpty.List({
            @PropNotEmpty(value = "id"),
            @PropNotEmpty(value = "amount")
    }) Map request);
}
