package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.helper.CorePlatformsValidated;

import java.util.List;
import java.util.Map;

/**
 * 集团.
 *
 * <AUTHOR>
 */
@CorePlatformsValidated
public interface MspGroupService {

    /**
     * 获取集团.
     *
     * @param request 空
     * @return
     */
    Map getGroup(Map request);

    /**
     * 获取集团用户.
     *
     * @param request 空
     * @return
     */
    Map getGroupUser(Map request);

    /**
     * 分页查询集团用户.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                name                名称
     *                account_id          account表id
     *                role                角色/职务，super_admin：超级管理员，admin：管理员
     *                merchant_auth       商户权限，1：所有商户，2：授权商户
     *                status              状态：0：禁用；1:正常
     *                deleted
     * @return
     */
    ListResult findGroupUsers(Map request);

    /**
     * 获取集团用户商户授权.
     *
     * @param request merchant_id         商户id
     *                merchant_ids        商户id数组
     *                merchant_sn         商户编号
     *                merchant_name       商户名称  模糊查询
     *                deleted
     * @return
     */
    List<Map> getGroupUserMerchantAuths(Map request);

    /**
     * 分页查询集团用户商户权限.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                merchant_id         商户id
     *                merchant_ids        商户id数组
     *                merchant_sn         商户编号
     *                merchant_name       商户名称  模糊查询
     *                deleted
     * @return
     */
    ListResult findGroupUserMerchantAuths(Map request);

    /**
     * 提交下载集团对账单请求.
     *
     * @param request date_start      开始时间
     *                date_end        结束时间
     */
    Map submitExportGroupBillTask(@PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_DATE_START, message = "开始时间不能为空"),
                    @PropNotEmpty(value = ConstantUtil.KEY_DATE_END, message = "结束时间不能为空")
            })
            Map request);

    /**
     * 提交下载集团大容量对账单请求.
     *
     * @param request date_start      开始时间
     *                date_end        结束时间
     */
    Map submitExportGroupBillTask_csv(@PropNotEmpty.List({
            @PropNotEmpty(value = ConstantUtil.KEY_DATE_START, message = "开始时间不能为空"),
            @PropNotEmpty(value = ConstantUtil.KEY_DATE_END, message = "结束时间不能为空")
    })
            Map request);

    /**
     * 查询下载处理情况.
     *
     * @param request taskId          下载任务id，可以不传，不传则查当前用户登录session会话期内提交的最新下载请求
     */
    Map searchExportGroupBillTaskStatus(Map request);

}
