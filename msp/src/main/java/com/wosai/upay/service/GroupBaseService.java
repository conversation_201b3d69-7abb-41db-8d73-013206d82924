package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.util.HttpRequestUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Map;

/**
 * yaming.rong.
 */
public abstract class GroupBaseService {

    /**
     * 获取集团ID.
     *
     * @return
     */
    protected String getSessionGroupId() {
        return getSessionUser().get("group_id") + "";
    }

    /**
     * 获取用户ID.
     *
     * @return
     */
    protected String getSessionUserId() {
        return getSessionUser().get("user_id") + "";
    }

    /**
     * 获取集团ID.
     *
     * @return
     */
    protected String getSessionGroupId(HttpServletRequest request) {
        return ((Map) request.getSession().getAttribute(GroupUserLoginService.SESSION_USER)).get("group_id") + "";
    }

    protected Map getSessionUser() {
        return (Map) HttpRequestUtil.getSession().getAttribute(GroupUserLoginService.SESSION_USER);
    }

}
