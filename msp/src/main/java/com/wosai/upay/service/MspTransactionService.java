package com.wosai.upay.service;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bean.TransactionUpdateReq;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.transaction.model.UpdateTransactionReq;
import com.wosai.upay.transaction.service.TransactionServiceV2;
import java.util.List;
import java.util.Map;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.helper.CorePlatformsValidated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@CorePlatformsValidated
public interface MspTransactionService {
    /**
     * 获取商户订单列表
     *
     * @param request transaction_sn
     *                store_sn
     *                store_name
     *                payway
     *                sub_payway
     *                status
     *                min_total_amount
     *                max_total_amount
     *                type
     * @return
     */
    ListResult findTransactions(Map request);

    /**
     * 根据订单号获取交易明细
     *
     * @param request order_sn
     * @return
     */
    List getTransactionListByOrderSn(
            @PropNotEmpty(value = "order_sn", message = "{value} 订单号不能为空")
                    Map request);

    /**
     * 创建流水查询导出任务
     *
     * @param request
     * @return
     */
    Map createExportTransactionQueryTask(Map<String, Object> request);

    boolean isGrayscaleExist();

    Map getSetting(
            @PropNotEmpty(value = "type", message = "{value} 设置类型不能为空")
                    Map map);

    void updateSetting(@PropNotEmpty.List({
            @PropNotEmpty(value = "type", message = "{value}不能为空"),
            @PropNotEmpty(value = "value", message = "{value}不能为空")})
                               Map map);

    Map updateTansactionInfo(Map request);

}
