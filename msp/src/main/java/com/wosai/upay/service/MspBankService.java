package com.wosai.upay.service;


import com.wosai.upay.common.bean.ListResult;

import java.util.List;
import java.util.Map;

/**
 * 银行相关服务接口
 *
 * <AUTHOR>
public interface MspBankService {

    /**
     * 获取银行列表.
     *
     * @return
     */
    List getBankNames(Map<String, Object> request);

    /**
     * 根据城市、银行名称获取银行网店列表.
     * <p>
     * 该接口返回的数据可能是不准确的，因为银行的名称中就可能包含城市或者省份，比如苏州银行.
     *
     * @param request
     * @return
     */
    List<String> getBranchNamesByBankNameAndGeoPosition(Map<String, Object> request);

    /**
     * 根据银行卡获得银行信息
     *
     * @param request
     * @return
     */
    String getBankByCardNo(Map<String, Object> request);


    List<Map> getDistricts();

    ListResult findBankInfos(Map request);
}