package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.profit.sharing.constant.CommonConstant;
import com.wosai.upay.bean.PayWayResp;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.constant.CacheConstant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.department.Department;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.StatementObjectConfig;
import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.service.*;
import com.wosai.upay.user.api.service.DepartmentService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.CommonUtil;
import com.wosai.upay.util.DateTimeUtil;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.OrderUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.wosai.upay.service.MspUserLoginService.ROLE_DEPARTMENT_MANAGER;


/**
 * Created by kay on 16/9/1.
 */
@Service
@SuppressWarnings("unchecked")
public class MspOrderStatementServiceImpl extends BaseService implements MspOrderStatementService {
    private final Logger logger = LoggerFactory.getLogger(MspOrderStatementServiceImpl.class);

    /**
     * 导出对账单数据，最大支持导出多长时间
     * 31天
     */
    public static final long EXPORT_TRANSACTION_MAX_TIME = 31 * 24 * 60 * 60 * 1000l;
    /**
     * 导出对账单限制最大任务数的时间范围
     */
    public static final long EXPORT_RUNNING_LIMIT_TIME = 7 * 24 * 60 * 60 * 1000l;
    /**
     * 导出对账单限制时间范围的最大任务数
     */
    public static final long EXPORT_RUNNING_LIMIT_COUNT = 2l;


    public static final String DAY_SDF_PATTERN_YYYYMMDD = "yyyyMMdd";

    @Autowired
    private UserService userService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private TranslateService translateService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private TransactionService transactionService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    private ExportService exportService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private StatementObjectConfigService statementObjectConfigService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private MspUserLoginService mspUserLoginService;

    @Autowired
    private MspUserService mspUserService;

    @Autowired
    private IPayWayService iPayWayService;


    @Override
    public List getTransactionListByOrderSn(Map request) {
        String orderSn = BeanUtil.getPropString(request, "order_sn");
        List<Map> list = transactionService.getTransactionListByOrderSn(orderSn);
        if (list != null) {
            for (Map<String, Object> transaction : list) {
                String merchantId = transaction.get("merchant_id") + "";
                CommonUtil.checkJurisdiction(merchantId);
            }
        }
        if (list != null && list.size() != 0) {
            translateService.translateOrderOperatorNames(list, BeanUtil.getPropString(list.get(0), ConstantUtil.KEY_MERCHANT_ID));
        }
        return list;
    }


    @Override
    public ListResult findTaskApplyLogs(Map request) {
        if (request == null) {
            request = new HashMap();
        }
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        long sevenDaysAgo = new Date().getTime() - EXPORT_RUNNING_LIMIT_TIME;
        if (pageInfo.getDateStart() == null || pageInfo.getDateStart() < sevenDaysAgo) {
            pageInfo.setDateStart(sevenDaysAgo);
        }
        request.put(StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_MSP);
        request.put(StatementTaskLog.USER_ID, BeanUtil.getPropString(getSessionInfos(null), "user_id"));
        ListResult result = taskLogService.findTaskApplyLogs(pageInfo, request);
        if (result != null && result.getRecords() != null) {
            for (Map task : result.getRecords()) {
                addUserNameInfo(task);
            }
        }
        return result;
    }

    @Override
    public Map getTaskApplyLog(Map request) {
        String taskApplyLogId = BeanUtil.getPropString(request, "taskApplyLogId");
        if (StringUtil.empty(taskApplyLogId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出任务id不可为空!");
        }
        Map task = taskLogService.getTaskApplyLog(taskApplyLogId);
        addUserNameInfo(task);
        return task;
    }

    /**
     * 添加用户姓名信息
     *
     * @param task
     */
    private void addUserNameInfo(Map task) {
        String user_id = BeanUtil.getPropString(task, StatementTaskLog.USER_ID);
        if (StringUtil.empty(user_id)) {
            Map account = null;
            try {
                account = userService.getAccount(user_id);
            } catch (Exception e) {
            }
            task.put("user_name", BeanUtil.getPropString(account, Account.USERNAME));
            task.put("user_nickname", BeanUtil.getPropString(account, Account.NICKNAME));
        }
    }

    @Override
    public void deleteTaskApplyLog(Map request) {
        String taskApplyLogId = BeanUtil.getPropString(request, "taskApplyLogId");
        if (StringUtil.empty(taskApplyLogId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出任务id不可为空!");
        }
//        Map taskApplyLog = getTaskApplyLog(request);
//        if (taskApplyLog == null) {
//            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出任务不存在!");
//        }
//        if (StatementTaskLog.APPLY_STATUS_RUNNING.contains(BeanUtil.getPropInt(taskApplyLog, StatementTaskLog.APPLY_STATUS))) {
//            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出任务正在执行中不允许删除!");
//        }
        taskLogService.deleteTaskApplyLog(taskApplyLogId);
    }

    @Override
    public ListResult findOrders(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        if (pageInfo.getDateStart() == null) {
            pageInfo.setDateStart(new Date().getTime() - EXPORT_RUNNING_LIMIT_TIME);
        }

        return orderService.getOrderList(pageInfo, getQueryOrderParams(request));
    }

    @Override
    public Map getOrderDetail(Map request) {
        Map sessionInfo = getSessionInfos(null);
        List<String> merchantIds = (List) BeanUtil.getProperty(sessionInfo, "merchant_ids");
        if (merchantIds == null || merchantIds.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "非法请求，获取不到用户信息!");
        }
        String orderId = BeanUtil.getPropString(request, "order_id");
        if (!StringUtil.empty(orderId)) {
            Map order = orderService.getOrderDetailByOrderId(orderId);
            if (order != null) {
                if (!merchantIds.contains(BeanUtil.getPropString(order, Order.MERCHANT_ID, ""))) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "非法请求，用户没有查看该订单的权限!");
                }
            }
            return order;
        }
        String orderSn = BeanUtil.getPropString(request, "order_sn");
        if (!StringUtil.empty(orderSn)) {
            Map order = orderService.getOrderDetailByOrderSn(orderSn);
            if (order != null) {
                if (!merchantIds.contains(BeanUtil.getPropString(order, Order.MERCHANT_ID, ""))) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "非法请求，用户没有查看该订单的权限!");
                }
                //  添加payWay对应的payName
                logger.info("----getOrderDetail,添加payWayName-----");
                Object payway = order.get("payway");
                String payWayName = null;
                if (payway != null){
                    payWayName = getPayWayNameKeyByPayWay(Integer.parseInt(payway.toString()));
                }
                if (payWayName != null){
                    order.put("pay_way_name", payWayName);
                }
            }
            return order;
        }
        return null;
    }

    private String getPayWayNameKeyByPayWay(Integer payWay) {
        String payWayNameKey = "PAY_WAY_DNQR_1000";
        if (payWay == null){
            return payWayNameKey;
        }
        List<PayWayResp> payWayRespList = iPayWayService.getPayWay();
        for (PayWayResp resp : payWayRespList){
            if (resp.getPayWay() != null && resp.getPayWay().equals(payWay)){
                return resp.getI18nKey();
            }
        }
        return payWayNameKey;
    }

    @Override
    public Map createExportOrderTask(Map<String, Object> request) {
        Map sessionInfo = getSessionInfos(BeanUtil.getProperty(request, "httpRequest"));
        String user_id = BeanUtil.getPropString(sessionInfo, "user_id");

        //检查是否允许导出
        allowExport(user_id, "order");

        //参数检查
        long date_start = BeanUtil.getPropLong(request, "date_start");
        long date_end = BeanUtil.getPropLong(request, "date_end");
        if (date_start <= 0 || date_end <= 0 || date_start > date_end) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "订单导出时间有误");
        }
        String timeZoneStr = BeanUtil.getPropString(request, "timeZone");
        int upayQueryType = BeanUtil.getPropInt(request, "upayQueryType");
        return exportService.createExportStatementTask(CollectionUtil.hashMap(
                StatementTaskLog.TYPE, StatementTaskLog.TYPE_ORDER,
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_MSP,
                StatementTaskLog.TITLE, getExportTitle(date_start, date_end, BeanUtil.getPropString(sessionInfo, "merchant_name"), StatementTaskLog.TYPE_ORDER, timeZoneStr, upayQueryType),
                StatementTaskLog.USER_ID, user_id
        ), getQueryOrderParams(request));
    }


    public Map createExportWalletTask(Map<String, Object> request) {
        Map sessionInfo = getSessionInfos(BeanUtil.getProperty(request, "httpRequest"));

        String user_id = BeanUtil.getPropString(sessionInfo, "user_id");

        //检查是否允许导出
        allowExport(user_id, "wallet");

        //参数检查
        long date_start = BeanUtil.getPropLong(request, "date_start");
        long date_end = BeanUtil.getPropLong(request, "date_end");
        if (date_start <= 0 || date_end <= 0 || date_start > date_end) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "余额对账单导出时间有误");
        }
        String timeZoneStr = BeanUtil.getPropString(request, "timeZone");

        final Map queryOrderParams = getQueryOrderParams(request);
        queryOrderParams.put(StatementObjectConfig.OBJECT_ID, BeanUtil.getPropString(sessionInfo, "object_id"));

        boolean isSuperAdmin = BeanUtil.getPropBoolean(sessionInfo, "isSuperAdmin");
        if (!isSuperAdmin) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "只有superAdmin账号才可导出余额对账单");
        }

        return exportService.createExportStatementTask(CollectionUtil.hashMap(
                StatementTaskLog.TYPE, StatementTaskLog.TYPE_BALANCE,
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_MSP,
                StatementTaskLog.TITLE, getExportTitle(date_start, date_end, BeanUtil.getPropString(sessionInfo, "merchant_name"),
                        StatementTaskLog.TYPE_BALANCE, timeZoneStr, -1),
                StatementTaskLog.USER_ID, user_id
        ), queryOrderParams);
    }

    private Map getQueryOrderParams(Map<String, Object> request) {
        Map sessionInfo = getSessionInfos(BeanUtil.getProperty(request, "httpRequest"));
        Map params = CollectionUtil.hashMap(
                "store_name", BeanUtil.getPropString(request, "store_name"),
                "store_sn", BeanUtil.getPropString(request, "store_sn"),
                "store_id", BeanUtil.getPropString(request, "store_id"),
                "merchant_id", BeanUtil.getPropString(request, "merchant_id"),
                "merchant_sn", BeanUtil.getPropString(request, "merchant_sn"),
                "merchant_name", BeanUtil.getPropString(request, "merchant_name"),
                "payway", BeanUtil.getPropString(request, "payway"),
                "sub_payway", BeanUtil.getPropString(request, "sub_payway"),
                "status", BeanUtil.getPropString(request, "status"),
                "min_total_amount", BeanUtil.getPropString(request, "min_total_amount"),
                "max_total_amount", BeanUtil.getPropString(request, "max_total_amount"),
                "order_sn", BeanUtil.getPropString(request, "order_sn"),
                "client_sn", BeanUtil.getPropString(request, "client_sn"),
                "trade_no", BeanUtil.getPropString(request, "trade_no"),
                "terminal_sn", BeanUtil.getPropString(request, "terminal_sn"),
                "provider", BeanUtil.getPropString(request, "provider"),
                "device_fingerprint", BeanUtil.getPropString(request, Terminal.DEVICE_FINGERPRINT),
                "date_start", BeanUtil.getPropLong(request, "date_start"),
                "date_end", BeanUtil.getPropLong(request, "date_end"),
                "last_record_ctime", BeanUtil.getPropLong(request, "last_record_ctime"),//上次返回的按时间倒叙排的最后一条记录的ctime
                "merchant_ids", (List) BeanUtil.getProperty(sessionInfo, "merchant_ids"),
                "department_ids", (List) BeanUtil.getProperty(sessionInfo, "department_ids"),
                "merchant_user_id", BeanUtil.getPropString(sessionInfo, "merchant_user_id"),
                "timeZone", BeanUtil.getPropString(request, "timeZone"),
                "upayQueryType", BeanUtil.getPropInt(request, "upayQueryType")
        );
        List<String> departmentIds = (List<String>) BeanUtil.getProperty(sessionInfo, "department_ids");

        String type = BeanUtil.getPropString(sessionInfo, "type");
        boolean isSuperAdmin = BeanUtil.getPropBoolean(sessionInfo, "isSuperAdmin");
        if (type.equals("merchant") && !isSuperAdmin) {
            if (departmentIds != null && departmentIds.size() > 0) {
                Set storeIds = departmentService.listStoresByDepartmentId(getSessionMerchantId(), departmentIds);
                params.put("store_ids", new ArrayList<>(storeIds));
            } else {
                params.put("store_ids", (List) BeanUtil.getProperty(sessionInfo, "store_ids"));
            }
        }
        //支付源订单号
        String channel_trade_no = BeanUtil.getPropString(request, "channel_trade_no");
        if (!StringUtil.empty(channel_trade_no)) {
            String orderSn = OrderUtil.getOrderSnByChannelTradeNo(channel_trade_no);
            if (StringUtil.empty(orderSn)) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "源交易订单号没有在系统中找到！");
            }
            params.put("order_sn", orderSn);
        }

        return params;
    }

    @Override
    public void insertOrUpdate(Map config) {
        Map sessionInfo = getSessionInfos(null);
        String objectId = BeanUtil.getPropString(sessionInfo, "object_id");
        String language = BeanUtil.getPropString(config, StatementObjectConfig.LANGUAGE);
        if (StringUtil.empty(language)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "language不合法");
        }
        int splitType = BeanUtil.getPropInt(config, StatementObjectConfig.SPLIT_TYPE);
        if (splitType == 0) {
            config.put(StatementObjectConfig.SPLIT_TYPE, 0);
        }
        Map sheetParams = (Map) BeanUtil.getProperty(config, StatementObjectConfig.SHEET_PARAMS);
        int sheetType = BeanUtil.getPropInt(sheetParams, StatementObjectConfig.SHEET_TYPE);

        if (sheetType == 0) {
            Map sheet = CollectionUtil.hashMap(StatementObjectConfig.SHEET_TYPE, 0,
                    StatementObjectConfig.TERMINAL_TYPE, 1,
                    StatementObjectConfig.CRO, Arrays.asList(StatementObjectConfig.TRANSACTION_NO, StatementObjectConfig.PAID_AMOUNT, StatementObjectConfig.CHANRGE, StatementObjectConfig.SHARING_AMOUNT, StatementObjectConfig.SETTLEMENT_AMOUNT));
            config.put(StatementObjectConfig.SHEET_PARAMS, sheet);
        } else if (sheetType == 1) {
            Map sheet = CollectionUtil.hashMap(StatementObjectConfig.SHEET_TYPE, 1,
                    StatementObjectConfig.TERMINAL_TYPE, 0,
                    StatementObjectConfig.CRO, Arrays.asList(
                            StatementObjectConfig.TRANSACTION_NO, StatementObjectConfig.TRANSACTION_AMOUNT, StatementObjectConfig.REFUND_NO, StatementObjectConfig.REFUND_AMOUNT,
                            StatementObjectConfig.MERCHANT_DISCOUNT, StatementObjectConfig.WOSAI_DISCOUNT, StatementObjectConfig.PAYMENT_TYPE_DISCOUNT, StatementObjectConfig.MERCHANT_DISCOUNT_PREPAID_DISCOUNT,
                            StatementObjectConfig.MERCHANT_DISCOUNT_NON_PREPAID_DISCOUNT, StatementObjectConfig.PAID_AMOUNT, StatementObjectConfig.CHANRGE, StatementObjectConfig.SHARING_AMOUNT, StatementObjectConfig.SETTLEMENT_AMOUNT),
                    StatementObjectConfig.PAYWAY, Arrays.asList(
                            StatementObjectConfig.ALIPAY_HK, StatementObjectConfig.ALIPAY_OLD, StatementObjectConfig.ALIPAY_NEW, StatementObjectConfig.WEIXIN,
                            StatementObjectConfig.BAIPAY, StatementObjectConfig.JDPAY, StatementObjectConfig.QQPAY, StatementObjectConfig.LKLPAY,
                            StatementObjectConfig.YLCODEPAY,
                            StatementObjectConfig.ALIPAY_HK_LOCAL,
                            StatementObjectConfig.ALIPAY_INTL,
                            StatementObjectConfig.YIPAY, StatementObjectConfig.WEIXIN_HK,
                            StatementObjectConfig.GIFT_CARD));
            config.put(StatementObjectConfig.SHEET_PARAMS, sheet);
        } else if (sheetType == 2) {
            config.put(StatementObjectConfig.SHEET_PARAMS, sheetParams);
        } else {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "sheet_type不合法");
        }

        Map sheetDetailParams = (Map) BeanUtil.getProperty(config, StatementObjectConfig.SHEET_DETAIL_PARAMS);
        int sheetDetailType = BeanUtil.getPropInt(sheetDetailParams, StatementObjectConfig.SHEET_DETAIL_TYPE);
        if (sheetDetailType == 0) {
            Map sheet = CollectionUtil.hashMap(StatementObjectConfig.SHEET_DETAIL_TYPE, 0);
            config.put(StatementObjectConfig.SHEET_DETAIL_PARAMS, sheet);
        } else if (sheetDetailType == 1) {
            Map sheet = CollectionUtil.hashMap(StatementObjectConfig.SHEET_DETAIL_TYPE, 1);
            config.put(StatementObjectConfig.SHEET_DETAIL_PARAMS, sheet);
        }
        config.put(StatementObjectConfig.OBJECT_ID, objectId);
        statementObjectConfigService.insertOrUpdate(config);
    }

    @Override
    public Map getStatementConfig() {
        Map sessionInfo = getSessionInfos(null);
        String objectId = BeanUtil.getPropString(sessionInfo, "object_id");
        return statementObjectConfigService.getConfigByObjectId(objectId);
    }


    @Override
    public Map createExportTransactionTask(Map<String, Object> request) {
        Map sessionInfo = getSessionInfos(BeanUtil.getProperty(request, "httpRequest"));
        String userId = BeanUtil.getPropString(sessionInfo, "user_id");
        String type = BeanUtil.getPropString(sessionInfo, "type");
        boolean isSuperAdmin = BeanUtil.getPropBoolean(sessionInfo, "isSuperAdmin");
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        boolean exportHistoricalTransaction = BeanUtil.getPropBoolean(request, "historical", false);
        if (StringUtil.empty(merchantId)) {
            merchantId = getSessionMerchantId();
        }
        //检查是否允许导出
        allowExport(userId, "transaction");

        //参数检查
        long dateStart = BeanUtil.getPropLong(request, "date_start");
        long dateEnd = BeanUtil.getPropLong(request, "date_end");

        if (dateStart <= 0 || dateEnd <= 0 || dateStart > dateEnd || dateEnd - dateStart > EXPORT_TRANSACTION_MAX_TIME) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "对账单导出时间超过最大限制");
        }

        if (request == null) {
            request = new HashedMap();
        }
        //是否含有自定义参数
        boolean hasParams = false;

        request.put(StatementObjectConfig.OBJECT_ID, BeanUtil.getPropString(sessionInfo, "object_id"));
        //门店号、管理员维度
        String storeSn = BeanUtil.getPropString(request, "store_sn");
        List<String> needStoreSns = (List<String>) MapUtils.getObject(request, "store_sns");
        String merchantUserId = BeanUtil.getPropString(request, "merchant_user_id");
        if (!StringUtil.empty(merchantUserId)) {
            request.put("merchant_user_id", merchantUserId);
            request.put("merchant_id", merchantId);
            hasParams = true;
        }
        if (!StringUtil.empty(storeSn) || !CollectionUtils.isEmpty(needStoreSns)) {
            if (needStoreSns == null) {
                needStoreSns = new ArrayList<>();
            }
            if (!StringUtil.empty(storeSn)) {
                needStoreSns.add(storeSn);
            }

            List<String> storeSns = (List) BeanUtil.getProperty(sessionInfo, "store_sns");
            if (storeSns == null || !storeSns.containsAll(needStoreSns)) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户下找不到该门店，请重新输入");
            }
            List<String> storeIds = getStoreIdsBySns(needStoreSns);
            if (storeIds.size() == 1) {
                request.put("store_id", storeIds.get(0));
            }
            if (storeIds.size() > 1) {
                request.put("store_ids", storeIds);
            }
            List<String> merchantIds = getMerchantIdsByStoreIds(storeIds);
            if (merchantIds.size() > 0) {
                request.put("merchant_id", merchantIds.get(0));
            }
            hasParams = true;
        }

        String role = getSessionUserRole();
        if (StringUtils.equals(role, ROLE_DEPARTMENT_MANAGER) && StringUtils.isEmpty(storeSn) && (needStoreSns == null || needStoreSns.size() == 0)) {
            Object accountId = HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
            List<Map> stores = mspUserService.getMerchantUserDepartmentStoreAuths(CollectionUtil.hashMap(
                    "account_id", accountId
            ));
            List<String> storeIds = new ArrayList<>();
            for (Map store : stores) {
                storeIds.add(MapUtils.getString(store, "id"));
            }
            request.put("store_ids", storeIds);
            request.put("merchant_id", merchantId);
            hasParams = true;
        }

        //部门纬度
        List<String> departmentIds = (List<String>) BeanUtil.getProperty(request, "department_ids");
        if (!CollectionUtils.isEmpty(departmentIds)) {
            if ("".equals(departmentIds.get(0))) {
                List<Map<String, Object>> listDepartment = departmentService.listDepartmentTree(merchantId, null);
                if (listDepartment.size() == 0) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户下找不到部门，请重新输入");
                }
                if (StringUtils.equals(role, ROLE_DEPARTMENT_MANAGER)) {
                    departmentIds = (List<String>) BeanUtil.getProperty(sessionInfo, "department_ids");
                }

            } else {
                Map department = departmentService.getDepartment(departmentIds.get(0));
                merchantId = BeanUtil.getPropString(department, Department.MERCHANT_ID);
                if (StringUtil.empty(merchantId)) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户下找不到该部门，请重新输入");
                }
            }
            request.put("merchant_id", merchantId);
            request.put("department_ids", departmentIds);
            hasParams = true;
        }


        //商户号维度
        String merchantSn = BeanUtil.getPropString(request, "merchant_sn");
        List<String> needMerchantSns = (List<String>) MapUtils.getObject(request, "merchant_sns");

        if (!StringUtil.empty(merchantSn) || !CollectionUtils.isEmpty(needMerchantSns)) {
            if (needMerchantSns == null) {
                needMerchantSns = new ArrayList<>();
            }
            if (!StringUtil.empty(merchantSn)) {
                needMerchantSns.add(merchantSn);
            }

            List<String> merchantSns = (List) BeanUtil.getProperty(sessionInfo, "merchant_sns");
            if (merchantSns == null || !merchantSns.containsAll(needMerchantSns)) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户下找不到该门店，请重新输入");
            }
            request.put("merchant_ids", getMerchantIdsBySns(needMerchantSns));
            hasParams = true;
        }
        int exportType = StatementTaskLog.TYPE_TRANSACTION;
        if (type.equals("group")) {
            request.put("group_name", BeanUtil.getPropString(sessionInfo, "group_name"));
            request.put("group_sn", BeanUtil.getPropString(sessionInfo, "group_sn"));
            request.put("group_id", BeanUtil.getPropString(sessionInfo, "group_id"));
            exportType = StatementTaskLog.TYPE_TRANSACTION_GROUP;
        }
        if (exportHistoricalTransaction) {
            if (exportType == StatementTaskLog.TYPE_TRANSACTION) {
                exportType = StatementTaskLog.TYPE_HISTORICAL_TRANSACTION;
            } else if (exportType == StatementTaskLog.TYPE_TRANSACTION_GROUP) {
                exportType = StatementTaskLog.TYPE_HISTORICAL_GROUP_TRANSACTION;
            }
        }
        String timeZoneStr = BeanUtil.getPropString(request, "timeZone");
        int upayQueryType = BeanUtil.getPropInt(request, "upayQueryType");
        //如果没有自定义参数 传入当前商户ids集合
        if (!hasParams) {
            request.put("merchant_ids", BeanUtil.getProperty(sessionInfo, "merchant_ids"));
            if (type.equals("merchant") && !isSuperAdmin) {
                request.put("store_ids", BeanUtil.getProperty(sessionInfo, "store_ids"));
            }
        }
        return exportService.createExportStatementTask(CollectionUtil.hashMap(
                StatementTaskLog.TYPE, exportType,
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_MSP,
                StatementTaskLog.TITLE, getExportTitle(dateStart, dateEnd, BeanUtil.getPropString(sessionInfo, "merchant_name"), StatementTaskLog.TYPE_TRANSACTION, timeZoneStr, upayQueryType),
                StatementTaskLog.USER_ID, userId
        ), request);
    }


    @Override
    public Map createExportSharingBookTask(Map request) {
        Map sessionInfo = getSessionInfos(BeanUtil.getProperty(request, "httpRequest"));
        String userId = BeanUtil.getPropString(sessionInfo, "user_id");

        allowExport(userId, "sharing_book");
        //参数检查
        long dateStart = BeanUtil.getPropLong(request, "date_start");
        long dateEnd = BeanUtil.getPropLong(request, "date_end");

        if (dateStart <= 0 || dateEnd <= 0 || dateStart > dateEnd || dateEnd - dateStart > EXPORT_TRANSACTION_MAX_TIME) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "对账单导出时间超过最大限制");
        }
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        String receiverMerchantSn = BeanUtil.getPropString(request, CommonConstant.RECEIVER_MERCHANT_SN);
        if (StringUtil.empty(merchantId) && StringUtil.empty(receiverMerchantSn)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "系统错误，不允许导出");
        }
        if (request == null) {
            request = new HashedMap();
        }
        request.put("merchant_id", merchantId);
        String timeZoneStr = BeanUtil.getPropString(request, "timeZone");
        int upayQueryType = BeanUtil.getPropInt(request, "upayQueryType");
        return exportService.createExportStatementTask(CollectionUtil.hashMap(
                StatementTaskLog.TYPE, StatementTaskLog.TYPE_SHARING_BOOK,
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_MSP,
                StatementTaskLog.TITLE, getExportTitle(dateStart, dateEnd, BeanUtil.getPropString(sessionInfo, "merchant_name"), StatementTaskLog.TYPE_SHARING_BOOK, timeZoneStr, upayQueryType),
                StatementTaskLog.USER_ID, userId
        ), request);
    }

    private String getExportTitle(long time_begin, long time_end, String merchant_name, int type, String timeZoneStr, int upayQueryType) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DAY_SDF_PATTERN_YYYYMMDD);
        if (!StringUtils.isBlank(timeZoneStr)) {
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone(timeZoneStr));
        }
        String title = simpleDateFormat.format(time_begin) + "_" + simpleDateFormat.format(time_end);
        if (upayQueryType == 1) {
            title = "银行卡" + title;
        }

        if (type == StatementTaskLog.TYPE_ORDER) {
            title = "订单" + title;
        } else if (type == StatementTaskLog.TYPE_TRANSACTION) {
            title = "对账单" + title;
        } else if (type == StatementTaskLog.TYPE_BALANCE) {
            title = "余额对账单" + title;
        } else if (type == StatementTaskLog.TYPE_SHARING_BOOK) {
            title = "分账明细" + title;
        }
        return merchant_name + title;
    }

    /**
     * 检查是否允许导出
     */
    private void allowExport(String user_id, String type) {
        //检查当前时间 系统是否允许导出对账单
        Map closeExportInfo = cacheService.getCacheMap(CacheConstant.KEY_PRE_DISABLE_EXPORT, type);//KEY_PRE_DISABLE_EXPORT_ 由于最新api包去掉该常量,故只写在本项目
        List<Map> disable_times = (List) BeanUtil.getProperty(closeExportInfo, "disable_times");
        if (disable_times != null && disable_times.size() > 0) {
            String disable_reason = BeanUtil.getPropString(closeExportInfo, "disable_reason");
            long now = new Date().getTime();
            long currentTimeToBegin = now - DateTimeUtil.getOneDayStart(now);
            for (Map disable_time : disable_times) {
                if (currentTimeToBegin >= BeanUtil.getPropLong(disable_time, "begin") && currentTimeToBegin <= BeanUtil.getPropLong(disable_time, "end")) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, disable_reason);
                }
            }
        }


        //检查用户七日内正在执行的任务数是否小于2
        long now = new Date().getTime();
        long sevenDaysAgo = now - EXPORT_RUNNING_LIMIT_TIME;
        ListResult taskResult = taskLogService.findTaskApplyLogs(new PageInfo(1, 10, sevenDaysAgo, now), CollectionUtil.hashMap(
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_MSP,
                StatementTaskLog.USER_ID, user_id,
                StatementTaskLog.APPLY_STATUSES, StatementTaskLog.APPLY_STATUS_RUNNING
        ));
        if (taskResult != null && taskResult.getTotal() >= EXPORT_RUNNING_LIMIT_COUNT) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("当前已有%s个对账单任务在生成中,请等待任务完成再来操作", EXPORT_RUNNING_LIMIT_COUNT));
        }
    }

    /**
     * 根据商户sn集合获取商户id集合
     *
     * @param merchant_sns
     * @return
     */
    List<String> getMerchantIdsBySns(List<String> merchant_sns) {
        List<String> merchant_ids = new ArrayList<>();
        if (merchant_sns != null && merchant_sns.size() > 0) {
            ListResult result = merchantService.findMerchants(new PageInfo(1, merchant_sns.size()), CollectionUtil.hashMap(
                    "merchant_sns", merchant_sns
            ));
            if (result != null && result.getRecords() != null && result.getRecords().size() > 0) {
                for (Map merchant : result.getRecords()) {
                    merchant_ids.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
                }
            }
        }
        return merchant_ids;
    }

    /**
     * 根据门店sn集合获取门店id集合
     *
     * @param store_sns
     * @return
     */
    List<String> getStoreIdsBySns(List<String> store_sns) {
        List<String> store_ids = new ArrayList<>();
        if (store_sns != null && store_sns.size() > 0) {
            for (String store_sn : store_sns) {
                store_ids.add(BeanUtil.getPropString(storeService.getStoreByStoreSn(store_sn), DaoConstants.ID));
            }
        }
        return store_ids;
    }

    /**
     * 根据门店Id集合获取商户id集合
     *
     * @param store_ids
     * @return
     */
    List<String> getMerchantIdsByStoreIds(List<String> store_ids) {
        List<String> merchant_ids = new ArrayList<>();
        if (store_ids != null && store_ids.size() > 0) {
            for (String store_id : store_ids) {
                Map store = storeService.getStore(store_id);
                if (store != null && !merchant_ids.contains(BeanUtil.getPropString(store, Store.MERCHANT_ID))) {
                    merchant_ids.add(BeanUtil.getPropString(store, Store.MERCHANT_ID));
                }
            }
        }
        return merchant_ids;
    }

    /**
     * 根据管理员获取merchant_id
     *
     * @param merchantUserId
     * @return
     */
    String getMerchantIdByMerchantUserId(String merchantUserId) {
        Map merchantUser = userService.getMerchantUser(merchantUserId);
        return BeanUtil.getPropString(merchantUser, MerchantUser.MERCHANT_ID);
    }

}


