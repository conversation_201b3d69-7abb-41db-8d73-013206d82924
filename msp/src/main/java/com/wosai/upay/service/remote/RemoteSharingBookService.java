package com.wosai.upay.service.remote;

import com.wosai.upay.common.bean.ListResult;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/8/21.
 */
public interface RemoteSharingBookService {

    /**
     * 获取分账支出明细信息
     * @param id 分账id信息
     * @return 结果信息
     */
    Map<String,Object> getSharingBook(String id);

    /**
     * 查询分账支出明细信息
     * @param conditions
     *  merchant_id
     *  store_id
     *  terminal_id
     *  model_id,
     *  merchant_sn
     *  transaction_id 分账流水号
     *  order_sn 交易订单号
     *  transaction_client_sn 交易流水号
     *  payer_account_organization
     *  receiver_id,
     *  receiver_name,
     *  receiver_client_sn
     *  receiver_merchant_sn
     *  receiver_account_no
     *  status
     * @param pagination 分页信息
     * @return 分账详情
     */
    ListResult querySharingBooks(Map<String, Object> conditions, Map<String, Object> pagination);

}
