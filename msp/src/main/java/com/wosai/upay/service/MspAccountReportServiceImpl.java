package com.wosai.upay.service;

import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.service.IAccountReportService;
import com.wosai.service.IAccountReportServiceProxy;
import com.wosai.service.bean.TAccountRecordReport;
import com.wosai.service.bean.TSwitchConfigResult;
import com.wosai.service.enums.ReportLoadType;
import com.wosai.service.enums.TransactionTypeEnum;
import com.wosai.service.param.AccountRecordReportParam;
import com.wosai.upay.exception.UpayException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import avro.shaded.com.google.common.collect.Maps;
import avro.shaded.com.google.common.collect.Sets;

import static com.wosai.upay.util.HttpRequestUtil.getSession;

@Service
public class MspAccountReportServiceImpl extends BaseService implements MspAccountReportService {

    @Autowired
    private IAccountReportService accountReportService;
    @Autowired
    private IAccountReportServiceProxy accountReportServiceProxy;

    public static final Logger logger = LoggerFactory.getLogger(MspAccountReportServiceImpl.class);


    @Override
    public TAccountRecordReport getAccountRecordReport(Map request) {
        Map sessionInfo = getSessionInfos(BeanUtil.getProperty(request, "httpRequest"));
        List<String> merchantIds = (List) BeanUtil.getProperty(sessionInfo, "merchant_ids");
        if (CollectionUtils.isEmpty(merchantIds)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "非法请求，获取不到用户信息!");
        }
        Boolean isSuperAdmin = (Boolean) sessionInfo.get("isSuperAdmin");

        String merchantIdsParam = (String) request.get("merchantIds");

        AccountRecordReportParam param = new AccountRecordReportParam();

        //设置merchantIds
        if (StringUtils.hasText(merchantIdsParam)) {
            Sets.SetView<String> intersection = Sets.intersection(Sets.newHashSet(merchantIds), Sets.newHashSet(merchantIdsParam.split(",")));
            if (intersection.size() == 0) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "非法请求，没权限查看!");
            }

            param.setMerchantIds(Sets.newHashSet(intersection.iterator()));

        } else {
            param.setMerchantIds(Sets.newHashSet(merchantIds));
        }


        //设置storeIds
        String storeIdsParam = (String) request.get("storeIds");
        if (isSuperAdmin) {
            if (StringUtils.hasText(storeIdsParam)) {
                param.setStoreIds(Sets.newHashSet(storeIdsParam.split(",")));
            }
        } else {

            List<String> storeIds = (List) BeanUtil.getProperty(sessionInfo, "store_ids");
            if (CollectionUtils.isEmpty(storeIds)) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "非法请求，获取不到门店信息!");
            }

            if (StringUtils.hasText(storeIdsParam)) {
                Sets.SetView<String> intersection = Sets.intersection(Sets.newHashSet(storeIds), Sets.newHashSet(storeIdsParam.split(",")));
                if (intersection.size() == 0) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "非法请求，没权限查看!");
                }
                param.setStoreIds(Sets.newHashSet(intersection.iterator()));
            } else {
                param.setStoreIds(Sets.newHashSet(storeIds));
            }
        }

        param.setStartDate((String) request.get("startDate"));
        param.setEndDate((String) request.get("endDate"));
        param.setSize((Integer) request.get("size"));
        String reportLoadTypes = (String) request.get("reportLoadTypes");
        if (StringUtils.hasText(reportLoadTypes)) {
            param.setReportLoadTypes(ReportLoadType.name2Enums(reportLoadTypes.split(",")));
        }
        final String transactionType = BeanUtil.getPropString(request, "upayQueryType");
        if ("1".equals(transactionType)) {
            param.setTransactionType(TransactionTypeEnum.BANKCARD_PAY);
        } else {
            param.setTransactionType(TransactionTypeEnum.MOBILE_PAY);
        }
        param.setOffsetHour(BeanUtil.getPropInt(request, "offsetHour", 0));
        return accountReportService.getAccountRecordReport(param);
    }

//    @Override
//    public boolean checkDataIsExist() {
//        return checkDataIsExist("0");
//    }

    @Override
    public boolean checkDataIsExist(Map<String, Object> params) {
        Map<String, Object> param = Maps.newHashMap();
        final String upayQueryType = BeanUtil.getPropString(params, "upayQueryType");
        if ("1".equals(upayQueryType)) {
            param.put("transactionType", "BANKCARD_PAY");
        } else {
            param.put("transactionType", "MOBILE_PAY");
        }
        return accountReportServiceProxy.checkDataIsExist(param);
    }

    @Override
    public void switchConfigUpdate(Map<String, Object> params) {
        String accountId = (String) getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        int status = BeanUtil.getPropInt(params, "status");
        String time = BeanUtil.getPropString(params, "time");
        String bankcard_time = BeanUtil.getPropString(params, "bankcard_time");
        Object emails = BeanUtil.getNestedProperty(params, "body.emails");
        if (emails != null && !StringUtil.empty(emails.toString())) {
            String[] emailArr = emails.toString().split(",");
            if (emailArr.length > 5) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "邮件最多只能设置5个!");
            }
            for (String anEmailArr : emailArr) {
                if (!emailFormat(anEmailArr)) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "邮件格式不合法!");
                }
            }
        }
        Map<String, Object> body = Maps.newHashMap();
        body.put("emails", emails);

        logger.info("switchConfigUpdate rpc invoke");
        accountReportService.switchConfigUpdate(accountId, 0, status, time, bankcard_time, JSON.toJSONString(body));
    }

    @Override
    public TSwitchConfigResult getSwitchConfig(Map<String, Object> params) {
        String accountId = (String) getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        return accountReportService.getSwitchConfig(accountId, 0);
    }

    private static final String pattern1 = "^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$";

    public static boolean emailFormat(String email) {
        boolean tag = true;

        final Pattern pattern = Pattern.compile(pattern1);
        final Matcher mat = pattern.matcher(email);
        if (!mat.find()) {
            tag = false;
        }
        return tag;
    }

}
