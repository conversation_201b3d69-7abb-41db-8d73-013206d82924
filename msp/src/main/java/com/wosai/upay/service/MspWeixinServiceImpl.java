package com.wosai.upay.service;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.member.bean.BMerchant;
import com.wosai.upay.member.bean.WeixinConfig;
import com.wosai.upay.member.service.BMerchantService;
import com.wosai.upay.member.service.DictService;
import com.wosai.weixin.open.bean.AuthorizerInfo;
import com.wosai.weixin.open.service.AuthService;
import com.wosai.weixin.service.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * .
 *
 * <AUTHOR>
 */
@Service
public class MspWeixinServiceImpl extends BaseService implements MspWeixinService {

    @Autowired
    private BMerchantService bMerchantService;

    @Autowired
    private DictService dictService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private AuthService authService;

    @Value("${weixin-open.url}")
    private String weixinOpenUrl;

    @Value("${web-platforms-msp.server}")
    private String webPlatformsMspServer;

    @Override
    public String getWeixinOpenUrl() {
        return weixinOpenUrl;
    }

    @Override
    public String getWebPlatformsMspServer() {
        return webPlatformsMspServer;
    }

    @Override
    public AuthorizerInfo getAuthorizerInfo(Map request) throws IOException {
        return authService.getAuthorizerInfo(getMerchantWeixinAppId());
    }

    @Override
    public Map getBMerchantWithWeixinConfig(Map request) {
        return bMerchantService.getBMerchant(getSessionMerchantId());
    }

    @Override
    public Map createOrUpdateBMerchantWithWeixinConfig(Map request) {
        request.put("id", getSessionMerchantId());
        return bMerchantService.createOrUpdateBMerchant(request);
    }

    @Override
    public Map updateBMerchantWithWeixinConfig(Map request) {
        request.put("id", getSessionMerchantId());
        return bMerchantService.updateBMerchant(request);
    }

    @Override
    public Map updateBMerchantWeixinTemplates(Map request) {
        Map bMerchant = new HashMap();
        bMerchant.put("id", getSessionMerchantId());
        Map weixinConfig = CollectionUtil.hashMap(WeixinConfig.TEMPLATE_MESSAGE, request);
        bMerchant.put(BMerchant.WEIXIN_CONFIG, weixinConfig);
        return (Map) ((Map) bMerchantService.updateBMerchant(bMerchant).get(BMerchant.WEIXIN_CONFIG)).get(WeixinConfig.TEMPLATE_MESSAGE);
    }

    @Override
    public void unbindBMerchantWeixinConfig(Map request) {
        bMerchantService.unbindBMerchantWeixinConfig(getSessionMerchantId());
    }

    @Override
    public Map getWeixinTemplatesSceneAndRule(Map request) {
        return dictService.getWeixinTemplatesSceneAndRule();
    }

    @Override
    public Map getWeixinIndustry(Map request) {
        return messageService.getIndustry(CollectionUtil.hashMap("appId", getMerchantWeixinAppId()));
    }

    @Override
    public Map getWeixinMessageTemplates(Map request) {
        return messageService.getTemplateList(CollectionUtil.hashMap("appId", getMerchantWeixinAppId()));
    }

    private String getMerchantWeixinAppId() {
        Map bMerchant = bMerchantService.getBMerchant(getSessionMerchantId());
        if (bMerchant == null || bMerchant.get(BMerchant.WEIXIN_CONFIG) == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "未配置微信公众号！");
        }
        Map weixinConfig = (Map) bMerchant.get(BMerchant.WEIXIN_CONFIG);
        String appId = (String) weixinConfig.get(WeixinConfig.APPID);
        if (appId == null || appId.length() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "未配置微信公众号！");
        }
        return appId;
    }

}
