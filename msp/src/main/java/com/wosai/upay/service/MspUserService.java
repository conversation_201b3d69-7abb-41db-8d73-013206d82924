package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.helper.CorePlatformsValidated;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/5/12.
 */

@CorePlatformsValidated
public interface MspUserService {

    /**
     * 用户列表
     * @param request
     *      contain_super_admin 是否查超级管理员，默认是，仅当值是"no"时才不会查出超级管理员
     *      store_id            门店id
     *      account_id          user表里面的id
     *      role                角色
     *      store_auth          门店权限，1：所有门店，2：授权门店
     *      name                名称
     *      status              状态：0：禁用；1:正常
     *      deleted
     *      cellphone           手机号码，该属性从account表中查
     *      store_id            store_id，该属性从merchant_user_store_auth表中查
     *
     * @return ListResult
     */

    ListResult findMerchantUsers(Map request);

    /**
     * 判断account_id是否已经关联过商户用户
     *
     * @param request account_id
     * @return
     */
    boolean alreadyRelatedMerchantUser(Map request);

    /**
     * 创建用户
     * @param request
     * @return
     */
    Map createMerchantUser(Map request);

    /**
     * 根据用户id获取用户
     * @param request
     * @return
     */
    Map getMerchantUser(Map request);

    /**
     * 根据用户id修改用户.
     *
     * @param request
     */
    Map updateMerchantUser(Map request);

    /**
     * 根据用户id禁用用户.
     *
     * @param request
     *          merchant_user_id
     */
    void disableMerchantUser(Map request);

    /**
     * 根据用户id删除用户.
     *
     * @param request
     *          merchant_user_id
     */
    void deleteMerchantUser(Map request);

    /**
     * 保存商户门店权限
     * @param request
     *          merchant_user_id
     *          store_ids
     * @return
     */
    List<Map> saveMerchantUserStoreAuth(Map request);


    /**
     * 获取商户用户门店授权
     * @param request
     *      merchant_user_id    商户用户id
     *      store_id            门店id
     *      deleted
     * @return
     */
    List<Map> getMerchantUserStoreAuths(Map request);

    /**
     * 分页查询商户用户门店授权
     * @param request
     *      merchant_user_id    商户用户id
     *      store_id            门店id
     *      deleted
     * @return
     */
    ListResult findMerchantUserStoreAuths(Map request);


    /**
     * 保存商户部门权限
     * @param request
     *          merchant_user_id
     *          department_ids
     * @return
     */
    List<Map> saveMerchantUserDepartmentAuth(Map request);


    /**
     * 获取商户用户部门授权
     * @param request
     *      merchant_user_id    商户用户id
     *      department_id            部门id
     *      deleted
     * @return
     */
    List<Map> getMerchantUserDepartmentAuths(Map request);

    /**
     * 获取商户用户部门授权的门店信息
     * @param request
     *      merchant_user_id    商户用户id
     *      department_id            部门id
     *      deleted
     * @return
     */
    List<Map> getMerchantUserDepartmentStoreAuths(Map request);

    /**
     * 分页查询商户用户部门授权
     * @param request
     *      merchant_user_id    商户用户id
     *      department_id            部门id
     *      deleted
     * @return
     */
    ListResult findMerchantUserDepartmentAuths(Map request);

    /**
     * 修改密码
     * @param request
     *  password
     *  sms_code
     */
    void updateAccountPassword(Map request);

    /**
     * 修改用户商户后台退款权限.
     * user_id
     * permission_msp_refund
     * @param request
     */
    void updateMerchantUserMspRefundPermission(Map request);


    /**
     * 修改商户管理密码
     *
     * @param request
     * password_old
     * password_new
     * @return
     */
    void updateMerchantManagerPassword(Map request);

    /**
     * 重置商户管理密码
     *
     * @param request
     * sms_code
     * password_new
     * @return
     */
    void resetMerchantManagerPassword(Map request);
}
