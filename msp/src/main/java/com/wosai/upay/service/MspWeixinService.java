package com.wosai.upay.service;

import com.wosai.upay.helper.CorePlatformsValidated;
import com.wosai.weixin.open.bean.AuthorizerInfo;

import java.io.IOException;
import java.util.Map;

/**
 * .
 *
 * <AUTHOR>
 */
@CorePlatformsValidated
public interface MspWeixinService {

    String WEIXIN_OPEN_AUTH_REDIRECT_URL_KEY = "WEIXIN_OPEN_AUTH_REDIRECT_URL";

    String getWeixinOpenUrl();

    String getWebPlatformsMspServer();

    /**
     * 获取授权的微信公众号信息[公众号第三方平台接口].
     *
     * @param request 无参数，仅需登录状态即可
     * @return
     */
    AuthorizerInfo getAuthorizerInfo(Map request) throws IOException;

    /**
     * 获取商户微信配置[会员基础服务接口].
     *
     * @param request 无参数，仅需登录状态即可
     * @return
     */
    Map getBMerchantWithWeixinConfig(Map request);

    /**
     * 创建或更新B端商户及微信配置[会员基础服务接口].
     * 未修改属性不用传.
     *
     * @param request id不需要传，传也会被忽略
     */
    Map createOrUpdateBMerchantWithWeixinConfig(Map request);

    /**
     * 更新B端商户及微信配置[会员基础服务接口].
     * 未修改属性不用传.
     *
     * @param request id不需要传，传也会被忽略
     */
    Map updateBMerchantWithWeixinConfig(Map request);

    /**
     * 更新微信公众号模板配置[会员基础服务接口].
     *
     * @param request 模板配置
     */
    Map updateBMerchantWeixinTemplates(Map request);

    /**
     * 解绑B端商户微信配置[会员基础服务接口].
     *
     * @param request
     */
    void unbindBMerchantWeixinConfig(Map request);

    /**
     * 获取微信模板应用场景及规则[会员基础服务接口].
     *
     * @param request 无参数，仅需登录状态即可
     * @return
     */
    Map getWeixinTemplatesSceneAndRule(Map request);

    /**
     * 获取商户的微信设置行业[微信公公众号服务接口].
     *
     * @param request 无参数，仅需登录状态即可
     * @return
     */
    Map getWeixinIndustry(Map request);

    /**
     * 获取商户的微信模板列表[微信公公众号服务接口].
     *
     * @param request 无参数，仅需登录状态即可
     * @return
     */
    Map getWeixinMessageTemplates(Map request);

}
