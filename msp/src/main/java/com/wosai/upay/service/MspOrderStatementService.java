package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.helper.CorePlatformsValidated;

import java.util.List;
import java.util.Map;

/**
 * Created by kay on 16/9/1.
 */
@CorePlatformsValidated
public interface MspOrderStatementService {


    /**
     * 根据订单号获取交易明细
     *
     * @param request order_sn
     * @return
     */
    List getTransactionListByOrderSn(
            @PropNotEmpty(value = "order_sn", message = "{value} 订单号不能为空")
                    Map request);

    /**
     * 获取导出任务集合
     *
     * @param request
     * @return
     */
    ListResult findTaskApplyLogs(Map request);

    /**
     * 获取单个导出任务
     *
     * @param request
     * @return
     */
    Map getTaskApplyLog(Map request);

    /**
     * 删除一个导出任务
     *
     * @param request
     */
    void deleteTaskApplyLog(Map request);

    /**
     * 订单查询
     *
     * @param request merchant_id
     *                merchant_sn
     *                merchant_name
     *                store_id
     *                store_sn
     *                store_name
     *                payway
     *                sub_payway
     *                status
     *                min_total_amount
     *                max_total_amount
     *                order_sn
     *                client_sn
     *                trade_no
     *                terminal_sn
     * @return
     */
    ListResult findOrders(Map request);

    /**
     * 获取订单详情
     * 增加查看权限 -  只能看到自己商户的订单
     * @param request
     * @return
     */
    Map getOrderDetail(Map request);

    /**
     * 创建订单导出任务
     * @param request
     * @return
     */
    Map createExportOrderTask(Map<String, Object> request);

    /**
     * 创建余额对账单导出任务
     * @param request
     * @return
     */
    Map createExportWalletTask(Map<String, Object> request);

    /**
     * 创建对账单导出任务
     *
     * @param request
     * @return
     */
    Map createExportTransactionTask(Map<String, Object> request);

    /**
     * 更新或写入账单配置
      * @param config
     */
    void insertOrUpdate(Map config);

    /**
     * 查询账单配置
     * @param
     * @return
     */
    Map getStatementConfig();


    /**
     *
     * @param request
     * @return
     */
    Map createExportSharingBookTask(Map request);



}
