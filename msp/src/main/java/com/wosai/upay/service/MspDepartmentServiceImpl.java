package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.department.Department;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.user.api.service.DepartmentService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.HttpRequestUtil;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.wosai.upay.service.MspUserLoginService.ROLE_DEPARTMENT_MANAGER;
import static com.wosai.upay.service.MspUserLoginService.ROLE_SUPER_ADMIN;

/**
 * Created by lihebin on 18/3/8.
 */
@SuppressWarnings("unchecked")
@Service
public class MspDepartmentServiceImpl extends BaseService implements MspDepartmentService {

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private UserService userService;

    @Autowired
    private StoreService storeService;

    @Override
    public Map<String, Object> createDepartment(Map<String, Object> request) {
        request.put("merchant_id", getSessionMerchantId());
        String departmentName = BeanUtil.getPropString(request, Department.NAME);
        if (StringUtil.empty(departmentName)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "name参数为空");
        }
        String role = getSessionUserRole();
        if (!StringUtils.equals(role, ROLE_SUPER_ADMIN)) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "无权限");
        }
        Map queryFilter = CollectionUtil.hashMap(
                "department_name", departmentName
        );
        ListResult departments = departmentService.getDepartmentListByMerchantId(getSessionMerchantId(), null, queryFilter);
        if (departments.getTotal() != 0 || departments.getRecords().size() > 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "部门组织已存在");
        }
        String parentDepartmentSn = BeanUtil.getPropString(request, Department.PARENT_DEPARTMENT_SN);
        if (!StringUtil.empty(parentDepartmentSn)) {
            Map departmentTree = departmentService.getDepartmentByDepartmentSn(parentDepartmentSn);
            int type = BeanUtil.getPropInt(departmentTree, Department.TYPE);
            if (type == 1) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "部门已是最后一层，无法创建新部门");
            }
        }
        Map departmentParam = CollectionUtil.hashMap(Department.MERCHANT_ID, getSessionMerchantId(),
                Department.NAME, departmentName,
                Department.STATUS, 1,
                Department.PARENT_DEPARTMENT_SN, parentDepartmentSn,
                Department.TYPE, 0
        );
        Map department = departmentService.createDepartment(departmentParam);
        if (MapUtils.isEmpty(department)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "创建部门失败");
        }
        departmentService.removeDepartmentCachedParams(getSessionMerchantId());
        return department;
    }

    @Override
    public Map<String, Object> updateDepartment(Map<String, Object> request) {
        String role = getSessionUserRole();
        if (!StringUtils.equals(role, ROLE_SUPER_ADMIN)) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "无权限");
        }
        return departmentService.updateDepartment(request);
    }

    @Override
    public List<Map> createDepartmentStore(Map<String, Object> request) {
        String role = getSessionUserRole();
        if (!StringUtils.equals(role, ROLE_SUPER_ADMIN)) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "无权限");
        }
        String merchantId = getSessionMerchantId();
        String departmentSn = BeanUtil.getPropString(request, Department.DEPARTMENT_SN);
        List<String> storeIds = (List<String>) BeanUtil.getProperty(request, "store_ids");
        if (StringUtil.empty(departmentSn) || storeIds.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "参数为空");
        }
        List<Map<String, Object>> departmentTree = departmentService.listDepartmentTree(merchantId, departmentSn);
        if (!(departmentTree == null || departmentTree.size() == 0)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "部门不能挂载门店");
        }
        List<Map> listResult = new ArrayList<>();
        for (String storeId : storeIds) {
            Map requestParam = CollectionUtil.hashMap(
                    Department.DEPARTMENT_SN, departmentSn,
                    Department.STORE_ID, storeId
            );
            Map departmentStore = departmentService.createDepartmentStore(requestParam);
            Map store = storeService.getStore(storeId);
            departmentStore.put(Store.NAME, BeanUtil.getPropString(store, Store.NAME));
            departmentStore.put(Store.SN, BeanUtil.getPropString(store, Store.SN));
            listResult.add(departmentStore);
        }
        Map treeParam = CollectionUtil.hashMap(
                Department.SN, departmentSn,
                Department.TYPE, 1
        );
        departmentService.updateDepartmentBySn(treeParam);
        departmentService.removeDepartmentCachedParams(getSessionMerchantId());
        return listResult;
    }

    @Override
    public List<Map> getDepartmentStore(Map<String, Object> request) {
        String departmentSn = BeanUtil.getPropString(request, Department.DEPARTMENT_SN);
        return departmentService.getDepartmentStoreByDepartmentSn(departmentSn);
    }

    @Override
    public void deleteDepartmentStore(Map<String, Object> request) {
        String role = getSessionUserRole();
        if (!StringUtils.equals(role, ROLE_SUPER_ADMIN)) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "无权限");
        }
        String storeId = BeanUtil.getPropString(request, "store_id");
        String departmentSn = BeanUtil.getPropString(request, "department_sn");
        if (StringUtil.empty(storeId) || StringUtil.empty(departmentSn)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "参数异常");
        }
        departmentService.deleteDepartmentStoreByStoreId(departmentSn, storeId);
        List<Map> departmentStore = departmentService.getDepartmentStoreByDepartmentSn(departmentSn);
        if (departmentStore == null || departmentStore.size() == 0) {
            Map reuqestParam = CollectionUtil.hashMap(Department.SN, departmentSn,
                    Department.TYPE, 0);
            departmentService.updateDepartmentBySn(reuqestParam);
        }
        departmentService.removeDepartmentCachedParams(getSessionMerchantId());
    }


    @Override
    public ListResult findDepartments(Map<String, Object> request) {
        String merchantId = getSessionMerchantId();
        if (StringUtils.isBlank(merchantId)) {
            throw new UpayException(UpayException.CODE_SESSION_ERROR, "未在Session中取到登录商户的");
        }
        Map queryFilter = CollectionUtil.hashMap(
                Department.DEPARTMENT_SN, BeanUtil.getPropString(request, Department.DEPARTMENT_SN),
                "department_name", BeanUtil.getPropString(request, "department_name")
        );
        String role = getSessionUserRole();
        Object accountId = HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);

        //增加部门限制
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        ListResult departments = departmentService.getDepartmentListByMerchantId(merchantId, pageInfo, queryFilter);
        if (StringUtils.equals(role, ROLE_SUPER_ADMIN)) {
            return departments;
        }
        if (StringUtils.equals(role, ROLE_DEPARTMENT_MANAGER)) {
            return getDepartmentHandle(departments, merchantId);
        }
        throw new UpayException(UpayException.CODE_ACCESS_DENIED, "拒绝访问");
    }

    private ListResult getDepartmentHandle(ListResult departmentsResult, String merchantId) {
        ListResult listResult = new ListResult();
        List<Map> list = new ArrayList<>();
        List<Map> departments = departmentsResult.getRecords();
        Object accountId = HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        Map request = CollectionUtil.hashMap(
                "account_id",accountId
        );
        List<Map> departmentAuths = userService.getMerchantUserDepartmentAuths(request);

        Set departmentSns = new LinkedHashSet();
        for (Map departmentAuth : departmentAuths) {
            List<String> departmentSnList = Arrays.asList(BeanUtil.getPropString(departmentAuth, Department.SN));
            Set listDepartment = departmentService.getDepartmentsByDepartment(merchantId, departmentSnList);
            departmentSns.addAll(listDepartment);
            departmentSns.add(BeanUtil.getPropString(departmentAuth, Department.SN));
        }
        int num = 0;
        for (Map department : departments) {
            if (departmentSns.contains(BeanUtil.getPropString(department, Department.SN))) {
                list.add(department);
                num++;
            }
        }

        listResult.setTotal(num);
        listResult.setRecords(list);
        return listResult;
    }


    @Override
    public List<Map> listDepartmentTreeByParent(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        if (StringUtil.empty(merchantId)) {
            merchantId = getSessionMerchantId();
        }
        if (StringUtils.isBlank(merchantId)) {
            throw new UpayException(UpayException.CODE_SESSION_ERROR, "未在Session中取到登录商户的");
        }
        String role = getSessionUserRole();
        String parentDepartmentSn = BeanUtil.getPropString(request, Department.PARENT_DEPARTMENT_SN);
        List<Map> result = new ArrayList<>();
        //增加部门限制
        if (StringUtils.equals(role, ROLE_SUPER_ADMIN)) {

            List<Map<String, Object>> listDepartmentTree = departmentService.listDepartmentTree(merchantId, parentDepartmentSn);
            return listDepartmentTreePath(listDepartmentTree);
        } else if (StringUtils.equals(role, ROLE_DEPARTMENT_MANAGER)) {
            List<Map> departments = userService.getMerchantUserDepartmentAuths(CollectionUtil.hashMap(
                    "merchant_user_id", getSessionUserId(),
                    "merchant_id", merchantId
            ));
            if (StringUtil.empty(parentDepartmentSn)) {
                for (Map department : departments) {
                    String departmentId = BeanUtil.getPropString(department, Department.DEPARTMENT_ID);
                    result.add(departmentService.getDepartment(departmentId));
                }
                return result;
            }
            List<Map<String, Object>> listDepartmentTree = departmentService.listDepartmentTree(merchantId, parentDepartmentSn);
            return listDepartmentTreePath(listDepartmentTree);
        } else {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "权限不够");
        }
    }

    private List<Map> listDepartmentTreePath(List<Map<String, Object>> listDepartmentTree) {
        List<Map> result = new ArrayList<>();
        for (Map<String, Object> departmentTree : listDepartmentTree) {
            result.add(departmentTree);
        }
        return result;
    }

    @Override
    public void deleteDepartmentTree(Map<String, Object> request) {
        String role = getSessionUserRole();
        if (!StringUtils.equals(role, ROLE_SUPER_ADMIN)) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "无权限");
        }
        String merchantId = getSessionMerchantId();
        if (StringUtils.isBlank(merchantId)) {
            throw new UpayException(UpayException.CODE_SESSION_ERROR, "未在Session中取到登录商户的");
        }
        Map department = departmentService.getDepartmentByDepartmentSn(BeanUtil.getPropString(request, Department.DEPARTMENT_SN));
        if (!merchantId.equals(BeanUtil.getPropString(department, Department.MERCHANT_ID))) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "未在Session中取到删除部门的正确商户");
        }
        String departmentSn = BeanUtil.getPropString(department, Department.SN);
        if (StringUtil.empty(departmentSn)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "departmentSn不能为空");
        }
        deleteDepartmentHandle(departmentSn);
        deleteDepartmentTree(merchantId, departmentSn);
        departmentService.removeDepartmentCachedParams(merchantId);
    }


    private void deleteDepartmentTree(String merchantId, String parentDepartmentSn) {
        List<Map<String, Object>> listDepartment = departmentService.listDepartmentTree(merchantId, parentDepartmentSn);
        if (listDepartment == null || listDepartment.size() == 0) {
            return;
        }
        for (Map<String, Object> departmentMap : listDepartment) {
            if (MapUtils.isEmpty(departmentMap)) {
                continue;
            }
            String departmentSn = BeanUtil.getPropString(departmentMap, Department.SN);
            if (StringUtil.empty(departmentSn)) {
                continue;
            }
            deleteDepartmentHandle(departmentSn);
            deleteDepartmentTree(merchantId, departmentSn);
        }
    }


    private void deleteDepartmentHandle(String departmentSn){
        departmentService.deleteMerchantUserDepartmentAuthsByDepartmentSn(departmentSn);
        departmentService.deleteDepartmentBySn(departmentSn);
        departmentService.deleteDepartmentStoreByDepartmentSn(departmentSn);

    }


}
