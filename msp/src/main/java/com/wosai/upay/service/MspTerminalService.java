package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.helper.CorePlatformsValidated;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/5/11.
 */

/**
 * 接口定义，参数校验规则定义在接口方法上，校验失败的错误提示可以支持i18n，具体做法是在src/main/resources/ValidationMessages.properties资源文件中定义错误提示的键值对，然后在这里引用错误提示键。
 * 本地化的资源文件加后缀，例如ValidationMessages_zh_CN.properties。
 *
 * <AUTHOR>

@CorePlatformsValidated
public interface MspTerminalService {

    /**
     * 终端列表
     * @param request (pageInfo)
     * @query_condition:
     *                  terminal_sn
     *                  terminal_name
     *
     * @return  ListResult
     */

    public ListResult findTerminals(Map<String, Object> request);


    /**
     * 修改终端.
     *
     * @param request id                  UUID
     *                device_fingerprint  设备指纹
     *                name                终端名
     *                type                类型  1: SQB_APP  2: SQB_SDK   3: SQB_POS  10: SATURN
     *                sdk_version
     *                os_version
     *                longitude           经度
     *                latitude            纬度
     *                client_sn           商户外部终端号
     *                extra               扩展字段
     *                target
     *                target_type
     *                vendor_app_id
     *                amount  固定金额
     *                remark 固定金额的备注
     *                version
     * @return
     */

    Map<String, Object> updateTerminal(Map<String, Object> request);



    /**
     * 创建激活码
     *
     * @param request id                  UUID
     *
     * @return Map
     */
    Map createActivationCode(Map<String, Object> request);
    /**
     * 获取可用激活码
     *
     * @param request id                  UUID
     *
     * @return Map
     */
    ListResult getActivationCodes(Map<String, Object> request);
    /**
     * 分页查询Vendor.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                name                名称
     *                sn                  服务商可见的编号
     *                status
     *                contact_phone       联系固定电话号码
     *                contact_cellphone   联系移动电话号码
     *                deleted
     * @return
     */
    ListResult findVendors(Map<String, Object> request);

}
