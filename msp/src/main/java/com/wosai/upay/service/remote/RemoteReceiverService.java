package com.wosai.upay.service.remote;

import com.wosai.upay.common.bean.ListResult;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/11/29.
 */
public interface RemoteReceiverService {
    /**
     * 查询收款方
     * @param conditions
     *  name 模糊查询
     *  id
     *  model_id
     *  merchant_sn
     *  client_sn
     *  status
     * @param pagination 分页信息
     * @return 结果
     */
    ListResult queryReceivers(Map<String,Object> conditions, Map pagination);
}
