package com.wosai.upay.service;


import com.wosai.upay.common.bean.ListResult;

import java.util.Map;

public interface MspProfitShareService {

    ListResult getMerchantAsReceivers(Map map);

    Map<String,Object> getMerchantShareConfig(Map map);

    ListResult queryOutSharingBook(Map map);

    ListResult queryInSharingBook(Map map);

    ListResult querySharingBook(Map map);

    Map getSharingTransaction(Map map);

    /**
     *
     * @param request
     *  export_type 1: 入账导出 2: 出账导出
     * @return
     */
    Map createExportSharingBookTask(Map request);
}
