package com.wosai.upay.service;

import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.service.enumeration.AccessTokenType;
import com.wosai.upay.bean.PayWayResp;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.constant.CacheConstant;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.merchant.audit.api.model.Store;
import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.model.UpdateTransactionReq;
import com.wosai.upay.transaction.service.CacheService;
import com.wosai.upay.transaction.service.ExportService;
import com.wosai.upay.transaction.service.TaskLogService;
import com.wosai.upay.transaction.service.TransactionServiceV2;
import com.wosai.upay.user.api.service.DepartmentService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.*;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import static com.wosai.upay.service.MspUserLoginService.ROLE_SUPER_ADMIN;


@Service
public class MspTransactionServiceImpl extends BaseService implements MspTransactionService {
    private static final Logger logger = LoggerFactory.getLogger(MspTransactionServiceImpl.class);

    /**
     * 导出对账单限制最大任务数的时间范围
     */
    public static final long EXPORT_RUNNING_LIMIT_TIME = 7 * 24 * 60 * 60 * 1000l;

    public static final String DAY_SDF_PATTERN_YYYYMMDD = "yyyyMMdd";

    @Autowired
    private TransactionServiceV2 transactionServiceV2;
    @Autowired
    private UserService userService;
    @Autowired
    private TranslateService translateService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    private ExportService exportService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private com.wosai.service.IMerchantGrayService grayService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private BusinessLogService businessLogService;
    @Autowired
    private IPayWayService iPayWayService;


    @Override
    public ListResult findTransactions(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        if (pageInfo.getDateStart() == null) {
            pageInfo.setDateStart(new Date().getTime() - EXPORT_RUNNING_LIMIT_TIME);
        }
        Map queryParams = getQueryOrderParams(request);
        Map sessionInfo = getSessionInfos(BeanUtil.getProperty(request, "httpRequest"));
        String merchantSn = BeanUtil.getPropString(request, "merchant_sn", "");
        if (!StringUtil.empty(merchantSn)) {
            @SuppressWarnings("unchecked")
            List<String> queryMerchantSns = (List) BeanUtil.getProperty(sessionInfo, "merchant_sns");
            if (queryMerchantSns.contains(merchantSn)) {
                Map merchant = merchantService.getMerchantBySn(merchantSn);
                queryParams.put("merchant_ids", Collections.singletonList(BeanUtil.getPropString(merchant, DaoConstants.ID)));
            } else {
                return new ListResult(0, new ArrayList<Map>());
            }
        }
        String merchantName = BeanUtil.getPropString(request, "merchant_name", "");
        if (!StringUtil.empty(merchantName)) {
            @SuppressWarnings("unchecked")
            List<String> queryMerchantIds = (List) BeanUtil.getProperty(queryParams, "merchant_ids");
            if (queryMerchantIds == null || queryMerchantIds.size() == 0) {
                return new ListResult(0, new ArrayList<Map>());
            }
            ListResult listResult = merchantService.findMerchants(new PageInfo(1, 5000), CollectionUtil.hashMap("merchant_ids", queryMerchantIds));
            List<Map> merchants = listResult.getRecords();
            if (listResult.getTotal() == 0) return new ListResult(0, new ArrayList<Map>());
            queryMerchantIds = new ArrayList<>();
            for (Map merchant : merchants) {
                if (BeanUtil.getPropString(merchant, Merchant.NAME, "").contains(merchantName)) {
                    queryMerchantIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
                }
            }
            if (queryMerchantIds.size() == 0) return new ListResult(0, new ArrayList<Map>());
            queryParams.put("merchant_ids", queryMerchantIds);
        }
        String storeSn = BeanUtil.getPropString(request, "store_sn", "");
        if (!StringUtil.empty(storeSn)) {
            List<String> storeSns = (List) BeanUtil.getProperty(sessionInfo, "store_sns");
            if (storeSns.contains(storeSn)) {
                Map<String, Object> storeMap = storeService.getStoreByStoreSn(storeSn);
                queryParams.put("store_ids", BeanUtil.getPropString(storeMap, DaoConstants.ID));
            } else {
                return new ListResult(0, new ArrayList<Map>());
            }
        }
        String storeName = BeanUtil.getPropString(request, "store_name", "");
        if (!StringUtil.empty(storeName)) {
            List<String> queryStoreIds = (List) BeanUtil.getProperty(queryParams, "store_ids");
            if (queryStoreIds == null) queryStoreIds = (List) BeanUtil.getProperty(sessionInfo, "store_ids");
            if (queryStoreIds == null || queryStoreIds.size() == 0) {
                return new ListResult(0, new ArrayList<Map>());
            }
            ListResult listResult = storeService.findStores(new PageInfo(1, 5000), CollectionUtil.hashMap("store_ids", queryStoreIds, "store_name", storeName));
            if (listResult.getTotal() == 0) return new ListResult(0, new ArrayList<Map>());
            List<Map> storeInfo = listResult.getRecords();
            queryStoreIds = new ArrayList<>();
            for (Map store : storeInfo) {
                queryStoreIds.add(BeanUtil.getPropString(store, DaoConstants.ID));
            }
            if (queryStoreIds.size() == 0) return new ListResult(0, new ArrayList<Map>());
            queryParams.put("store_ids", queryStoreIds);
        }
        ListResult result = transactionServiceV2.getTransactionList(pageInfo, queryParams);
        List<Map> list = result.getRecords();
        if (list != null) {
            for (Map<String, Object> transaction : list) {
                String merchantId = transaction.get("merchant_id") + "";
                CommonUtil.checkJurisdiction(merchantId);
                //  添加payWay对应的payName
                logger.info("----findTransactions,添加payWayName-----");
                Object payway = transaction.get("payway");
                String payWayName = null;
                if (payway != null){
                    payWayName = getPayWayNameKeyByPayWay(Integer.parseInt(payway.toString()));
                }
                if (payWayName != null){
                    transaction.put("pay_way_name", payWayName);
                }
            }
        }
        translateService.translateOrderOperatorNames(result.getRecords(), getSessionMerchantId());
        return result;
    }

    private String getPayWayNameKeyByPayWay(Integer payWay) {
        String payWayNameKey = "PAY_WAY_DNQR_1000";
        if (payWay == null){
            return payWayNameKey;
        }
        List<PayWayResp> payWayRespList = iPayWayService.getPayWay();
        for (PayWayResp resp : payWayRespList){
            if (resp.getPayWay() != null && resp.getPayWay().equals(payWay)){
                return resp.getI18nKey();
            }
        }
        return payWayNameKey;
    }


    @Override
    public List getTransactionListByOrderSn(Map request) {
        List<Map<String, Object>> res = transactionServiceV2.getTransactionListByOrderSn(BeanUtil.getPropString(request, "order_sn"));
        if (res != null) {
            for (Map<String, Object> transaction : res) {
                String merchantId = transaction.get("merchant_id") + "";
                CommonUtil.checkJurisdiction(merchantId);
                //  添加payWay对应的payName
                logger.info("----getTransactionListByOrderSn,添加payWayName-----");
                Object payway = transaction.get("payway");
                String payWayName = null;
                if (payway != null){
                    payWayName = getPayWayNameKeyByPayWay(Integer.parseInt(payway.toString()));
                }
                if (payWayName != null){
                    transaction.put("pay_way_name", payWayName);
                }
            }
        }
        return res;
    }


    @Override
    public Map createExportTransactionQueryTask(Map<String, Object> request) {
        String user_id = getSessionUserId();
        Map sessionInfo = getSessionInfos(BeanUtil.getProperty(request, "httpRequest"));

        //检查是否允许导出
        allowExport(user_id, "transaction_query");

        //参数检查
        long date_start = BeanUtil.getPropLong(request, "date_start");
        long date_end = BeanUtil.getPropLong(request, "date_end");
        if (date_start <= 0 || date_end <= 0 || date_start > date_end) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "流水明细导出时间有误");
        }

        String timeZoneStr = BeanUtil.getPropString(request, "timeZone");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DAY_SDF_PATTERN_YYYYMMDD);
        if (!StringUtils.isBlank(timeZoneStr)) {
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone(timeZoneStr));
        }

        int upayQueryType = BeanUtil.getPropInt(request, "upayQueryType");
        String name = (upayQueryType == 1) ? "银行卡流水明细" : "流水明细";
        String exportName = String.format("%s%s%s_%s", BeanUtil.getPropString(sessionInfo, "merchant_name"), name, simpleDateFormat.format(date_start), simpleDateFormat.format(date_end));

        Map queryParams = getQueryOrderParams(request);
        String merchantSn = BeanUtil.getPropString(request, "merchant_sn", "");
        if (!StringUtil.empty(merchantSn)) {
            @SuppressWarnings("unchecked")
            List<String> queryMerchantSns = (List) BeanUtil.getProperty(sessionInfo, "merchant_sns");
            if (queryMerchantSns.contains(merchantSn)) {
                Map merchant = merchantService.getMerchantBySn(merchantSn);
                queryParams.put("merchant_ids", Collections.singletonList(BeanUtil.getPropString(merchant, DaoConstants.ID)));
            } else {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "没有匹配的商户号");
            }
        }
        String merchantName = BeanUtil.getPropString(request, "merchant_name", "");
        if (!StringUtil.empty(merchantName)) {
            @SuppressWarnings("unchecked")
            List<String> queryMerchantIds = (List) BeanUtil.getProperty(queryParams, "merchant_ids");
            if (queryMerchantIds == null || queryMerchantIds.size() == 0) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "没有匹配的商户名称");
            }
            ListResult listResult = merchantService.findMerchants(new PageInfo(1, 5000), CollectionUtil.hashMap("merchant_ids", queryMerchantIds));
            List<Map> merchants = listResult.getRecords();
            if (listResult.getTotal() == 0) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "没有匹配的商户名称");
            }
            queryMerchantIds = new ArrayList<>();
            for (Map merchant : merchants) {
                if (BeanUtil.getPropString(merchant, Merchant.NAME, "").contains(merchantName)) {
                    queryMerchantIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
                }
            }
            if (queryMerchantIds.size() == 0) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "没有匹配的商户名称");
            }
            queryParams.put("merchant_ids", queryMerchantIds);
        }
        String storeSn = BeanUtil.getPropString(request, "store_sn", "");
        if (!StringUtil.empty(storeSn)) {
            List<String> storeSns = (List) BeanUtil.getProperty(sessionInfo, "store_sns");
            if (storeSns.contains(storeSn)) {
                Map<String, Object> storeMap = storeService.getStoreByStoreSn(storeSn);
                queryParams.put("store_ids", BeanUtil.getPropString(storeMap, DaoConstants.ID));
            } else {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "没有匹配的门店号");
            }
        }
        String storeName = BeanUtil.getPropString(request, "store_name", "");
        if (!StringUtil.empty(storeName)) {
            List<String> queryStoreIds = (List) BeanUtil.getProperty(queryParams, "store_ids");
            if (queryStoreIds == null) queryStoreIds = (List) BeanUtil.getProperty(sessionInfo, "store_ids");
            if (queryStoreIds == null || queryStoreIds.size() == 0) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "没有匹配的门店名称");
            }
            ListResult listResult = storeService.findStores(new PageInfo(1, 5000), CollectionUtil.hashMap("store_ids", queryStoreIds, Store.NAME, storeName));
            if (listResult.getTotal() == 0) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "没有匹配的门店名称");
            }
            List<Map> storeInfo = listResult.getRecords();
            queryStoreIds = new ArrayList<>();
            for (Map store : storeInfo) {
                queryStoreIds.add(BeanUtil.getPropString(store, DaoConstants.ID));
            }
            if (queryStoreIds.size() == 0) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "没有匹配的门店名称");
            }
            queryParams.put("store_ids", queryStoreIds);
        }
        return exportService.createExportStatementTask(CollectionUtil.hashMap(
                StatementTaskLog.TYPE, StatementTaskLog.TYPE_TRANSACTION_QUERY,
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_MSP,
                StatementTaskLog.TITLE, exportName,
                StatementTaskLog.USER_ID, user_id
        ), getQueryOrderParams(request));
    }

    private Map getQueryOrderParams(Map<String, Object> request) {
        Map sessionInfo = getSessionInfos(BeanUtil.getProperty(request, "httpRequest"));
        Map params = CollectionUtil.hashMap(
                "provider", BeanUtil.getPropString(request, "provider"),
                "trade_no", BeanUtil.getPropString(request, "trade_no"),
                "client_sn", BeanUtil.getPropString(request, "client_sn"),
                "merchant_id", BeanUtil.getPropString(request, "merchant_id"),
                "store_id", BeanUtil.getPropString(request, "store_id"),
                "transaction_sn", BeanUtil.getPropString(request, "transaction_sn"),
                "terminal_sn", BeanUtil.getPropString(request, "terminal_sn"),
                "payway", BeanUtil.getPropString(request, "payway"),
                "sub_payway", BeanUtil.getPropString(request, "sub_payway"),
                "status", BeanUtil.getPropString(request, "status"),
                "min_total_amount", BeanUtil.getPropString(request, "min_total_amount"),
                "max_total_amount", BeanUtil.getPropString(request, "max_total_amount"),
                "store_id", BeanUtil.getPropString(request, ConstantUtil.KEY_STORE_ID),
                "device_fingerprint", BeanUtil.getPropString(request, Terminal.DEVICE_FINGERPRINT),
                "type", BeanUtil.getPropString(request, Terminal.TYPE),
                "date_start", BeanUtil.getPropLong(request, "date_start"),
                "date_end", BeanUtil.getPropLong(request, "date_end"),
                "last_record_ctime", BeanUtil.getPropLong(request, "last_record_ctime"),//上次返回的按时间倒叙排的最后一条记录的ctime
                "merchant_ids", (List) BeanUtil.getProperty(sessionInfo, "merchant_ids"),
                "department_ids", (List) BeanUtil.getProperty(sessionInfo, "department_ids"),
                "merchant_user_id", BeanUtil.getPropString(sessionInfo, "merchant_user_id"),
                "timeZone", BeanUtil.getPropString(request, "timeZone"),
                "upayQueryType", BeanUtil.getPropInt(request, "upayQueryType")
        );

        String type = BeanUtil.getPropString(sessionInfo, "type");
        List<String> departmentIds = (List<String>) BeanUtil.getProperty(sessionInfo, "department_ids");
        boolean isSuperAdmin = BeanUtil.getPropBoolean(sessionInfo, "isSuperAdmin");
        if (type.equals("merchant") && !isSuperAdmin) {
            if (departmentIds != null && departmentIds.size() > 0) {
                Set storeIds = departmentService.listStoresByDepartmentId(getSessionMerchantId(), departmentIds);
                params.put("store_ids", new ArrayList<>(storeIds));
            } else {
                params.put("store_ids", (List) BeanUtil.getProperty(sessionInfo, "store_ids"));
            }
        }
        //支付源订单号
        String channel_trade_no = BeanUtil.getPropString(request, "channel_trade_no");
        if (!StringUtil.empty(channel_trade_no) && StringUtil.empty(BeanUtil.getPropString(params, "transaction_sn"))) {
            String orderSn = OrderUtil.getOrderSnByChannelTradeNo(channel_trade_no);
            if (StringUtil.empty(orderSn)) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "源交易订单号没有在系统中找到！");
            }
            params.put("transaction_sn", orderSn);
        }

        //收款通道订单号
        String trade_no = BeanUtil.getPropString(request, "trade_no");
        if (BeanUtil.getPropInt(request, "upayQueryType") != 1 && !StringUtil.empty(trade_no) && StringUtil.empty(BeanUtil.getPropString(params, "transaction_sn"))) {
            String orderSn = OrderUtil.getOrderSnByTradeNo(trade_no);
            if (StringUtil.empty(orderSn)) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "收款通道订单号没有在系统中找到！");
            }
            params.put("transaction_sn", orderSn);
        }
        return params;
    }

    /**
     * 检查是否允许导出
     */
    private void allowExport(String user_id, String type) {
        //检查当前时间 系统是否允许导出对账单
        Map closeExportInfo = cacheService.getCacheMap(CacheConstant.KEY_PRE_DISABLE_EXPORT, type);//KEY_PRE_DISABLE_EXPORT_ 由于最新api包去掉该常量,故只写在本项目
        List<Map> disable_times = (List) BeanUtil.getProperty(closeExportInfo, "disable_times");
        if (disable_times != null && disable_times.size() > 0) {
            String disable_reason = BeanUtil.getPropString(closeExportInfo, "disable_reason");
            long now = new Date().getTime();
            long currentTimeToBegin = now - DateTimeUtil.getOneDayStart(now);
            for (Map disable_time : disable_times) {
                if (currentTimeToBegin >= BeanUtil.getPropLong(disable_time, "begin") && currentTimeToBegin <= BeanUtil.getPropLong(disable_time, "end")) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, disable_reason);
                }
            }
        }

        //检查用户七日内正在执行的任务数是否小于2
        long now = new Date().getTime();
        long sevenDaysAgo = now - MspOrderStatementServiceImpl.EXPORT_RUNNING_LIMIT_TIME;
        ListResult taskResult = taskLogService.findTaskApplyLogs(new PageInfo(1, 10, sevenDaysAgo, now), CollectionUtil.hashMap(
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_MSP,
                StatementTaskLog.USER_ID, user_id,
                StatementTaskLog.APPLY_STATUSES, StatementTaskLog.APPLY_STATUS_RUNNING
        ));
        if (taskResult != null && taskResult.getTotal() >= MspOrderStatementServiceImpl.EXPORT_RUNNING_LIMIT_COUNT) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("当前已有%s个对账单任务在生成中,请等待任务完成再来操作", MspOrderStatementServiceImpl.EXPORT_RUNNING_LIMIT_COUNT));
        }
    }

    @Override
    public boolean isGrayscaleExist() {
        return grayService.isExist(CollectionUtil.hashMap("type", 0, "merchant_id", getSessionMerchantId()));
    }

    @Override
    public Map getSetting(Map map) {
        int type = BeanUtil.getPropInt(map, "type");
        return grayService.query(CollectionUtil.hashMap("type", type, "merchant_id", getObjectId()));
    }

    @Override
    public void updateSetting(Map map) {
        String role = getSessionUserRole();
        if (BeanUtil.getPropInt(map, "type") == AccessTokenType.OFFSET_HOUR.getCode()
                && !StringUtils.equals(role, ROLE_SUPER_ADMIN)) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "无权限");
        }
        map.put("merchant_id", getObjectId());
        grayService.update(map);

        if (BeanUtil.getPropInt(map, "type") == AccessTokenType.OFFSET_HOUR.getCode()) {
            int oldValue = BeanUtil.getPropInt(getSetting(map), "value");
            //保存业务日志
            int value = BeanUtil.getPropInt(map, "value");
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, CollectionUtil.hashMap(
                            CommonConstant.MERCHANT_ID, map.get(CommonConstant.MERCHANT_ID),
                            CommonConstant.STATUS, getOfferSetDesc(oldValue)
                    ),
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, CollectionUtil.hashMap(
                            CommonConstant.MERCHANT_ID, map.get(CommonConstant.MERCHANT_ID),
                            CommonConstant.STATUS, getOfferSetDesc(value)
                    ),
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, CommonConstant.MERCHANT,
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BizOpLog.BUSINESS_FUNCTION_CODE, "1000172",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, CommonConstant.MERCHANT_GRAY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, CommonConstant.MERCHANT_ID,
                            BizOpLog.REMARK, "自定义对账结算周期"
                    )
            ));
        }

    }

    @Override
    public Map updateTansactionInfo(Map request) {
            try {
                UpdateTransactionReq updateTransactionReq = new UpdateTransactionReq();
                String tsn = BeanUtil.getPropString(request, "tsn");
                String reflect = BeanUtil.getPropString(request, "reflect");
                if (StringUtil.empty(tsn) || StringUtil.empty(reflect)) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "流水或者备注不能为空");
                }
                updateTransactionReq.setTsn(tsn);
                updateTransactionReq.setReflect(reflect);
                Map<String, Object> result = transactionServiceV2
                        .updateTransactionInfo(updateTransactionReq);
                if (String.valueOf(result.get("code")).equals("200")) {
                    return  CollectionUtil.hashMap(
                            "code", 50000,
                            "msg", "处理成功");
                } else {
                    return  CollectionUtil.hashMap(
                            "code", UpayException.CODE_UNKNOWN_ERROR,
                            "msg", result.get("msg"));
                }
            } catch (Exception e) {
                throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "更新备注失败");
            }
    }

    private String getOfferSetDesc(int value) {
        return ((value < 10) ? "0" : "") + value + ":00:00——" + ((value == 0) ? "当日" : "次日")
                + (((23 + value) % 24) < 10 ? "0" : "") + ((23 + value) % 24) + ":59:59";
    }
}
