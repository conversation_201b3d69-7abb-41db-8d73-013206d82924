package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.HttpClientUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Map;

/**
 * Created by ericyang on 2018/3/22.
 */
@Service
public class H5PaymentServiceImpl extends BaseService implements H5PaymentService {
    @Autowired
    private UserService userService;

    @Value("${h5payment.proxy.url}")
    private String visualProxyUrl;

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/proxy")
    @ResponseBody
    public void proxy(Map<String, Object> request, HttpServletResponse response){
        String code = UpayException.CODE_SUCCESS + "";
        String message = "SUCCESS";
        Object data = null;
        try {
            //补全商户信息
            addProxyParams(request);
            String proxyUlr = BeanUtil.getPropString(request, "proxy_url");
            if (StringUtil.empty(proxyUlr)){
                proxyUlr = visualProxyUrl;
            }
            if (request != null && request.containsKey("proxy_url")){
                request.remove("proxy_url");
            }

            //执行转发
            String rs = processRequestJsonRPC(proxyUlr, JacksonUtil.toJsonString(request));
            Map result = JacksonUtil.toBean(rs, Map.class);
            // jsonrpc
            if (result.containsKey("jsonrpc")) {
                if (result.containsKey("result")) { // 正常返回结果
                    data = result.get("result");
                } else if (result.containsKey("error")) {
                    code = BeanUtil.getPropString(result.get("error"), "code");
                    message = BeanUtil.getPropString(result.get("error"), "message");
                    if (code == null) {
                        code = UpayException.CODE_UNKNOWN_ERROR + "";
                    }
                    data = BeanUtil.getProperty(result.get("error"), "data");
                }
            } else if (result.containsKey("code")) { // 结构 {code:, message:, data,}
                if (result.containsKey("data")) { // 正常返回结果
                    data = result.get("data");
                }
                code = BeanUtil.getPropString(result, "code");
                message = BeanUtil.getPropString(result, "message");
                if (code == null) {
                    code = UpayException.CODE_UNKNOWN_ERROR + "";
                }
            }
        } catch (IOException e) {
            code = UpayException.CODE_IO_EXCEPTION + "";
            message = e.getMessage();
        } catch (Exception e) {
            code = UpayException.CODE_UNKNOWN_ERROR + "";
            message = e.getMessage();
        }
        try {
            buildResult(code, message, data, response);
        } catch (Exception e) {

        }
    }

    private String processRequestJsonRPC(String url, String methodAndParams) throws IOException {
        return HttpClientUtil.getPostResponseString(url, methodAndParams);
    }

    private void addProxyParams(Map request){
        if (request == null) {
            request = CollectionUtil.hashMap();
        }
        //从session中拿取proxy参数
        HttpSession session = HttpRequestUtil.getSession();
        Object session_proxy_params = session.getAttribute("H5_PAYMENT_PROXY_PARAMS");
        //session中不存在重新获取 并 保存session
        if (session_proxy_params == null) {
            String merchant_id = "";

            Map session_user = (Map) HttpRequestUtil.getSession().getAttribute(GroupUserLoginService.SESSION_USER);
            String user_id = BeanUtil.getPropString(session_user, "user_id", "");
            String group_id = BeanUtil.getPropString(session_user, "group_id", "");
            //普通商户
            if (StringUtil.empty(group_id)) {
                merchant_id = BeanUtil.getPropString(session_user, ConstantUtil.KEY_MERCHANT_ID);
            }
//            Map sessionInfo = getSessionInfos(null);
//            String merchant_id = BeanUtil.getPropString(sessionInfo , "object_id");
            //集团商户
//            else {
//                List<Map> group_merchants = groupService.getGroupUserMerchantAuths(CollectionUtil.hashMap(
//                        GroupUserMerchantAuth.GROUP_ID, group_id,
//                        GroupUserMerchantAuth.GROUP_USER_ID, user_id
//                ));
//                for (Map group_merchant : group_merchants) {
//                    merchant_ids.add(BeanUtil.getPropString(group_merchant, ConstantUtil.KEY_MERCHANT_ID));
//                    merchant_sns.add(BeanUtil.getPropString(group_merchant, ConstantUtil.KEY_MERCHANT_SN));
//                }
//            }
            //门店信息
//            Map user = userService.getMerchantUser(user_id);
//            boolean isStoreAuth = !BeanUtil.getPropString(user, "role", "").equals(MspUserLoginService.ROLE_SUPER_ADMIN)  && BeanUtil.getPropInt(user, "store_auth") == 2;
            //集团或非授权商户显示商户下所有门店
//            if (!StringUtil.empty(group_id) || !isStoreAuth){
//                for (String merchant_id : merchant_ids) {
//                    ListResult stores = storeService.getStoreListByMerchantId(merchant_id, new PageInfo(1, 1000), null);
//                    int page = 1;
//                    while (stores != null && stores.getRecords() != null && stores.getRecords().size() > 0){
//                        for (Map store : stores.getRecords()) {
//                            store_ids.add(BeanUtil.getPropString(store, DaoConstants.ID));
//                            store_sns.add(BeanUtil.getPropString(store, Store.SN));
//                        }
//                        page ++;
//                        stores = storeService.getStoreListByMerchantId(merchant_id, new PageInfo(page, 1000), null);
//                    }
//                }
//            }

            session_proxy_params = CollectionUtil.hashMap(
                    "user_id", user_id,
                    "merchant_id", merchant_id
            );
            session.setAttribute("H5_PAYMENT_PROXY_PARAMS", session_proxy_params);
        }
        Map visual_proxy_params = (Map) session_proxy_params;

        Map params = (Map)BeanUtil.getProperty(request, "params");
        params.put("user_id", BeanUtil.getProperty(visual_proxy_params, "user_id"));
        params.put("merchant_id", BeanUtil.getProperty(visual_proxy_params, "merchant_id"));
    }

    private void buildResult(String code, String message, Object data, HttpServletResponse resp) throws IOException {
        resp.setContentType("application/json");
        resp.setCharacterEncoding("UTF-8");
        String result;
        result = "{\"code\": \"" + code + "\"," +
                "\"message\": \"" + message + "\"," +
                "\"data\": " + JacksonUtil.toJsonString(data) + "}";
        resp.getWriter().print(result);
    }
}
