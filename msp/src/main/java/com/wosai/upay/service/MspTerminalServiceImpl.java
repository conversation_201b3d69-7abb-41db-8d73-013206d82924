package com.wosai.upay.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.VendorService;
import com.wosai.upay.helper.TerminalHelper;
import com.wosai.upay.qrcode.bean.Qrcode;
import com.wosai.upay.qrcode.bean.Tag;
import com.wosai.upay.qrcode.service.QrcodeService;
import com.wosai.upay.util.CommonUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by lijunjie on 16/5/11.
 */

@SuppressWarnings("unchecked")
@Service
public class MspTerminalServiceImpl extends BaseService implements MspTerminalService {

    @Autowired
    private VendorService vendorService;
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private StoreService storeService;

    @Autowired
    private QrcodeService qrcodeService;

    private ObjectMapper objectMapper = new ObjectMapper();


//    public MspTerminalServiceImpl(String upay_qrcode_url){
//        JsonRpcHttpClient client = null;
//        try {
//            client = new JsonRpcHttpClient(new URL(upay_qrcode_url + "rpc/qrcode"));
//        } catch (MalformedURLException e) {
//            throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
//        }
//        this.qrcodeService = ProxyUtil.createClientProxy(
//                getClass().getClassLoader(),
//                QrcodeService.class,
//                client);
//    }

    @Override
    public ListResult findTerminals(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        String merchantId = getSessionMerchantId();
        String storeId = BeanUtil.getPropString(request, "store_id");

        Map queryFilter = CollectionUtil.hashMap(
                "sn", BeanUtil.getPropString(request, "terminal_sn"),
                "name", BeanUtil.getPropString(request, "terminal_name"),
                "client_sn", BeanUtil.getPropString(request, "client_sn"),
                "device_fingerprint", BeanUtil.getPropString(request, "device_fingerprint"),
                "showActivationCode", BeanUtil.getPropString(request, "showActivationCode")
        );

        ListResult data = terminalService.getTerminals(merchantId, storeId, pageInfo, queryFilter);
        if (data.getTotal() > 0) {
            List<Map> records = data.getRecords();
            ListResult stores = storeService.findStores(new PageInfo(1, pageInfo.getPageSize()), CollectionUtil.hashMap(
                    "store_ids", CommonUtil.getValues(records, Terminal.STORE_ID)
            ));
            Map<String, Map> storeMap = CommonUtil.convert(stores.getRecords(), ConstantUtil.KEY_ID);
            for (Map record : records) {
                String tempId = MapUtils.getString(record, Terminal.STORE_ID);
                Map store = MapUtils.getMap(storeMap, tempId);
                record.put("store_client_sn", MapUtils.getString(store, Store.CLIENT_SN));
                // beez 版本不需要
//                if (TerminalHelper.isQRCodeType(BeanUtil.getPropInt(record, Terminal.TYPE))) {
//                    String qrcode = BeanUtil.getPropString(record, Terminal.DEVICE_FINGERPRINT);
//                    String qrcodeId = qrcodeService.getQrcodeIdByQrcode(qrcode);
//                    Map qrcodeTag = qrcodeService.getQrcodeTag(qrcodeId, "PRESET_AMOUNT");
//                    if (!MapUtils.isEmpty(qrcodeTag)) {
//                        try {
//                            Map tagValue = objectMapper.readValue(BeanUtil.getPropString(qrcodeTag, "value"), Map.class);
//                            record.put("PRESET_AMOUNT", BeanUtil.getPropLong(tagValue, "amount"));
//                            record.put("remark", BeanUtil.getPropString(tagValue, "remark"));
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                        }
//                    }
//                }
            }
        }

        return data;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> updateTerminal(Map<String, Object> request) {
        String name = BeanUtil.getPropString(request, ConstantUtil.KEY_NAME);
        Map terminal = terminalService.getTerminal(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
        if (TerminalHelper.isQRCodeType(BeanUtil.getPropInt(terminal, Terminal.TYPE))) {
            String qrcode = BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT);
            if (!StringUtil.empty(name)) {
                qrcodeService.updateQrcodeNameByQrcode(qrcode, name);
            }
            String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
            long presetAmount = BeanUtil.getPropLong(request, "amount", -1);
            String remark = BeanUtil.getPropString(request, "remark");
            Map requestTag;
            if (presetAmount == -1 && StringUtil.empty(remark)) {
                requestTag = CollectionUtil.hashMap(Qrcode.QRCODE_STORE_ID, storeId, Qrcode.QR_CODE, qrcode);
            } else {
                requestTag = CollectionUtil.hashMap(Qrcode.QRCODE_STORE_ID, storeId, Qrcode.QR_CODE, qrcode, Tag.TAG_VALUE_AMOUNT, presetAmount, Tag.TAG_VALUE_REMARK, remark);
            }
            qrcodeService.updateQrcodeTagPreSetAmount(requestTag);
        }
        request.remove("amount");
        request.remove("remark");
        return terminalService.updateTerminal(request);
    }

    @Override
    public Map createActivationCode(Map<String, Object> request) {
        String solicitor_sn = BeanUtil.getPropString(request, "solicitor_sn");
        String code = BeanUtil.getPropString(request, "code");
        String storeSn = BeanUtil.getPropString(request, "store_sn");
        String terminal_name = BeanUtil.getPropString(request, "terminal_name");
        long limit = BeanUtil.getPropLong(request, "limit");
        return terminalService.createActivationCodeV2(solicitor_sn, code, storeSn, terminal_name, limit);
    }

    @Override
    public ListResult getActivationCodes(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        String store_id = BeanUtil.getPropString(request, "store_id");
        String merchant_id = BeanUtil.getPropString(request, "merchant_id");//getSessionMerchantId() ;
        CommonUtil.checkJurisdiction(merchant_id);
        ListResult terminalActivationCoderes = terminalService.getActivationCodes(merchant_id, store_id, pageInfo, request);
        List<Map> resMap = terminalActivationCoderes.getRecords();
        List<Map> resRecords = new ArrayList<>();
        for (Map terminalActivationCodeMap : resMap) {
            Map map = new HashMap();
            map.put("code", terminalActivationCodeMap.get("code"));
            map.put("default_terminal_name", terminalActivationCodeMap.get("default_terminal_name"));
            map.put("remaining", terminalActivationCodeMap.get("remaining"));
            map.put("usage_limits", terminalActivationCodeMap.get("usage_limits"));
            map.put("expire_time", terminalActivationCodeMap.get("expire_time"));
            resRecords.add(map);
        }
        ListResult res = new ListResult();
        res.setTotal(terminalActivationCoderes.getTotal());
        res.setRecords(resRecords);
        return res;
    }

    @Override
    public ListResult findVendors(Map<String, Object> request) {
        return vendorService.findVendors(PageInfoUtil.extractPageInfo(request), request);
    }


}
