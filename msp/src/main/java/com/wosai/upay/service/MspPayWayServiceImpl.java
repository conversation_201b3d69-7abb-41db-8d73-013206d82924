package com.wosai.upay.service;

import com.wosai.ka.core.dto.response.GetPayWayListResp;
import com.wosai.ka.core.dto.response.JsonResult;
import com.wosai.ka.core.dto.response.PayWayRes;
import com.wosai.ka.core.enums.ResultCode;
import com.wosai.ka.core.service.PayWayService;
import com.wosai.upay.bean.PayWayResp;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> on 2022-07-07
 */
@Service
public class MspPayWayServiceImpl implements MspPayWayService{

    public static final Logger logger = LoggerFactory.getLogger(MspPayWayServiceImpl.class);

    @Autowired
    private PayWayService payWayService;

    @Override
    @Cacheable(value = "getPayWay")
    public List<PayWayResp> getPayWay() {
        return getPayWayListResult();
    }

    private List<PayWayResp> getPayWayListResult() {
        JsonResult<GetPayWayListResp> rpcResult = payWayService.getPayWayList();
        List<PayWayResp> result = new ArrayList<>();
        if(!ResultCode.SUCCESS.getCode().equals(rpcResult.getCode())){
            //查询pay way失败返回null
            return result;
        }
        GetPayWayListResp data = rpcResult.getData();
        if (data == null || CollectionUtils.isEmpty(data.getList())){
            return result;
        }
        List<PayWayRes> list = data.getList();
        for (PayWayRes payWayRes : list){
            PayWayResp resp = buildPayWayResp(payWayRes);
            result.add(resp);
        }
        return result;

    }

    private PayWayResp buildPayWayResp(PayWayRes payWayRes) {

        PayWayResp resp = new PayWayResp();
        resp.setPayWay(payWayRes.getPayWay());
        resp.setPayWayName(payWayRes.getPayWayName());
        resp.setPayWayDesc(payWayRes.getPayWayDesc());
        resp.setLogo(payWayRes.getLogo());
        resp.setGreyLogo(payWayRes.getGreyLogo());
        resp.setBicCode(payWayRes.getBicCode());
        return resp;

    }
}
