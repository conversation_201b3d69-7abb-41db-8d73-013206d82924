package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.helper.CorePlatformsValidated;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * Created by chenyu on 2018/5/30.
 */
@CorePlatformsValidated
public interface MspMerchantInvoiceService {

    Map findOrders(Map request);


    void submit(Map request);

    void updateMerchantInvoiceConfig(Map request);

    Map getMerchantInvoiceConfig(Map request);

    Map getAuditDetail(Map request);

    void mergeOrder(Map request);

    void confirmOrders(Map request);
}
