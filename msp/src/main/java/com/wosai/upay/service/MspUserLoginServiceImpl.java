package com.wosai.upay.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.app.common.PageFilter;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.model.user.SpecialAuthWhitelist;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.proxy.JwtUtils;
import com.wosai.upay.proxy.UserInfo;
import com.wosai.upay.user.api.service.SpecialAuthWhitelistService;
import com.wosai.web.api.ListResult;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by jianfree on 4/4/16.
 */
@Service
public class MspUserLoginServiceImpl extends AbsUserLoginService implements MspUserLoginService {
    private static final Logger logger = LoggerFactory.getLogger(MspUserLoginService.class);

    private static final String SESSION_USER = "msp_account"; //存储在session中的user信息

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private SpecialAuthWhitelistService specialAuthWhitelistService;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private UcUserAccountService ucUserAccountService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public List getUserList() {
        Object accountId = getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        logger.info("GroupUserLoginServiceImpl获取到的accountId = {}，是否是新创建的,result= {}",accountId,getSession().isNew());
        if(accountId == null){
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
        }

        // 获取MerchantId,pageMerchantUser方法需要
        String ucUserId = accountId.toString();
        List<UcMerchantUserInfo> merchantUserInfoList = merchantUserServiceV2.getMerchantUserByUcUserId(ucUserId);
        if(CollectionUtils.isEmpty(merchantUserInfoList)) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "商户平台无此用户或者此用户被禁用");
        }

        List<Map> recordListMap = new ArrayList<>();
        List<UcMerchantUserInfo> closeRecords = new ArrayList<UcMerchantUserInfo>();
        List<UcMerchantUserInfo> notAdminRecords = new ArrayList<UcMerchantUserInfo>();
        for(UcMerchantUserInfo mspUser: merchantUserInfoList){
            Map map = objectMapper.convertValue(mspUser, Map.class);
            String merchantId1 = mspUser.getMerchant_id();
            Map merchant = merchantService.getMerchant(merchantId1);
            if (BeanUtil.getPropInt(merchant, Merchant.STATUS) == Merchant.STATUS_CLOSED) {
                closeRecords.add(mspUser);
            }
            String role = mspUser.getRole();
            if (!MspUserLoginService.ROLE_SUPER_ADMIN.equals(role) && !MspUserLoginService.ROLE_ADMIN.equals(role)&& !MspUserLoginService.ROLE_DEPARTMENT_MANAGER.equals(role)) {
                notAdminRecords.add(mspUser);
            }
            map.put(ConstantUtil.KEY_ID, mspUser.getMerchant_user_id());
            map.put(ConstantUtil.KEY_MERCHANT_SN, BeanUtil.getPropString(merchant, Merchant.SN));
            map.put(ConstantUtil.KEY_MERCHANT_NAME, BeanUtil.getPropString(merchant, Merchant.NAME));
            map.put(ConstantUtil.KEY_MERCHANT_ID, BeanUtil.getPropString(merchant, DaoConstants.ID));
            recordListMap.add(map);
        }
        return recordListMap;
    }

    @Override
    public void changeUserContext(Map request) {
        // 查询ucUser信息
        Object accountId = getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        if(accountId == null){
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
        }
        UcUserInfo ucUserById = ucUserAccountService.getUcUserById(accountId.toString());

        // 查询merchantUser信息
        String merchantUserId = BeanUtil.getPropString(request, "user_id");
        UcMerchantUserInfo merchantUserById = merchantUserServiceV2.getMerchantUserById(merchantUserId);
        String role = merchantUserById.getRole();

        Map account = objectMapper.convertValue(ucUserById, Map.class);
        account.put("username", ucUserById.getCellphone());
        account.put("nickname", ucUserById.getNickname());
        account.put("avatar", ucUserById.getAvatar());
        account.put("gender", ucUserById.getGender());
        account.put("user_id", merchantUserId);
        account.put("role", role);
        String ucUserId = merchantUserById.getUcUserInfo().getUc_user_id();
        if(!accountId.equals(ucUserId)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "用户不存在");
        }

        String merchantId = merchantUserById.getMerchant_id();
        Map merchant = merchantService.getMerchant(merchantId);
        if(merchant == null){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户不存在");
        }

        account.put(ConstantUtil.KEY_MERCHANT_SN, BeanUtil.getPropString(merchant, Merchant.SN));
        account.put(ConstantUtil.KEY_MERCHANT_NAME, BeanUtil.getPropString(merchant, Merchant.NAME));
        account.put(ConstantUtil.KEY_MERCHANT_ID, BeanUtil.getPropString(merchant, DaoConstants.ID));

        // TODO 权限控制暂不清楚
        extendAccountMspRefundPermission(account);

        UserInfo info = new UserInfo();
        info.setMsp_account_id((String)accountId);
        info.setMsp_type(0);
        info.setMsp_role(role);
        info.setMsp_merchant_id(merchantId);

        account.put("proxy_info",info);
        account.put("id_token", JwtUtils.jwtToken(info.toMap()));

        //todo 设置角色与权限
        getSession().setAttribute(SESSION_USER, account);
        getSession().removeAttribute("MERCHANT_STORE_PARAMS");
    }

    @Override
    public Map getCurrentUser() {
        Map account = (Map)getSession().getAttribute(SESSION_USER);
        return BeanUtil.getPart(account, Arrays.asList(Account.USERNAME, Account.NICKNAME,
                Account.AVATAR, Account.GENDER, ConstantUtil.KEY_MERCHANT_SN, ConstantUtil.KEY_MERCHANT_NAME,
                ConstantUtil.KEY_MERCHANT_ID, "user_id", "role", "permission_list","id_token"
        ));

    }

    /**
     * 给账号添加权限列表
     * @param account
     * @return
     */
    private Map extendAccountMspRefundPermission(Map account) {
        String  merchantId = BeanUtil.getPropString(account, ConstantUtil.KEY_MERCHANT_ID);
        Map merchantPermission = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, merchantId);

        if(merchantPermission == null || merchantPermission.isEmpty()) {
            return account;
        }

        //超级管理员默认有商户后台退款权限
        if(MerchantUser.ROLE_SUPER_ADMIN.equals(BeanUtil.getPropString(account, MerchantUser.ROLE))) {
            account.put("permission_list", Arrays.asList(
                    "PERMISSION_MSP_REFUND"
            ));
            return account;
        }
        String userId = BeanUtil.getPropString(account, "user_id");
        Map permission = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, userId);
        int objectType = BeanUtil.getPropInt(permission, SpecialAuthWhitelist.OBJECT_TYPE);
        if(SpecialAuthWhitelist.OBJECT_TYPE_MERCHANT_USER == objectType) {
            account.put("permission_list", Arrays.asList(
                    "PERMISSION_MSP_REFUND"
            ));
        }
        return account;

    }



}
