package com.wosai.upay.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.wosai.app.common.PageFilter;
import com.wosai.app.dto.*;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.GroupService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Group;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.GroupUser;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.model.user.SpecialAuthWhitelist;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.proxy.JwtUtils;
import com.wosai.upay.proxy.UserInfo;
import com.wosai.upay.user.api.service.SpecialAuthWhitelistService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.HttpRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * yaming.rong.
 */
@Service
public class GroupUserLoginServiceImpl implements GroupUserLoginService {
    private static final Logger logger = LoggerFactory.getLogger(MspUserLoginService.class);

    @Autowired
    private UserService userService;

    @Autowired
    private SpecialAuthWhitelistService specialAuthWhitelistService;

    @Autowired
    private GroupService groupService;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private UcUserAccountService ucUserAccountService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public List getUserList() {
        Object accountId = getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        logger.info("GroupUserLoginServiceImpl获取到的accountId = {}",getSession().isNew());
        if (accountId == null) {
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
        }

        PageFilter pageFilter = new PageFilter(1, 100);
        QueryGroupUserReq queryGroupUserReq = new QueryGroupUserReq();
        queryGroupUserReq.setAccount_id(accountId.toString());
        queryGroupUserReq.setStatus(GroupUser.STATUS_ENABLED);

        com.wosai.web.api.ListResult<GroupUserInfo> groupUsers = groupService.findGroupUsers(pageFilter, queryGroupUserReq);
        if (groupUsers == null || groupUsers.getRecords().size() == 0) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "平台无此用户或者此用户被禁用");
        }

        if (groupUsers.getRecords().size() > 1) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "当前用户绑定了多个商户");
        }

        List<GroupUserInfo> records = groupUsers.getRecords();
        List<Map> recordsListMap = new ArrayList<>();
        List<GroupUserInfo> closeRecords = new ArrayList<>();
        List<GroupUserInfo> notAdminRecords = new ArrayList<>();
        for (GroupUserInfo groupUser : records) {
            String groupId = groupUser.getGroup_id();
            GroupSimpleInfo groupSimpleInfo = groupService.getGroupById(groupId);
            if (groupSimpleInfo.getStatus() == Group.STATUS_CLOSED) {
                closeRecords.add(groupUser);
            }
            String role = groupUser.getRole();
            if (!MspUserLoginService.ROLE_SUPER_ADMIN.equals(role) && !MspUserLoginService.ROLE_ADMIN.equals(role)) {
                notAdminRecords.add(groupUser);
            }

            Map map = objectMapper.convertValue(groupUser, Map.class);
            map.put(ConstantUtil.KEY_ID, groupUser.getId());
            map.put("group_id", groupUser.getGroup_id());
            map.put("group_sn", groupSimpleInfo.getSn());
            map.put("group_name", groupSimpleInfo.getName());
            recordsListMap.add(map);
        }
        if (closeRecords.size() != 0) {
            for (GroupUserInfo groupUser : closeRecords) {
                records.remove(groupUser);
            }
        }
        if (notAdminRecords.size() != 0) {
            for (GroupUserInfo groupUser  : notAdminRecords) {
                records.remove(groupUser);
            }
        }
        if (groupUsers.getRecords().size() == 0) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "此用户可以管理的集团已被关闭或此用户的角色不可登录");
        }
        List<GroupUserInfo> list = groupUsers.getRecords();
        if (list.size() == 1) {
            changeUserContext(CollectionUtil.hashMap("user_id", list.get(0).getGroup_id()));
        }
        return list;
    }

    @Override
    public void changeUserContext(Map request) {
        Object accountId = getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        if (accountId == null) {
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
        }
        UcUserInfo ucUserById = ucUserAccountService.getUcUserById(accountId.toString());

        Map accountMap = objectMapper.convertValue(ucUserById, Map.class);

        String groupUserId = BeanUtil.getPropString(request, "user_id");
        GroupUserInfo groupUserById = groupService.getGroupUserById(groupUserId);
        String role = groupUserById.getRole();
        accountMap.put("user_id", groupUserId);
        accountMap.put("role", role);
        accountMap.put("operator_id",groupUserId);

        if (!accountId.equals(groupUserById.getAccount_id())) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "用户不存在");
        }
        String groupId = groupUserById.getGroup_id();
        GroupSimpleInfo groupSimpleInfo = groupService.getGroupById(groupId);
        if (groupSimpleInfo == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "集团不存在");
        }

        String groupSn = groupSimpleInfo.getSn();
        accountMap.put("group_id", groupId);
        accountMap.put("group_sn", groupSn);
        accountMap.put("group_name", groupSimpleInfo.getName());


        List<Object> merchantIds = Lists.newArrayList();

        PageFilter pageFilter = new PageFilter(1, 100);
        QueryGroupMerchantAuthReq queryGroupMerchantAuthReq = new QueryGroupMerchantAuthReq();
        queryGroupMerchantAuthReq.setGroup_id(groupId);
        queryGroupMerchantAuthReq.setGroup_user_id(groupUserId);
        com.wosai.web.api.ListResult<GroupUserMerchantAuthInfo> groupUserMerchantAuths = groupService.findGroupUserMerchantAuths(pageFilter, queryGroupMerchantAuthReq);
        List<GroupUserMerchantAuthInfo> records = groupUserMerchantAuths.getRecords();

        if(records!=null){
            for (GroupUserMerchantAuthInfo group_merchant : records) {
                merchantIds.add(group_merchant.getMerchant_id());
            }
            accountMap.put("merchant_ids",merchantIds);
        }



        extendGroupUserMspRefundPermission(accountMap);

        UserInfo info = new UserInfo();
        info.setMsp_account_id((String)accountId);
        info.setMsp_type(1);
        info.setMsp_role(role);
        info.setMsp_group_id(groupId);
        info.setMsp_group_sn(groupSn);

        accountMap.put("proxy_info",info);
        accountMap.put("id_token", JwtUtils.jwtToken(info.toMap()));

        getSession().setAttribute(GroupUserLoginService.SESSION_USER, accountMap);
    }

    @Override
    public Map getCurrentUser() {
        Map account = (Map) getSession().getAttribute(GroupUserLoginService.SESSION_USER);
        return BeanUtil.getPart(account, Arrays.asList(Account.USERNAME, Account.NICKNAME,
                Account.AVATAR, Account.GENDER, "group_id", "group_sn",
                "group_name", "user_id", "role", "permission_list","id_token"
        ));
    }

    private HttpSession getSession() {
        return HttpRequestUtil.getSession();
    }

    private HttpServletRequest getRequest() {
        return HttpRequestUtil.getRequest();
    }

    private boolean isExistsByUser(ListResult listResult) {
        return listResult == null || listResult.getRecords().size() == 0;
    }

    /**
     * 给账号添加权限列表
     * @param account
     * @return
     */
    private Map extendGroupUserMspRefundPermission(Map account) {
        String groupId = BeanUtil.getPropString(account, "group_id");
        Map groupPermission = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, groupId);

        if(groupPermission == null || groupPermission.isEmpty()) {
            return account;
        }

        //超级管理员默认有商户后台退款权限
        if(MerchantUser.ROLE_SUPER_ADMIN.equals(BeanUtil.getPropString(account, MerchantUser.ROLE))) {
            account.put("permission_list", Arrays.asList(
                    "PERMISSION_MSP_REFUND"
            ));
            return account;
        }
        String userId = BeanUtil.getPropString(account, "user_id");
        Map permission = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, userId);
        int objectType = BeanUtil.getPropInt(permission, SpecialAuthWhitelist.OBJECT_TYPE);
        if(SpecialAuthWhitelist.OBJECT_TYPE_MERCHANT_USER == objectType) {
            account.put("permission_list", Arrays.asList(
                    "PERMISSION_MSP_REFUND"
            ));
        }
        return account;

    }

}
