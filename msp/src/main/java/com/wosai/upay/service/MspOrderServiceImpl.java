package com.wosai.upay.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.googlecode.jsonrpc4j.ProxyUtil;
import com.wosai.app.backend.api.service.IMerchantService;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.sdk.support.CommonRedisOps;
import com.wosai.upay.bean.Order;
import com.wosai.upay.bean.Transaction;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TerminalActivationCode;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.model.user.MspRefundTerminal;
import com.wosai.upay.core.model.user.SpecialAuthWhitelist;
import com.wosai.upay.core.service.*;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.service.remote.OrderService;
import com.wosai.upay.transaction.service.TransactionService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.user.api.service.MspRefundTerminalService;
import com.wosai.upay.user.api.service.SpecialAuthWhitelistService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.SheetUtil;
import com.wosai.upay.util.UpayStatement;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.wosai.upay.common.util.ConstantUtil.KEY_MERCHANT_ID;
import static com.wosai.upay.common.util.ConstantUtil.KEY_STORE_ID;


/**
 * Created by kay on 16/9/1.
 */

public class MspOrderServiceImpl extends BaseService implements MspOrderService {

    private static final Logger logger = LoggerFactory.getLogger(MspOrderServiceImpl.class);
    public static final int MAX_EXPORT_THREAD_COUNT = 2; //最多同时有多少个线程执行
    ThreadPoolExecutor exportExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(MAX_EXPORT_THREAD_COUNT);
    /**
     * 大容量导出csv格式数据，最大支持导出多长时间
     * 31天
     */
    public static final long EXPORT_CSV_MAX_TIME = 31 * 24 * 60 * 60 * 1000l;
    private static final String VENDOR_APP_APPID_SERVICE = "2016111100000033";
    protected static final String TEMPLDATE_NORMAL = "veqof4";
    protected static final String MSP_CACHE_PRE = "msp_";


    @Autowired
    private LogService logService;
    @Autowired
    private UserService userService;
    @Autowired
    protected SmsSendService smsSendService;
    @Autowired
    private CommonRedisOps redisOps;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    OssFileUploader ossFileUploader;
    @Autowired
    private TranslateService translateService;
    @Autowired
    private IMerchantService iMerchantService;
    @Autowired
    private MspRefundTerminalService mspRefundTerminalService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private BusinssCommonService businssCommonService;
    @Autowired
    private SpecialAuthWhitelistService specialAuthWhitelistService;
    @Autowired
    private VendorService vendorService;
    @Autowired
    private TransactionService transactionService;
    @Autowired
    private MspBusinessLogService mspBusinessLogService;
    @Autowired
    private com.wosai.upay.transaction.service.OrderService orderServiceV2;//upay transaction的接口
    @Autowired
    private UpayOrderService upayOrderService;

    private OrderService orderService; //backend upay项目提供的接口
    private String upayGatewayServer;

    public MspOrderServiceImpl(String backendUpayUrl, String upayGatewayServer) {
        JsonRpcHttpClient client = null;
        try {
            client = new JsonRpcHttpClient(new URL(backendUpayUrl + "rpc/upayorder"));
        } catch (MalformedURLException e) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        this.orderService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                OrderService.class,
                client);
        this.upayGatewayServer = upayGatewayServer;
    }

    @Override
    public Map createTaskApplyLog(Map<String, Object> request) {
        return logService.createTaskApplyLog(request);
    }

    @Override
    public void deleteTaskApplyLog(Map<String, Object> request) {
        logService.deleteTaskApplyLog((String) request.get(DaoConstants.ID));
    }

    @Override
    public Map updateTaskApplyLog(Map taskApplyLog) {
        return logService.updateTaskApplyLog(taskApplyLog);
    }

    @Override
    public Map getTaskApplyLog(Map<String, Object> request) {
        return logService.getTaskApplyLog((String) request.get(DaoConstants.ID));
    }

    @Override
    public ListResult findTaskApplyLogs(Map<String, Object> request) {
        return logService.findTaskApplyLogs(PageInfoUtil.extractPageInfo(request), request);
    }

    @Override
    public String getfindalTaskId() {
        return (String) HttpRequestUtil.getSession().getAttribute("task_apply_log_id");
    }

    @Override
    public Map createStatement(Map<String, Object> params) {
        params.put(KEY_MERCHANT_ID, getSessionMerchantId());
        params.put(KEY_STORE_ID, getSessionStoreId());
        params.put("user_id", getSessionUserId());
        Map taskApplyLog = this.createTaskApplyLog(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, 1,
                TaskApplyLog.APPLY_SYSTEM, 4,
                TaskApplyLog.APPLY_DATE, new java.sql.Date(new Date().getTime()),
                TaskApplyLog.PAYLOAD, params,
                TaskApplyLog.USER_ID, getSessionUserId()
        ));

        final String taskApplyLogId = BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID);
        params.put("task_apply_log_id", taskApplyLogId);
        final Map paramsFinal = params;
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                try {
                    exportStatement(paramsFinal);
                } catch (Exception e) {
                    logger.info("task " + taskApplyLogId + "failed" + e.getMessage());
                    updateTaskApplyLogStatus(taskApplyLogId, 3);
                }
            }
        };

        try {
            exportExecutor.submit(runnable);
        } catch (Exception e) {
            updateTaskApplyLogStatus(taskApplyLogId, 3);
            throw new UpayException(UpayException.CODE_IO_EXCEPTION, e.getMessage());
        }
        HttpRequestUtil.getSession().setAttribute("task_apply_log_id", taskApplyLogId);
        return taskApplyLog;
    }


    @Override
    public Map createStatementCsv(Map<String, Object> params) {
        /**
         * 获取session中商户、商店、用户id
         * 放入map参数集合中
         */
        params.put(KEY_MERCHANT_ID, getSessionMerchantId());
        params.put(KEY_STORE_ID, getSessionStoreId());
        params.put("user_id", getSessionUserId());
//        params.put(KEY_MERCHANT_ID, "da6b3cf6-af99-11e5-9ec3-00163e00625b");
//        params.put("user_id", "cf32ed75-faf1-11e5-ab7f-00163e0043c9");

        //登录检查
        if (empty(BeanUtil.getPropString(params, KEY_MERCHANT_ID))) {
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
        }

        //参数检查
        long date_start = BeanUtil.getPropLong(params, "date_start");
        long date_end = BeanUtil.getPropLong(params, "date_end");
        if (date_start <= 0 || date_end <= 0 || date_start > date_end || date_end - date_start > EXPORT_CSV_MAX_TIME) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "开始和结束时间不合法");
        }

        //调用导出任务
        try {
            params = orderService.createStatementCsv(params);
        } catch (Exception e) {
            throw new UpayException(UpayException.CODE_IO_EXCEPTION, e.getMessage());
        }

        //如果任务创建成功将任务id放入session中
        if (params != null && params.containsKey("task_apply_log_id")) {
            HttpRequestUtil.getSession().setAttribute("task_apply_log_id", BeanUtil.getPropString(params, "task_apply_log_id"));
        }
        return params;
    }

    /**
     * 字符串判断是否为空
     *
     * @param s
     * @return
     */
    boolean empty(String s) {
        return s == null || s.isEmpty();
    }


    public void exportStatement(Map params) throws Exception {
        String taskApplyLogId = BeanUtil.getPropString(params, "task_apply_log_id");
        updateTaskApplyLogStatus(taskApplyLogId, 1);
        String merchantId = BeanUtil.getPropString(params, KEY_MERCHANT_ID);
        String storeId = BeanUtil.getPropString(params, KEY_STORE_ID);
        String userId = BeanUtil.getPropString(params, "user_id");

        long start = BeanUtil.getPropLong(params, "date_start");
        long end = BeanUtil.getPropLong(params, "date_end");
        Map merchant = merchantService.getMerchant(merchantId);
        Map context = CollectionUtil.hashMap(
                "merchant_id", merchantId,
                "start", new Date(start), "end", new Date(end),
                "merchant_sn", BeanUtil.getPropString(merchant, Merchant.SN),
                "merchant_name", BeanUtil.getPropString(merchant, Merchant.NAME)
        );
        PageInfo pageInfo = new PageInfo(1, 50000, start, end);
        Map queryFilter = CollectionUtil.hashMap(
                "status", Arrays.asList(Transaction.STATUS_SUCCESS),
                "storeName", BeanUtil.getPropString(params, "store_name")
        );
        Map user = userService.getMerchantUser(userId);
        if (!BeanUtil.getPropString(user, "role", "").equals(MspUserLoginService.ROLE_SUPER_ADMIN) && BeanUtil.getPropInt(user, "store_auth") == 2) {
            //user对应的门店
            LinkedList storeIds = new LinkedList();
            List<Map> userStores = userService.getMerchantUserStoreAuths(CollectionUtil.hashMap(
                    "merchant_id", merchantId,
                    "merchant_user_id", userId
                    )
            );
            for (Map userStore : userStores) {
                storeIds.add(BeanUtil.getPropString(userStore, KEY_STORE_ID));
            }
            //todo backend_upay 加上 storeIds
            queryFilter.put("storeIds", storeIds);
        }
        ListResult result = upayOrderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
        translateService.translateOrderOperatorNames(result.getRecords(), merchantId);
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet summarySheet = workbook.createSheet("账单汇总");
        UpayStatement.buildUpayStatementSummary(context, result.getRecords(), summarySheet);
        SheetUtil util = new SheetUtil(summarySheet);
        util.mergeCell(2, 0, 2, 3);
        util.setCellAlignment(2, 0, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER);
        util.mergeCell(6, 0, 6, 10);
        util.setCellAlignment(6, 0, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER);
        HSSFSheet detailSheet = workbook.createSheet("账单明细");
        UpayStatement.buildUpayStatementDetail(context, result.getRecords(), detailSheet);
        util = new SheetUtil(detailSheet);

        logger.info("create excel success " + taskApplyLogId);
        String ossFileUrl = uploadStatementToOSS("xls", workbook, userId);
        logger.info("uploadStatementToOSS  success " + ossFileUrl + " " + taskApplyLogId);
        updateTaskApplyLog(CollectionUtil.hashMap(
                ConstantUtil.KEY_ID, taskApplyLogId,
                TaskApplyLog.APPLY_STATUS, 2,
                TaskApplyLog.APPLY_RESULT, ossFileUrl
        ));
    }

    private String uploadStatementToOSS(String ext, HSSFWorkbook workbook, String userId) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        byte[] content = bos.toByteArray();
        bos.close();
        // calculate path
//        String digest = toSHA1(content);
//        logger.info("uploadStatementToOSS  sha1 " + digest);
//        String dirName = digest.substring(0, 2);
//        String fullName = "portal/statement/" + dirName + "/" + digest.substring(2) + "." + ext;
        String fullName = "portal/statement/" + userId + "/" + new Date().getTime() + "." + ext;
        ByteArrayInputStream bais = new ByteArrayInputStream(content);
        //todo 开个保存报表的bucket
        ossFileUploader.uploadIfNotExists(OssFileUploader.IMAGE_BUCKET_NAME, fullName, bais, content.length);
        return fullName;
    }

    private static String toSHA1(byte[] convertme) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("SHA-1");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return byteArrayToHexString(md.digest(convertme));
    }

    private static String byteArrayToHexString(byte[] b) {
        String result = "";
        for (int i = 0; i < b.length; i++) {
            result +=
                    Integer.toString((b[i] & 0xff) + 0x100, 16).substring(1);
        }
        return result;
    }

    private void updateTaskApplyLogStatus(String id, int status) {
        Map taskApplyLog = logService.getTaskApplyLog(id);
        taskApplyLog.put(TaskApplyLog.APPLY_STATUS, status);
        updateTaskApplyLog(taskApplyLog);
    }


    @Override
    public Map refundVerifyCode(Map request) {
        String cellphone = getSessionAccountUsername();
        //生成六位验证码
        String vcode = new Random().nextInt(899999) + 100000 + "";
        String cacheKey = String.format("%s%s", MSP_CACHE_PRE, cellphone);
        //将验证码和电话号码保存在redis中，有效时间1分钟
        try {
            redisOps.set(cacheKey, vcode, 60, TimeUnit.SECONDS);
            boolean sendFlag = smsSendService.sendSmsAuthCode(cellphone, vcode, TEMPLDATE_NORMAL);
            if (sendFlag) {
                return CollectionUtil.hashMap("status", true, "message", "验证码发送成功，请及时查看手机短信");
            }
        } catch (Exception e) {
            logger.error("refundVerifyCode:{},{}", cellphone, e);
        }
        return CollectionUtil.hashMap("status", false, "message", "验证码发送失败，请稍后重试");
    }

    @Override
    public Map refund(Map request) {

        checkVerifyCode(request);
        String orderSn = BeanUtil.getPropString(request, "order_sn"); //根据请求信息查订单id
        //TODO
        Map order = orderServiceV2.getOrderDetailByOrderSn(orderSn);//根据订单id获取订单详情order
        String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        checkRefundPermission(merchantId);
        checkRefundPassword(request);
        long refundType = BeanUtil.getPropLong(request, "refund_type");
        Map refundTerminalInfo = getRefundTerminal(order, refundType);
        String refund_amount = BeanUtil.getPropString(request, "refund_amount");
        String refund_request_no = BeanUtil.getPropString(request, "refund_request_no");
        String refund_detail = BeanUtil.getPropString(request, "refund_detail");
        Map params = CollectionUtil.hashMap(
                "terminal_sn", BeanUtil.getPropString(refundTerminalInfo, "terminal_sn"),
                "sn", orderSn,
                "refund_request_no", refund_request_no,
                //todo: 收银员标记 msp 前缀
                "operator", getSessionAccountUsername(),
                "refund_amount", refund_amount,
                //todo check
                "extended", CollectionUtil.hashMap(
                        "refund_detail", refund_detail
                ),
                //备注
                "reflect", refund_detail
        );
        logger.info("refund request:{}", params);
        String upayGateWayServer = BeanUtil.getPropString(request, "upay_gateway_server");
        if (StringUtil.empty(upayGateWayServer)) {
            upayGateWayServer = upayGatewayServer;
        }
        if (request.containsKey("upay_gateway_server")) {
            request.remove("upay_gateway_server");
        }

        HttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(upayGateWayServer + "/upay/v2/refund");
        HttpResponse response = this.getRefundResponse(params, httpClient, httpPost);
        return this.getRefundResult(orderSn, response, refund_detail, refund_detail);
    }

    @Override
    public Map intlRefund(Map request) {
        try {

            String orderSn = BeanUtil.getPropString(request, "order_sn"); //根据请求信息查订单id
            //TODO
            Map order = orderServiceV2.getOrderDetailByOrderSn(orderSn);//根据订单id获取订单详情order
            String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
            checkRefundPermission(merchantId);
            checkRefundPassword(request);
            long refundType = BeanUtil.getPropLong(request, "refund_type");
            Map refundTerminalInfo = getRefundTerminal(order, refundType);
            String refund_amount = BeanUtil.getPropString(request, "refund_amount");
            String refund_request_no = BeanUtil.getPropString(request, "refund_request_no");
            String refund_detail = BeanUtil.getPropString(request, "refund_detail");
            Map params = CollectionUtil.hashMap(
                    "terminal_sn", BeanUtil.getPropString(refundTerminalInfo, "terminal_sn"),
                    "sn", orderSn,
                    "refund_request_no", refund_request_no,
                    //todo: 收银员标记 msp 前缀
                    "operator", getSessionAccountUsername(),
                    "refund_amount", refund_amount,
                    //todo check
                    "extended", CollectionUtil.hashMap(
                            "refund_detail", refund_detail
                    ),
                    //备注
                    "reflect", refund_detail
            );
            logger.info("refund request:{}", params);
            String upayGateWayServer = BeanUtil.getPropString(request, "upay_gateway_server");
            if (StringUtil.empty(upayGateWayServer)) {
                upayGateWayServer = upayGatewayServer;
            }
            if (request.containsKey("upay_gateway_server")) {
                request.remove("upay_gateway_server");
            }

            HttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(upayGateWayServer + "/upay/v2/refund");
            HttpResponse response = this.getRefundResponse(params, httpClient, httpPost);
            return this.getRefundResult(orderSn, response, refund_detail, refund_detail);
        }catch (Throwable e){
            logger.error("error",e);
            throw e;
        }
    }


    private HttpResponse getRefundResponse(Map params, HttpClient httpClient, HttpPost httpPost) {
        HttpResponse response;
        try {
            String json = new ObjectMapper().writeValueAsString(params);
            StringEntity postingString = new StringEntity(json);
            httpPost.setEntity(postingString);
            httpPost.setHeader("Content-type", "application/json");
            response = httpClient.execute(httpPost);
        } catch (Throwable e) {
            logger.info("refund service exception:" + e.getMessage(), e);
            e.printStackTrace();
            throw new RuntimeException("refund service 连接异常:" + e.getMessage(), e);
        }
        return response;
    }

    private Map getRefundResult(String orderSn, HttpResponse response, String refund_detail2, String refund_detail) {
        Map result;
        try {
            if (response.getStatusLine().getStatusCode() == 200) {
                String json = EntityUtils.toString(response.getEntity(), "utf-8");
                logger.info("response entity json string: " + json);
                result = JSON.parseObject(json);
                int result_code = BeanUtil.getPropInt(result, "result_code");
                //接口调用的业务逻辑成功返回
                if (result_code == 200) {
                    Map biz_response = (Map) BeanUtil.getProperty(result, "biz_response");
                    String bussiness_code = BeanUtil.getPropString(biz_response, "result_code");
                    if (!bussiness_code.equals("REFUND_SUCCESS")) {
//                        String error_code = BeanUtil.getPropString(biz_response, "error_code");
                        String error_message = BeanUtil.getPropString(biz_response, "error_message");
                        throw new UpayException(UpayException.CODE_INVALID_PARAMETER, error_message);
                    } else {
                        Map data = (Map) BeanUtil.getProperty(biz_response, "data");
                        long ctime = BeanUtil.getPropLong(data, "ctime");
                        List<Map<String, Object>> transactionList = transactionService.getTransactionListByOrderSn(orderSn);
                        if (transactionList != null && transactionList.size() > 0) {
                            //保存业务日志
                            mspBusinessLogService.sendBusinessLog(CollectionUtil.hashMap(
                                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, null,
                                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, transactionList.get(transactionList.size() - 1),
                                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                            BizOpLog.BUSINESS_OBJECT_CODE, "transaction",
                                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "transaction",
                                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id",
                                            BizOpLog.REMARK, refund_detail2,
                                            BizOpLog.OP_TIME, ctime,
                                            BizOpLog.BUSINESS_FUNCTION_CODE, "1000060"
                                    )
                            ));
                        }
                        return data;
                    }
                } else {
//                    String error_code = BeanUtil.getPropString(result, "error_code");
                    String error_message = BeanUtil.getPropString(result, "error_message");
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, error_message);
                }
            } else {
                String errJson = EntityUtils.toString(response.getEntity());
                logger.info("退款请求响应失败: " + errJson);
                throw new UpayException(UpayException.CODE_NET_CONNECT_ERROR, "退款请求失败,退款服务繁忙");
            }
        } catch (UpayException e) {
            logger.info("refund service 返回业务数据异常:" + e.getMessage(), e);
            throw new UpayException(e.getCode(), e.getMessage());
        } catch (Throwable e) {
            logger.info("refund service 解析返回数据异常:" + e.getMessage(), e);
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "解析返回数据异常: " + e.getMessage());
        }
    }

    private Map getRefundTerminal(Map order, long refundType) {
        if (order == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "订单号不正确");
        }
        String terminalId = null;
        String storeId = "";
        if (MspRefundTerminal.REFUND_TYPE_ORI_STORE_ORI_TERMINAL == refundType) {
            terminalId = BeanUtil.getPropString(order, Order.TERMINAL_ID);
        } else if (MspRefundTerminal.REFUND_TYPE_ORI_STORE_REFUND_TERMINAL == refundType) {
            String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
            storeId = BeanUtil.getPropString(order, Order.STORE_ID);

            ListResult result = mspRefundTerminalService.findMspRefundTerminals(
                    null,
                    CollectionUtil.hashMap(
                            MspRefundTerminal.REFUND_TYPE, MspRefundTerminal.REFUND_TYPE_ORI_STORE_REFUND_TERMINAL,
                            MspRefundTerminal.MERCHANT_ID, merchantId,
                            MspRefundTerminal.STORE_ID, storeId
                    ));
            if (result.getTotal() < 1) {
                Map refundTerminal = createActivatedTerminal(storeId, CollectionUtil.hashMap(
                        ConstantUtil.KEY_TERMINAL_NAME, "退款终端"
                ));
                terminalId = BeanUtil.getPropString(refundTerminal, DaoConstants.ID);
                mspRefundTerminalService.createMspRefundTerminal(CollectionUtil.hashMap(
                        MspRefundTerminal.REFUND_TYPE, MspRefundTerminal.REFUND_TYPE_ORI_STORE_REFUND_TERMINAL,
                        MspRefundTerminal.MERCHANT_ID, merchantId,
                        MspRefundTerminal.STORE_ID, storeId,
                        MspRefundTerminal.TERMINAL_ID, terminalId
                ));

            } else {
                Map refundTerminal = result.getRecords().get(0);
                terminalId = BeanUtil.getPropString(refundTerminal, MspRefundTerminal.TERMINAL_ID);
            }
        } else if (MspRefundTerminal.REFUND_TYPE_REFUND_STORE_REFUND_TERMINAL == refundType) {
            String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
            //todo 真实门店和终端被删除
            ListResult result = mspRefundTerminalService.findMspRefundTerminals(
                    null,
                    CollectionUtil.hashMap(
                            MspRefundTerminal.REFUND_TYPE, MspRefundTerminal.REFUND_TYPE_REFUND_STORE_REFUND_TERMINAL,
                            MspRefundTerminal.MERCHANT_ID, merchantId
                    ));
            if (result.getTotal() < 1) {
                //创建退款门店
                Map refundStore = storeService.createStore(CollectionUtil.hashMap(
                        Store.MERCHANT_ID, merchantId,
                        Store.NAME, "总部门店"
                ));
                storeId = BeanUtil.getPropString(refundStore, DaoConstants.ID);
                //todo: 事务 -- 创建退款终端
                Map refundTerminal = createActivatedTerminal(storeId, CollectionUtil.hashMap(
                        ConstantUtil.KEY_TERMINAL_NAME, "总部门店退款终端"
                ));
                terminalId = BeanUtil.getPropString(refundTerminal, DaoConstants.ID);
                mspRefundTerminalService.createMspRefundTerminal(CollectionUtil.hashMap(
                        MspRefundTerminal.REFUND_TYPE, MspRefundTerminal.REFUND_TYPE_REFUND_STORE_REFUND_TERMINAL,
                        MspRefundTerminal.MERCHANT_ID, merchantId,
                        MspRefundTerminal.STORE_ID, storeId,
                        MspRefundTerminal.TERMINAL_ID, terminalId
                ));
                //关联所有账户
                if (getSessionObjectType().equals("merchant")) {
                    bindMerchantAdminUserStore(merchantId, storeId);
                }
            } else {
                Map refundTerminal = result.getRecords().get(0);
                storeId = BeanUtil.getPropString(refundTerminal, MspRefundTerminal.STORE_ID);
                terminalId = BeanUtil.getPropString(refundTerminal, MspRefundTerminal.TERMINAL_ID);
            }
        } else {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "未填写退款门店和终端类型");
        }
        return CollectionUtil.hashMap(
                "terminal_id", terminalId,
                "terminal_sn", businssCommonService.getTerminalSnById(terminalId)
        );
    }

    private void checkVerifyCode(Map request) {
        String vcode = BeanUtil.getPropString(request, "verify_code");
        String cellphone = getSessionAccountUsername();
        String cacheKey = String.format("%s%s", MSP_CACHE_PRE, cellphone);
        String cacheCode = redisOps.get(cacheKey);
        if (!"999999".equals(vcode)) {
            if (StringUtil.empty(cacheCode)) {
                throw new UpayException(UpayException.CODE_ACCESS_DENIED, "已超过验证码有效时间,请重新获取验证码");

            }
            if (!cacheCode.equals(vcode)) {
                throw new UpayException(UpayException.CODE_ACCESS_DENIED, "验证码输入错误，请重新输入");
            }
        }
        redisOps.delete(cacheKey);
    }

    private void checkRefundPassword(Map request) {
        String orderSn = BeanUtil.getPropString(request, "order_sn");
        logger.info("step1");
        Map order = orderServiceV2.getOrderDetailByOrderSn(orderSn);
        String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
        String password = BeanUtil.getPropString(request, "password");
        Map result = iMerchantService.checkMerchantManagerPassword(merchantId, password);
        logger.info("step2");
        if (10000 != BeanUtil.getPropInt(result, "code")) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "退款密码不正确");
        }
        logger.info("step2");
    }

    /**
     * 1.判断当前主体(集团、商户) 是否有支付源主体的权限
     * 2.判断支付源主体orderMerchantId 是否有权限
     * 3.判断当前主体(集团、商户)是否有权限
     * 4.判断当前账户是否有权限(超级管理员默认有)
     *
     * @param orderMerchantId
     */
    private void checkRefundPermission(String orderMerchantId) {
        Map sessionInfos = getSessionInfos(null);
        List merchantIds = (List) BeanUtil.getProperty(sessionInfos, "merchant_ids");

        //1.判断当前主体(集团、商户) 是否有支付源主体的权限
        if (!merchantIds.contains(orderMerchantId)) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "没有处理该商户订单的权限");
        }

        //2.判断支付源主体orderMerchantId 是否有权限
        Map orderMerchantAuth = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND,
                orderMerchantId
        );
        if (orderMerchantAuth == null || orderMerchantAuth.isEmpty()) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "订单商户暂未开通商户后台退款权限");
        }

        //3.判断当前主体(集团、商户)是否有权限
        String objectId = BeanUtil.getPropString(sessionInfos, "object_id");
        Map merchantAuth = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND,
                objectId
        );
        if (merchantAuth == null || merchantAuth.isEmpty()) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "登录账户暂未开通商户后台退款权限");
        }

        //4.判断当前账户是否有权限(超级管理员默认有)
        //超级管理员默认有商户后台退款权限
        if (MerchantUser.ROLE_SUPER_ADMIN.equals(getSessionUserRole())) {
            return;
        }
        String userId = getSessionUserId();
        Map permission = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, userId);
        if (permission == null || permission.isEmpty()) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "用户暂未开通商户后台退款权限");
        }

    }


    private Map createActivatedTerminal(String storeId, Map terminalInfo) {
        String storeSn = businssCommonService.getStoreSnById(storeId);
        String clientSn = BeanUtil.getPropString(terminalInfo, Terminal.CLIENT_SN);
        String deviveFingerPrint = BeanUtil.getPropString(terminalInfo, Terminal.DEVICE_FINGERPRINT);
        String osVer = BeanUtil.getPropString(terminalInfo, Terminal.OS_VERSION);
        String sdkVer = BeanUtil.getPropString(terminalInfo, Terminal.SDK_VERSION);
        String longitude = BeanUtil.getPropString(terminalInfo, Terminal.LONGITUDE);
        String latitude = BeanUtil.getPropString(terminalInfo, Terminal.LATITUDE);
        Object extraInfo = BeanUtil.getProperty(terminalInfo, Terminal.EXTRA);
        String defaultTerminalName = BeanUtil.getPropString(terminalInfo, ConstantUtil.KEY_TERMINAL_NAME);
        Map activation = terminalService.createActivationCodeV2(null, null, storeSn, defaultTerminalName, 1);
        Map terminal = terminalService.activateV2(
                VENDOR_APP_APPID_SERVICE,
                BeanUtil.getPropString(activation, TerminalActivationCode.CODE),
                clientSn,
                deviveFingerPrint,
                defaultTerminalName,
                osVer,
                sdkVer,
                longitude,
                latitude,
                extraInfo);
        return terminal;
    }

}


