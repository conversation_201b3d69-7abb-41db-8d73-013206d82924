package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.user.GroupUserMerchantAuth;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

@Service
public class MspVisualServiceImpl implements MspVisualService {
    private static final Logger logger = LoggerFactory.getLogger(MspVisualServiceImpl.class);

    @Autowired
    private StoreService storeService;
    @Autowired
    private GroupService groupService;
    @Value("${visual.proxy.url}")
    private String visualProxyUrl;
    @Autowired
    private UserService userService;


    @Override
    public void proxy(Map<String, Object> request, HttpServletResponse response) {
        String code = UpayException.CODE_SUCCESS + "";
        String message = "SUCCESS";
        Object data = null;
        try {
            //补全商户、门店信息
            addProxyParams(request);
            String proxyUlr = BeanUtil.getPropString(request, "proxy_url");
            if (StringUtil.empty(proxyUlr)){
                proxyUlr = visualProxyUrl;
            }
            if (request != null && request.containsKey("proxy_url")){
                request.remove("proxy_url");
            }

            //执行转发
            String rs = processRequestJsonRPC(proxyUlr, JacksonUtil.toJsonString(request));
            Map result = JacksonUtil.toBean(rs, Map.class);
            // jsonrpc
            if (result.containsKey("jsonrpc")) {
                if (result.containsKey("result")) { // 正常返回结果
                    data = result.get("result");
                } else if (result.containsKey("error")) {
                    code = BeanUtil.getPropString(result.get("error"), "code");
                    message = BeanUtil.getPropString(result.get("error"), "message");
                    if (code == null) {
                        code = UpayException.CODE_UNKNOWN_ERROR + "";
                    }
                    data = BeanUtil.getProperty(result.get("error"), "data");
                }
            } else if (result.containsKey("code")) { // 结构 {code:, message:, data,}
                if (result.containsKey("data")) { // 正常返回结果
                    data = result.get("data");
                }
                code = BeanUtil.getPropString(result, "code");
                message = BeanUtil.getPropString(result, "message");
                if (code == null) {
                    code = UpayException.CODE_UNKNOWN_ERROR + "";
                }
            }
        } catch (IOException e) {
            code = UpayException.CODE_IO_EXCEPTION + "";
            message = e.getMessage();
        } catch (Exception e) {
            code = UpayException.CODE_UNKNOWN_ERROR + "";
            message = e.getMessage();
        }
        try {
            buildResult(code, message, data, response);
        } catch (Exception e) {

        }
    }

    public String processRequestJsonRPC(String url, String methodAndParams) throws IOException {
        return HttpClientUtil.getPostResponseString(url, methodAndParams);
    }

    /**
     * 构建返回结果.
     *
     * @param code 响应码
     * @param data 数据
     * @param resp
     * @throws IOException
     */
    private void buildResult(String code, String message, Object data, HttpServletResponse resp) throws IOException {
        resp.setContentType("application/json");
        resp.setCharacterEncoding("UTF-8");
        String result;
        result = "{\"code\": \"" + code + "\"," +
                "\"message\": \"" + message + "\"," +
                "\"data\": " + JacksonUtil.toJsonString(data) + "}";
        resp.getWriter().print(result);
    }

    /**
     * 增加多余参数
     * {
     * user_id: '',
     * merchant_ids: [],
     * merchant_sns: [],
     * store_ids: [store_id1 , store_id2 ....],
     * store_sns: [store_sn1 , store_sn2 ....]
     * }
     */
    private void addProxyParams(Map request) {
        if (request == null) {
            request = CollectionUtil.hashMap();
        }
        //从session中拿取proxy参数
        HttpSession session = HttpRequestUtil.getSession();
        Object session_proxy_params = session.getAttribute("VISUAL_PROXY_PARAMS");
        //session中不存在重新获取 并 保存session
        if (session_proxy_params == null) {
            List<String> merchant_ids = new ArrayList<>();
            List<String> merchant_sns = new ArrayList<>();
            List<String> store_ids = new ArrayList<>();
            List<String> store_sns = new ArrayList<>();

            Map session_user = (Map) HttpRequestUtil.getSession().getAttribute(GroupUserLoginService.SESSION_USER);
            String user_id = BeanUtil.getPropString(session_user, "user_id", "");
            String group_id = BeanUtil.getPropString(session_user, "group_id", "");
            //普通商户
            if (StringUtil.empty(group_id)) {
                merchant_ids.add(BeanUtil.getPropString(session_user, ConstantUtil.KEY_MERCHANT_ID));
                merchant_sns.add(BeanUtil.getPropString(session_user, ConstantUtil.KEY_MERCHANT_SN));
            }
            //集团商户
            else {
                List<Map> group_merchants = groupService.getGroupUserMerchantAuths(CollectionUtil.hashMap(
                        GroupUserMerchantAuth.GROUP_ID, group_id,
                        GroupUserMerchantAuth.GROUP_USER_ID, user_id
                ));
                for (Map group_merchant : group_merchants) {
                    merchant_ids.add(BeanUtil.getPropString(group_merchant, ConstantUtil.KEY_MERCHANT_ID));
                    merchant_sns.add(BeanUtil.getPropString(group_merchant, ConstantUtil.KEY_MERCHANT_SN));
                }
            }
            //门店信息
            Map user = userService.getMerchantUser(user_id);
            boolean isStoreAuth = !BeanUtil.getPropString(user, "role", "").equals(MspUserLoginService.ROLE_SUPER_ADMIN)  && BeanUtil.getPropInt(user, "store_auth") == 2;
            //集团或非授权商户显示商户下所有门店
            if (!StringUtil.empty(group_id) || !isStoreAuth){
                for (String merchant_id : merchant_ids) {
                    ListResult stores = storeService.getStoreListByMerchantId(merchant_id, new PageInfo(1, 1000), null);
                    int page = 1;
                    while (stores != null && stores.getRecords() != null && stores.getRecords().size() > 0){
                        for (Map store : stores.getRecords()) {
                            store_ids.add(BeanUtil.getPropString(store, DaoConstants.ID));
                            store_sns.add(BeanUtil.getPropString(store, Store.SN));
                        }
                        page ++;
                        stores = storeService.getStoreListByMerchantId(merchant_id, new PageInfo(page, 1000), null);
                    }
                }
            }
            //授权商户只显示改授权门店
            else {
                //user对应的门店
                LinkedList storeIds = new LinkedList();
                List<Map> userStores = userService.getMerchantUserStoreAuths(CollectionUtil.hashMap(
                        "merchant_id", merchant_ids.size() > 0 ? merchant_ids.get(0) : "",
                        "merchant_user_id", user_id
                ));
                for(Map userStore: userStores){
                    Map store = storeService.getStore(BeanUtil.getPropString(userStore, ConstantUtil.KEY_STORE_ID));
                    if (store != null){
                        store_ids.add(BeanUtil.getPropString(store, DaoConstants.ID));
                        store_sns.add(BeanUtil.getPropString(store, Store.SN));
                    }
                }
            }
            session_proxy_params = CollectionUtil.hashMap(
                    "user_id", user_id,
                    "merchant_sns", merchant_sns,
                    "store_sns", store_sns
            );
            session.setAttribute("VISUAL_PROXY_PARAMS", session_proxy_params);
        }
        Map visual_proxy_params = (Map) session_proxy_params;
        Set<String> keys = visual_proxy_params.keySet();

        Map params = (Map)BeanUtil.getProperty(request, "params");
        for (String key : keys) {
            params.put(key, BeanUtil.getProperty(visual_proxy_params, key));
        }


    }
}
