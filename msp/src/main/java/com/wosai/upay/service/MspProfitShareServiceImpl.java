package com.wosai.upay.service;

import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.googlecode.jsonrpc4j.ProxyUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.StringUtils;

import com.wosai.profit.sharing.constant.CommonConstant;
import com.wosai.profit.sharing.model.Receiver;
import com.wosai.profit.sharing.model.SharingBook;
import com.wosai.profit.sharing.model.SharingConfig;
import com.wosai.profit.sharing.model.SharingTransaction;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.service.remote.RemoteReceiverService;
import com.wosai.upay.service.remote.RemoteSharingBookService;
import com.wosai.upay.service.remote.RemoteSharingConfigService;
import com.wosai.upay.service.remote.RemoteSharingTransactionService;


import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.service.ExportService;
import com.wosai.upay.util.HttpRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

import static com.wosai.upay.common.util.ConstantUtil.KEY_MERCHANT_ID;

@Service
public class MspProfitShareServiceImpl extends BaseService implements MspProfitShareService, InitializingBean {

    private RemoteSharingBookService sharingBookService;

    private RemoteSharingTransactionService sharingTransactionService;

    private RemoteSharingConfigService sharingConfigService;
    private RemoteReceiverService receiverService;

    private static final Logger LOGGER = LoggerFactory.getLogger(MspProfitShareServiceImpl.class);

    public static final String SHARING_BOOK_EXPORT_TYPE_IN = "1" ;
    public static final String SHARING_BOOK_EXPORT_TYPE_OUT = "2" ;

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private ExportService exportService;
    @Autowired
    private MspOrderStatementService mspOrderStatementService;

    @Value("${jsonrpc.profit.share.server}")
    private String profitShareUrl;


    @Override
    public ListResult getMerchantAsReceivers(Map map) {
        String merchantId = getSessionMerchantId();
        String merchantSn = BeanUtil.getPropString(merchantService.getMerchant(merchantId), Merchant.SN);
        if(merchantSn == null){
            return ListResult.emptyListResult();
        }
        return receiverService.queryReceivers(CollectionUtil.hashMap(
                Receiver.MERCHANT_SN, merchantSn
        ), getPagination(map));
    }

    @Override
    public Map<String, Object> getMerchantShareConfig(Map map) {
        String merchantId = getSessionMerchantId();
        Map<String,Object> sharingConfig = sharingConfigService.getSharingConfigByMerchantId(merchantId);
        if(sharingConfig == null || sharingConfig.isEmpty()){
            sharingConfig = new HashMap<>();
            sharingConfig.put(SharingConfig.ID, merchantId);
            sharingConfig.put(SharingConfig.STATUS, SharingConfig.STATUS_DISABLED);
        }
        return sharingConfig;
    }

    @Override
    public ListResult queryOutSharingBook(Map map) {
        map.put(KEY_MERCHANT_ID, getSessionMerchantId());
        LOGGER.info("出账商户id:{}",KEY_MERCHANT_ID);
        return querySharingBook(map);
    }

    @Override
    public ListResult queryInSharingBook(Map map) {
        Map merchant = merchantService.getMerchant(getSessionMerchantId());
        LOGGER.info("入账商户id:{}",getSessionMerchantId());
        LOGGER.info("入账商户:{}",merchant);
        map.put("receiver_merchant_sn", BeanUtil.getPropString(merchant, Merchant.SN));
        return querySharingBook(map);
    }

    @Override
    @SuppressWarnings("unchecked")
    public ListResult querySharingBook(Map map) {
        ListResult sharingBookList = sharingBookService.querySharingBooks(map, getPagination(map));
        return sharingBookList;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map getSharingTransaction(Map map) {
        String receiverId = BeanUtil.getPropString(map, "receiver_id");
        Map<String, Object> result = sharingTransactionService.getSharingTransaction(BeanUtil.getPropString(map, DaoConstants.ID));
        Map<String,Object> extraParam = (Map<String, Object>) BeanUtil.getProperty(result, SharingTransaction.EXTRA_PARAMS);
        String ratio = "";
        result.put("ratio", ratio);
        if(extraParam == null) return result;
        Map<String,Object> profitSharing = (Map<String, Object>) BeanUtil.getProperty(extraParam,SharingTransaction.EXTRA_PARAMS_PROFIT_SHARING);
        if(profitSharing == null) return result;
        List<Map<String, Object>> receiverInfo = (List<Map<String, Object>>) BeanUtil.getProperty(profitSharing, "receivers");
        if (receiverInfo != null) {
            for (Map<String, Object> m : receiverInfo) {
                if (BeanUtil.getPropString(m, DaoConstants.ID).equals(receiverId)) {
                    ratio = BeanUtil.getPropString(m, "ratio");
                }
            }
            result.put("ratio", ratio);
        }
        return result;
    }

    @Override
    public Map createExportSharingBookTask(Map request) {
        String exportType = BeanUtil.getPropString(request, "export_type");
        if(SHARING_BOOK_EXPORT_TYPE_IN.equals(exportType)){
            Map merchant = merchantService.getMerchant(getSessionMerchantId());
            LOGGER.info("入账商户id:{}",getSessionMerchantId());
            LOGGER.info("入账商户:{}",merchant);
            request.put("receiver_merchant_sn", BeanUtil.getPropString(merchant, Merchant.SN));
        }else if(SHARING_BOOK_EXPORT_TYPE_OUT.equals(exportType)){
            request.put(KEY_MERCHANT_ID, getSessionMerchantId());
            LOGGER.info("出账商户id:{}",KEY_MERCHANT_ID);
        }else {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "不合法的导出类型字段export_type");
        }
        return mspOrderStatementService.createExportSharingBookTask(request);
    }

    @Override
    public void afterPropertiesSet() {
        initRemoteSharingBookClient();
        initRemoteSharingTransactionClient();
        initRemoteSharingConfigClient();
        initRemoteReceiverServiceClient();
    }

    private void initRemoteSharingBookClient(){
        JsonRpcHttpClient client;
        try {
            client = new JsonRpcHttpClient(new URL(profitShareUrl + "rpc/sharingBook"));
        } catch (MalformedURLException e) {
            throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        sharingBookService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                RemoteSharingBookService.class,
                client);
    }

    private void initRemoteSharingTransactionClient(){
        JsonRpcHttpClient client;
        try {
            client = new JsonRpcHttpClient(new URL(profitShareUrl + "rpc/sharingTransaction"));
        } catch (MalformedURLException e) {
            throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        sharingTransactionService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                RemoteSharingTransactionService.class,
                client);
    }

    private void initRemoteSharingConfigClient(){
        JsonRpcHttpClient client;
        try {
            client = new JsonRpcHttpClient(new URL(profitShareUrl + "rpc/sharingConfig"));
        } catch (MalformedURLException e) {
            throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        sharingConfigService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                RemoteSharingConfigService.class,
                client);
    }

    private void initRemoteReceiverServiceClient(){
        JsonRpcHttpClient client;
        try {
            client = new JsonRpcHttpClient(new URL(profitShareUrl + "rpc/receiver"));
        } catch (MalformedURLException e) {
            throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        receiverService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                RemoteReceiverService.class,
                client);
    }


    private Map<String,Object> getPagination(Map map) {
        Map<String,Object> pageInfo = new HashMap<>();
        pageInfo.put(CommonConstant.PAGE,BeanUtil.getPropInt(map, CommonConstant.PAGE, 1));
        pageInfo.put(CommonConstant.PAGE_SIZE,BeanUtil.getPropInt(map, CommonConstant.PAGE_SIZE, 10));
        if (map.containsKey("start_time")) {
            pageInfo.put("start_timestamp",BeanUtil.getPropLong(map, "start_time"));
        }
        if (map.containsKey("end_time")) {
            pageInfo.put("end_timestamp",BeanUtil.getPropLong(map, "end_time"));
        }
        pageInfo.put("order_by", Collections.singletonList(new HashMap<String, Object>() {{
            put("field", "ctime");
            put("order", "-1");
        }}));
        return pageInfo;
    }
}
