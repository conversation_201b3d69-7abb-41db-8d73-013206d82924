package com.wosai.upay.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wosai.business.audit.model.AuditConstant;
import com.wosai.business.audit.model.AuditRecord;
import com.wosai.data.util.CollectionUtil;
import com.wosai.operation.bean.MerchantInvoiceConfig;
import com.wosai.operation.bean.MerchantInvoiceOrder;
import com.wosai.operation.service.MerchantInvoiceService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.CommonUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Created by chenyu on 2018/5/30.
 */
@SuppressWarnings("unchecked")
@Service
public class MspMerchantInvoiceServiceImpl extends BaseService implements MspMerchantInvoiceService{

    @Autowired
    private MerchantInvoiceService merchantInvoiceService;

    @Override
    public Map findOrders(Map request) {
        if ("merchant".equals(getSessionObjectType())) {
            request.put(MerchantInvoiceOrder.MERCHANT_ID, getSessionMerchantId());
        } else {
            request.put(MerchantInvoiceOrder.GROUP_ID, getSessionGroupId());
        }
        //拿到返回的结果后，取出 records 来拿到开票状态为未提交的最小的月份，根据 order_month
        ListResult result = merchantInvoiceService.findMerchantInvoiceOrders(PageInfoUtil.extractPageInfo(request), request);
        List<Map> records = result.getRecords();
        List orderMonths = merchantInvoiceService.getOrderMonthsOfUnsubmitOrder(records);
        //把拿到的最早的月份，放到结果里面，用 Map 包一下 ListResult 结果，加一个 extra key 放 earliestMonth
        return CollectionUtil.hashMap("total", result.getTotal(),
                                                        "records", result.getRecords(),
                                                            "extra", JSONArray.parseArray(JSON.toJSONString(orderMonths)));
    }

    @Override
    public void submit(Map request) {
//        获取当前的开票配置信息
        Map auditInfo = getMerchantInvoiceConfig(null);
        int status = MapUtils.getIntValue(auditInfo, "audit_record_status", -1);
        if (status == MerchantInvoiceConfig.AUDIT_STATUS_WAIT) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "审核中无法进行重新提交!");
        }
        request.put(MerchantInvoiceOrder.MERCHANT_ID, getObjectId());
        request.put(MerchantInvoiceOrder.APPLY_USER_ID, getSessionUserId());
        request.put(MerchantInvoiceOrder.APPLY_USER_NAME, getSessionAccountUsername());
        request.put(AuditRecord.APPLY_SYSTEM, AuditConstant.APPLY_SYSTEM_MSP);
        merchantInvoiceService.submit(request);
    }

    @Override
    public void updateMerchantInvoiceConfig(Map request) {
        merchantInvoiceService.updateMerchantInvoiceConfig(request);
        if (!"merchant".equals(getSessionObjectType())) {
            List<Map> configs = merchantInvoiceService.findSubMerchant(getSessionGroupId(), MerchantInvoiceConfig.IS_BIND_TRUE);
            if (!CollectionUtils.isEmpty(configs)) {
                for (Map config : configs) {
                    merchantInvoiceService.updateMerchantInvoiceConfig(CollectionUtil.hashMap(
                            ConstantUtil.KEY_ID, MapUtils.getString(config, ConstantUtil.KEY_ID),
                            MerchantInvoiceConfig.MODE, MapUtils.getString(request, MerchantInvoiceConfig.MODE),
                            MerchantInvoiceConfig.ADDRESS, MapUtils.getString(request, MerchantInvoiceConfig.ADDRESS),
                            MerchantInvoiceConfig.PROVINCE, MapUtils.getString(request, MerchantInvoiceConfig.PROVINCE),
                            MerchantInvoiceConfig.CITY, MapUtils.getString(request, MerchantInvoiceConfig.CITY),
                            MerchantInvoiceConfig.DISTRICT, MapUtils.getString(request, MerchantInvoiceConfig.DISTRICT),
                            MerchantInvoiceConfig.CONTACT_NAME, MapUtils.getString(request, MerchantInvoiceConfig.CONTACT_NAME),
                            MerchantInvoiceConfig.CONTACT_CELLPHONE, MapUtils.getString(request, MerchantInvoiceConfig.CONTACT_CELLPHONE),
                            MerchantInvoiceConfig.SEND_UNDER_TWO_HUNDRED, MapUtils.getString(request, MerchantInvoiceConfig.SEND_UNDER_TWO_HUNDRED)
                    ));
                }
            }
        }
    }

    @Override
    public Map getMerchantInvoiceConfig(Map request) {
        return merchantInvoiceService.getConfigByMerchantId(getObjectId());
    }

    @Override
    public Map getAuditDetail(Map request) {
        return merchantInvoiceService.getSummaryConfig(getObjectId());
    }


    @Override
    public void mergeOrder(Map request) {
        List<String> ids = CommonUtil.convertToList(MapUtils.getObject(request, "ids"));
        if (CollectionUtils.isEmpty(ids)) {
            throw new UpayException(500, "订单不能为空!");
        }
        //用merchantId查审核状态
        String merchantIdStr = getSessionMerchantId();
        Map config = merchantInvoiceService.getSummaryConfig(merchantIdStr);
        int auditRecordStatus = MapUtils.getIntValue(config, MerchantInvoiceConfig.AUDIT_STATUS);
        if (auditRecordStatus !=  MerchantInvoiceConfig.AUDIT_STATUS_PASS) {
            throw new UpayException(500, "开票信息尚未通过审核，不可手动提交合并开票申请!");
        }


        request.put(MerchantInvoiceOrder.APPLY_USER_ID, getSessionUserId());
        request.put(MerchantInvoiceOrder.APPLY_USER_NAME, getSessionAccountUsername());
        if (ids.size() == 1) {
            request.put(ConstantUtil.KEY_ID,ids.get(0));
            confirmOrders(request);
        } else {
            merchantInvoiceService.mergeOrder(request);
        }
    }


    @Override
    public void confirmOrders(Map request) {
        merchantInvoiceService.confirmOrders(request);
    }



}
