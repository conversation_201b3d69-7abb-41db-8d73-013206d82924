package com.wosai.upay.service;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.enums.UinvoiceUrlEnum;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.helper.AbstractJsonRpcRequestProxy;
import com.wosai.upay.helper.IJsonRpcRequestProxy;
import com.wosai.upay.util.HttpRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by j5land on 18/4/10.
 */
@Service
public class MspUinvoiceServiceImpl extends AbstractJsonRpcRequestProxy implements MspUinvoiceService,IJsonRpcRequestProxy {

    private Logger logger = LoggerFactory.getLogger(MspUinvoiceServiceImpl.class);

    @Value("${uinvoice.basic.proxy.url}")
    private String basicUrl;

    @Value("${uinvoice.core.proxy.url}")
    private String coreUrl;

    @Override
    public void addProxyRequestParams(Map request) {
        if (request == null) {
            request = CollectionUtil.hashMap();
        }
        Map session_user = (Map) HttpRequestUtil.getSession().getAttribute(GroupUserLoginService.SESSION_USER);
        if (WosaiMapUtils.isEmpty(session_user)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "缺少用户登录信息，请登录");
        }
        String user_id = BeanUtil.getPropString(session_user, "user_id", "");
        String group_id = BeanUtil.getPropString(session_user, "group_id", "");
        String merchant_sn = BeanUtil.getPropString(session_user, "merchant_sn", "");
        //普通商户
        String merchant_id = null;
        if (StringUtil.empty(group_id)) {
            merchant_id = BeanUtil.getPropString(session_user, ConstantUtil.KEY_MERCHANT_ID);
        }
        Map proxy_params = CollectionUtil.hashMap(
                "merchant_id", merchant_id,
                "group_id", group_id,
                "user_id", user_id,
                "merchant_sn", merchant_sn
        );
        logger.info("proxy_params = {}", proxy_params);
        Set<String> keys = proxy_params.keySet();
        Object object = BeanUtil.getProperty(request, "params");
        if (object !=null && object instanceof List){
            List<Map> list = (List<Map>) BeanUtil.getProperty(request, "params");
            if (WosaiCollectionUtils.isNotEmpty(list)){
                Map paramsMap = list.get(0);
                for (String key : keys) {
                    paramsMap.put(key, BeanUtil.getProperty(proxy_params, key));
                }
            }
        }
    }

    @Override
    public Map<String, Object> proxy(UinvoiceUrlEnum uinvoiceUrlEnum, String methodName, Map<String, Object> request) {
        String url = basicUrl;
        if (uinvoiceUrlEnum == UinvoiceUrlEnum.CORE){
            url = coreUrl;
        }
        Map<String, Object> requestParamsAndMethod = CollectionUtil.hashMap();
        requestParamsAndMethod.put(CONSTANT_TEXT_JSONRPC, CONSTANT_TEXT_20);
        requestParamsAndMethod.put(CONSTANT_TEXT_METHOD, methodName);
        requestParamsAndMethod.put(CONSTANT_TEXT_ID, 0);
        List<Object> list = new ArrayList<>();
        list.add(request);
        requestParamsAndMethod.put(CONSTANT_TEXT_PARAMS, list);
        return processAndBuildRequest(url, requestParamsAndMethod);
    }
}
