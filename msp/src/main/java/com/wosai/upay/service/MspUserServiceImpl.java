package com.wosai.upay.service;

import com.wosai.app.backend.api.service.IMerchantService;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.common.exception.CommonAccessDeniedException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.department.Department;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.model.user.MerchantUserStoreAuth;
import com.wosai.upay.core.model.user.SpecialAuthWhitelist;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.user.api.service.MspRefundTerminalService;
import com.wosai.upay.user.api.service.SpecialAuthWhitelistService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.CommonUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by lijunjie on 16/5/12.
 */

@Service
public class MspUserServiceImpl extends BaseService implements MspUserService {
    private Logger logger = LoggerFactory.getLogger(MspUserServiceImpl.class);


    @Autowired
    private UserService userService;
    @Autowired
    private SpecialAuthWhitelistService specialAuthWhitelistService;
    @Autowired
    private IMerchantService iMerchantService;
    @Autowired
    private CommonLoginService commonLoginService;
    @Autowired
    private MspBusinessLogService mspBusinessLogService;
    @Autowired
    private MspRefundTerminalService mspRefundTerminalService;
    @Autowired
    private StoreService storeService;

    /**
     * 用户列表
     *
     * @param request
     * @return ListResult
     */
    @Override
    public ListResult findMerchantUsers(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        String merchantId = getSessionMerchantId();
        request.put("merchant_id", merchantId);
        return userService.findMerchantUsers(pageInfo, request);
    }

    @Override
    public boolean alreadyRelatedMerchantUser(Map request) {
        String accountId = BeanUtil.getPropString(request, MerchantUser.ACCOUNT_ID, "");
        if (accountId.length() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "手机号已使用，请更换其他手机号");
        }
        return userService.findMerchantUsers(new PageInfo(1, 1), request).getRecords().size() > 0;
    }

    /**
     * 创建用户
     *
     * @param request
     * @return
     */
    @Override
    public Map createMerchantUser(Map request) {
        long permission_msp_refund = BeanUtil.getPropLong(request, "permission_msp_refund");
        request.remove("permission_msp_refund");
        request.put("merchant_id", getSessionMerchantId());
        String storeIds = BeanUtil.getPropString(request, "auth_store_ids");
        if (!StringUtils.isEmpty(storeIds)) {
            String[] storeIdArr = storeIds.split(",");
            for (String storeId : storeIdArr) {
                Map store = storeService.getStoreByStoreId(storeId);
                if (store != null) {
                    String storeMerchantId = (String) store.get("merchant_id");
                    if (!(getSessionMerchantId().equalsIgnoreCase(storeMerchantId))) {
                        logger.error("sessionMerchantId :{}", getSessionMerchantId());
                        throw new CommonAccessDeniedException("该商户无修改权限");
                    }
                }
            }
        }
        Map user = userService.createMerchantUser(request);
        String userId = BeanUtil.getPropString(user, DaoConstants.ID);
        //todo check super_admin permission
        //开通管理员的商户后台退款权限
        if (SpecialAuthWhitelist.MSP_REFUND_PERMISSION_OPEN == permission_msp_refund) {
            specialAuthWhitelistService.createSpecialAuthWhitelist(CollectionUtil.hashMap(
                    SpecialAuthWhitelist.AUTH_TYPE, SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND,
                    SpecialAuthWhitelist.OBJECT_ID, userId,
                    SpecialAuthWhitelist.OBJECT_TYPE, SpecialAuthWhitelist.OBJECT_TYPE_MERCHANT_USER
            ));
        }
        user.put("permission_msp_refund", permission_msp_refund);
        return user;
    }

    @Override
    public Map getMerchantUser(Map request) {
        Map user = userService.getMerchantUser(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
        return extendMerchantUserMspRefundPermission(user);
    }

    @Override
    public Map updateMerchantUser(Map request) {
        String storeIds = BeanUtil.getPropString(request, "auth_store_ids");
        Map resMerchantUser = userService.getMerchantUser(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
        String merchantId = (String) resMerchantUser.get("merchant_id");
        if (!StringUtils.isEmpty(storeIds)) {
            String[] storeIdArr = storeIds.split(",");
            for (String storeId : storeIdArr) {
                Map store = storeService.getStoreByStoreId(storeId);
                if (store != null) {
                    String storeMerchantId = (String) store.get("merchant_id");
                    for (String sessionMerchantId : CommonUtil.getSessionMerchantIds()) {
                        if ((!merchantId.equalsIgnoreCase(sessionMerchantId)) || (!merchantId.equalsIgnoreCase(storeMerchantId))) {
                            logger.error("sessionMerchantId :{}", sessionMerchantId);
                            throw new CommonAccessDeniedException("该商户无修改权限");
                        }
                    }
                }
            }
        }
        if (resMerchantUser != null) {
            String merchant_id = resMerchantUser.get("merchant_id") + "";
            for (String sessionMerchantId : CommonUtil.getSessionMerchantIds()) {
                if (!sessionMerchantId.equals(merchant_id)) {
                    logger.error("sessionMerchantId :{}", sessionMerchantId);
                    throw new CommonAccessDeniedException("该商户无修改权限");
                }
            }
        }

        return userService.updateMerchantUser(request);
    }

    /**
     * 通用检查是否有对账户操作的权限
     *
     * @param merchantUserId
     */
    private void checkPermissionOpMerchantUser(String merchantUserId) {
        if (StringUtil.empty(merchantUserId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "账户不存在");
        }
        ListResult result = findMerchantUsers(CollectionUtil.hashMap(DaoConstants.ID, merchantUserId));
        if (result == null || result.getRecords() == null || result.getRecords().size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "账户不存在");
        }
        Map sessionInfo = getSessionInfos(null);
        List<String> merchantIds = (List<String>) BeanUtil.getProperty(sessionInfo, "merchant_ids");
        if (merchantIds == null || !merchantIds.contains(BeanUtil.getPropString(result.getRecords().get(0), MerchantUser.MERCHANT_ID))) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "没有对该账户操作的权限");
        }
    }

    /**
     * 当前账户是否有退款权限
     */
    private void checkPermissionHasRefund() {
        Map sessionInfos = getSessionInfos(null);

        //1.判断当前主体(集团、商户)是否有权限
        String objectId = BeanUtil.getPropString(sessionInfos, "object_id");
        Map merchantAuth = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND,
                objectId
        );
        if (merchantAuth == null || merchantAuth.isEmpty()) {
            commonLoginService.logout(null);
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "登录账户暂未开通商户后台退款权限");
        }

        //2.判断当前账户是否有权限(超级管理员默认有)
        //超级管理员默认有商户后台退款权限
        if (MerchantUser.ROLE_SUPER_ADMIN.equals(getSessionUserRole())) {
            return;
        }
        String userId = getSessionUserId();
        Map permission = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, userId);
        if (permission == null || permission.isEmpty()) {
            commonLoginService.logout(null);
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "用户暂未开通商户后台退款权限");
        }
    }

    @Override
    public void updateMerchantUserMspRefundPermission(Map request) {
        String userId = BeanUtil.getPropString(request, "user_id");
        //检查权限
        checkPermissionOpMerchantUser(userId);

        //获取账户信息
        ListResult result = findMerchantUsers(CollectionUtil.hashMap(DaoConstants.ID, userId));
        Map user = result.getRecords().get(0);
        if (MerchantUser.ROLE_SUPER_ADMIN.equals(BeanUtil.getPropString(user, MerchantUser.ROLE))) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "超级管理员权限不可修改");
        }
        String objectName = BeanUtil.getPropString(user, "account_name");

        //检查操作人是否有操作退款权限
        checkPermissionHasRefund();

        //是否有权限变动
        long permissionOld = getMerchantUserMspRefundPermission(userId);
        int permission = BeanUtil.getPropInt(request, "permission_msp_refund");
        if (!SpecialAuthWhitelist.MSP_REFUND_PERMISSIONS.contains(permission)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "permission_msp_refund参数不支持!");
        }
        if (permission == permissionOld) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "无效或重复操作!");
        }

        Map mspRefundPermissionOld = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, userId
        );
        if (mspRefundPermissionOld != null) {
            mspRefundPermissionOld.put("object_name", objectName);
        }

        String mspRefundPermissionId = BeanUtil.getPropString(mspRefundPermissionOld, DaoConstants.ID);
        if (SpecialAuthWhitelist.MSP_REFUND_PERMISSION_CLOSE == permission && !StringUtil.empty(mspRefundPermissionId)) {
            specialAuthWhitelistService.deleteSpecialAuthWhitelist(mspRefundPermissionId);
        } else if (SpecialAuthWhitelist.MSP_REFUND_PERMISSION_OPEN == permission) {
            specialAuthWhitelistService.createSpecialAuthWhitelist(
                    CollectionUtil.hashMap(
                            SpecialAuthWhitelist.AUTH_TYPE, SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND,
                            SpecialAuthWhitelist.OBJECT_ID, userId,
                            SpecialAuthWhitelist.OBJECT_TYPE, SpecialAuthWhitelist.OBJECT_TYPE_MERCHANT_USER
                    )
            );
        }
        Map mspRefundPermissionNew = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, userId
        );
        if (mspRefundPermissionNew != null) {
            mspRefundPermissionNew.put("object_name", objectName);
        }

        //保存业务日志
        mspBusinessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, mspRefundPermissionOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, mspRefundPermissionNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "special_auth_whitelist",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "special_auth_whitelist",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "object_id",
                        BizOpLog.REMARK, "",
                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000059"
                )
        ));
    }

    @Override
    public void disableMerchantUser(Map request) {
        userService.disableMerchantUser(BeanUtil.getPropString(request, MerchantUserStoreAuth.MERCHANT_USER_ID));
    }

    @Override
    public void deleteMerchantUser(Map request) {
        Map merchantUser = userService.getMerchantUser(BeanUtil.getPropString(request, MerchantUserStoreAuth.MERCHANT_USER_ID));
        if (merchantUser != null) {
            String merchant_id = merchantUser.get("merchant_id") + "";
            for (String sessionMerchantId : CommonUtil.getSessionMerchantIds()) {
                if (!sessionMerchantId.equals(merchant_id)) {
                    logger.error("sessionMerchantId :{}", sessionMerchantId);
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "操作的商户id不正确，删除失败");
                }
            }
        }
        userService.deleteMerchantUser(BeanUtil.getPropString(request, MerchantUserStoreAuth.MERCHANT_USER_ID));
    }

    @Override
    public List<Map> saveMerchantUserStoreAuth(Map request) {
        return userService.saveMerchantUserStoreAuth(BeanUtil.getPropString(request, MerchantUserStoreAuth.MERCHANT_USER_ID), BeanUtil.getPropString(request, "store_ids"));
    }

    @Override
    public List<Map> getMerchantUserStoreAuths(Map request) {
        request.put("merchant_id", getSessionMerchantId());
        return userService.getMerchantUserStoreAuths(request);
    }

    @Override
    public ListResult findMerchantUserStoreAuths(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        request.put("merchant_id", getSessionMerchantId());
        return userService.findMerchantUserStoreAuths(pageInfo, request);
    }

    @Override
    public List<Map> saveMerchantUserDepartmentAuth(Map request) {
        Map merchantUser = userService.getMerchantUser(BeanUtil.getPropString(request, MerchantUserStoreAuth.MERCHANT_USER_ID));
        String role = BeanUtil.getPropString(merchantUser, MerchantUser.ROLE);
        if (StringUtils.equals(MspUserLoginService.ROLE_DEPARTMENT_MANAGER, role) || StringUtil.empty(role)) {
            return userService.saveMerchantUserDepartmentAuth(BeanUtil.getPropString(request, MerchantUserStoreAuth.MERCHANT_USER_ID), BeanUtil.getPropString(request, "department_ids"));
        }
        throw new UpayException(UpayException.CODE_ACCESS_DENIED, "该账号无法关联部门");
    }

    @Override
    public List<Map> getMerchantUserDepartmentAuths(Map request) {
        request.put("merchant_id", getSessionMerchantId());
        return userService.getMerchantUserDepartmentAuths(request);
    }

    @Override
    public List<Map> getMerchantUserDepartmentStoreAuths(Map request) {
        request.put("merchant_id", getSessionMerchantId());
        request.put("merchant_user_id", getSessionUserId());
        List<Map> departmentAuths = userService.getMerchantUserDepartmentAuths(request);
        List<String> departmentIds = new ArrayList<>();
        for (Map departmentAuth : departmentAuths) {
            departmentIds.add(BeanUtil.getPropString(departmentAuth, Department.DEPARTMENT_ID));
        }
        return departmentService.listStoresByDepartment(getSessionMerchantId(), departmentIds);
    }

    @Override
    public ListResult findMerchantUserDepartmentAuths(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        request.put("merchant_id", getSessionMerchantId());
        return userService.findMerchantUserDepartmentAuths(pageInfo, request);
    }

    @Override
    public void updateAccountPassword(Map request) {
        Map account = userService.getAccount(BeanUtil.getPropString(request, DaoConstants.ID));
        String password = BeanUtil.getPropString(request, "password");
        String username = BeanUtil.getPropString(account, "username");
        userService.updateAccountPassword(username, password, false);
    }


    private long getMerchantUserMspRefundPermission(String userId) {
        if (StringUtil.empty(userId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "集团id不可为空");
        }
        Map permission = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, userId
        );
        long objectType = BeanUtil.getPropLong(permission, SpecialAuthWhitelist.OBJECT_TYPE);
        if (permission != null &&
                objectType == SpecialAuthWhitelist.OBJECT_TYPE_MERCHANT_USER) {
            return SpecialAuthWhitelist.MSP_REFUND_PERMISSION_OPEN;
        } else {
            return SpecialAuthWhitelist.MSP_REFUND_PERMISSION_CLOSE;
        }

    }

    private Map extendMerchantUserMspRefundPermission(Map user) {
        //超级管理员默认有商户后台退款权限
        if (MerchantUser.ROLE_SUPER_ADMIN.equals(BeanUtil.getPropString(user, MerchantUser.ROLE))) {
            user.put("permission_msp_refund", SpecialAuthWhitelist.MSP_REFUND_PERMISSION_OPEN);
            return user;
        }
        String userId = BeanUtil.getPropString(user, DaoConstants.ID);
        long permission_msp_refund = getMerchantUserMspRefundPermission(userId);
        user.put("permission_msp_refund", permission_msp_refund);
        return user;
    }


    @Override
    public void updateMerchantManagerPassword(Map request) {
        checkMerchantManagerPasswordEditPermision();
        String merchantId = getSessionMerchantId();
        String passwordOld = BeanUtil.getPropString(request, "password_old");
        String passwordNew = BeanUtil.getPropString(request, "password_new");
        iMerchantService.updateMerchantManagerPassword(merchantId, passwordOld, passwordNew);
    }

    @Override
    public void resetMerchantManagerPassword(Map request) {
        checkMerchantManagerPasswordEditPermision();
        String merchantId = getSessionMerchantId();
        String smsCode = BeanUtil.getPropString(request, "sms_code");
        String passwordNew = BeanUtil.getPropString(request, "password_new");

        String cellphone = getSessionAccountUsername();

        commonLoginService.verifySmsCodeByCaptcha(CollectionUtil.hashMap(
                "cellphone", cellphone,
                "sms_code", smsCode
        ));
        iMerchantService.updateMerchantManagerPasswordWithNew(merchantId, passwordNew);
    }

    private void checkMerchantManagerPasswordEditPermision() {
        String merchantId = getSessionMerchantId();

        Map merchantAuth = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND,
                merchantId
        );
        if (SpecialAuthWhitelist.OBJECT_TYPE_MERCHANT != BeanUtil.getPropInt(merchantAuth, SpecialAuthWhitelist.OBJECT_TYPE)) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "商户暂未开通商户后台退款权限");
        }

        //超级管理员默认有商户后台退款权限
        if (!MerchantUser.ROLE_SUPER_ADMIN.equals(getSessionUserRole())) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "用户没有修改商户管理密码的权限");
        }

    }

}
