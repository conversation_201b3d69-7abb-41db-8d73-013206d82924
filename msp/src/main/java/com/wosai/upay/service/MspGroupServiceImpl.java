package com.wosai.upay.service;

import com.alibaba.fastjson.JSONObject;
import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.googlecode.jsonrpc4j.ProxyUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.bean.Transaction;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Group;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.model.user.GroupUser;
import com.wosai.upay.core.model.user.GroupUserMerchantAuth;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.service.remote.OrderService;
import com.wosai.upay.util.GroupUpayStatement;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.SheetUtil;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * .
 *
 * <AUTHOR>
 */
public class MspGroupServiceImpl extends GroupBaseService implements MspGroupService {

    Logger logger = LoggerFactory.getLogger(MspGroupServiceImpl.class);

    @Autowired
    GroupService groupService;
    @Autowired
    UserService userService;
    @Autowired
    LogService logService;
    @Autowired
    OssFileUploader ossFileUploader;
    @Autowired
    private TranslateService translateService;

    @Autowired
    private UpayOrderService upayOrderService;

    private OrderService orderService; //backend upay项目提供的接口

    public MspGroupServiceImpl(String backendUpayUrl) {
        JsonRpcHttpClient client = null;
        try {
            client = new JsonRpcHttpClient(new URL(backendUpayUrl + "rpc/upayorder"));
        } catch (MalformedURLException e) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        this.orderService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                OrderService.class,
                client);
    }

    @Override
    public Map getGroup(Map request) {
        return groupService.getGroup(getSessionGroupId());
    }

    @Override
    public Map getGroupUser(Map request) {
        return groupService.getGroupUser(getSessionUserId());
    }

    @Override
    public ListResult findGroupUsers(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        request.put(GroupUser.GROUP_ID, getSessionGroupId());
        return groupService.findGroupUsers(pageInfo, request);
    }

    @Override
    public List<Map> getGroupUserMerchantAuths(Map request) {
        request.put(GroupUserMerchantAuth.GROUP_ID, getSessionGroupId());
        request.put(GroupUserMerchantAuth.GROUP_USER_ID, getSessionUserId());
        return groupService.getGroupUserMerchantAuths(request);
    }

    @Override
    public ListResult findGroupUserMerchantAuths(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        request.put(GroupUserMerchantAuth.GROUP_ID, getSessionGroupId());
        request.put(GroupUserMerchantAuth.GROUP_USER_ID, getSessionUserId());
        return groupService.findGroupUserMerchantAuths(pageInfo, request);
    }

    public final int MAX_EXPORT_THREAD_COUNT = 2;
    private ThreadPoolExecutor exportExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(MAX_EXPORT_THREAD_COUNT);

    @Override
    public Map submitExportGroupBillTask(final Map request) {
        try {
            long start = new SimpleDateFormat("yyyy-MM-dd").parse((String) request.get(ConstantUtil.KEY_DATE_START)).getTime();
            long end = new SimpleDateFormat("yyyy-MM-dd").parse((String) request.get(ConstantUtil.KEY_DATE_END)).getTime();
            if (start > end) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "结束日期应不小于开始日期");
            }
            if ((end - start) > 30 * 24 * 3600 * 1000L) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "日期跨度不能超过1个月");
            }
        } catch (ParseException e) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "开始或结束日期格式不正确");
        }

        if (exportExecutor.getActiveCount() >= MAX_EXPORT_THREAD_COUNT) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "系统导出任务繁忙，请稍后重试");
        }
        String userId = getSessionUserId();
        request.put("group_id", getSessionGroupId());
        request.put("user_id", userId);
        Map taskApplyLog = logService.createTaskApplyLog(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, 1,
                TaskApplyLog.APPLY_SYSTEM, 4,
                TaskApplyLog.APPLY_DATE, new java.sql.Date(new Date().getTime()),
                TaskApplyLog.PAYLOAD, request,
                TaskApplyLog.APPLY_STATUS, 0,
                TaskApplyLog.USER_ID, getSessionUserId()
        ));
        final String taskId = BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID);
        request.put("task_id", taskId);
        try {
            Runnable runnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        exportGroupBillTask(request);
                    } catch (Exception e) {
                        logger.error("exportGroupBillTask run error", e);
                        updateTaskApplyLogStatus(taskId, 3, null);
                    }
                }
            };
            Future future = exportExecutor.submit(runnable);
        } catch (Exception e) {
            logger.error("submitExportGroupBillTask interrupted", e);
            updateTaskApplyLogStatus(taskId, 3, null);
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, UpayException.getCodeDesc(UpayException.CODE_UNKNOWN_ERROR));
        }
        HttpRequestUtil.getSession().setAttribute(SESSION_EXPORT_GROUP_BILL_TASK_ID_KEY, taskId);
        return CollectionUtil.hashMap("taskId", taskId);
    }

    @Override
    public Map submitExportGroupBillTask_csv(Map request) {
        try {
            long start = new SimpleDateFormat("yyyy-MM-dd").parse((String) request.get(ConstantUtil.KEY_DATE_START)).getTime();
            long end = new SimpleDateFormat("yyyy-MM-dd").parse((String) request.get(ConstantUtil.KEY_DATE_END)).getTime();
            if (start > end) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "结束日期应不小于开始日期");
            }
            if ((end - start) > 30 * 24 * 3600 * 1000L) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "日期跨度不能超过1个月");
            }
        } catch (ParseException e) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "开始或结束日期格式不正确");
        }

        String userId = getSessionUserId();
        String groupId = getSessionGroupId();
        Map group = groupService.getGroup(groupId);
        List<Map> merchantAuths = groupService.getGroupUserMerchantAuths(CollectionUtil.hashMap(GroupUserMerchantAuth.GROUP_ID, groupId, GroupUserMerchantAuth.GROUP_USER_ID, userId));
        request.put("group_id", groupId);
        request.put("user_id", userId);
        request.put("group", group);
        request.put("merchantAuths", merchantAuths);
        //调用导出任务
        try {
            request = orderService.submitExportGroupBillTask_csv(request);
        } catch (Exception e) {
            throw new UpayException(UpayException.CODE_IO_EXCEPTION, e.getMessage());
        }

        //如果任务创建成功将任务id放入session中
        if (request != null && request.containsKey("taskId")){
            HttpRequestUtil.getSession().setAttribute("SESSION_EXPORT_GROUP_BILL_TASK_ID_KEY", BeanUtil.getPropString(request,"taskId"));
        }
        return request;
    }

    private void exportGroupBillTask(Map request) throws ParseException, IOException {
        String taskId = BeanUtil.getPropString(request, "task_id");
        logger.trace("exportGroupBillTask {} start...", taskId);
        updateTaskApplyLogStatus(taskId, 1, null); // 执行中

        String groupId = BeanUtil.getPropString(request, "group_id");
        String userId = BeanUtil.getPropString(request, "user_id");
        String dateStart = BeanUtil.getPropString(request, "date_start") + " 00:00:00";
        String dateEnd = BeanUtil.getPropString(request, "date_end") + " 23:59:59";

        Map group = groupService.getGroup(groupId);

        Date start = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse(dateStart + ".000");
        Date end = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse(dateEnd + ".999");

        // 集团信息
        Map context = CollectionUtil.hashMap(
                "group_id", groupId,
                "start", start, "end", end,
                "group_sn", BeanUtil.getPropString(group, Group.SN),
                "group_name", BeanUtil.getPropString(group, Group.NAME)
        );

        PageInfo pageInfo = new PageInfo(1, Integer.MAX_VALUE, start.getTime(), end.getTime());
        Map queryFilter = CollectionUtil.hashMap(
                "status", Arrays.asList(Transaction.STATUS_SUCCESS),
                "weixin_free_recharge_mch", BeanUtil.getPropBoolean(request, "weixin_free_recharge_mch", false) // 是否微信免充值商户
        );
        List<Map> merchantAuths = groupService.getGroupUserMerchantAuths(CollectionUtil.hashMap(GroupUserMerchantAuth.GROUP_ID, groupId, GroupUserMerchantAuth.GROUP_USER_ID, userId));
        long total_numbers = 0;
        PageInfo pageInfoCount = new PageInfo(1, 1, start.getTime(), end.getTime());
        logger.info("merchantAuths[{}]",merchantAuths);
        for (Map map : merchantAuths) {
            String merchantId = BeanUtil.getPropString(map, GroupUserMerchantAuth.MERCHANT_ID);

            long cnum = upayOrderService.getTransactionList(merchantId, null, pageInfoCount, queryFilter).getTotal();
            total_numbers = total_numbers +cnum;
            logger.info("single_log merchant_id[{}],pageInfoCount[{}],queryFilter[{}],totalNum[{}],cnum[{}]",merchantId,pageInfoCount,queryFilter,total_numbers,cnum);
            if (total_numbers > 50000){
                logger.info("create excel limmit 50000 " + taskId);
                updateTaskApplyLogStatus(taskId, 3, "数据量超过5w条，请缩小时间范围或使用大容量对账单"); // 执行失败
                logger.trace("exportGroupBillTask {} end...", taskId);
                return;
            }
        }

        List result = new ArrayList();
        for (Map map : merchantAuths) {
            String merchantId = BeanUtil.getPropString(map, GroupUserMerchantAuth.MERCHANT_ID);
            List<Map> list = upayOrderService.getTransactionList(merchantId, null, pageInfo, queryFilter).getRecords();
            translateService.translateOrderOperatorNames(list, merchantId);
            result.addAll(list);
        }

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet summarySheet = workbook.createSheet("账单汇总");
        StringBuffer summary = GroupUpayStatement.buildUpayStatementSummary(context, result);
        SheetUtil util = new SheetUtil(summarySheet);
        for (String line : summary.toString().split("\n")) {
            util.appendRow(Arrays.asList(line.split("\t")));
        }
        util.mergeCell(2, 0, 2, 3);
        util.setCellAlignment(2, 0, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER);
        util.mergeCell(6, 0, 6, 10);
        util.setCellAlignment(6, 0, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER);
        StringBuffer details = GroupUpayStatement.buildUpayStatementDetail(context, result);
        HSSFSheet detailSheet = workbook.createSheet("账单明细");
        util = new SheetUtil(detailSheet);
        for (String line : details.toString().split("\n")) {
            util.appendRow(Arrays.asList(line.split("\t")));
        }
        logger.info("create excel success " + taskId);
        String resultUrl = uploadStatementToOSS("xls", workbook, userId);
        logger.info("uploadStatementToOSS  success " + resultUrl + " " + taskId);
        updateTaskApplyLogStatus(taskId, 2, resultUrl); // 执行成功
        logger.trace("exportGroupBillTask {} end...", taskId);
    }

    private String uploadStatementToOSS(String ext, HSSFWorkbook workbook, String userId) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        byte[] content = bos.toByteArray();
        bos.close();
        String fullName = "portal/statement/" + userId + "/" + new Date().getTime() + "." + ext;
        ByteArrayInputStream bais = new ByteArrayInputStream(content);
        ossFileUploader.uploadIfNotExists(OssFileUploader.IMAGE_BUCKET_NAME, fullName, bais, content.length);
        return fullName;
    }

    /**
     * 上传file
     * @param ext
     * @param sb
     * @param userId
     * @return
     * @throws IOException
     */
    private String uploadStatementToOSS(String ext, StringBuffer sb ,String userId,String fileName) throws IOException{
        byte[] content = sb.toString().getBytes("GB2312");
        String fullName = "portal/statement/" + userId + "/" + fileName + "." + ext;
        ByteArrayInputStream bais = new ByteArrayInputStream(content);
        //todo 开个保存报表的bucket
        ossFileUploader.uploadIfNotExists(OssFileUploader.IMAGE_BUCKET_NAME, fullName, bais, content.length);
        return fullName;
    }

    private void updateTaskApplyLogStatus(String id, int status, String resultUrl) {
        Map taskApplyLog = logService.getTaskApplyLog(id);
        taskApplyLog.put(TaskApplyLog.APPLY_STATUS, status);
        if (resultUrl != null) {
            taskApplyLog.put(TaskApplyLog.APPLY_RESULT, resultUrl);
        }
        logService.updateTaskApplyLog(taskApplyLog);
    }

    private static final String SESSION_EXPORT_GROUP_BILL_TASK_ID_KEY = "exportGroupBillTaskId";

    @Override
    public Map searchExportGroupBillTaskStatus(Map request) {
        String taskId = (String) request.get("taskId");
        if (StringUtil.empty(taskId)) {
            taskId = (String) HttpRequestUtil.getSession().getAttribute(SESSION_EXPORT_GROUP_BILL_TASK_ID_KEY);
        }
        if (StringUtil.empty(taskId)) {
            return null;
        }
        Map log = logService.getTaskApplyLog(taskId);
        if (log != null) {
            return CollectionUtil.hashMap("taskId", taskId,
                    "status", log.get(TaskApplyLog.APPLY_STATUS),
                    TaskApplyLog.PAYLOAD, log.get(TaskApplyLog.PAYLOAD),
                    TaskApplyLog.USER_ID, log.get(TaskApplyLog.USER_ID),
                    "result", log.get(TaskApplyLog.APPLY_RESULT));
        }
        return null;
    }
}
