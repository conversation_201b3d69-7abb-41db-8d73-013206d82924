package com.wosai.upay.service;

import com.wosai.einvoice.model.*;
import com.wosai.upay.helper.CorePlatformsValidated;

import java.util.Map;

@CorePlatformsValidated
public interface MspEInvoiceService {

    // ==================== EInvoiceMerchantService 商户管理相关方法 ====================

    /**
     * 创建商户基本信息
     * @param request 商户基本信息请求
     * @return MerchantInvoiceInfoResponse 包含商户基本信息
     */
    MerchantInvoiceInfoResponse createMerchantBasicInfo(Map request);

    /**
     * 修改商户基本信息
     * @param request 商户基本信息请求
     * @return MerchantInvoiceInfoResponse 包含更新后的商户基本信息
     */
    MerchantInvoiceInfoResponse updateMerchantBasicInfo(Map request);

    /**
     * 查询商户开票相关信息
     * @param request 商户信息
     * @return MerchantInvoiceInfoResponse 包含商户基本信息
     */
    MerchantInvoiceInfoResponse getMerchantInvoiceInfo(Map request);

    /**
     * 修改商户客户端信息
     * @param request 商户客户端信息请求
     * @return MerchantInvoiceInfoResponse 包含更新后的客户端信息和验证状态
     */
    MerchantInvoiceInfoResponse updateMerchantClientInfo(Map request);

    /**
     * 验证商户客户端凭证
     * @param request 请求参数
     * @return Boolean 验证结果
     */
    Boolean validateClientInfo(Map request);

}
