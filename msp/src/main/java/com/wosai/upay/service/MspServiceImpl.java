package com.wosai.upay.service;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.bank.model.MerchantBusinessLicense;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.upay.bean.Transaction;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.exception.CommonAccessDeniedException;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.helper.TerminalHelper;
import com.wosai.upay.job.service.ContractWeixinService;
import com.wosai.upay.merchant.audit.api.service.ApplicationService;
import com.wosai.upay.qrcode.bean.Qrcode;
import com.wosai.upay.qrcode.service.QrcodeService;
import com.wosai.upay.transaction.service.UpayOrderService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.OrderUtil;
import com.wosai.upay.util.SheetUtil;
import com.wosai.upay.util.UpayStatement;
import org.apache.commons.collections.MapUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.wosai.upay.common.util.ConstantUtil.KEY_MERCHANT_ID;
import static com.wosai.upay.common.util.ConstantUtil.KEY_MERCHANT_SN;
import static com.wosai.upay.exception.UpayException.CODE_INVALID_PARAMETER;

public class MspServiceImpl implements MspService {
    private static final Logger logger = LoggerFactory.getLogger(MspServiceImpl.class);

    private static final String SESSION_USER = "msp_account";

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private UserService userService;
    @Autowired
    private TranslateService translateService;

    @Autowired
    private QrcodeService qrcodeService;

    @Autowired
    private ContractWeixinService contractWeixinService;

    @Autowired
    BankBusinessLicenseService bankBusinessLicenseService;

    @Value("${jsonrpc.upay-qrcode.server}")
    private String upayQrcodeUrl;

    @Value("${jsonrpc.qrcode-picture.server}")
    private String qrcodePictureUrl;

    @Autowired
    private UpayOrderService upayOrderService;//upay-transaction的接口

    private ObjectMapper objectMapper = new ObjectMapper();
    private String alipayAuthintoNotifyURL;

    public MspServiceImpl(String alipayAuthintoNotifyURL) {
        this.alipayAuthintoNotifyURL = alipayAuthintoNotifyURL;
    }


    public String exportQrcodeImage(String qrcode) throws Throwable {
        JsonRpcHttpClient client = new JsonRpcHttpClient(new URL(qrcodePictureUrl + "/rpc/image"));
        return client.invoke("exportQrcodeImage", new Object[]{qrcode}, String.class);
    }


    @Override
    public void unbindTerminal(Map<String, Object> request) {
        String terminalId = BeanUtil.getPropString(request, DaoConstants.ID);
        Map terminal = terminalService.getTerminal(terminalId);
        if (terminal != null) {
            String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
            checkMerchantIdPermission(merchantId);
            terminalService.unbindTerminal(terminalId);
        }
    }

    @Override
    public void disableStore(Map<String, Object> request) {
        String storeId = BeanUtil.getPropString(request, DaoConstants.ID);
        checkStorePermission(storeId);
        Map store = storeService.getStore(storeId);
        if (store != null) {
            int preStatus = BeanUtil.getPropInt(store, Store.STATUS, 0);
            if (preStatus == Store.STATUS_CLOSED) {
                throw new CommonAccessDeniedException("关闭的门店不能修改为禁用状态");
            }
        }
        storeService.disableStore(storeId);
    }

    @Override
    public void enableStore(Map<String, Object> request) {
        String storeId = BeanUtil.getPropString(request, DaoConstants.ID);
        checkStorePermission(storeId);
        storeService.enableStore(storeId);
    }

    @Override
    public void closeStore(Map<String, Object> request) {
        String storeId = BeanUtil.getPropString(request, DaoConstants.ID);
        checkStorePermission(storeId);
        storeService.closeStore(storeId);
    }

    private void checkStorePermission(String storeId) {
        Map store = storeService.getStore(storeId);
        if (store != null) {
            String merchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
            checkMerchantIdPermission(merchantId);
        }
    }


    private void checkMerchantIdPermission(String merchantId) {
        Map account = ((Map) HttpRequestUtil.getSession().getAttribute("msp_account"));
        String sessionMerchantId = (String) account.get("merchant_id");
        List<String> sessionMerchantIds = (List<String>) account.get("merchant_ids");
        //普通商户
        if (WosaiStringUtils.isNotEmpty(sessionMerchantId) && (!merchantId.equalsIgnoreCase(sessionMerchantId))) {
            throw new CommonAccessDeniedException("权限不足");
        }
        if (sessionMerchantIds != null && (!sessionMerchantIds.contains(merchantId)) && (!"super_admin".equalsIgnoreCase(account.get("role") + ""))) {
            throw new CommonAccessDeniedException("权限不足");
        }

    }

    /**
     * 查询门店列表
     *
     * @param request
     * @requetTYpe Map
     * @author:lijunjie
     * @returnTyep Map
     */
    @Override
    public ListResult queryStoreList(Map request) {
        request.put(Store.MERCHANT_ID, getSessionMerchantId());
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return storeService.findStores(pageInfo, request);
    }

    @Override
    public ListResult getOrderList(Map request) {
        request.put(KEY_MERCHANT_ID, getSessionMerchantId());
        request.put("user_id", getSessionUserId());
        return findOrders(request);
    }

    private ListResult findOrders(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        String userId = BeanUtil.getPropString(request, "user_id");
        Map queryFilter = CollectionUtil.hashMap(
                "orderSn", BeanUtil.getPropString(request, "order_sn"),
                "storeSn", BeanUtil.getPropString(request, "store_sn"),
                "storeName", BeanUtil.getPropString(request, "store_name"),
                "payway", BeanUtil.getPropString(request, "payway"),
                "subPayway", BeanUtil.getPropString(request, "sub_payway"),
                "status", BeanUtil.getPropString(request, "status"),
                "minTotalAmount", BeanUtil.getPropString(request, "min_total_amount"),
                "maxTotalAmount", BeanUtil.getPropString(request, "max_total_amount"),
                "merchantId", merchantId,
                "storeId", BeanUtil.getPropString(request, ConstantUtil.KEY_STORE_ID),
                "deviceFingerprint", BeanUtil.getPropString(request, Terminal.DEVICE_FINGERPRINT)
        );
        Map user = userService.getMerchantUser(userId);
        if (!BeanUtil.getPropString(user, "role", "").equals(MspUserLoginService.ROLE_SUPER_ADMIN) && BeanUtil.getPropInt(user, "store_auth") == 2) {
            //user对应的门店
            LinkedList storeIds = new LinkedList();
            List<Map> userStores = userService.getMerchantUserStoreAuths(CollectionUtil.hashMap(
                    "merchant_id", merchantId,
                    "merchant_user_id", userId
            ));
            for (Map userStore : userStores) {
                storeIds.add(BeanUtil.getPropString(userStore, ConstantUtil.KEY_STORE_ID));
            }
            queryFilter.put("storeIds", storeIds);
        }
        ListResult result = upayOrderService.getOrderList(pageInfo, queryFilter);
        translateService.translateOrderOperatorNames(result.getRecords(), merchantId);
        return result;
    }

    @Override
    public List getTransactionListByOrderSn(Map request) {
        String orderSn = BeanUtil.getPropString(request, "order_sn");
        List list = upayOrderService.getTransactionListByOrderSn(orderSn);
        if (list != null && list.size() != 0) {
            translateService.translateOrderOperatorNames(list, BeanUtil.getPropString(list.get(0), ConstantUtil.KEY_MERCHANT_ID));
        }
        return list;
    }

    @Override
    public void exportOrderList(Map params, HttpServletRequest request, HttpServletResponse response) throws IOException {
        long page = BeanUtil.getPropLong(params, ConstantUtil.KEY_PAGE, 1);
        long pageSize = BeanUtil.getPropLong(params, ConstantUtil.KEY_PAGESIZE, 5000);
        String merchantId = getSessionMerchantId(request);
        String userId = getSessionUserId(request);
        params.put(ConstantUtil.KEY_PAGE, page);
        params.put(ConstantUtil.KEY_PAGESIZE, pageSize);
        params.put(ConstantUtil.KEY_MERCHANT_ID, merchantId);
        params.put("user_id", userId);
        ListResult listResult = findOrders(params);
        HSSFWorkbook workbook = OrderUtil.buildOrdersExcelV2(listResult.getRecords());
        String fileName = new SimpleDateFormat("yyyy-MM-dd_HHmmss").format(new Date()) + "exportOrders.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }

    @Override
    public void exportStatement(Map params, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String merchantId = null;
        String storeId = null;
        //todo 为了方便导出对账单，暂时留个后门
        if (params.containsKey("door_merchant_id")) {
            merchantId = BeanUtil.getPropString(params, "door_merchant_id");
        } else {
            if (request.getSession() == null || getSessionMerchantId(request) == null) {
                throw new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
            }
            merchantId = getSessionMerchantId(request);
        }
        long start = BeanUtil.getPropLong(params, "date_start");
        long end = BeanUtil.getPropLong(params, "date_end");
        Map merchant = merchantService.getMerchant(merchantId);
        Map context = CollectionUtil.hashMap(
                "merchant_id", merchantId,
                "start", new Date(start), "end", new Date(end),
                "merchant_sn", BeanUtil.getPropString(merchant, Merchant.SN),
                "merchant_name", BeanUtil.getPropString(merchant, Merchant.NAME)
        );
        PageInfo pageInfo = new PageInfo(1, 50000, start, end);
        Map queryFilter = CollectionUtil.hashMap(
                "status", Arrays.asList(Transaction.STATUS_SUCCESS),
                "storeName", BeanUtil.getPropString(params, "store_name")
        );
        ListResult result = upayOrderService.getTransactionList(merchantId, storeId, pageInfo, queryFilter);
        translateService.translateOrderOperatorNames(result.getRecords(), merchantId);
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet summarySheet = workbook.createSheet("账单汇总");
        UpayStatement.buildUpayStatementSummary(context, result.getRecords(), summarySheet);
        SheetUtil util = new SheetUtil(summarySheet);
        util.mergeCell(2, 0, 2, 3);
        util.setCellAlignment(2, 0, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER);
        util.mergeCell(6, 0, 6, 10);
        util.setCellAlignment(6, 0, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER);
        HSSFSheet detailSheet = workbook.createSheet("账单明细");
        UpayStatement.buildUpayStatementDetail(context, result.getRecords(), detailSheet);
        util = new SheetUtil(detailSheet);
        String fileName = new SimpleDateFormat("yyyy-MM-dd_HHmmss").format(new Date()) + "statement.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }


    @Override
    public String getAlipayAuthintoNotifyURL(Map request) {
        return alipayAuthintoNotifyURL;
    }

    @Override
    public Map alipayAuthintoNotify(Map request) {
        //授权信息同步不在通过此接口
//        String merchantId = getSessionMerchantId();
//        String  alipayAppAuthToken = BeanUtil.getPropString(request, "alipayAppAuthToken");
//        if (!StringUtil.empty(alipayAppAuthToken)) {
//            Map merchant = merchantService.getMerchantByMerchantId(merchantId);
//            if (merchant != null) {
//                Map  updateData = CollectionUtil.hashMap(
//                        TransactionParam.APP_AUTH_TOKEN, alipayAppAuthToken
//                );
//                tradeConfigService.updateAlipayV2TradeParams(merchantId, updateData);
//                tradeConfigService.updateAlipayV2WapTradeParams(merchantId, updateData);
//            }
//        }
        return null;
    }

    @Override
    public Map<Integer, String> getExceptionCodesAndDesc() {
        return UpayException.CODES_DESC_MAP;
    }

    @Override
    public Map getMerchant() {
        Map merchant = merchantService.getMerchant(getSessionMerchantId());
        // beez 版本不需要
//        Map<String, Object> customerPhoneInfo = contractWeixinService.getWeixinUpdateTime(CollectionUtil.hashMap("merchant_sn", BeanUtil.getPropString(merchant, "sn")));
//        merchant.put("customer_phone_mtime", BeanUtil.getPropString(customerPhoneInfo, DaoConstants.MTIME)); //微信客服电话
        merchant.put("customer_phone_mtime", null); //微信客服电话
        return merchant;
    }


    @Override
    public Map getMerchantApplicationBase() {
        String merchantId = getSessionMerchantId();
        ListResult listResult = applicationService.findApplicationBases(new PageInfo(1, 10), CollectionUtil.hashMap(ConstantUtil.KEY_MERCHANT_ID, merchantId));
        if (listResult == null || listResult.getRecords().size() == 0) {
            return null;
        } else {
            return listResult.getRecords().get(0);
        }
    }

    @Override
    public Map getMerchantBankAccount() {
        return merchantService.getMerchantBankAccountByMerchantId(getSessionMerchantId());
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getStore(Map<String, Object> request) {
        String storeId = BeanUtil.getPropString(request, DaoConstants.ID);
        Map store = storeService.getStoreByStoreId(storeId);
        if (store != null) {
            String merchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
            checkMerchantIdPermission(merchantId);
        }
        return store;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getTerminal(Map<String, Object> request) {
        Map<String, Object> map = terminalService.getTerminalByTerminalId((String) request.get(DaoConstants.ID));
        if (map != null) {
            String merchantId = BeanUtil.getPropString(map, Terminal.MERCHANT_ID);
            checkMerchantIdPermission(merchantId);

            Map<String, Object> activationCodeMap = terminalService.getTerminalActivationCodeByTerminalId((String) request.get(DaoConstants.ID));
            if (activationCodeMap != null) {
                map.put("activation_code", activationCodeMap.get(TerminalActivationCode.CODE));
            }
            if (TerminalHelper.isQRCodeType(BeanUtil.getPropInt(map, Terminal.TYPE))) {
                String qrcode = BeanUtil.getPropString(map, Terminal.DEVICE_FINGERPRINT);
                String qrcodeId = qrcodeService.getQrcodeIdByQrcode(qrcode);
                Map qrcodeTag = qrcodeService.getQrcodeTag(qrcodeId, "PRESET_AMOUNT");
                if (!MapUtils.isEmpty(qrcodeTag)) {
                    try {
                        Map tagValue = objectMapper.readValue(BeanUtil.getPropString(qrcodeTag, "value"), Map.class);
                        map.put("PRESET_AMOUNT", BeanUtil.getPropLong(tagValue, "amount"));
                        map.put("remark", BeanUtil.getPropString(tagValue, "remark"));
                    } catch (Exception e) {
                        logger.error("getTerminal:{}", e);
                    }
                }
            }
        }
        return map;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getTerminalActivationCodeByTerminalId(Map<String, Object> request) {
        return terminalService.getTerminalActivationCodeByTerminalId((String) request.get(ConstantUtil.KEY_TERMINAL_ID));
    }


    /**
     * 获取merchantId
     *
     * @return
     */
    private String getSessionMerchantId() {
        return ((Map) HttpRequestUtil.getSession().getAttribute("msp_account")).get("merchant_id") + "";
    }

    private String getSessionUserId() {
        return ((Map) HttpRequestUtil.getSession().getAttribute("msp_account")).get("user_id") + "";
    }

    /**
     * 获取merchantId
     *
     * @return
     */
    private String getSessionMerchantId(HttpServletRequest request) {
        return ((Map) request.getSession().getAttribute("msp_account")).get("merchant_id") + "";
    }

    private String getSessionUserId(HttpServletRequest request) {
        return ((Map) request.getSession().getAttribute("msp_account")).get("user_id") + "";
    }


    /**
     * 基础信息接口
     *
     * @param request key merchant_sn
     * @return
     */
    @Override
    public Map baseInfo(Map request) {
        String merchantSn = BeanUtil.getPropString(request, KEY_MERCHANT_SN);
        String merchantId = BeanUtil.getPropString(request, KEY_MERCHANT_ID);
        if (!StringUtil.empty(merchantSn))
            return merchantService.getMerchantByMerchantSn(merchantSn);
        if (!StringUtil.empty(merchantId))
            return merchantService.getMerchantByMerchantId(KEY_MERCHANT_ID);

        throw new UpayException(CODE_INVALID_PARAMETER, "{value} 值中 merchant_sn与merchant_id不能同时为空值{}");

    }

    @Override
    public Map getMerchantDeveloper(Map request) {
        String merchantId = getSessionMerchantId();
        return merchantService.getMerchantDeveloperByMerchantId(merchantId);
    }

    @Override
    public Map getMerchantConfigFormalParams(Map request) {
        String merchantId = getSessionMerchantId();
        int payway = BeanUtil.getPropInt(request, MerchantConfig.PAYWAY);
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        Map result = new HashMap();
        result.put(MerchantConfig.B2C_FORMAL, BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.B2C_FORMAL, false));
        result.put(MerchantConfig.C2B_FORMAL, BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.C2B_FORMAL, false));
        result.put(MerchantConfig.WAP_FORMAL, BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.WAP_FORMAL, false));
        result.put(MerchantConfig.PARAMS, BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS));
        return result;
    }

    @Override
    public Map getMerchantTradeValidateParams() {
        String merchantId = getSessionMerchantId();
        return tradeConfigService.getMerchantTradeValidateParams(merchantId);
    }

    @Override
    public List getAnalyzedMerchantConfigs() {
        String merchantId = getSessionMerchantId();
        return tradeConfigService.getAnalyzedMerchantConfigs(merchantId);
    }

    @Override
    public String exportQrcodeImage(Map request) throws Throwable {
        String qrcode = BeanUtil.getPropString(request, Qrcode.QR_CODE);
        return exportQrcodeImage(qrcode);
    }

    @Override
    public void updateCustomerPhone(Map request) throws Exception {
        Map result = contractWeixinService.updateWeixinParams(request);
        if ("0".equals(BeanUtil.getPropString(result, "result_code"))) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, BeanUtil.getPropString(result, "message", "修改客服电话失败"));
        }
    }

    @Override
    public MerchantBusinessLicense getMerchantBusinessLicense(Map request) {
        return bankBusinessLicenseService.getBusinessLicenseByMerchantId(getSessionMerchantId());
    }
}
