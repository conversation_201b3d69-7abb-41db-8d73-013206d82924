package com.wosai.upay.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Map;

public interface MspImageService {


    /**
     * 上传图片，同时返回POI信息
     *
     * @param images        图片
     * @param watermarkFlag 是否加水印
     * @param ocrType       null/front/back
     * @param longitude     经度
     * @param latitude      纬度
     * @param version       版本号
     * @return photo, province, city, district, address, time, longitude, latitude, version,
     */
    Map uploadWithPoi(MultipartFile images, boolean watermarkFlag, String ocrType, double longitude, double latitude, String version,
                      boolean isTolerant) throws IOException;

    /**
     * 授权url
     *
     * @param url
     * @return:
     * @author: huangwenbin
     * @date: 2019/2/22 13:46
     */
    Map encrypt(Map url) throws UnsupportedEncodingException;
}
