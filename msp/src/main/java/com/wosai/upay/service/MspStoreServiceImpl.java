package com.wosai.upay.service;

import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.trade.service.SwitchService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.user.api.service.SpecialAuthWhitelistService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.util.HttpRequestUtil;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;

import static com.wosai.upay.common.util.ConstantUtil.KEY_MERCHANT_ID;
import static com.wosai.upay.service.MspUserLoginService.ROLE_DEPARTMENT_MANAGER;
import static com.wosai.upay.service.MspUserLoginService.ROLE_SUPER_ADMIN;

/**
 * Created by lijunjie on 16/5/11.
 */
@SuppressWarnings("unchecked")
@Service
public class MspStoreServiceImpl extends BaseService implements MspStoreService {
    private static final Logger logger = LoggerFactory.getLogger(MspStoreServiceImpl.class);

    @Autowired
    private StoreService storeService;
    @Autowired
    private GroupUserLoginService userLoginService;
    @Autowired
    private SpecialAuthWhitelistService specialAuthWhitelistService;
    @Autowired
    private SwitchService switchService;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private UcUserAccountService ucUserAccountService;


    @Override
    public Map<String, Object> createStore(Map<String, Object> request) {
        if (!getCreateStoreLimit(null)) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "未开通新增门店权限");
        }
        request.put("merchant_id", getSessionMerchantId());
        return storeService.createStore(request);
    }

    @Override
    public Map<String, Object> updateStore(Map<String, Object> request) {
        return storeService.updateStore(request);
    }

    @Autowired
    private MspUserService userService;

    @Override
    public ListResult findStores(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        if (StringUtil.empty(merchantId)) {
            merchantId = getSessionMerchantId();
        }
        if (StringUtils.isBlank(merchantId)) {
            throw new UpayException(UpayException.CODE_SESSION_ERROR, "未在Session中取到登录商户的");
        }

        Map queryFilter = CollectionUtil.hashMap(
                "merchant_id", merchantId,
                "store_sn", BeanUtil.getPropString(request, "store_sn"),
                "store_name", BeanUtil.getPropString(request, "store_name"),
                "client_sn", BeanUtil.getPropString(request, "client_sn")
        );

        Map account = userLoginService.getCurrentUser();
        String role = MapUtils.getString(account, "role");
        Object accountId = HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);


        //增加门店限制
        if (!StringUtils.equals(role, ROLE_SUPER_ADMIN)) {
            List<String> storeIds = new ArrayList<>();

            String mspRole = getSessionUserRole();
            if (StringUtils.equals(mspRole, ROLE_DEPARTMENT_MANAGER)) {
                Map stores = getSessionInfos(null);
                storeIds= (List<String>) BeanUtil.getProperty(stores,"store_ids");
            } else {
                List<UcMerchantUserInfo> merchantUserInfos = merchantUserServiceV2.getMerchantUserByUcUserId(accountId.toString());
                if(org.apache.commons.collections.CollectionUtils.isEmpty(merchantUserInfos)) {
                    throw new UpayException(UpayException.CODE_ACCESS_DENIED, "商户平台无此用户或者此用户被禁用");
                }
                String finalMerchantId = merchantId;
                String merchantUserId = merchantUserInfos.stream().filter(new Predicate<UcMerchantUserInfo>() {
                    @Override
                    public boolean test(UcMerchantUserInfo e) {
                        return finalMerchantId.equals(e.getMerchant_id());
                    }
                }).map(new Function<UcMerchantUserInfo, String>() {
                    @Override
                    public String apply(UcMerchantUserInfo ucMerchantUserInfo) {
                        return ucMerchantUserInfo.getMerchant_user_id();
                    }
                }).findFirst().orElse("-1");
                List<String> storeIdsByMerchantUserId = merchantUserServiceV2.getStoreIdsByMerchantUserId(merchantUserId);
                storeIds.addAll(storeIdsByMerchantUserId);
            }
            queryFilter.put("store_ids", storeIds);

        }

        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return storeService.findStores(pageInfo, queryFilter);
    }

    @Override
    public boolean getCreateStoreLimit(Map<String, Object> request) {
        if (StringUtils.isBlank(getSessionMerchantId())) {
            return false;
        }
        Map whiteList = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(4, getSessionMerchantId());
        return !CollectionUtils.isEmpty(whiteList);
    }

    @Override
    public void configureAcrossStoreRefundSwitch(Map<String, Object> request) {
        String merchantId = getSessionMerchantId();
        Integer switchStatus = MapUtil.getInteger(request, "switch_status");
        if (StringUtils.isBlank(merchantId) || switchStatus == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "缺少必要参数");
        }

        Map<String, Object> req = new HashMap<>();
        req.put("merchant_id", merchantId);
        if (switchStatus == 0) {
            switchService.closeAcrossStoreRefund(req);
            return;
        }
        switchService.openAcrossStoreRefund(req);
    }

    @Override
    public int queryAcrossStoreRefundSwitch(Map<String, Object> request) {
        String merchantId = getSessionMerchantId();
        if (StringUtils.isBlank(merchantId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "缺少必要参数");
        }

        Map<String, Object> req = new HashMap<>();
        req.put("merchant_id", merchantId);
        return switchService.queryAcrossStoreRefundStatus(req);
    }

}
