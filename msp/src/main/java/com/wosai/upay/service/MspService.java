package com.wosai.upay.service;


import com.wosai.upay.bank.model.MerchantBusinessLicense;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.helper.CorePlatformsValidated;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.qrcode.bean.Qrcode;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 接口定义，参数校验规则定义在接口方法上，校验失败的错误提示可以支持i18n，具体做法是在src/main/resources/ValidationMessages.properties资源文件中定义错误提示的键值对，然后在这里引用错误提示键。
 * 本地化的资源文件加后缀，例如ValidationMessages_zh_CN.properties。
 *
 * <AUTHOR>
@CorePlatformsValidated
public interface MspService {



    void unbindTerminal(Map<String, Object> request);

    /**
     * 根据storeId禁用Store.
     *
     * @param request id                  UUID
     * @return
     */
    void disableStore(Map<String, Object> request);

    /**
     * 根据storeId启用Store.
     *
     * @param request id                  UUID
     * @return
     */
    void enableStore(Map<String, Object> request);

    /**
     * 根据storeId关闭Store.
     *
     * @param request id                  UUID
     * @return
     */
    void closeStore(Map<String, Object> request);

    /**
     * 查询门店列表
     *
     * @param request
     * @requetTYpe Map
     * @author:lijunjie
     * @returnTyep Map
     */
    ListResult queryStoreList(Map request);


    /**
     * 获取商户订单列表
     *
     * @param request order_sn
     *                store_sn
     *                store_name
     *                payway
     *                sub_payway
     *                status
     *                min_total_amount
     *                max_total_amount
     * @return
     */
    ListResult getOrderList(Map request);

    /**
     * 根据订单号获取交易明细
     *
     * @param request order_sn
     * @return
     */
    List getTransactionListByOrderSn(
            @PropNotEmpty(value = "order_sn", message = "{value} 订单号不能为空")
            Map request);

    /**
     * 导出订单列表
     *
     * @param params   参数同getOrderList
     * @param response
     */
    void exportOrderList(Map params, HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 导出账单汇总与明细
     *
     * @param params   date_start
     *                 date_end
     * @param response
     */
    void exportStatement(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_DATE_START, message = "{value} 起始日期不能为空"),
                    @PropNotEmpty(value = ConstantUtil.KEY_DATE_END, message = "{value} 结束日期不能为空")
            })
            Map params, HttpServletRequest request, HttpServletResponse response) throws IOException;


    String getAlipayAuthintoNotifyURL(Map request);

    /**
     * @param request alipayAppAuthToken
     * @return
     */
    Map alipayAuthintoNotify(Map request);


    /**
     * 获取异常编号及描述.
     *
     * @return
     */
    Map<Integer, String> getExceptionCodesAndDesc();


    /**
     * 获取商户基本信息
     * 基础信息， 营业执照
     *
     * @return
     */
    Map getMerchant();

    /**
     * 获取商户照片信息
     *
     * @return
     */
    Map getMerchantApplicationBase();

    /**
     * 获取银行账户信息
     *
     * @return
     */
    Map getMerchantBankAccount();

    /**
     * 获取门店.
     *
     * @param request id                  UUID
     *
     * @return Map
     */
    Map<String, Object> getStore(Map<String, Object> request);


    /**
     * 获取终端.
     *
     * @param request id                  UUID
     * @return
     */
    Map<String, Object> getTerminal(Map<String, Object> request);

    /**
     * 获取终端激活码.
     *
     * @param request terminal_id                  UUID
     * @return
     */
    Map<String, Object> getTerminalActivationCodeByTerminalId(Map<String, Object> request);

    /**
     * 基础信息接口
     *
     * @param request key merchant_sn
     * @return
     */
    public Map baseInfo(Map request);


    /**
     * 获取商户开发者参数
     *
     * @param request
     * @return
     */
    Map getMerchantDeveloper(Map request);

    /**
     * 获取商户正式的配置参数
     *
     * @param request payway
     * @return
     */
    Map getMerchantConfigFormalParams(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = MerchantConfig.PAYWAY, message = "{value} 支付方式不能为空"),
            })
            Map request);

    /**
     * 获取商户基本的交易校验信息 如商户限额
     *
     * @return
     */
    Map getMerchantTradeValidateParams();

    /**
     * 获取商户收款通道是否开通，是否正式，费率， 正式参数等信息
     *
     * @return
     */
    List getAnalyzedMerchantConfigs();

    /**
     * 导出门店码
     * @param request
     */
    String exportQrcodeImage(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = Qrcode.QR_CODE, message = "{value}不能为空"),
            })
            Map request) throws Throwable;

    void updateCustomerPhone(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id", message = "{value}不能为空"),
            @PropNotEmpty(value = "merchant_sn", message = "{value}不能为空"),
            @PropNotEmpty(value = "service_phone", message = "{value}不能为空"),
    })Map request) throws Exception;

    MerchantBusinessLicense getMerchantBusinessLicense(Map request);
}
