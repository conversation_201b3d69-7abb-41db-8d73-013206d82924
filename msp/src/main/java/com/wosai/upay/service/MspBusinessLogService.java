package com.wosai.upay.service;


import com.wosai.upay.common.bean.ListResult;

import java.util.List;
import java.util.Map;

/**
 * Created by ji<PERSON><PERSON><PERSON> ma on 2017/11/5.
 */
public interface MspBusinessLogService {

    /**
     * 获得所有Business_Object_Colum
     * @return
     */
    public ListResult findBizObjectColumns(Map<String, Object> request);


    /**
     * 获得所有Business_Object
     * @return
     */
    public ListResult findBizObjects(Map<String, Object> request);

    /**
     * 获取业务对象下所有字段
     * @param request
     * @return
     */
    public List<Map<String,Object>> findBizObjectColumnsByObjectCode(Map<String, Object> request);

    /**
     * 获取业务对象下业务字段映射
     */
    public List<String> findObjectColumns(String bizObjectCode);

    /**
     * 创建并发送业务日志
     * @param params
     */
    public void sendBusinessLog(Map<String, Object> params);

    /**
     * 创建业务日志
     * @param bizOpLog
     * @return
     */
    public Map createBizOpLog(Map bizOpLog);

    public ListResult findBizOpLogs(Map request);
    public Map getBizOpLogDetail(Map request);

}
