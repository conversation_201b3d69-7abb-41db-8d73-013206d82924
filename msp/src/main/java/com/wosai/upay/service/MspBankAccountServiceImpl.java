package com.wosai.upay.service;

import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.bank.model.*;
import com.wosai.upay.bank.model.verify.VerifyResp;
import com.wosai.upay.bank.service.BankAccountChangeService;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.user.api.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-12-05
 */
@Service
public class MspBankAccountServiceImpl extends BaseService implements MspBankAccountService {

    @Autowired
    private BankAccountChangeService service;

    @Autowired
    private BankService bankService;

    @Autowired
    private UserService userService;

    @Override
    public BankAccountChangeApply applyBankAccountChange(Map request) {
        MchBankChangeVo mchBankChangeVo = JSON.parseObject(JSON.toJSONString(request), MchBankChangeVo.class);
        mchBankChangeVo.setPlatform(Request.KEY_PLATFORM_MSP)
                .setMerchant_id(getSessionMerchantId())
        .setApply_cellphone(getSessionAccountUsername());
        return service.applyBankAccountChange(mchBankChangeVo);
    }

    @Override
    public BankAccountChangeApply getLatestApply(Map request) {
        request.put(Request.MERCHANT_ID, getSessionMerchantId());
        request.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_MSP);
        return service.getLatestApply(request);
    }

    @Override
    public BankAccountChangeApplyDetail getApplyDetail(Map request) {
        request.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_MSP);
        BankAccountChangeApplyDetail apply = service.getApplyDetail(request);
        return apply.getMerchant_id().equals(getSessionMerchantId()) ? apply : null;
    }

    @Override
    public AllowChangeTypeVo getAllowChangeTypes(Map request) {
        request.put(Request.MERCHANT_ID, getSessionMerchantId());
        return service.getAllowChangeTypes(request);
    }

    @Override
    public VerifyResp verifyAmount(Map request) {
        request.put("source", Request.KEY_PLATFORM_MSP);
        request.put("submitter", getSessionAccountUsername());
        return service.verifyAmount(request);
    }

    @Override
    public Map cancelApply(Map request) {
        request.put("source", Request.KEY_PLATFORM_MSP);
        request.put("submitter", getSessionAccountUsername());
        service.cancelApply(request);
        return Collections.emptyMap();
    }

    @Override
    public Map preCheckForDiffName(Map request) {
        appendMspParams(request);
        return service.preCheck(request);
    }

    @Override
    public Map bindMerchantBankAccount(Map merchantBankAccount) {
        appendMspParams(merchantBankAccount);
        return bankService.bindMerchantBankAccount(merchantBankAccount);
    }

    @Override
    public boolean updateBankOtherMessage(Map merchantBankAccount) {
        appendMspParams(merchantBankAccount);
        return bankService.updateBankOtherMessage(merchantBankAccount);
    }

    @Override
    public ListResult findMerchantBankAccounts(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        appendMspParams(request);

        ListResult result = bankService.findMerchantBankAccounts(pageInfo, request);
        long count = 0;
        List<Map> resultList = new ArrayList<>();
        for (Map resultMap : result.getRecords()) {
            int identityType = BeanUtil.getPropInt(resultMap, Request.KEY_IDENTITY_TYPE);
            if (identityType == Request.KEY_IDENTITY_TYPE_SAME) {
                resultMap.remove(Request.KEY_IDENTITY_TYPE);
                resultList.add(resultMap);
                count++;
            }
        }
        return new ListResult(count, resultList);
    }

    @Override
    public Map replaceMerchantBankAccount(Map merchantBankAccountPre) {
        appendMspParams(merchantBankAccountPre);
        return bankService.replaceMerchantBankAccount(merchantBankAccountPre);
    }

    @Override
    public void deletedMerchantBankAccountPre(Map<String, Object> request) {
        appendMspParams(request);
        bankService.deletedMerchantBankAccountPre(request);
    }

    @Override
    public Map updateMerchantBankAccount(Map merchantBankAccountPre) {
        appendMspParams(merchantBankAccountPre);
        return bankService.updateMerchantBankAccount(merchantBankAccountPre);
    }

    @Override
    public Map checkoutAllowChangeCard(Map params) {
        appendMspParams(params);
        return bankService.checkoutAllowChangeCard(params);
    }

    @Override
    public Map verifyAmountForSameName(Map request) {
        appendMspParams(request);
        request.put(Request.KEY_REMARK, "验证金额");
        return bankService.verifyAmount(request);
    }

    private void appendMspParams(Map params) {
        params.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_MSP);
        params.put(Request.KEY_OPERATOR, getSessionAccountUsername());
        params.put(Request.MERCHANT_ID, getSessionMerchantId());
    }
}
