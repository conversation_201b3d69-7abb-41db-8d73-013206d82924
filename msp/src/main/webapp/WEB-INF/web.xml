<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://java.sun.com/xml/ns/javaee"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         id="WebApp_ID" version="2.5">

  <display-name>Spring MVC</display-name>
  <description>Spring MVC web application</description>


  <!--
      - Location of the XML file that defines the root application context.
      - Applied by ContextLoaderServlet.
  -->
  <context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>classpath:spring/business-config.xml, classpath:spring/tools-config.xml, classpath:spring/application-security.xml</param-value>
  </context-param>

  <listener>
    <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
  </listener>

  <!--
      - Servlet that dispatches request to registered handlers (Controller implementations).
  -->
  <servlet>
    <servlet-name>dispatcher</servlet-name>
    <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
    <init-param>
      <param-name>contextConfigLocation</param-name>
      <param-value>classpath:spring/mvc-core-config.xml</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
  </servlet>

  <servlet-mapping>
    <servlet-name>dispatcher</servlet-name>
    <url-pattern>/</url-pattern>
  </servlet-mapping>


  <!-- used so we can use forms of method type 'PUT' and 'DELETE'
       see here: http://static.springsource.org/spring/docs/current/spring-framework-reference/html/view.html#rest-method-conversion
  -->
  <filter>
    <filter-name>httpMethodFilter</filter-name>
    <filter-class>org.springframework.web.filter.HiddenHttpMethodFilter</filter-class>
  </filter>

  <filter-mapping>
    <filter-name>httpMethodFilter</filter-name>
    <servlet-name>dispatcher</servlet-name>
  </filter-mapping>

  <filter>
    <filter-name>crossDomainFilter</filter-name>
    <filter-class>com.wosai.upay.filter.CrossDomainFilter</filter-class>
  </filter>

  <filter-mapping>
    <filter-name>crossDomainFilter</filter-name>
    <url-pattern>/*</url-pattern>

  </filter-mapping>

  <filter>
  <filter-name>loginCheckerFilter</filter-name>
  <filter-class>com.wosai.upay.filter.LoginCheckerFilter</filter-class>
  <init-param>
    <param-name>sessionKey</param-name>
    <param-value>msp_account</param-value>
  </init-param>
  <init-param>
    <param-name>notCheckUrls</param-name>
    <param-value>login,sendSmsCode,logout,getUserList,changeUserContext,getExceptionCodesAndDesc,exportStatement,department,terminal,pay_ways</param-value>
  </init-param>
</filter>

  <filter-mapping>
    <filter-name>loginCheckerFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>

<!--  <filter>-->
<!--    <filter-name>sessionFilter</filter-name>-->
<!--    <filter-class>com.wosai.upay.filter.SessionFilter</filter-class>-->
<!--    <init-param>-->
<!--      <param-name>notCheckUrls</param-name>-->
<!--      <param-value>login,sendSmsCode,logout,getUserList,changeUserContext,getExceptionCodesAndDesc,exportStatement,department,terminal</param-value>-->
<!--    </init-param>-->
<!--  </filter>-->

<!--  <filter-mapping>-->
<!--    <filter-name>sessionFilter</filter-name>-->
<!--    <url-pattern>/*</url-pattern>-->
<!--  </filter-mapping>-->

    <session-config>
        <!-- session 会话超时失效时间：15分钟 -->
        <session-timeout>15</session-timeout>
    </session-config>

</web-app>
