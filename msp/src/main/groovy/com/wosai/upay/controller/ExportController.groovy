package com.wosai.upay.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.wosai.data.bean.BeanUtil
import com.wosai.data.util.CollectionUtil
import com.wosai.data.util.StringUtil
import com.wosai.upay.exception.UpayException
import com.wosai.upay.rest.ast.ASTHelper
import com.wosai.upay.service.MspGroupService
import com.wosai.upay.service.MspService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse
import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.ThreadPoolExecutor

/**
 * Created by jianfree on 31/3/16.
 */
@Controller
@RequestMapping("/api/export")
class ExportController {
    public static final MAX_EXPORT_THREAD_COUNT = 2; //最多同时有多少个线程执行
    ThreadPoolExecutor exportExecutor = Executors.newFixedThreadPool(MAX_EXPORT_THREAD_COUNT);

    private Logger log = LoggerFactory.getLogger(ExportController.class);
    @Autowired
    private ASTHelper astHelper;
    @Autowired
    MspService mspService;
    @Autowired
    MspGroupService mspGroupService;


    @RequestMapping(produces = "application/octet-stream" ,value = "/exportOrderList")
    @ResponseBody
    public void exportOrderList(@RequestParam Map params, HttpServletRequest request, HttpServletResponse response){
        Runnable runnable = new Runnable() {
            @Override
            void run() {
                mspService.exportOrderList(params, request, response);
            }
        }
        submitExportTask(runnable, "exportOrderList", params, response);
    }

    @RequestMapping(produces = "application/octet-stream" ,value = "/exportStatement")
    @ResponseBody
    public void exportStatement(@RequestParam Map params, HttpServletRequest request,  HttpServletResponse response){
        Runnable runnable = new Runnable() {
            @Override
            void run() {
                mspService.exportStatement(params, request, response);
            }
        }
        submitExportTask(runnable, "exportStatement", params, response);
    }

    private void submitExportTask(Runnable runnable, String methodName, Map params, HttpServletResponse response){
        long start = System.currentTimeMillis()
        try {
            astHelper.logMethod(log, methodName, params)
            if(exportExecutor.getActiveCount() >= MAX_EXPORT_THREAD_COUNT){
                throw new UpayException(UpayException.CODE_ACCESS_DENIED, "系统导出任务繁忙，请稍后重试");
            }
            Future future = exportExecutor.submit(runnable)
            future.get();
            astHelper.handleResult(log, null)
        } catch (Exception e) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().write(new ObjectMapper().writeValueAsString(astHelper.handleException(log, e)));
        } finally {
            long end = System.currentTimeMillis()
            astHelper.logMethodTime(log, methodName, start, end)
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "/exportGroupBill")
    @ResponseBody
    public void exportGroupBill(
            @RequestParam(value = "taskId", required = false) String taskId, HttpServletRequest request, HttpServletResponse response) {
        Map rs = mspGroupService.searchExportGroupBillTaskStatus(CollectionUtil.hashMap("taskId", taskId));
        int status = BeanUtil.getPropInt(rs, "status", 0);
        String result = BeanUtil.getPropString(rs, "result", "");
        if (status == 2 && !StringUtil.empty(result)) {
            response.sendRedirect("http://images.wosaimg.com/" + result);
        } else {
            response.getWriter().write("");
        }
    }

}
