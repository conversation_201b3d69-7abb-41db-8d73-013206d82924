package com.wosai.upay.controller

import com.wosai.upay.service.MspVisualService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

import javax.servlet.http.HttpServletResponse

/**
 * 可视化相关接口
 */
@Controller
@RequestMapping("/api/visual")
public class MspVisualController {
    @Autowired
    private MspVisualService mspVisualService;

     @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/proxy")
     @ResponseBody
     public void proxy(@RequestBody Map request, HttpServletResponse response) {
         mspVisualService.proxy(request, response);
     }

}
