package com.wosai.upay.controller

import com.wosai.upay.rest.ast.ASTHelper
import com.wosai.upay.service.MspImageService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@SuppressWarnings("unchecked")
@Controller
@RequestMapping("/api/images")
class MspImageController {

    private Logger log = LoggerFactory.getLogger(MspImageController.class);

    @Autowired
    protected ASTHelper astHelper;

    @Autowired
    private MspImageService mspImageService;

    @RequestMapping(method = RequestMethod.POST, consumes = "multipart/form-data", produces = "application/json", value = "/poi")
    public Map uploadWithPOI(@RequestPart(value = "file") MultipartFile file,
                             @RequestParam(value = "watermark_flag", defaultValue = "false") boolean watermarkFlag,
                             @RequestParam(value = "ocr_type", required = false) String ocrType,
                             @RequestParam(defaultValue = "0") double longitude,
                             @RequestParam(defaultValue = "0") double latitude,
                             @RequestParam(value = "is_tolerant", defaultValue = "false") boolean isTolerant,
                             @RequestParam(required = false) String v) throws IOException {
        return astHelper.handleResult(log, mspImageService.uploadWithPoi(file, watermarkFlag, ocrType, longitude, latitude, v, isTolerant));
    }

    @RequestMapping(method = RequestMethod.POST, value = "/encrypt")
    public Map encrypt(@RequestBody Map url) throws IOException {
        return astHelper.handleResult(log, mspImageService.encrypt(url))
    }

}
