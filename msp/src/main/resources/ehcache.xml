<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="ehcache.xsd"
         updateCheck="false">
    <diskStore path="java.io.tmpdir"/>

    <!-- objects are evicted from the cache every 60 seconds -->
    <cache name="foo"
           timeToLiveSeconds="60"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>

    <cache name="findObjectColumns"
           timeToLiveSeconds="3600"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>

    <cache name="findBizObjectColumnsByObjectCode"
           timeToLiveSeconds="3600"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>

    <cache name="getPayWay"
           timeToLiveSeconds="30"
           maxElementsInMemory="100"
           eternal="false"
           overflowToDisk="false"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="1"
           memoryStoreEvictionPolicy="LRU"/>

    <!--
      name:缓存名称。
      maxElementsInMemory:缓存最大数目
      maxElementsOnDisk：硬盘最大缓存个数。
      eternal:对象是否永久有效，一但设置了，timeout将不起作用。
      overflowToDisk:是否保存到磁盘，当系统当机时
      timeToIdleSeconds:设置对象在失效前的允许闲置时间（单位：秒）。仅当eternal=false对象不是永久有效时使用，可选属性，默认值是0，也就是可闲置时间无穷大。
      timeToLiveSeconds:设置对象在失效前允许存活时间（单位：秒）。最大时间介于创建时间和失效时间之间。仅当eternal=false对象不是永久有效时使用，默认是0.，也就是对象存活时间无穷大。
      diskPersistent：是否缓存虚拟机重启期数据 Whether the disk store persists between restarts of the Virtual Machine. The default value is false.
      diskSpoolBufferSizeMB：这个参数设置DiskStore（磁盘缓存）的缓存区大小。默认是30MB。每个Cache都应该有自己的一个缓冲区。
      diskExpiryThreadIntervalSeconds：磁盘失效线程运行时间间隔，默认是120秒。
      memoryStoreEvictionPolicy：当达到maxElementsInMemory限制时，Ehcache将会根据指定的策略去清理内存。默认策略是LRU（最近最少使用）。你可以设置为FIFO（先进先出）或是LFU（较少使用）。
      clearOnFlush：内存数量最大时是否清除。
      memoryStoreEvictionPolicy:可选策略有：LRU（最近最少使用，默认策略）、FIFO（先进先出）、LFU（最少访问次数）。
       -->

</ehcache>
