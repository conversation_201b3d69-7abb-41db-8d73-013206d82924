spring.application.name=web-platforms-msp

#core-business
#jsonrpc.core-business.server=http://core-business.dev.shouqianba.com/
#jsonrpc.core-business.server=http://localhost:11114/core-business
jsonrpc.core-business.server=http://core-business.test.shouqianba.com/

#bank-info-service
jsonrpc.bank-info-service.server=http://bank-info-service.test.shouqianba.com/

#merchant-audit-service
jsonrpc.merchant-audit.server=http://merchant-audit-service.test.shouqianba.com/

#user-service
jsonrpc.user-service.server=http://user-service.test.shouqianba.com/

#sms host短信网关发送地址
sms.host=http://sms-gateway.test.shouqianba.com/sms/send

#backend.upay.server
backend.upay.server=http://backend.test.shouqianba.com/

#msp入驻支付宝回调页面
msp.alipay.authinto.returnURL=http://upay-web-backend.test.shouqianba.com/msp/alipayAuthintoNotify


jsonrpc.upay-qrcode.server=http://upay-qrcode.test.shouqianba.com/

#upay-transaction-api
#jsonrpc.upay-transaction-api-querry.server=http://upay-transaction.test.shouqianba.com/
#jsonrpc.upay-transaction-api-export.server=http://upay-transaction.test.shouqianba.com/
jsonrpc.upay-transaction-api-querry.server=http://global-upay-transaction:19899/
jsonrpc.upay-transaction-api-export.server=http://global-upay-transaction:19899/
#会员基础服务
jsonrpc.upay-member.server=http://upay-member.test.shouqianba.com/
#微信公众号服务
jsonrpc.weixin-service.server=http://weixin-service.test.shouqianba.com/
#微信公众号第三方平台服务
jsonrpc.weixin-open.server=http://weixin-open.test.shouqianba.com/
#微信公众号第三方平台外网访问授权域名
weixin-open.url=http://m.test.wosai.cn/weixinopen/

#web-platforms-msp自身地址，用于提供给外部回调
web-platforms-msp.server=http://web-platforms-msp.test.shouqianba.com/

#微信免充值商户名单，半角逗号分隔
weixin_free_recharge_mchs=da6b3cf6-af99-11e5-9ec3-00163e00625b

#可视化转发接口地址
visual.proxy.url=http://wosai-data-revealingboard.test2.shouqianba.com/report

#app-backend-api
jsonrpc.app-backend-api.server=http://app-backend-process-service.test.shouqianba.com/

#upay-gateway
upay-gateway.server=http://upay-gateway.test.shouqianba.com/

#ka-core-business-api
jsonrpc.ka-core-business-api.server=http://116.62.244.38:29196/

#business-log-service
jsonrpc.bizlog.server = http://business-log.test.shouqianba.com/

#business-audit-api
jsonrpc.bizaudit.server = http://business-audit.test.shouqianba.com/
#shouqianba-merchant-api
jsonrpc.shouqianba-merchant-api.server=http://merchant.test.shouqianba.com/
#merchant-contract-api
jsonrpc.merchant-contract-api.server = http://merchant-contract.test.shouqianba.com/
#upay-grayscale
jsonrpc.upay-grayscale.server=http://upay-grayscale.test.shouqianba.com/
#h5支付
h5payment.proxy.url=http://h5-payment.test2.shouqianba.com/
uinvoice.basic.proxy.url = http://localhost:9000/rpc/basic
uinvoice.core.proxy.url = http://localhost:9000/rpc/core

jsonrpc.qrcode-picture.server = *************:11156

#redis
redis.url=***********
redis.port=6379
redis.database=2
redis.password=wosai1234
#cacheService\u7F13\u5B58\u6570\u636E\u7684\u8FC7\u671F\u65F6\u95F4\uFF0C\u5355\u4F4D\u6BEB\u79D2
redis.cacheService.expiredTime=86400
#cacheService\u7F13\u5B58\u6570\u636Ekey\u7684\u524D\u7F00
redis.cacheService.keyPrefix=mini_info
jsonrpc.merchant-contract-job.server =http://merchant-contract-job-test3.test.shouqianba.com

#transaction-report
jsonrpc.transaction-report.server = http://transaction-report.test.shouqianba.com

#merchant-invoice-api
#jsonrpc.merchant-invoice-api.server=http://localhost:20098/
jsonrpc.merchant-invoice-api.server=http://merchant-invoice.test.shouqianba.com



jsonrpc.profit.share.server = http://profit-sharing.test.shouqianba.com/

jsonrpc.merchant-bank-service.server=http://merchant-bank-service.test.shouqianba.com

jsonrpc.shouqianba-risk-service-api.server=http://shouqianba-risk-service.test.shouqianba.com

jsonrpc.e-invoice.server=http://upay-invoice.beta.iwosai.com
com.wosai.oss.static-bucket=wosai-statics
com.wosai.oss.static-base-url=https://statics.wosaimg.com
com.wosai.oss.internal=true

#trade-manage-service
jsonrpc.trade-manage-service.server=http://trade-manage-service.test.shouqianba.com/

jsonrpc.merchant-user-service.server=https://mapi.bayarlah.net/merchant-user-service


