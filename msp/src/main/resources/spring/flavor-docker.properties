spring.application.name=web-platforms-msp

#core-business
jsonrpc.core-business.server=http://core-business

#bank-info-service
jsonrpc.bank-info-service.server=http://bank-info-service.test.shouqianba.com/

#merchant-audit-service
jsonrpc.merchant-audit.server=http://merchant-audit-service.test.shouqianba.com/

#user-service
jsonrpc.user-service.server=http://user-service.test.shouqianba.com/

#sms host短信网关发送地址
sms.host=http://dev.wosai.cn:8210/sms/send

#backend.upay.server
backend.upay.server=http://backend-upay/

#msp入驻支付宝回调页面
msp.alipay.authinto.returnURL=http://upay-web-backend.dev.shouqianba.com/msp/alipayAuthintoNotify


jsonrpc.upay-qrcode.server=http://upay-qrcode/

#upay-transaction-api
jsonrpc.upay-transaction-api-querry.server=http://upay-transaction.dev.shouqianba.com/
jsonrpc.upay-transaction-api-export.server=http://upay-transaction.dev.shouqianba.com/

#会员基础服务
jsonrpc.upay-member.server=http://upay-member.dev.shouqianba.com/
#微信公众号服务
jsonrpc.weixin-service.server=http://weixin-service.dev.shouqianba.com/
#微信公众号第三方平台服务
jsonrpc.weixin-open.server=http://weixin-open.dev.shouqianba.com/
#微信公众号第三方平台外网访问授权域名
weixin-open.url=http://m.test.wosai.cn/weixinopen/

#web-platforms-msp自身地址，用于提供给外部回调
web-platforms-msp.server=http://web-platforms-msp/

#微信免充值商户名单，半角逗号分隔
weixin_free_recharge_mchs=

#可视化转发接口地址
visual.proxy.url=http://wosai-data-revealingboard.dev.shouqianba.com/report

#app-backend-api
jsonrpc.app-backend-api.server=http://app-backend-process-service.dev.shouqianba.com/

#upay-gateway
upay-gateway.server=http://upay-gateway.dev.shouqianba.com/

#business-log-service
jsonrpc.bizlog.server = http://business-log.dev.shouqianba.com/

#shouqianba-merchant-api
jsonrpc.shouqianba-merchant-api.server=http://merchant.test.shouqianba.com/

#upay-grayscale
jsonrpc.upay-grayscale.server=http://upay-grayscale.test.shouqianba.com/

#h5支付
h5payment.proxy.url=http://h5-payment.test2.shouqianba.com/

#uinvoice-gateway
uinvoice.basic.proxy.url = http://uinvoice-gateway.dev.shouqianba.com/rpc/basic
uinvoice.core.proxy.url = http://uinvoice-gateway.dev.shouqianba.com/rpc/core

#transaction-report
jsonrpc.transaction-report.server = http://transaction-report.test.shouqianba.com

jsonrpc.qrcode-picture.server = 10.24.243.217:11156

jsonrpc.profit.share.server = http://profit-sharing.test.shouqianba.com/


#redis
redis.url=redis
redis.port=6379
redis.database=2
redis.password=wosai1234
#cacheService缓存数据的过期时间，单位毫秒
redis.cacheService.expiredTime=86400
#cacheService缓存数据key的前缀
redis.cacheService.keyPrefix=mini_info


#merchant-invoice-api
jsonrpc.merchant-invoice-api.server=http://merchant-invoice.test.shouqianba.com
com.wosai.oss.internal=true

#trade-manage-service
jsonrpc.trade-manage-service.server=http://trade-manage-service.test.shouqianba.com/
