spring.application.name=web-platforms-msp

#core-business
jsonrpc.core-business.server=http://core-business.core.svc.cluster.local/

#bank-info-service
jsonrpc.bank-info-service.server=http://bank-info-service.core.svc.cluster.local/

#merchant-audit-service
jsonrpc.merchant-audit.server=http://merchant-audit-service.osp.svc.cluster.local/

#user-service
jsonrpc.user-service.server=http://user-service.core.svc.cluster.local/

#sms host短信网关发送地址
sms.host=http://sms-gateway.core.svc.cluster.local/sms/send

#backend.upay.server
backend.upay.server=http://backend-upay.pay.svc.cluster.local/

#msp入驻支付宝回调页面
msp.alipay.authinto.returnURL=http://upay-web-backend.pay.svc.cluster.local/msp/alipayAuthintoNotify


jsonrpc.upay-qrcode.server=http://upay-qrcode.core.svc.cluster.local/

#upay-transaction-api
jsonrpc.upay-transaction-api-querry.server=http://upay-transaction.core.svc.cluster.local/
jsonrpc.upay-transaction-api-export.server=http://upay-transaction.core.svc.cluster.local/


#会员基础服务
jsonrpc.upay-member.server=http://upay-member.loan.svc.cluster.local/
#微信公众号服务
jsonrpc.weixin-service.server=http://weixin-service.mk.svc.cluster.local/
#微信公众号第三方平台服务
jsonrpc.weixin-open.server=http://weixin-open.mk.svc.cluster.local/
#微信公众号第三方平台外网访问授权域名
weixin-open.url=https://weixin-open.iwosai.com/weixinopen/

#web-platforms-msp自身地址，用于提供给外部回调
web-platforms-msp.server=https://web-platforms-msp.iwosai.com/

#微信免充值商户名单，半角逗号分隔
weixin_free_recharge_mchs=bdf9b7f1-e01d-11e5-9ec3-00163e00625b,fce9c876-5d47-11e6-b79f-00163e00625b,d272abea-2875-11e6-bc0c-00163e00625b

#可视化转发接口地址
visual.proxy.url=http://wosai-data-revealingboard.beta.iwosai.com/report

#app-backend-api
jsonrpc.app-backend-api.server=http://app-backend-service.core.svc.cluster.local/

#upay-gateway
upay-gateway.server=http://upay-gateway.pay.svc.cluster.local/

#business-log-service
jsonrpc.bizlog.server = http://business-log.osp.svc.cluster.local/
#business-audit-api
jsonrpc.bizaudit.server = http://business-audit.osp.svc.cluster.local/


#shouqianba-merchant-api
jsonrpc.shouqianba-merchant-api.server=http://shouqianba-merchant-service.osp.svc.cluster.local/
#merchant-contract-api
jsonrpc.merchant-contract-api.server = http://merchant-contract.core.svc.cluster.local/
#upay-grayscale
jsonrpc.upay-grayscale.server=http://upay-grayscale.pay.svc.cluster.local/
#h5\u652F\u4ED8
h5payment.proxy.url=http://f-p-backend.shouqianba.com/

#uinvoice-gateway
uinvoice.basic.proxy.url = http://uinvoice-gateway.beta.iwosai.com/rpc/basic
uinvoice.core.proxy.url = http://uinvoice-gateway.beta.iwosai.com/rpc/core

jsonrpc.qrcode-picture.server = http://shouqianba-picture-service.core.svc.cluster.local

jsonrpc.profit.share.server = http://profit-sharing.pay.svc.cluster.local/

jsonrpc.merchant-bank-service.server=http://merchant-bank-service.core.svc.cluster.local

#ka-core-business-api
jsonrpc.ka-core-business-api.server=http://ka-core-business:29196/

#redis
redis.url=r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
redis.port=6379
redis.database=2
redis.password=roFXzHwXPY3RnI%5
#cacheService缓存数据的过期时间，单位毫秒
redis.cacheService.expiredTime=86400
#cacheService缓存数据key的前缀
redis.cacheService.keyPrefix=mini_info
jsonrpc.merchant-contract-job.server =http://merchant-contract-job.core.svc.cluster.local

#transaction-report
jsonrpc.transaction-report.server = http://transaction-report.core.svc.cluster.local

#merchant-invoice-api
jsonrpc.merchant-invoice-api.server=http://merchant-invoice.osp.svc.cluster.local/

jsonrpc.shouqianba-risk-service-api.server=http://shouqianba-risk-service.osp.svc.cluster.local

com.wosai.oss.static-bucket=wosai-statics
com.wosai.oss.static-base-url=https://statics.wosaimg.com
com.wosai.oss.internal=false

#trade-manage-service
#jsonrpc.trade-manage-service.server=http://trade-manage.core.svc.cluter.local/
jsonrpc.trade-manage-service.server=http://trade-manage.beta.iwosai.com/

jsonrpc.merchant-user-service.server=https://mapi.bayarlah.net/merchant-user-service

jsonrpc.e-invoice.server=http://upay-invoice.beta.iwosai.com
