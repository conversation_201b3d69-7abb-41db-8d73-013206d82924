spring.application.name=web-platforms-msp

#core-business
jsonrpc.core-business.server=http://core-business:19800/

#user-service
jsonrpc.user-service.server=http://user-service:11303/

#app-backend-api
jsonrpc.app-backend-api.server=http://app-backend:10013/

#upay-gateway
upay-gateway.server=http://upay-gateway:19801/

#upay-transaction-api
jsonrpc.upay-transaction-api-querry.server=http://global-upay-transaction:19899/
jsonrpc.upay-transaction-api-export.server=http://global-upay-transaction:19899/

##################

#bank-info-service
jsonrpc.bank-info-service.server=http://bank-info-service.internal.shouqianba.com/

#merchant-audit-service
jsonrpc.merchant-audit.server=http://merchant-audit-service.internal.shouqianba.com/



#sms host\u77ED\u4FE1\u7F51\u5173\u53D1\u9001\u5730\u5740
sms.host=http://sms-api.wosai.cn:8200/sms/send

#backend.upay.server
backend.upay.server=http://backend-portal.shouqianba.com/

#msp\u5165\u9A7B\u652F\u4ED8\u5B9D\u56DE\u8C03\u9875\u9762
msp.alipay.authinto.returnURL=https://upay-web-backend.shouqianba.com/msp/alipayAuthintoNotify


jsonrpc.upay-qrcode.server=http://upay-qrcode-internal.shouqianba.com/


#\u4F1A\u5458\u57FA\u7840\u670D\u52A1
jsonrpc.upay-member.server=http://upay-member-internal.shouqianba.com/
#\u5FAE\u4FE1\u516C\u4F17\u53F7\u670D\u52A1
jsonrpc.weixin-service.server=http://weixin-service-internal.shouqianba.com/
#\u5FAE\u4FE1\u516C\u4F17\u53F7\u7B2C\u4E09\u65B9\u5E73\u53F0\u670D\u52A1
jsonrpc.weixin-open.server=http://weixin-open-internal.shouqianba.com/
#\u5FAE\u4FE1\u516C\u4F17\u53F7\u7B2C\u4E09\u65B9\u5E73\u53F0\u5916\u7F51\u8BBF\u95EE\u6388\u6743\u57DF\u540D
weixin-open.url=http://weixin.open.wosai.cn/weixinopen/

#web-platforms-msp\u81EA\u8EAB\u5730\u5740\uFF0C\u7528\u4E8E\u63D0\u4F9B\u7ED9\u5916\u90E8\u56DE\u8C03
web-platforms-msp.server=https://web-platforms-msp.shouqianba.com/

#\u5FAE\u4FE1\u514D\u5145\u503C\u5546\u6237\u540D\u5355\uFF0C\u534A\u89D2\u9017\u53F7\u5206\u9694
weixin_free_recharge_mchs=31ce3636-dc5b-11e6-9007-ecf4bbdeffbc,5c0c6a3e-dc5c-11e6-9007-ecf4bbdeffbc,fc911dbc-dc5c-11e6-9007-ecf4bbdeffbc,64a671cc-e127-11e6-9007-ecf4bbdeffbc,f3e77903-e127-11e6-9007-ecf4bbdeffbc,a9c2be27-e12a-11e6-9007-ecf4bbdeffbc,37138014-e12b-11e6-9007-ecf4bbdeffbc,c49e4f43-e12b-11e6-9007-ecf4bbdeffbc,4565bdb1-e12d-11e6-9007-ecf4bbdeffbc,c70bc073-b300-11e5-9987-6c92bf21cf9b

#\u53EF\u89C6\u5316\u8F6C\u53D1\u63A5\u53E3\u5730\u5740
visual.proxy.url=http://wosai-data-revealingboard-internal.shouqianba.com/report

#business-log-service
jsonrpc.bizlog.server = http://business-log.internal.shouqianba.com/
#business-audit-api
jsonrpc.bizaudit.server = http://business-audit.internal.shouqianba.com/


#shouqianba-merchant-api
jsonrpc.shouqianba-merchant-api.server=http://merchant.internal.shouqianba.com/

#merchant-invoice-api
jsonrpc.merchant-invoice-api.server=  http://merchant-invoice.internal.shouqianba.com/
#merchant-contract-api
jsonrpc.merchant-contract-api.server = http://merchant-contract-internal.shouqianba.com/

#ka-core-business-api
jsonrpc.ka-core-business-api.server=http://ka-core-business:29196/

#upay-grayscale
jsonrpc.upay-grayscale.server=http://upay-grayscale.internal.shouqianba.com/

#h5\u652F\u4ED8
h5payment.proxy.url=http://f-p-backend.shouqianba.com/

#uinvoice-gateway
uinvoice.basic.proxy.url = http://internal-gw-i.shouqianba.com/rpc/basic
uinvoice.core.proxy.url = http://internal-gw-i.shouqianba.com/rpc/core

jsonrpc.qrcode-picture.server =http://picture-internal.shouqianba.com

jsonrpc.profit.share.server = http://profit-sharing.internal.shouqianba.com/

#redis
redis.url=r-zf8z76phvdg5rkz3bw.redis.kualalumpur.rds.aliyuncs.com
redis.port=6379
redis.database=2
redis.password=XjtvPIV3moQKT723
#cacheService\u7F13\u5B58\u6570\u636E\u7684\u8FC7\u671F\u65F6\u95F4\uFF0C\u5355\u4F4D\u6BEB\u79D2
redis.cacheService.expiredTime=86400
#cacheService\u7F13\u5B58\u6570\u636Ekey\u7684\u524D\u7F00
redis.cacheService.keyPrefix=mini_info
jsonrpc.merchant-contract-job.server =http://merchant-contract-job.internal.shouqianba.com

#transaction-report
jsonrpc.transaction-report.server = http://transaction-report.internal.shouqianba.com

jsonrpc.merchant-bank-service.server=http://merchant-bank-service.internal.shouqianba.com
jsonrpc.shouqianba-risk-service-api.server=http://internal.risk.shouqianba.com

com.wosai.oss.static-bucket=wosai-statics
com.wosai.oss.static-base-url=https://statics.wosaimg.com
com.wosai.oss.internal=true

#trade-manage-service
jsonrpc.trade-manage-service.server=http://trade-manage-service.internal.shouqianba.com/

jsonrpc.merchant-user-service.server=https://mapi.bayarlah.net/merchant-user-service

jsonrpc.e-invoice.server=http://upay-invoice.beta.iwosai.com
