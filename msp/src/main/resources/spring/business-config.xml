<?xml version="1.0" encoding="UTF-8"?>
<!--
Data and Service layers
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jpa="http://www.springframework.org/schema/data/jpa"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
			   http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa.xsd
			   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
			   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <bean class="com.wosai.upay.common.helper.UpayMethodValidationPostProcessor">
        <property name="validatedAnnotationType" value="com.wosai.upay.helper.CorePlatformsValidated"/>
    </bean>

    <bean class="com.wosai.upay.proxy.RpcProxyHandlerMapping"/>
    <bean class="com.wosai.upay.proxy.RpcProxyHandlerMappingAdaptor"/>

    <context:component-scan base-package="com.wosai.upay.service"/>
    <bean id="springContextHolder" class="com.wosai.upay.common.util.SpringContextHolder" lazy-init="false"/>

    <bean name="objectMapper" class="com.wosai.upay.common.helper.MyObjectMapper"/>
    <bean class="com.wosai.upay.service.CoreUserServiceImpl"/>
    <bean id="OssUrlEncrypt" name="OssUrlEncrypt" class="com.wosai.oss.OssUrlEncrypt"/>
    <bean id="OssStsClient" name="OssStsClient" class="com.wosai.oss.OssStsClient"/>
    <bean id="OssProperties" name="OssProperties" class="com.wosai.oss.configuration.OssProperties">
        <property name="staticBucket" value="${com.wosai.oss.static-bucket}"></property>
        <property name="staticBaseUrl" value="${com.wosai.oss.static-base-url}"></property>
        <property name="internal" value="${com.wosai.oss.internal}"></property>
    </bean>

    <context:property-placeholder location="classpath:spring/flavor-${shouqianba.flavor:default}.properties"/>

    <!-- 接入监控服务 -->
    <import resource="classpath*:/wosai-tracing.xml"/>
    <import resource="classpath*:/wosai-tracing-http.xml"/>
    <import resource="classpath*:/wosai-tracing-redis.xml"/>

    <import resource="redis-config.xml"/>
    <import resource="classpath:META-INF/sdk.xml"/>
    <import resource="dynamic-beans.xml"/>
    <bean class="com.wosai.upay.service.AstHelperImpl"/>
    <bean class="com.wosai.upay.service.CommonLoginServiceImpl">
        <property name="loginNeedCellphoneVerify" value="false"></property>
        <property name="loginSafeCheck" value="true"></property>
    </bean>
    <bean class="com.wosai.upay.service.SmsSendServiceImpl">
        <property name="smsHost" value="${sms.host}"></property>
    </bean>

    <bean class="com.wosai.upay.service.MspServiceImpl">
        <constructor-arg name="alipayAuthintoNotifyURL" value="${msp.alipay.authinto.returnURL}"></constructor-arg>
    </bean>

    <bean class="com.wosai.upay.service.MspOrderServiceImpl">
        <constructor-arg name="backendUpayUrl" value="${backend.upay.server}"></constructor-arg>
        <constructor-arg name="upayGatewayServer" value="${upay-gateway.server}"></constructor-arg>
    </bean>

    <bean class="com.wosai.upay.service.MspGroupServiceImpl">
        <constructor-arg name="backendUpayUrl" value="${backend.upay.server}"></constructor-arg>
    </bean>

    <!--<bean class="com.wosai.upay.service.MspTerminalServiceImpl">-->
    <!--<constructor-arg name="upay_qrcode_url" value="${jsonrpc.upay-qrcode.server}"></constructor-arg>-->
    <!--</bean>-->

    <bean id="jsonRpcRequestListener" class="com.wosai.upay.helper.JsonRpcRequestListener">
        <property name="projectName" value="web-platforms-msp"></property>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/user"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.UserService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <!--  引入merchant-user-service相关rpc接口  -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-user-service.server}/rpc/ucUser"></property>
        <property name="serviceInterface" value="com.wosai.app.service.v2.UcUserAccountService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-user-service.server}/rpc/merchantuserV2"></property>
        <property name="serviceInterface" value="com.wosai.app.service.v2.MerchantUserServiceV2"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-user-service.server}/rpc/group"></property>
        <property name="serviceInterface" value="com.wosai.app.service.GroupService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <!--  引入merchant-user-service相关rpc接口  -->

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/userRole"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.UserRoleService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/merchant"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MerchantService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.StoreService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/terminal"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TerminalService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/tradeConfig"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TradeConfigService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/application"></property>
        <property name="serviceInterface" value="com.wosai.upay.merchant.audit.api.service.ApplicationService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/userToken"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.UserTokenService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/vendor"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.VendorService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <bean class="com.wosai.upay.service.CommonServiceImpl"/>
    <bean class="com.wosai.upay.service.TranslateServiceImpl"/>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}/rpc/industry"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.IndustryService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/log"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.LogService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/group"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.GroupService"/>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>


    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/department"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.DepartmentService"/>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/common"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.BusinssCommonService"/>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/specialAuthWhitelist"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.SpecialAuthWhitelistService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/mspRefundTerminal"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.MspRefundTerminalService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-member.server}/rpc/bmerchant"></property>
        <property name="serviceInterface" value="com.wosai.upay.member.service.BMerchantService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-member.server}/rpc/dict"></property>
        <property name="serviceInterface" value="com.wosai.upay.member.service.DictService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.weixin-service.server}/rpc/message"></property>
        <property name="serviceInterface" value="com.wosai.weixin.service.service.MessageService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.weixin-open.server}/rpc/auth"></property>
        <property name="serviceInterface" value="com.wosai.weixin.open.service.AuthService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>


    <!-- upay-transaction-api begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-querry.server}/rpc/order"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.OrderService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-querry.server}/rpc/upayorder"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.UpayOrderService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-export.server}/rpc/export"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.ExportService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-querry.server}/rpc/transaction"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TransactionService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-export.server}/rpc/statementTask"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TaskLogService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-querry.server}/rpc/cache"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.CacheService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-querry.server}/rpc/statementObjectConfig"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.StatementObjectConfigService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl"
                  value="${jsonrpc.upay-transaction-api-querry.server}rpc/transaction_v2"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TransactionServiceV2"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <!-- upay-transaction-api end -->

    <!-- app-backend-api begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.app-backend-api.server}/rpc/merchantconfig"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IMerchantService"></property>
        <property name="serverName" value="app-backend-service"/>
    </bean>
    <!-- app-backend-api begin -->

    <!-- businsess-log  begin-->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizlog.server}rpc/object"></property>
        <property name="serviceInterface" value="com.wosai.business.log.service.BizObjectService"/>
        <property name="serverName" value="business-log-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizlog.server}rpc/objectColumn"></property>
        <property name="serviceInterface" value="com.wosai.business.log.service.BizObjectColumnService"/>
        <property name="serverName" value="business-log-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizlog.server}rpc/function"></property>
        <property name="serviceInterface" value="com.wosai.business.log.service.BizFunctionService"/>
        <property name="serverName" value="business-log-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizlog.server}rpc/opLog"></property>
        <property name="serviceInterface" value="com.wosai.business.log.service.BizOpLogService"/>
        <property name="serverName" value="business-log-service"/>
    </bean>
    <!-- businsess-log  end-->

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-qrcode.server}/rpc/qrcode"></property>
        <property name="serviceInterface" value="com.wosai.upay.qrcode.service.QrcodeService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>



    <!-- business-audit -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizaudit.server}rpc/audititem"></property>
        <property name="serviceInterface" value="com.wosai.business.audit.service.AuditItemService"></property>
        <property name="serverName" value="business-audit-service"></property>
    </bean>


    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizaudit.server}rpc/auditrecord"></property>
        <property name="serviceInterface" value="com.wosai.business.audit.service.AuditRecordService"></property>
        <property name="serverName" value="business-audit-service"></property>
    </bean>

    <!-- business-audit-api end -->



    <!-- shouqianba-merchant-service -->
<!--    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">-->
<!--        <property name="serviceUrl" value="${jsonrpc.shouqianba-merchant-api.server}rpc/merchantInvoice"></property>-->
<!--        <property name="serviceInterface" value="com.wosai.merchant.service.MerchantInvoiceService"></property>-->
<!--        <property name="serverName" value="shouqianba-merchant-service"></property>-->
<!--    </bean>-->

    <!-- shouqianba-merchant-service end -->

    <!-- merchant-invoice-service -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-invoice-api.server}rpc/merchantInvoice"></property>
        <property name="serviceInterface" value="com.wosai.operation.service.MerchantInvoiceService"></property>
        <property name="serverName" value="merchant-invoice-service"></property>
    </bean>

    <!-- merchant-invoice-service end -->

    <!-- ka-core-business-api -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.ka-core-business-api.server}rpc/pay_way"></property>
        <property name="serviceInterface" value="com.wosai.ka.core.service.PayWayService"></property>
        <property name="serverName" value="ka-core-business-service"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <!-- ka-core-business-api end -->

    <!-- merchant-contract begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-contract-api.server}rpc/providerTradeParams"></property>
        <property name="serviceInterface" value="com.wosai.upay.merchant.contract.service.ProviderTradeParamsService"/>
        <property name="serverName" value="merchant-contract"/>
    </bean>
    <!-- merchant-contract end -->

    <!-- upay-grayscale begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-grayscale.server}rpc/merchantGray"></property>
        <property name="serviceInterface" value="com.wosai.service.IMerchantGrayService"/>
        <property name="serverName" value="upay-grayscale"/>
    </bean>
    <!-- upay-grayscale end -->

    <!-- transaction-report begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.transaction-report.server}/rpc/account_report"></property>
        <property name="serviceInterface" value="com.wosai.service.IAccountReportService"/>
        <property name="serverName" value="transaction-report"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.transaction-report.server}/rpc/account_report_proxy"></property>
        <property name="serviceInterface" value="com.wosai.service.IAccountReportServiceProxy"/>
        <property name="serverName" value="transaction-report"/>
    </bean>
    <!-- transaction-report end -->


    <!-- trade-manage-service -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.trade-manage-service.server}rpc/switch"></property>
        <property name="serviceInterface" value="com.wosai.trade.service.SwitchService"></property>
        <property name="serverName" value="manage-service"/>
    </bean>


    <!-- merchant-contract-job begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-contract-job.server}/rpc/contractWeixin"></property>
        <property name="serviceInterface" value="com.wosai.upay.job.service.ContractWeixinService"/>
        <property name="serverName" value="merchant-contract-job"/>
    </bean>
    <!-- merchant-contract-job end -->

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-bank-service.server}/rpc/changeMerchantBank"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.service.BankAccountChangeService"/>
        <property name="serverName" value="merchant-bank-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-bank-service.server}/rpc/merchantBank"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.service.BankService"/>
        <property name="serverName" value="merchant-bank-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-bank-service.server}/rpc/bank_business_license"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.service.BankBusinessLicenseService"/>
        <property name="serverName" value="merchant-bank-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}/rpc/bankinfo"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.BankInfoService"/>
        <property name="serverName" value="bank-info-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}/rpc/cardbin"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.CardBinService"/>
        <property name="serverName" value="bank-info-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}/rpc/withdrawbank"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.WithdrawBankService"/>
        <property name="serverName" value="bank-info-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}/rpc/districts"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.DistrictsService"></property>
        <property name="serverName" value="bank-info-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-risk-service-api.server}/rpc/ocr"></property>
        <property name="serviceInterface" value="com.wosai.risk.service.IOcrService"/>
        <property name="serverName" value="shouqianba-risk-service"/>
        <property name="objectMapper">
            <bean class="com.wosai.upay.bean.IgnoreMapper" />
        </property>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.e-invoice.server}/rpc/invoice/merchant"></property>
        <property name="serviceInterface" value="com.wosai.einvoice.api.EInvoiceMerchantService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>

    <!-- 定义文件上传 -->
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver"/>

</beans>
