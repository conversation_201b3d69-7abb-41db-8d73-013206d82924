<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <property resource="logback-${shouqianba.flavor:-default}.properties"/>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <!-- To enable JMX Management -->
    <jmxConfigurator/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="com.wosai.upay.common.helper.NonEscapingJsonGeneratorDecorator"/>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{0} - %msg%n</pattern>
            <charset>UTF-8</charset>
            <includeCallerData>true</includeCallerData>
        </encoder>
    </appender>

    <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="com.wosai.upay.common.helper.NonEscapingJsonGeneratorDecorator"/>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{0} - %msg%n</pattern>
            <charset>UTF-8</charset>
            <includeCallerData>true</includeCallerData>
        </encoder>
        <target>System.err</target>
    </appender>

    <!-- ROOT appender -->
    <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logDir:-.}/upay-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="com.wosai.upay.common.helper.NonEscapingJsonGeneratorDecorator"/>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{0} - %msg%n</pattern>
            <charset>UTF-8</charset>
            <includeCallerData>true</includeCallerData>
        </encoder>
    </appender>



    <root level="info">
        <appender-ref ref="${rootAppender}"/>
    </root>

    <logger name="com.wosai" level="trace"/>
    <!-- None root loggers can override configurations defined higher in the hierarchy. -->
    <!--
    <logger name="com.wosai.controller" level="debug"/>
    <logger name="com.wosai.service" level="debug" additivity="false">
        <appender-ref ref="file"/>
    </logger>
    -->

</configuration>
