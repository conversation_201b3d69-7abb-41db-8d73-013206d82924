<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.wosai.upay</groupId>
    <artifactId>web-platforms-parent</artifactId>
    <version>1.2.2-beez-SNAPSHOT</version>
  </parent>

  <groupId>com.wosai.upay</groupId>
  <artifactId>msp</artifactId>
  <version>1.2.2-beez-SNAPSHOT</version>
  <packaging>war</packaging>


  <dependencies>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
      <version>3.1.4.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.wosai.upay.bank</groupId>
      <artifactId>merchant-bank-service-api</artifactId>
      <version>1.1.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-annotations</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
      <dependency>
      <groupId>com.wosai.trade</groupId>
      <artifactId>manage-api</artifactId>
      <version>1.0.2-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>app-backend-api</artifactId>
      <version>1.1.171-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.wosai</groupId>
      <artifactId>profit-sharing-api</artifactId>
      <version>1.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>upay-qrcode-api</artifactId>
      <version>1.2.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>web-platforms-common</artifactId>
      <version>1.2.2-beez-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>web-platforms-sdk</artifactId>
      <version>1.2.2-beez-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>upay-member-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>merchant-contract-api</artifactId>
      <version>1.0.3-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.wosai.weixin</groupId>
      <artifactId>service-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.wosai.weixin</groupId>
      <artifactId>open-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.2</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.8</version>
    </dependency>
    <dependency>
      <groupId>com.wosai</groupId>
      <artifactId>upay-grayscale-api</artifactId>
      <version>1.0.2-SNAPSHOT</version>
        <exclusions>
            <exclusion>
                <artifactId>wosai-util</artifactId>
                <groupId>com.wosai.pantheon</groupId>
            </exclusion>
        </exclusions>
    </dependency>
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>merchant-contract-job-api</artifactId>
      <version>1.5-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.wosai</groupId>
      <artifactId>transaction-report-api</artifactId>
      <version>1.1.3-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.nimbusds</groupId>
      <artifactId>nimbus-jose-jwt</artifactId>
      <version>8.2</version>
    </dependency>


    <dependency>
      <groupId>com.ctrip.framework.apollo</groupId>
      <artifactId>apollo-client</artifactId>
      <version>0.10.3</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.nextgen</groupId>
      <artifactId>springmvc-customization</artifactId>
      <version>2.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.common</groupId>
      <artifactId>wosai-common</artifactId>
      <version>1.6.7</version>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
      <version>4.3.9.RELEASE</version>
    </dependency>

    <!-- use ka-core-business begin -->
    <dependency>
      <groupId>com.wosai</groupId>
      <artifactId>ka-core-business-api</artifactId>
      <version>2.0.2-beez</version>
    </dependency>

    <!-- use ka-core-business end -->
   <!--global-upay-transaction-api-->
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>global-upay-transaction-api</artifactId>
      <version>1.2.38-ka-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>e-invoice-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>jsonrpc4j</artifactId>
          <groupId>com.wosai.middleware</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-lang3</artifactId>
          <groupId>org.apache.commons</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-core</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>logback-classic</artifactId>
          <groupId>ch.qos.logback</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.9.7</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.9.7</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.9.7</version>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <version>2.3</version>
        <configuration>
          <warName>msp</warName>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.mortbay.jetty</groupId>
        <artifactId>maven-jetty-plugin</artifactId>
        <configuration>
          <connectors>
            <connector implementation="org.mortbay.jetty.nio.SelectChannelConnector">
              <port>9203</port>
              <maxIdleTime>60000</maxIdleTime>
            </connector>
          </connectors>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.18.1</version>
        <configuration>
          <skipTests>true</skipTests>
        </configuration>
      </plugin>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
                <source>8</source>
                <target>8</target>
            </configuration>
        </plugin>
    </plugins>
  </build>

</project>
