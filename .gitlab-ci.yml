include:
  - project: "do/gitlab-ci"
    ref: master
    file: "/maven.yml"


variables:
  MSP: "msp"
  OSP: "osp"
  MAVEN_CLI_OPTS: "-DskipTests=true"
  JFROG_MVNC_OPTS: "--exclude-patterns=*"


.maven-build-base:
  script:
    - jfrog c show
    - jfrog mvnc --repo-deploy-snapshots=$ARTIFACTORY_REPO --repo-deploy-releases=$ARTIFACTORY_REPO --repo-resolve-releases=$ARTIFACTORY_REPO --repo-resolve-snapshots=$ARTIFACTORY_REPO --server-id-deploy=1 --server-id-resolve=1 $JFROG_MVNC_OPTS
    - jfrog  mvn clean -U install -DskipTests=true --build-name=$CI_PROJECT_NAME --build-number=$CI_PIPELINE_ID $MAVEN_CLI_OPTS
    - jfrog rt bce $CI_PROJECT_NAME $CI_PIPELINE_ID
    - jfrog rt bag $CI_PROJECT_NAME $CI_PIPELINE_ID
    - docker build -f ${MSP}/Dockerfile -t jfrog.wosai-inc.com/$DOCKER_REPO/$CI_PROJECT_NAME-${MSP}:$DOCKER_IMAGE_TAG ${MSP}
    - jfrog rt dp jfrog.wosai-inc.com/$DOCKER_REPO/$CI_PROJECT_NAME-${MSP}:$DOCKER_IMAGE_TAG $DOCKER_REPO --build-name=$CI_PROJECT_NAME-${MSP} --build-number=$CI_PIPELINE_ID
    - jfrog rt bp $CI_PROJECT_NAME-${MSP} $CI_PIPELINE_ID
    - docker build -f ${OSP}/Dockerfile -t jfrog.wosai-inc.com/$DOCKER_REPO/$CI_PROJECT_NAME-${OSP}:$DOCKER_IMAGE_TAG ${OSP}
    - jfrog rt dp jfrog.wosai-inc.com/$DOCKER_REPO/$CI_PROJECT_NAME-${OSP}:$DOCKER_IMAGE_TAG $DOCKER_REPO --build-name=$CI_PROJECT_NAME-${OSP} --build-number=$CI_PIPELINE_ID
    - jfrog rt bp $CI_PROJECT_NAME-${OSP} $CI_PIPELINE_ID