<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <parent>
    <groupId>com.wosai</groupId>
    <artifactId>common-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <modelVersion>4.0.0</modelVersion>
  <name>web-platforms-parent</name>
  <groupId>com.wosai.upay</groupId>
  <artifactId>web-platforms-parent</artifactId>
  <version>1.2.2-beez-SNAPSHOT</version>
  <packaging>pom</packaging>

  <modules>
    <module>osp</module>
    <module>vsp</module>
    <module>msp</module>
    <module>ssp</module>
    <module>web-platforms-common</module>
    <module>web-platforms-sdk</module>
  </modules>

  <properties>
    <core-business.version>2.9.10-SNAPSHOT</core-business.version>
    <app-backend-api.version>1.1.11-SNAPSHOT</app-backend-api.version>
    <spring.boot.version>1.1-SNAPSHOT</spring.boot.version>
    <jsonrpc4j.version>1.5.3</jsonrpc4j.version>
    <nextgen.version>2.0-SNAPSHOT</nextgen.version>
    <cglib-nodep.version>2.2</cglib-nodep.version>
    <app-gated-service.version>1.3-SNAPSHOT</app-gated-service.version>
    <shouqianba-withdraw-service.version>1.0.17-SNAPSHOT</shouqianba-withdraw-service.version>
    <shouqianba-risk-service.version>1.1.0-SNAPSHOT</shouqianba-risk-service.version>
    <tracing.version>2.5</tracing.version>
    <sales-system-backend-api.version>0.14.0-SNAPSHOT</sales-system-backend-api.version>
    <sales-system-service-api.version>0.5.3-SNAPSHOT</sales-system-service-api.version>
    <app-push-service-version>1.1.1-SNAPSHOT</app-push-service-version>
    <business-log.version>2.0.0-SNAPSHOT</business-log.version>
    <upay-transaction-api.version>1.2.46-SNAPSHOT</upay-transaction-api.version>
    <shouqianba-merchant-api.version>1.5.6-SNAPSHOT</shouqianba-merchant-api.version>
    <shouqianba-merchant-api.version>1.5.7-SNAPSHOT</shouqianba-merchant-api.version>
    <upay-side.version>1.0.1-SNAPSHOT</upay-side.version>
    <business-audit.version>1.0.1-SNAPSHOT</business-audit.version>
    <merchant-level-api.version>1.0.0-SNAPSHOT</merchant-level-api.version>
    <bsm-finance-service.version>1.0.5-SNAPSHOT</bsm-finance-service.version>
    <bsm-insurance-service.version>1.0.1-SNAPSHOT</bsm-insurance-service.version>
    <bsm-upay-activity-service.version>1.0.2-SNAPSHOT</bsm-upay-activity-service.version>
    <upay-activity-statistics.version>1.0.0-SNAPSHOT</upay-activity-statistics.version>
    <bsm-loan-service.version>1.1.1-SNAPSHOT</bsm-loan-service.version>
    <bsm-credit-pay-service.version>1.0.0-SNAPSHOT</bsm-credit-pay-service.version>
    <!-- Bean validation -->
    <validation.version>1.0.0.GA</validation.version>
    <hibernate-validator.version>4.3.0.Final</hibernate-validator.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>javax.mail</groupId>
      <artifactId>mail</artifactId>
      <version>1.5.0-b01</version>
    </dependency>
    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>2.3.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>3.12</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.2</version>
    </dependency>
    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
      <version>1.3.1</version>
    </dependency>
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>upay-wallet-api</artifactId>
      <version>1.1.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.biz</groupId>
      <artifactId>merchant-level-api</artifactId>
      <version>${merchant-level-api.version}</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>core-business-api</artifactId>
      <version>${core-business.version}</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>bank-info-api</artifactId>
      <version>1.0.3-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>merchant-audit-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>user-service-api</artifactId>
      <version>1.0.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.app</groupId>
      <artifactId>merchant-user-api</artifactId>
      <version>1.7.8</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>core-business-util</artifactId>
      <version>${core-business.version}</version>
    </dependency>

    <!-- use app-push-service begin -->
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>app-push-api</artifactId>
      <version>${app-push-service-version}</version>
    </dependency>
    <!-- use app-push-service end -->
    <!-- use app-backend-api begin -->
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>app-backend-api</artifactId>
      <version>${app-backend-api.version}</version>
    </dependency>
    <!-- use app-backend-api end -->

    <!-- use app-gated-service begin -->
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>app-gated-api</artifactId>
      <version>${app-gated-service.version}</version>
    </dependency>
    <!-- use app-gated-service end -->

    <!-- use shouqianba-withdraw-service begin -->
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>shouqianba-withdraw-service-api</artifactId>
      <version>${shouqianba-withdraw-service.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>upay-common</artifactId>
          <groupId>com.wosai.upay</groupId>
        </exclusion>
      </exclusions>

    </dependency>
    <!-- use shouqianba-withdraw-service end -->
    <!-- use business-log begin -->
    <dependency>
      <groupId>com.wosai</groupId>
      <artifactId>business-log-api</artifactId>
      <version>${business-log.version}</version>
    </dependency>
    <!-- use business-log end -->

    <!-- use upay-side begin -->
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>upay-side-api</artifactId>
      <version>${upay-side.version}</version>
    </dependency>
    <!-- use upay-side end -->

    <!-- support xlsx begin-->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>3.12</version>
    </dependency>
    <!-- support xlsx end-->

    <dependency>
      <groupId>com.wosai.assistant</groupId>
      <artifactId>sales-system-backend-api</artifactId>
      <version>${sales-system-backend-api.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.github.the-world-wang</groupId>
          <artifactId>jsonrpc4j</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.wosai.sales</groupId>
      <artifactId>sales-system-service-api</artifactId>
      <version>${sales-system-service-api.version}</version>
    </dependency>

    <dependency>
      <groupId>com.wosai</groupId>
      <artifactId>shouqianba-risk-api</artifactId>
      <version>${shouqianba-risk-service.version}</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.business</groupId>
      <artifactId>audit-api</artifactId>
      <version>${business-audit.version}</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.bsm</groupId>
      <artifactId>finance-backend-api</artifactId>
      <version>${bsm-finance-service.version}</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.bsm</groupId>
      <artifactId>insurance-backend-api</artifactId>
      <version>${bsm-insurance-service.version}</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>upay-activity-api</artifactId>
      <version>${bsm-upay-activity-service.version}</version>
    </dependency>
    <!--统计项目-->
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>statistics-backend-api</artifactId>
      <version>${upay-activity-statistics.version}</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.bsm</groupId>
      <artifactId>loan-backend-api</artifactId>
      <version>${bsm-loan-service.version}</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.bsm</groupId>
      <artifactId>credit-pay-backend-api</artifactId>
      <version>${bsm-credit-pay-service.version}</version>
    </dependency>

    <!--<dependency>-->
    <!--<groupId>com.wosai.upay</groupId>-->
    <!--<artifactId>upay-api</artifactId>-->
    <!--<version>2.20-SNAPSHOT</version>-->
    <!--</dependency>-->


    <!-- use transaction begin -->
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>upay-transaction-api</artifactId>
      <version>${upay-transaction-api.version}</version>
    </dependency>
    <!-- use transaction end -->
    <dependency>
      <groupId>com.wosai.upay</groupId>
      <artifactId>global-upay-transaction-api</artifactId>
      <version>1.2.38-ka-SNAPSHOT</version>
    </dependency>
    <!-- use merchant-invoice begin -->
    <dependency>
      <groupId>com.wosai.operation</groupId>
      <artifactId>merchant-invoice-api</artifactId>
      <version>0.0.2-SNAPSHOT</version>
    </dependency>
    <!-- use merchant-invoice end -->

    <!-- use shouqianba-merchant-service begin -->
    <dependency>
      <groupId>com.wosai</groupId>
      <artifactId>shouqianba-merchant-api</artifactId>
      <version>${shouqianba-merchant-api.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.wosai.upay</groupId>
          <artifactId>core-business-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.wosai</groupId>
          <artifactId>shouqianba-risk-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.briandilley.jsonrpc4j</groupId>
          <artifactId>jsonrpc4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.wosai.common</groupId>
          <artifactId>wosai-common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- use shouqianba-merchant-service end -->

    <dependency>
      <groupId>org.apache.kafka</groupId>
      <artifactId>kafka-clients</artifactId>
        <version>1.0.0-cp1</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.nextgen</groupId>
      <artifactId>data-common</artifactId>
      <version>${nextgen.version}</version>
    </dependency>

    <dependency>
      <groupId>com.wosai.nextgen</groupId>
      <artifactId>springmvc-customization</artifactId>
      <version>${nextgen.version}</version>
    </dependency>
    <dependency>
      <groupId>com.wosai.nextgen</groupId>
      <artifactId>nextgen-model</artifactId>
      <version>${nextgen.version}</version>
    </dependency>
    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
      <version>${validation.version}</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>${hibernate-validator.version}</version>
    </dependency>
    <!-- aop -->
    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjrt</artifactId>
      <version>1.7.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjweaver</artifactId>
      <version>1.7.2</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
      <exclusions>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-config</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>

    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>servlet-api</artifactId>
    </dependency>
    <dependency>
      <groupId>cglib</groupId>
      <artifactId>cglib-nodep</artifactId>
      <version>${cglib-nodep.version}</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>net.sf.ehcache</groupId>
      <artifactId>ehcache</artifactId>
      <version>2.10.1</version>
    </dependency>

    <dependency>
      <groupId>com.github.briandilley.jsonrpc4j</groupId>
      <artifactId>jsonrpc4j</artifactId>
      <version>${jsonrpc4j.version}</version>
    </dependency>

    <!-- Groovy dependencies-->
    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy-all</artifactId>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <scope>compile</scope>
    </dependency>
    <!--runtime-->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jcl-over-slf4j</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jul-to-slf4j</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>net.logstash.logback</groupId>
      <artifactId>logstash-logback-encoder</artifactId>
      <version>4.8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.1.3</version>
      <scope>compile</scope>
    </dependency>

    <!-- Spring Test framework dependencies -->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- used by Spring MVC Test framework -->
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-library</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-core</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- 接入监控服务-客户端 -->
    <dependency>
      <groupId>com.wosai</groupId>
      <artifactId>wosai-brave153-api</artifactId>
      <version>2.5.1-SNAPSHOT</version>
    </dependency>
    <!-- bsm -->
    <dependency>
      <groupId>com.wosai.bsm</groupId>
      <artifactId>metamorphosis</artifactId>
      <version>1.0.1-SNAPSHOT</version>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.wosai</groupId>
        <artifactId>spring3-boot-dependencies</artifactId>
        <version>1.1-SNAPSHOT</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <!-- we need this mojo to add extra source folder such that maven-sources-plugin functions properly -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>validate</phase>
            <goals><goal>add-source</goal></goals>
            <configuration>
              <sources>
                <source>src/main/groovy</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>

    </plugins>

    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.1</version>
          <configuration>
            <compilerId>groovy-eclipse-compiler</compilerId>
            <!--
                  <compilerArgument>nowarn</compilerArgument>
            -->
            <compilerArguments><Xlint/></compilerArguments>
            <verbose>false</verbose>
            <source>1.7</source>
            <target>1.7</target>
            <showWarnings>true</showWarnings>
          <useIncrementalCompilation>true</useIncrementalCompilation>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.codehaus.groovy</groupId>
              <artifactId>groovy-eclipse-compiler</artifactId>
              <version>2.9.1-01</version>
            </dependency>
            <dependency>
              <groupId>org.codehaus.groovy</groupId>
              <artifactId>groovy-eclipse-batch</artifactId>
              <version>2.3.7-01</version>
            </dependency>
            <!--  Uncomment to compile against Groovy 1.7.10 -->
            <!-- <dependency>
                 <groupId>org.codehaus.groovy</groupId>
                 <artifactId>groovy-eclipse-batch</artifactId>
                 <version>1.7.10-02</version>
                 </dependency> -->
          </dependencies>
        </plugin>


        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <versionRange>[1.0.0,)</versionRange>
                    <goals>
                      <goal>add-source</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore/>
                    <!--
                    <execute>
                      <runOnIncremental>true</runOnIncremental>
                            </execute>
                    -->
                  </action>
                </pluginExecution>
                <!--
                    <pluginExecution>
              <pluginExecutionFilter>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <versionRange>[1.0.0,)</versionRange>
                <goals>
                  <goal>copy-resources</goal>
                </goals>
              </pluginExecutionFilter>
              <action>
                <execute>
                  <runOnIncremental>true</runOnIncremental>
                        </execute>
              </action>
                    </pluginExecution>
                -->
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>

      </plugins>
    </pluginManagement>

  </build>

  <distributionManagement>
    <repository>
      <id>central</id>
      <name>maven-virtual-dev</name>
      <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>maven-virtual-dev</name>
      <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
    </snapshotRepository>
  </distributionManagement>


</project>
