<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wosai.upay</groupId>
        <artifactId>web-platforms-parent</artifactId>
        <version>1.2.2-beez-SNAPSHOT</version>
    </parent>

    <groupId>com.wosai.upay</groupId>
    <artifactId>osp</artifactId>
    <version>1.2.2-beez-SNAPSHOT</version>
    <packaging>war</packaging>

    <dependencies>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.8.11</version>
        </dependency>

        <dependency>
            <artifactId>opr-task-api</artifactId>
            <groupId>com.wosai.operation</groupId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.app</groupId>
            <artifactId>app-config-service-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>app-backend-api</artifactId>
            <version>1.1.26-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>notice-api</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.pub</groupId>
            <artifactId>alipay-authinto-api</artifactId>
            <version>1.8-rc1</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>22.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.operation</groupId>
            <artifactId>merchant-activity-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.operation</groupId>
            <artifactId>merchant-card-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>opr-point-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>reward-service-api</artifactId>
            <version>1.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.operation</groupId>
            <artifactId>merchant-tool-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>web-platforms-common</artifactId>
            <version>1.2.1-beez-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>janus-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-profit-api</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>web-platforms-sdk</artifactId>
            <version>1.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>merchant-contract-api</artifactId>
            <version>1.0.21-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-addin-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-addin-bean</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>shouqianba-terminal-service-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.iot</groupId>
            <artifactId>shouqianba-iot-api</artifactId>
            <version>2.1.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.8</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>1.2.1.RELEASE</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.6.3</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>clearance-service-api</artifactId>
            <version>1.3.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.8.47</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>alipay-risk-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>remit-gateway-api</artifactId>
            <version>0.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sp</groupId>
            <artifactId>sp-user-service-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>0.10.4</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.common</groupId>
            <artifactId>wosai-common</artifactId>
            <version>1.6.7</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>merchant-contract-job-api</artifactId>
            <version>2.1.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay.bank</groupId>
            <artifactId>merchant-bank-service-api</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>aop-backend-notification-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-swipe-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>core-business-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-poi-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-push-api</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>profit-sharing-api</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>upay-grayscale-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.trade</groupId>
            <artifactId>manage-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.shouqianba.workflow</groupId>
            <artifactId>sp-workflow-api</artifactId>
            <version>1.5.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.sp</groupId>
            <artifactId>merchant-management-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <warName>osp</warName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>maven-jetty-plugin</artifactId>
                <configuration>
                    <connectors>
                        <connector implementation="org.mortbay.jetty.nio.SelectChannelConnector">
                            <port>9204</port>
                            <maxIdleTime>60000</maxIdleTime>
                        </connector>
                    </connectors>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
