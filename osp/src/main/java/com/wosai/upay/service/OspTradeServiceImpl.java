package com.wosai.upay.service;

import com.google.common.collect.Sets;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.bean.Order;
import com.wosai.upay.client.SmsClient;
import com.wosai.upay.client.UpayGatewayClient;
import com.wosai.upay.client.UpayTransactionClient;
import com.wosai.upay.client.model.ReconciliationRequest;
import com.wosai.upay.client.model.RefundRequest;
import com.wosai.upay.client.model.SmsSendRequest;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TerminalActivationCode;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.model.user.MerchantUserStoreAuth;
import com.wosai.upay.core.model.user.MspRefundTerminal;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.user.api.service.MspRefundTerminalService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR> Date: 2019-10-17 Time: 17:08
 */
@Service
public class OspTradeServiceImpl implements OspTradeService {
    private static final Logger LOGGER = LoggerFactory.getLogger(OspTradeServiceImpl.class);
    private static final String VENDOR_APP_APPID_SERVICE = "2016111100000033";

    private static final int CAPTCHA_LENGTH = 6;
    private static final String TEMPLATE = "veqof4";
    private static final String SESSION_KEY_CAPTCHA = "captcha";

    private static final String REFUND_CAPTCHA = "captcha";
    private static final String REFUND_REQUEST_NO = "refundRequestNo";
    private static final String REFUND_AMOUNT = "refundAmount";
    private static final String REFUND_TERMINAL_SN = "terminalSn";
    private static final String REFUND_SN = "sn";
    private static final String REFUND_COMMENT = "comment";
    private static final String REFUND_TYPE = "refundType";

    private static final String RECONCILIATION_SN = "sn";
    private static final String RECONCILIATION_TERMINAL_SN = "terminalSn";

    private static final String ORDER_STATUS = "status";
    private static final Set<String> ALLOW_RECONCILIATION_ORDER_STATUS
            = Sets.newHashSet("0", "1300", "1501");


    @Resource
    private SmsClient smsClient;
    @Resource
    private UpayGatewayClient upayGatewayClient;
    @Resource
    private UpayTransactionClient upayTransactionClient;
    @Autowired
    private BusinessLogService businessLogService;
    @Resource
    private MspRefundTerminalService mspRefundTerminalService;
    @Resource
    private StoreService storeService;
    @Resource
    private TerminalService terminalService;
    @Resource
    private BusinssCommonService businssCommonService;
    @Resource
    private UserService userService;


    @Override
    public boolean sendSms() {
        String phone = getUserPhone();
        String captcha = genCaptcha();

        SmsSendRequest sendRequest = buildSmsSendRequest(phone, captcha);

        boolean success = smsClient.sendSms(sendRequest);
        if (success) {
            saveCaptchaToSession(captcha);
        }

        return success;
    }

    @Override
    public boolean refund(Map<String, Object> request) {
        //入参校验
        checkParams(request);
        //验证码校验
        if (!verifyCaptcha(request)) {
            throw new UpayException(UpayException.CODE_AUTH_CODE_ERROR, "验证码错误");
        }
        //构建退款请求参数
        RefundRequest refundRequest = buildRefundRequest(request);
        //调用网关退款接口
        boolean success = upayGatewayClient.refund(refundRequest);

        //调用网关退款成功，记录商户日志
        if (success) {
            String sn = MapUtil.getString(request, REFUND_SN);
            Map<String, Object> transaction = upayTransactionClient.getLastTransaction(sn);
            if (MapUtils.isEmpty(transaction)) {
                return true;
            }

            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, null,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, transaction,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "transaction",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "transaction",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id",
                            BizOpLog.REMARK, MapUtil.getString(request, REFUND_COMMENT),
                            BizOpLog.OP_TIME, System.currentTimeMillis(),
                            BizOpLog.BUSINESS_FUNCTION_CODE, "1000060"
                    )
            ));
        }

        return success;
    }


    @Override
    public boolean reconciliation(Map<String, Object> request) {
        String sn = MapUtil.getString(request, RECONCILIATION_SN);
        Map<String, Object> order = upayTransactionClient.getOrderDetail(sn);
        if (Objects.nonNull(order)) {
            String orderStatus = MapUtil.getString(order, ORDER_STATUS);
            if (ALLOW_RECONCILIATION_ORDER_STATUS.contains(orderStatus)) {
                ReconciliationRequest reconciliationRequest = buildReconciliationRequest(request);
                return upayGatewayClient.reconciliation(reconciliationRequest);
            }
        }

        return false;
    }


    private ReconciliationRequest buildReconciliationRequest(Map<String, Object> request) {
        String terminalSn = (String) request.get(RECONCILIATION_TERMINAL_SN);
        String sn = (String) request.get(RECONCILIATION_SN);

        ReconciliationRequest reconciliationRequest = new ReconciliationRequest();
        reconciliationRequest.setSn(sn);
        reconciliationRequest.setTerminalSn(terminalSn);
        return reconciliationRequest;
    }

    private void checkParams(Map<String, Object> request) {
        String captcha = (String) request.get(REFUND_CAPTCHA);
        String refundRequestNo = (String) request.get(REFUND_REQUEST_NO);
        String refundAmount = (String) request.get(REFUND_AMOUNT);
        String terminalSn = (String) request.get(REFUND_TERMINAL_SN);
        String sn = (String) request.get(REFUND_SN);
        String comment = (String) request.get(REFUND_COMMENT);
        String refundType = (String) request.get(REFUND_TYPE);
        if (StringUtils.isBlank(captcha) || captcha.length() != CAPTCHA_LENGTH) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "验证码非法");
        }
        if (StringUtils.isBlank(refundRequestNo)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "退款请求单号不能为空");
        }
        if (!StringUtils.isNumeric(refundAmount)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "退款金额非法");
        }
        if (StringUtils.isBlank(terminalSn)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "终端号不能为空");
        }
        if (StringUtils.isBlank(sn)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "订单号不能为空");
        }
        if (StringUtils.isBlank(comment)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "退款备注不能为空");
        }
        if (StringUtils.isEmpty(refundType)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "退款类型不能为空");
        }

    }

    private boolean verifyCaptcha(Map<String, Object> request) {
        String captcha = (String) request.get(REFUND_CAPTCHA);
        String sessionCaptcha = (String) HttpRequestUtil.getSession().getAttribute(SESSION_KEY_CAPTCHA);

        return StringUtils.equals(captcha, sessionCaptcha);
    }

    private RefundRequest buildRefundRequest(Map<String, Object> request) {
        String refundType = (String) request.get(REFUND_TYPE);
        String sn = MapUtil.getString(request, RECONCILIATION_SN);
        Map<String, Object> order = upayTransactionClient.getOrderDetail(sn);

        Map refundTerminalInfo = getRefundTerminal(order, Long.valueOf(refundType));

        String refundRequestNo = (String) request.get(REFUND_REQUEST_NO);
        String refundAmount = (String) request.get(REFUND_AMOUNT);
        String terminalSn = MapUtil.getString(refundTerminalInfo, "terminal_sn");
        String operator = getUserNickName();
        String comment = (String) request.get(REFUND_COMMENT);

        RefundRequest refundRequest = new RefundRequest();
        refundRequest.setTerminalSn(terminalSn);
        refundRequest.setSn(sn);
        refundRequest.setRefundRequestNo(refundRequestNo);
        refundRequest.setRefundAmount(refundAmount);
        refundRequest.setOperator(operator);
        refundRequest.setReflect(comment);
        return refundRequest;
    }

    private void saveCaptchaToSession(String captcha) {
        HttpSession httpSession = HttpRequestUtil.getSession();
        httpSession.setAttribute(SESSION_KEY_CAPTCHA, captcha);
    }

    private SmsSendRequest buildSmsSendRequest(String phone, String captcha) {
        SmsSendRequest.Vars vars = new SmsSendRequest.Vars();
        vars.setAuthcode(captcha);

        SmsSendRequest sendRequest = new SmsSendRequest();
        sendRequest.setTo(phone);
        sendRequest.setTemplate(TEMPLATE);
        sendRequest.setVars(vars);

        return sendRequest;
    }

    private String genCaptcha() {
        StringBuilder captchaTemp = new StringBuilder(CAPTCHA_LENGTH);
        ThreadLocalRandom random = ThreadLocalRandom.current();
        for (int i = 0; i < CAPTCHA_LENGTH; i++) {
            captchaTemp.append(random.nextInt(10));
        }

        return captchaTemp.toString();
    }

    private String getUserPhone() {
        return (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_CELLPHONE);
    }

    private String getUserNickName() {
        return (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
    }


    private Map getRefundTerminal(Map order, long refundType) {
        if (order == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "订单号不正确");
        }
        String terminalId = null;
        String storeId = "";
        if (MspRefundTerminal.REFUND_TYPE_ORI_STORE_ORI_TERMINAL == refundType) {
            terminalId = BeanUtil.getPropString(order, Order.TERMINAL_ID);
        } else if (MspRefundTerminal.REFUND_TYPE_ORI_STORE_REFUND_TERMINAL == refundType) {
            String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
            storeId = BeanUtil.getPropString(order, Order.STORE_ID);

            ListResult result = mspRefundTerminalService.findMspRefundTerminals(
                    null,
                    CollectionUtil.hashMap(
                            MspRefundTerminal.REFUND_TYPE, MspRefundTerminal.REFUND_TYPE_ORI_STORE_REFUND_TERMINAL,
                            MspRefundTerminal.MERCHANT_ID, merchantId,
                            MspRefundTerminal.STORE_ID, storeId
                    ));
            if (result.getTotal() < 1) {
                Map refundTerminal = createActivatedTerminal(storeId, CollectionUtil.hashMap(
                        ConstantUtil.KEY_TERMINAL_NAME, "退款终端"
                ));
                terminalId = BeanUtil.getPropString(refundTerminal, DaoConstants.ID);
                mspRefundTerminalService.createMspRefundTerminal(CollectionUtil.hashMap(
                        MspRefundTerminal.REFUND_TYPE, MspRefundTerminal.REFUND_TYPE_ORI_STORE_REFUND_TERMINAL,
                        MspRefundTerminal.MERCHANT_ID, merchantId,
                        MspRefundTerminal.STORE_ID, storeId,
                        MspRefundTerminal.TERMINAL_ID, terminalId
                ));

            } else {
                Map refundTerminal = result.getRecords().get(0);
                terminalId = BeanUtil.getPropString(refundTerminal, MspRefundTerminal.TERMINAL_ID);
            }
        } else if (MspRefundTerminal.REFUND_TYPE_REFUND_STORE_REFUND_TERMINAL == refundType) {
            String merchantId = BeanUtil.getPropString(order, Order.MERCHANT_ID);
            //todo 真实门店和终端被删除
            ListResult result = mspRefundTerminalService.findMspRefundTerminals(
                    null,
                    CollectionUtil.hashMap(
                            MspRefundTerminal.REFUND_TYPE, MspRefundTerminal.REFUND_TYPE_REFUND_STORE_REFUND_TERMINAL,
                            MspRefundTerminal.MERCHANT_ID, merchantId
                    ));
            if (result.getTotal() < 1) {
                //创建退款门店
                Map refundStore = storeService.createStore(CollectionUtil.hashMap(
                        Store.MERCHANT_ID, merchantId,
                        Store.NAME, "总部门店"
                ));
                storeId = BeanUtil.getPropString(refundStore, DaoConstants.ID);
                //todo: 事务 -- 创建退款终端
                Map refundTerminal = createActivatedTerminal(storeId, CollectionUtil.hashMap(
                        ConstantUtil.KEY_TERMINAL_NAME, "总部门店退款终端"
                ));
                terminalId = BeanUtil.getPropString(refundTerminal, DaoConstants.ID);
                mspRefundTerminalService.createMspRefundTerminal(CollectionUtil.hashMap(
                        MspRefundTerminal.REFUND_TYPE, MspRefundTerminal.REFUND_TYPE_REFUND_STORE_REFUND_TERMINAL,
                        MspRefundTerminal.MERCHANT_ID, merchantId,
                        MspRefundTerminal.STORE_ID, storeId,
                        MspRefundTerminal.TERMINAL_ID, terminalId
                ));
                //关联所有账户
                if (getSessionObjectType().equals("merchant")) {
                    bindMerchantAdminUserStore(merchantId, storeId);
                }
            } else {
                Map refundTerminal = result.getRecords().get(0);
                storeId = BeanUtil.getPropString(refundTerminal, MspRefundTerminal.STORE_ID);
                terminalId = BeanUtil.getPropString(refundTerminal, MspRefundTerminal.TERMINAL_ID);
            }
        } else {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "未填写退款门店和终端类型");
        }
        return CollectionUtil.hashMap(
                "terminal_id", terminalId,
                "terminal_sn", businssCommonService.getTerminalSnById(terminalId)
        );
    }

    private Map createActivatedTerminal(String storeId, Map terminalInfo) {
        String storeSn = businssCommonService.getStoreSnById(storeId);
        String clientSn = BeanUtil.getPropString(terminalInfo, Terminal.CLIENT_SN);
        String deviveFingerPrint = BeanUtil.getPropString(terminalInfo, Terminal.DEVICE_FINGERPRINT);
        String osVer = BeanUtil.getPropString(terminalInfo, Terminal.OS_VERSION);
        String sdkVer = BeanUtil.getPropString(terminalInfo, Terminal.SDK_VERSION);
        String longitude = BeanUtil.getPropString(terminalInfo, Terminal.LONGITUDE);
        String latitude = BeanUtil.getPropString(terminalInfo, Terminal.LATITUDE);
        Object extraInfo = BeanUtil.getProperty(terminalInfo, Terminal.EXTRA);
        String defaultTerminalName = BeanUtil.getPropString(terminalInfo, ConstantUtil.KEY_TERMINAL_NAME);
        Map activation = terminalService.createActivationCodeV2(null, null, storeSn, defaultTerminalName, 1);
        Map terminal = terminalService.activateV2(
                VENDOR_APP_APPID_SERVICE,
                BeanUtil.getPropString(activation, TerminalActivationCode.CODE),
                clientSn,
                deviveFingerPrint,
                defaultTerminalName,
                osVer,
                sdkVer,
                longitude,
                latitude,
                extraInfo);
        return terminal;
    }

    protected void bindMerchantAdminUserStore(String merchantId, String storeId) {
        if (StringUtil.empty(storeId) || StringUtil.empty(merchantId)) {
            return;
        }
        try {
            PageInfo pageInfo = new PageInfo(1, 1000, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
            Map params = CollectionUtil.hashMap(
                    ConstantUtil.KEY_MERCHANT_ID, merchantId,
                    MerchantUser.ROLE, MerchantUser.ROLE_ADMIN
            );
            ListResult users = userService.findMerchantUsers(pageInfo, params);
            while (users != null && users.getRecords() != null && users.getRecords().size() > 0) {
                LOGGER.info("bind users[{}]", users);
                for (Map user : users.getRecords()) {
                    String merchantUserId = BeanUtil.getPropString(user, DaoConstants.ID);
                    //已绑定
                    ListResult hasResult = userService.findMerchantUserStoreAuths(new PageInfo(1, 1), CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchantId,
                            MerchantUserStoreAuth.MERCHANT_USER_ID, merchantUserId,
                            MerchantUserStoreAuth.STORE_ID, storeId
                    ));
                    if (hasResult != null && hasResult.getRecords() != null && hasResult.getRecords().size() > 0) {
                        continue;
                    }

                    //执行绑定
                    //1.获取门店列表
                    //获取绑定列表
                    PageInfo pageInfoStore = new PageInfo(1, 1000, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
                    Map paramsStore = CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchantId,
                            MerchantUserStoreAuth.MERCHANT_USER_ID, merchantUserId
                    );
                    List<String> storeIds = new ArrayList<>();
                    storeIds.add(storeId);
                    ListResult storeAuths = userService.findMerchantUserStoreAuths(pageInfoStore, paramsStore);
                    LOGGER.info("bind users stores user[{}] stores[{}]", user, storeAuths);
                    while (storeAuths != null && storeAuths.getRecords() != null && storeAuths.getRecords().size() > 0) {
                        for (Map storeAuth : storeAuths.getRecords()) {
                            String storeIdAuth = BeanUtil.getPropString(storeAuth, MerchantUserStoreAuth.STORE_ID);
                            if (!StringUtil.empty(storeId) && !storeIds.contains(storeIdAuth)) {
                                storeIds.add(storeIdAuth);
                            }
                        }
                        pageInfoStore.setPage(pageInfoStore.getPage() + 1);
                        storeAuths = userService.findMerchantUsers(pageInfoStore, paramsStore);
                    }

                    //转为，分割符号
                    String storeIdStr = storeIds.get(0);
                    for (int i = 1; i < storeIds.size(); i++) {
                        storeIdStr = storeIdStr + "," + storeIds.get(i);
                    }
                    LOGGER.info("bind users stores save storeIdStr[{}]", storeIdStr);

                    //保存
                    userService.saveMerchantUserStoreAuth(merchantUserId, storeIdStr);

                }
                pageInfo.setPage(pageInfo.getPage() + 1);
                users = userService.findMerchantUsers(pageInfo, params);
            }
        } catch (Exception e) {
            LOGGER.error("绑定账户管理员门店异常merchantId[{}], storeId[{}]", merchantId, storeId, e);
        }
    }

    private String getSessionObjectType() {
        Map ospAccount = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        String groupId = BeanUtil.getPropString(ospAccount, "group_id", "");
        //普通商户
        if (StringUtil.empty(groupId)) {
            return "merchant";
        }
        //集团商户
        return "group";
    }
}
