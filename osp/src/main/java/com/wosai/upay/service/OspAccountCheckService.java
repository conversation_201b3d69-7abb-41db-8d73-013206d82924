package com.wosai.upay.service;

import java.util.Map;

public interface OspAccountCheckService {

    /**
     * 获取收钱吧app展示手续费汇总开关
     *
     * @param map
     * @return
     */
    Map<String, Object> getTradeFeeConfig(Map map);

    /**
     * 修改收钱吧app是否展示手续费汇总开关
     *
     * @param map
     * @return
     */
    void updateTradeFeeConfig(Map map);

    /**
     * 获取配置
     * merchant_id
     * type
     * @return
     */
    Map<String, Object> getSetting(Map map);

    /**
     * 获取对账周期偏移配置
     */
    Map<String, Object> getOffsetHour(Map map);


    /**
     * 修改配置
     */
    void updateSetting(Map map);
}
