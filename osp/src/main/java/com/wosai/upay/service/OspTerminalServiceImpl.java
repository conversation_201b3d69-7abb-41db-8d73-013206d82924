package com.wosai.upay.service;

import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.googlecode.jsonrpc4j.ProxyUtil;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.iot.api.rpc.SmartCloudSoundService;
import com.wosai.iot.api.rpc.vo.QueryVO;
import com.wosai.iot.api.rpc.vo.UnbindVO;
import com.wosai.iot.api.sp.service.DeviceQrcodeService;
import com.wosai.sales.core.service.TerminalLogService;
import com.wosai.upay.bean.TerminalModel;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.SpringWebUtil;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TerminalConfig;
import com.wosai.upay.core.service.*;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.helper.CommonUtil;
import com.wosai.upay.helper.TerminalHelper;
import com.wosai.upay.service.remote.QrcodeService;
import com.wosai.upay.swipe.service.SwipeServices;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpSession;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.wosai.upay.common.util.ConstantUtil.KEY_SN;


public class OspTerminalServiceImpl implements OspTerminalService {
    private static final Logger logger = LoggerFactory.getLogger(OspTerminalServiceImpl.class);

    @Autowired
    private TerminalService terminalService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private SupportService supportService;
    private QrcodeService qrcodeService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private BusinessLogService businessLogService;
    @Autowired
    private SmartCloudSoundService smartCloudSoundService;
    @Autowired
    private TerminalLogService terminalLogService;
    @Autowired
    private DeviceQrcodeService deviceQrcodeService;
    @Autowired
    private SwipeServices swipeServices;
    @Autowired
    private TradeConfigService tradeConfigService;

    public OspTerminalServiceImpl(String upay_qrcode_url) {
        JsonRpcHttpClient client = null;
        try {
            client = new JsonRpcHttpClient(new URL(upay_qrcode_url + "rpc/qrcode"));
        } catch (MalformedURLException e) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        this.qrcodeService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                QrcodeService.class,
                client);
    }

    @Override
    public Map bindQrcode(Map request) {
        String name;
        if (request.get("name") != null) {
            name = request.get("name").toString();
        } else {
            name = "门店码";
        }
        Map qrcodeNew = CollectionUtil.hashMap("store_sn", request.get("store_sn"),
                "qr_code", request.get("qr_code"), "name", name, "bind_by", SpringWebUtil.getCurrentRequest().getSession().getAttribute(CommonLoginService.SESSION_USERNAME), "bind_source", "SP平台");
        Map qrcodeMap = qrcodeService.bindQrcode(qrcodeNew);
        Map qrcode = qrcodeService.getQrcodeByCode(BeanUtil.getPropString(request, "qr_code"));


        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, null,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, qrcode,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_ADD,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "qrcode",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "terminal_id"
                )
        ));

        return qrcodeMap;
    }

    @Override
    public void enableTerminal(Map<String, Object> request) {
        String terminalId = BeanUtil.getPropString(request, DaoConstants.ID);
        Map terminal = terminalService.getTerminal(terminalId);
        //如果是门店码,upay-qrcode修改门店码和终端状态
        if (TerminalHelper.isQRCodeType(BeanUtil.getPropInt(terminal, Terminal.TYPE))) {
            String qrcode = BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT);
            Map terminalOld = terminalService.getTerminal(terminalId);
            qrcodeService.enableQrcodeByQrcode(qrcode);
            Map terminalNew = terminalService.getTerminal(terminalId);

            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, terminalOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, terminalNew,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "terminal",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id"
                    )
            ));

            deleteCache(terminalId);
            return;
        }

        Map terminalOld = terminalService.getTerminal(terminalId);
        terminalService.enableTerminal(terminalId);
        Map terminalNew = terminalService.getTerminal(terminalId);


        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, terminalOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, terminalNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "terminal",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id"
                )
        ));

        deleteCache(terminalId);
    }

    @Override
    public void disableTerminal(Map<String, Object> request) {
        String terminalId = BeanUtil.getPropString(request, DaoConstants.ID);
        Map terminal = terminalService.getTerminal(terminalId);
        //如果是门店码,upay-qrcode修改门店码和终端状态
        if (TerminalHelper.isQRCodeType(BeanUtil.getPropInt(terminal, Terminal.TYPE))) {
            String qrcode = BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT);
            Map terminalOld = terminalService.getTerminal(terminalId);
            qrcodeService.disableQrcodeByQrcode(qrcode);
            Map terminalNew = terminalService.getTerminal(terminalId);

            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, terminalOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, terminalNew,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "terminal",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id"
                    )
            ));
            deleteCache(terminalId);
            return;
        }


        Map terminalOld = terminalService.getTerminal(terminalId);
        terminalService.disableTerminal(terminalId);
        Map terminalNew = terminalService.getTerminal(terminalId);


        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, terminalOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, terminalNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "terminal",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id"
                )
        ));
        deleteCache(terminalId);
    }

    @Override
    public void unbindTerminal(Map<String, Object> request) {
        String terminalId = BeanUtil.getPropString(request, DaoConstants.ID);
        Map terminal = terminalService.getTerminal(terminalId);
        //如果是门店码,upay-qrcode修改门店码和终端状态
        if (TerminalHelper.isQRCodeType(BeanUtil.getPropInt(terminal, Terminal.TYPE))) {
            String qrcode = BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT);
            Map terminalOld = terminalService.getTerminal(terminalId);
            Map qrcodeOld = qrcodeService.getQrcodeByCode(qrcode);
            qrcodeService.deleteQrcodeTagByQrcodeId(BeanUtil.getPropString(qrcodeOld, DaoConstants.ID));
            qrcodeService.unbindQrcodeByQrcode(qrcode);
            Map terminalNew = terminalService.getTerminal(terminalId);
            Map qrcodeNew = qrcodeService.getQrcodeByCode(qrcode);
            long time = new Date().getTime();

            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, terminalOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, terminalNew,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "terminal",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id",
                            BizOpLog.OP_TIME, time
                    )
            ));

            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, qrcodeOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, qrcodeNew,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "qrcode",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "terminal_id",
                            BizOpLog.OP_TIME, time
                    )
            ));


            deleteCache(terminalId);
            return;
        }
        Map terminalOld = terminalService.getTerminal(terminalId);
        terminalService.unbindTerminal(terminalId);
        Map terminalNew = terminalService.getTerminal(terminalId);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, terminalOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, terminalNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "terminal",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id"
                )
        ));
        deleteCache(terminalId);
    }

    @Override
    public Map updateTerminal(Map<String, Object> request) {
        String name = BeanUtil.getPropString(request, ConstantUtil.KEY_NAME);
        if (!StringUtil.empty(name)) {
            Map terminal = terminalService.getTerminal(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
            if (TerminalHelper.isQRCodeType(BeanUtil.getPropInt(terminal, Terminal.TYPE))) {
                String qrcode = BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT);
                qrcodeService.updateQrcodeNameByQrcode(qrcode, name);
            }
        }
        String terminalId = BeanUtil.getPropString(request, ConstantUtil.KEY_ID);
        Map terminalOld = terminalService.getTerminal(terminalId);
        Map terminalNew = terminalService.updateTerminal(request);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, terminalOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, terminalNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "terminal",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id"
                )
        ));

        return terminalNew;
    }

    @Override
    public Map getTerminal(Map<String, Object> request) {
        return terminalService.getTerminal(BeanUtil.getPropString(request, DaoConstants.ID));
    }

    @Override
    public Map getTerminalBySn(Map<String, Object> request) {
        return terminalService.getTerminalBySn(BeanUtil.getPropString(request, ConstantUtil.KEY_SN));
    }

    @Override
    public ListResult findTerminals(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        ListResult data = terminalService.findTerminals(pageInfo, request);
        //查询门店client_sn
        addStoreInfo(data.getRecords(), CollectionUtil.hashMap(
                Store.CLIENT_SN, "store_client_sn"
        ));
        //查询改终端的型号
        addTerminalModel(data.getRecords());
        //查询刷卡终端信息
        addSwipeInfo(data.getRecords());
        return data;
    }

    @Override
    public Map createActivationCode(Map<String, Object> request) {
        String solicitor_sn = BeanUtil.getPropString(request, "solicitor_sn");
        String code = BeanUtil.getPropString(request, "code");
        String storeSn = BeanUtil.getPropString(request, "store_sn");
        String terminal_name = BeanUtil.getPropString(request, "terminal_name");
        long limit = BeanUtil.getPropLong(request, "limit");

        Map resultMap = terminalService.createActivationCodeV2(solicitor_sn, code, storeSn, terminal_name, limit);
        if (resultMap != null) {
            String usage_limits = BeanUtil.getPropString(resultMap, "usage_limits");
            String remaining = BeanUtil.getPropString(resultMap, "remaining");
            String expire_time = BeanUtil.getPropString(resultMap, "expire_time");
            String merchant_id = BeanUtil.getPropString(resultMap, "merchant_id");
            Map terminal_activation_codeNew = CollectionUtil.hashMap(
                    "code", code,
                    "default_terminal_name", terminal_name,
                    "status", "1",
                    "usage_limits", usage_limits,
                    "remaining", remaining,
                    "expire_time", expire_time,
                    "merchant_id", merchant_id
            );

            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, null,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, terminal_activation_codeNew,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_ADD,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "terminal_activation_code",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                    )
            ));
            return terminal_activation_codeNew;
        }
        return resultMap;
    }

    @Override
    public ListResult getActivationCodes(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        String store_id = BeanUtil.getPropString(request, "store_id");
        String merchant_id = BeanUtil.getPropString(request, "merchant_id");//getSessionMerchantId() ;
        return terminalService.getActivationCodes(merchant_id, store_id, pageInfo, request);
    }

    @Override
    public Map getHemayunInvoiceTerminal(Map<String, Object> request) {
        Map result = null;
        String qrCode = WosaiMapUtils.getString(request, "qr_code");
        if (WosaiStringUtils.isNotEmpty(qrCode)) {
            result = qrcodeService.getHemayunInvoiceTerminal(qrCode);
        }
        return result;
    }

    @Override
    public ListResult findSmartCloudSound(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        QueryVO queryVO = new QueryVO();
        queryVO.setMerchant_id(MapUtils.getString(request, "merchant_id"));
        queryVO.setStore_id(MapUtils.getString(request, "store_id"));
        queryVO.setName(MapUtils.getString(request, "name"));
        queryVO.setDevice_sn(MapUtils.getString(request, "sn"));

        ListResult data = smartCloudSoundService.getSmartCloudSoundList(pageInfo, queryVO);
        //查询门店client_sn
        addStoreInfo(data.getRecords(), CollectionUtil.hashMap(
                Store.CLIENT_SN, "store_client_sn",
                Store.NAME, "store_name"
        ));
        return data;
    }


    private void addStoreInfo(List<Map> data, Map<String, String> keyMaps) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        List<String> storeIds = CommonUtil.getValues(data, Terminal.STORE_ID);
        ListResult stores = storeService.findStores(new PageInfo(1, storeIds.size()), CollectionUtil.hashMap(
                "store_ids", CommonUtil.getValues(data, Terminal.STORE_ID)
        ));
        Map<String, Map> storeMap = CommonUtil.convert(stores.getRecords(), ConstantUtil.KEY_ID);
        for (Map record : data) {
            String storeId = MapUtils.getString(record, Terminal.STORE_ID);
            Map store = MapUtils.getMap(storeMap, storeId);
            for (String key : keyMaps.keySet()) {
                record.put(keyMaps.get(key), MapUtils.getString(store, key));
            }
        }
    }
    
    private void addSwipeInfo(List<Map> data) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        try {
            List<String> terminalIds = CommonUtil.getValues(data, DaoConstants.ID);
            Map<String, Map<String,Object>> result = swipeServices.getSwipeCardInfos(terminalIds);
            if(null == result || result.isEmpty()) {
                return;
            }
            for (Map record : data) {
                record.put("swipe", result.get(MapUtils.getString(record, DaoConstants.ID)));
            }
        }catch (Throwable e) {
            logger.error("error in swipeServices.getSwipeCardInfos", e);
        }
    }

    @Override
    public boolean unbindSmartCloudSound(Map<String, Object> request) {
        Long id = MapUtils.getLongValue(request, ConstantUtil.KEY_ID);
        UnbindVO unbindVO = new UnbindVO();
        unbindVO.setId(id);
        unbindVO.setOperator_id(getUserIdAndName()[0]);
        unbindVO.setOperator_name(getUserIdAndName()[1]);
        String unbindFrom = MapUtils.getString(request, "unbind_from");
        if (StringUtils.isNotBlank(unbindFrom)) {
            unbindVO.setUnbind_from(unbindFrom);
        }
        String remark = MapUtils.getString(request, "remark");
        if (StringUtils.isNotBlank(remark)) {
            unbindVO.setRemark(remark);
        }
        boolean isOk = smartCloudSoundService.unbindSmartCloudSound(unbindVO);
        terminalLogService.saveUnbindLogs(
                CollectionUtil.hashMap(
                        "unbind_operator_id", getUserIdAndName()[0],
                        "unbind_operator_name", getUserIdAndName()[1],
                        "data_source", "iot#iot_store_device",
                        "terminal_id", id
                )
        );
        return isOk;

    }

    @Override
    public Map getQrcodeByIot(Map<String, Object> param) {
        return deviceQrcodeService.getQrcodeByIot(param);
    }

    @Override
    public Map getIotByQrcode(Map<String, Object> param) {
        return deviceQrcodeService.getIotByQrcode(param);
    }

    @Override
    public List listTerminalConfigStatus(Map<String, Object> request) {
        String terminalId = BeanUtil.getPropString(request, "terminal_id");
        if (terminalId == null || terminalId.length() == 0) {
            throw new IllegalArgumentException("参数错误");
        }

        return tradeConfigService.getAnalyzedTerminalConfigs(terminalId);
    }

    @Override
    public void updateTerminalConfigStatus(Map request) {
        String terminalId = BeanUtil.getPropString(request, ConstantUtil.KEY_TERMINAL_ID);
        Integer payWay = BeanUtil.getPropInt(request, TerminalConfig.PAYWAY, -1);
        payWay = payWay == -1 ? null : payWay;

        Map terminalConfigBefore = tradeConfigService.getTerminalConfigByTerminalIdAndPayway(terminalId, payWay);
        tradeConfigService.updateTerminalConfigStatus(request);
        Map terminalConfigAfter = tradeConfigService.getTerminalConfigByTerminalIdAndPayway(terminalId, payWay);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, terminalConfigBefore,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, terminalConfigAfter,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "terminal",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "terminal_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "terminal_id"
                )
        ));

        String merchantSn = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_SN);
        supportService.removeCachedParams(merchantSn);
    }

    private String[] getUserIdAndName() {
        HttpSession session = HttpRequestUtil.getSession();
        Object accountId = session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        Object username = session.getAttribute(CommonLoginService.SESSION_USERNAME);

        return new String[]{String.valueOf(accountId), String.valueOf(username)};
    }


    private void deleteCache(String terminalId) {
        Map terminal = terminalService.getTerminal(terminalId);
        Map merchant = merchantService.getMerchantByMerchantId(BeanUtil.getPropString(terminal, "merchant_id"));
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
    }

    private void addTerminalModel(List<Map> records) {
        for (Map data : records) {
            String vendorAppAppId = (String) data.get("vendor_app_appid");
            data.put("model", TerminalModel.getDesc(vendorAppAppId));
        }
    }
}
