package com.wosai.upay.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.upay.common.bean.ListResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

public interface OspFinanceService {
    /**********************User Info***********************/
    /**
     * 根据商户ID或SN获取理财用户(ID优先)
     *
     * @param request
     * @return
     */
    Map<String, Object> getUserInfoByIdOrSn(Map<String, Object> request);

    /**
     * 获取理财商户列表
     *
     * @param request
     * @return
     */
    ListResult findUserInfoList(Map<String, Object> request);

    /**
     * 判断用户是否开通理财
     *
     * @param request
     * @return
     */
    Map<String, Object> isOpenFinance(Map<String, Object> request);

    /**********************User Amount***********************/
    /**
     * 获取当前理财余额详情
     *
     * @param request
     * @return
     */
    Map<String, Object> getUserAmountByUserId(Map<String, Object> request);

    /**
     * 获取理财余额变动流水详情
     *
     * @param request
     * @return
     */
    Map<String, Object> getUserAmountRecord(Map<String, Object> request);

    /**
     * 余额变动流水列表
     *
     * @param request
     * @return
     */
    ListResult findUserAmountRecordList(Map<String, Object> request);

    /**
     * 理财流水信息导出
     *
     * @param request
     * @return
     */
    Map exportUserAmountRecordList(Map<String, Object> request);

    /**
     * 对账任务信息列表
     *
     * @param request
     * @return
     */
    ListResult findStatementTaskList(Map<String, Object> request);

    /**
     * 删除对账任务信息
     *
     * @param request
     * @return
     */
    void deleteStatementTask(Map<String, Object> request);

    /***********************Income*************************/
    /**
     * 获取收益详情
     *
     * @param request
     * @return
     */
    Map<String, Object> getIncomeRecord(Map<String, Object> request);

    /**
     * 获取收益列表
     *
     * @param request
     * @return
     */
    ListResult findIncomeRecordList(Map<String, Object> request);

    /**
     * 获取用户最近两天的的最新一次收益
     *
     * @param request
     * @return
     */
    Map<String, Object> getLastTwoDayIncomeAmount(Map<String, Object> request);

    /**
     * 获取用户昨日收益金额
     *
     * @param request
     * @return
     */
    Map<String, Object> getIncomeAmountYesterday(Map<String, Object> request);

    /**
     * 获取用户前日收益金额
     *
     * @param request
     * @return
     */
    Map<String, Object> getIncomeAmountDayBeforeYesterday(Map<String, Object> request);

    /************************daily record**************************/
    /**
     * 获取理财交易详情
     *
     * @param request
     * @return
     */
    Map<String, Object> getDailyRecordByDate(Map<String, Object> request);

    /**
     * 获取理财交易最新详情(今天或昨天有万份收益的)
     *
     * @param request
     * @return
     */
    Map<String, Object> getLastTwoDayDailyRecord(Map<String, Object> request);

    /**
     * 获取理财交易信息列表
     *
     * @param request
     * @return
     */
    ListResult findDailyRecordList(Map<String, Object> request);

    /*****************************bank info***************************/
    /**
     * 获取支持快赎的理财银行卡列表
     *
     * @param request
     * @return
     */
    Map<String, Map> getAllWithdrawRealTimeBanks(Map<String, Object> request);

    /****************************rule****************************/
    /**
     * 获取余额自动转入理财规则
     *
     * @param request
     * @return
     */
    Map<String, Object> getBalanceAutoSwitchRule(Map<String, Object> request);

    /**************************withdraw record******************/
    /**
     * 获取理财详情
     *
     * @param request
     * @return
     */
    Map<String, Object> getWithdrawRecordDetail(Map<String, Object> request);

    /**
     * 获取提现列表
     *
     * @param request
     * @return
     */
    ListResult findWithdrawRecordList(Map<String, Object> request);

    /**
     * 获取打款结果
     *
     * @param request
     * @return
     */
    Map<String, Object> getWithdrawStatus(Map<String, Object> request);

    /**
     * 更新提现记录备注
     *
     * @param request
     * @return
     */
    Map<String, Object> updateWithdrawRecordRemark(Map<String, Object> request);

    /**
     * 获取打款结果列表
     *
     * @param request
     * @return
     */
    List<Map> findWithdrawStatusList(Map<String, Object> request);

    /**
     * 获取打款流水详情
     *
     * @param request
     * @return
     */
    ListResult getUserAmountTurnover(Map<String, Object> request);

    /***********************purchase record************************/

    /**
     * 分页查询申购记录
     *
     * @param request
     * @return
     */
    ListResult findPurchaseRecordsList(Map<String, Object> request);

    /**
     * 获取申购记录详情
     *
     * @param request
     * @return
     */
    Map<String, Object> getPurchaseRecord(Map<String, Object> request);

    /**
     * 根据type获取相应的规则
     * @param request
     * @return
     */
    Map<String, Object> getRuleByType(Map<String, Object> request);

    /**
     * 修改快赎规则
     * @param request
     * @return
     */
    Map<String, Object> updateRealtimeWithDrawRule(Map<String, Object> request) throws JsonProcessingException, UnsupportedEncodingException;

    /**
     * 修改慢赎规则
     * @param request
     * @return
     */
    Map<String, Object> updateStandardWithDrawRule(Map<String, Object> request);

    /**
     * 修改申购规则
     * @param request
     * @return
     */
    Map<String,Object> updatePurchaseRule(Map<String,Object> request);

    /**
     * 修改赎回全局系统开关
     * @param request
     * @return
     */
    Map<String,Object> updateWithdrawSystemDisableRule(Map<String,Object> request);

    /**
     * 修改全局银行禁用规则
     * @param request
     * @return
     */
    Map<String, Object> updateWithdrawBankDisableRule(Map<String, Object> request);

    /**
     * 添加全局理财相关公告配置
     * @param request
     * @return
     */
    Map<String, Object> addNotice(Map<String, Object> request);

    /**
     * app理财相关文案配置
     * @param request
     * @return
     */
    Map<String, Object> updateWalletSwitchRule(Map<String, Object> request);

    /**
     * 批量导入理财白名单
     *
     * @param file
     * @return
     */
    Map batchImportWhitelist(MultipartFile file);

    /**
     * 批量开关余额提现页面理财转入入口
     *
     * @param file
     * @return
     */
    Map batchOpenOrCloseFinancePurchaseInWalletWithdraw(MultipartFile file, String openOrClose);


    /**
     * 批量提交打款
     * @param request
     * @return
     */
    List<Map> submitRemit(Map<String, Object> request);

    /**
     * 打款成功
     * @param request
     * @return
     */
    List<Map> remitSuccess(Map<String,Object> request);

    /**
     * 打款失败需人工
     * @param request
     * @return
     */
    List<Map> remitFailToManual(Map<String, Object> request);
    /**
     * 等待打款结果
     * @param request
     * @return
     */
    List<Map> waitForRemitResult(Map<String, Object> request);
    /**
     * 查询打款结果
     * @param request
     * @return
     */
    ListResult queryRemitResult(Map<String, Object> request);

    /**
     * 打款失败已返还
     * @param request
     * @return
     */
    List<Map> transferFail(Map<String, Object> request);

    /**
     * 批量更新理财快赎的免费次数
     * @param request
     * @return
     */
    Map batchUpdateFinanceDrawRealTimeFreeCount(Map<String, Object> request);

    /**
     * 批量关闭理财账户
     * @param request
     * @return
     */
    Map batchCloseUser(Map<String, Object> request);

    /**
     * 批量开启理财账户
     * @param request
     * @return
     */
    Map batchEnableUser(Map<String, Object> request);
    /**
     * 批量禁用理财账户
     * @param request
     * @return
     */
    Map batchDisableUser(Map<String, Object> request);


    /**
     * 获取Notice规则
     * @param request
     * @return
     */
    Map getNoticeRule(Map request);

    /**
     * 批量重开理财用户名单
     * @param request
     * @return
     */
    Map batchReEnableUser(Map request);
    /**
     * 批量开通理财在线客服
     * @param request
     * @return
     */
    Map batchOnlineService(Map request);

    /**
     * 批量重新发起赎回
     */
    List batchReWithdraw(Map request);

    /**
     * 开通/关闭快赎
     */
    Map operateWithdrawRealTime(Map request);

    /**
     * 获取自动添加规则列表
     */
    List<Map> getAutoAddRules(Map request);

    /**
     * 根据code获取自动添加规则
     */
    Map getAutoAddRuleByCode(Map request);

    /**
     * 修改自动添加规则
     */
    Map updateAutoAddRule(Map request);

    /**
     * 新增自动添加规则
     */
    Map addAutoAddRule(Map request);

    /**
     * 根据code删除自动添加规则
     */
    void deleteAutoAddRuleByCode(Map request);


    /**
     * 批量结算-慢赎
     * @param request
     * @return
     */
    List<Map> batchWithdrawSlow(Map request);

    /**
     * 批量结算-快赎
     * @param request
     * @return
     */
    List<Map> batchWithdrawFast(Map request);


    /**
     * 获取用户当前换卡记录
     * @param request
     * @return
     */
    Map getUserCurrentBankAccountRecord(Map request);


    /**
     * 获取拉卡拉可用余额
     * @param request
     * @return
     */
    Map queryRedeemAmount(Map request);

    /**
     * 重新提交换卡
     * @param request
     */
    void reChangeCard(Map request);



}
