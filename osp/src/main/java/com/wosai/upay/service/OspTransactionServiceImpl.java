package com.wosai.upay.service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import com.wosai.upay.service.queryCheck.TradeQueryCheck;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.service.ExportService;
import com.wosai.upay.transaction.service.TaskLogService;
import com.wosai.upay.transaction.service.TransactionServiceV2;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.OrderUtil;

public class OspTransactionServiceImpl implements OspTransactionService {

    @Autowired
    private TransactionServiceV2 transactionServiceV2;
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private TranslateService translateService;
    @Autowired
    private ExportService exportService;
    @Autowired
    private TaskLogService taskLogService;

    public static final String DAY_SDF_PATTERN_YYYYMMDD = "yyyyMMdd";

    @Override
    public ListResult findTransactions(Map request) {
        TradeQueryCheck.timeSpanCheck(request);
		return transactionServiceV2.getTransactionList(PageInfoUtil.extractPageInfo(request), getQueryTransactionParams(request));
	}

    @Override
    public List getTransactionListByOrderSn(Map request) {
        return transactionServiceV2.getTransactionListByOrderSn(BeanUtil.getPropString(request, "order_sn"));
    }

    @Override
    public Map createExportTransactionQueryTask(Map<String, Object> request) {
		String user_id = BeanUtil.getPropString((Map) HttpRequestUtil.getSession().getAttribute("osp_account"), DaoConstants.ID);
        //系统检查是否可以导出
        allowExport(user_id, "transaction_query");

        //参数检查
        long date_start = BeanUtil.getPropLong(request, "date_start");
        long date_end = BeanUtil.getPropLong(request, "date_end");
        if (date_start <= 0 || date_end <= 0 || date_start > date_end) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "流水明细导出时间有误");
        }
        String timeZoneStr = BeanUtil.getPropString(request, "timeZone");
        int upayQueryType = BeanUtil.getPropInt(request, "upayQueryType");
        return exportService.createExportStatementTask(CollectionUtil.hashMap(
                StatementTaskLog.TYPE, StatementTaskLog.TYPE_TRANSACTION_QUERY,
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_SP,
                StatementTaskLog.TITLE, getExportTitle(date_start, date_end, "SP", StatementTaskLog.TYPE_ORDER, timeZoneStr, upayQueryType),
                StatementTaskLog.USER_ID, user_id
        ), getQueryTransactionParams(request));
	}
	
	 /**
     * 检查是否允许导出
     */
    private void allowExport(String user_id, String type) {
        //检查用户七日内正在执行的任务数是否小于50
        long now = new Date().getTime();
        long sevenDaysAgo = now - OspOrderStatementServiceImpl.EXPORT_RUNNING_LIMIT_TIME;
        ListResult taskResult = taskLogService.findTaskApplyLogs(new PageInfo(1, 10, sevenDaysAgo, now), CollectionUtil.hashMap(
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_SP,
                StatementTaskLog.USER_ID, user_id,
                StatementTaskLog.APPLY_STATUSES, StatementTaskLog.APPLY_STATUS_RUNNING
        ));
        if (taskResult != null && taskResult.getTotal() >= OspOrderStatementServiceImpl.EXPORT_RUNNING_LIMIT_COUNT) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("当前已有%s个对账单任务在生成中,请等待任务完成再来操作", OspOrderStatementServiceImpl.EXPORT_RUNNING_LIMIT_COUNT));
        }
    }
    
    private String getExportTitle(long time_begin, long time_end, String merchant_name, int type, String timeZoneStr, int upayQueryType) {
        SimpleDateFormat simpleDateFormat =  new SimpleDateFormat(DAY_SDF_PATTERN_YYYYMMDD);
        if(!StringUtils.isBlank(timeZoneStr)){
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone(timeZoneStr));
        }

        String begin = simpleDateFormat.format(time_begin);
        String end = simpleDateFormat.format(time_end);
        return merchant_name + (upayQueryType == 1 ? "银行卡": "") + "流水明细" + begin + "_" + end;
    }
    
    private Map getQueryTransactionParams(Map request) {
        Map queryFilter = CollectionUtil.hashMap(
                "store_name", BeanUtil.getPropString(request, "store_name"),
                "store_sn", BeanUtil.getPropString(request, "store_sn"),
                "store_id", BeanUtil.getPropString(request, "store_id"),
                "merchant_id", BeanUtil.getPropString(request, "merchant_id"),
                "merchant_sn", BeanUtil.getPropString(request, "merchant_sn"),
                "merchant_name", BeanUtil.getPropString(request, "merchant_name"),
                "payway", BeanUtil.getPropString(request, "payway"),
                "sub_payway", BeanUtil.getPropString(request, "sub_payway"),
                "status", BeanUtil.getPropString(request, "status"),
                "type", BeanUtil.getPropString(request, "type"),
                "min_total_amount", BeanUtil.getPropString(request, "min_total_amount"),
                "max_total_amount", BeanUtil.getPropString(request, "max_total_amount"),
                "transaction_sn", BeanUtil.getPropString(request, "transaction_sn"),
                "client_sn", BeanUtil.getPropString(request, "client_sn"),
                "trade_no", BeanUtil.getPropString(request, "trade_no"),
                "terminal_sn", BeanUtil.getPropString(request, "terminal_sn"),
                "provider", BeanUtil.getPropString(request, "provider"),
                "device_fingerprint", BeanUtil.getPropString(request, Terminal.DEVICE_FINGERPRINT),
                "date_start", BeanUtil.getPropLong(request, "date_start"),
                "date_end", BeanUtil.getPropLong(request, "date_end"),
                "timeZone", BeanUtil.getPropString(request, "timeZone"),
                "upayQueryType", BeanUtil.getPropInt(request, "upayQueryType")
        );
        //client_sn 新增 商户终端号
        String merchant_client_sn = BeanUtil.getPropString(request, "merchant_client_sn");
        if (!StringUtil.empty(merchant_client_sn)) {
            ListResult tmp = terminalService.findTerminals(new PageInfo(1, 1), CollectionUtil.hashMap("client_sn", merchant_client_sn));
            if (tmp != null && tmp.getRecords() != null && tmp.getRecords().size() > 0) {
                String merchant_id = (String) tmp.getRecords().get(0).get("merchant_id");
                queryFilter.put("merchant_id", merchant_id);
            } else {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户外部终端号不存在！");
            }
        }
        
        //支付源订单号
        String channel_trade_no = BeanUtil.getPropString(request, "channel_trade_no");
        if (!StringUtil.empty(channel_trade_no) && StringUtil.empty(BeanUtil.getPropString(queryFilter, "transaction_sn"))){
            String orderSn = OrderUtil.getOrderSnByChannelTradeNo(channel_trade_no);
            if (StringUtil.empty(orderSn)){
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "源交易订单号没有在系统中找到！");
            }
            queryFilter.put("transaction_sn", orderSn);
        }
        
        //收款通道订单号
        String trade_no = BeanUtil.getPropString(request, "trade_no");
        if (BeanUtil.getPropInt(request, "upayQueryType") != 1 && !StringUtil.empty(trade_no) && StringUtil.empty(BeanUtil.getPropString(queryFilter, "transaction_sn"))) {
                String orderSn = OrderUtil.getOrderSnByTradeNo(trade_no);
                if (StringUtil.empty(orderSn)) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "收款通道订单号没有在系统中找到！");
                }
                queryFilter.put("transaction_sn", orderSn);
        }

        return queryFilter;
    }
}
