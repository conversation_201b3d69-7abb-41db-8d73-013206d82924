package com.wosai.upay.service;


import com.wosai.data.util.StringUtil;
import com.wosai.shouqianba.withdrawservice.exception.WithdrawBizErrorException;
import com.wosai.upay.clearance.exception.BizException;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.constant.WeiXinBlackListConstant;
import com.wosai.upay.job.service.WeixinBlackListService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.text.DecimalFormat;
import java.util.*;

@Service
public class OspWeixinBlackListServiceImpl implements OspWeixinBlackListService {

    @Autowired
    private WeixinBlackListService weixinBlackListService;

    private static final Logger logger = LoggerFactory.getLogger(OspWeixinBlackListServiceImpl.class);

    private static final String [] WEIXIN_BLACK_LIST_COLUMN_NAME = new String[]{
            WeiXinBlackListConstant.LICENSE_NAME,
            WeiXinBlackListConstant.APPID
    };
    @Override
    public Map isAppidInWeixinBlackList(Map request) {
        return weixinBlackListService.isAppidInBlackList(request);
    }

    @Override
    public Map isLicenseNameInWeixinBlackList(Map request) {
        return weixinBlackListService.isLicenseNameInBlackList(request);
    }


    @Override
    public Map<String, Object> uploadBlackListExcel(MultipartFile multipartFile) {
        if(multipartFile == null){
            throw new WithdrawBizErrorException("导入失败,文件为空");
        }
        String fileName = multipartFile.getOriginalFilename();
        if (StringUtil.empty(fileName) || (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx"))) {
            throw new WithdrawBizErrorException("文件类型错误，支持上传.xls .xlsx格式文件");
        }
        File file = new File(System.getProperty("java.io.tmpdir") + "/" + CrudUtil.randomUuid() + multipartFile.getName());
        List<Map<String,Object>> weixinBlacklist;
        try {
            multipartFile.transferTo(file);
            weixinBlacklist = parseWeixinBlackListExcel(file);
        } catch (Exception e) {
            logger.error("文件解析失败"+e.getMessage(),e);
            throw new WithdrawBizErrorException("导入失败,文件解析失败");
        }
        if(weixinBlacklist.size() > 5000){
            throw new BizException("最大限制5000条");
        }
        weixinBlackListService.batchAddBlackList(weixinBlacklist);
        Map<String,Object> result = new HashMap<>();
        result.put("success",true);
        return result;
    }

    @Override
    public Map deleteBlackList(Map request) {
        return weixinBlackListService.deleteBlackList(request);
    }

    @Override
    public Map getBlackList(Map request) {
        return weixinBlackListService.getBlackList(request);
    }



    private List<Map<String,Object>> parseWeixinBlackListExcel(File file) throws Exception {
        if(file == null) return new ArrayList<>();
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(file);
        List<Map<String,Object>> result = new LinkedList<>();
        for (int numSheet = 0; numSheet < xssfWorkbook.getNumberOfSheets(); numSheet++) {
            XSSFSheet xssfSheet = xssfWorkbook.getSheetAt(numSheet);
            if (xssfSheet == null) {
                continue;
            }
            boolean title = true;
            for (int rowNum = 0; rowNum <= xssfSheet.getLastRowNum(); rowNum++) {
                XSSFRow xssfRow = xssfSheet.getRow(rowNum);
                if (xssfRow == null || title) {
                    title = false;
                    continue;
                }
                Map<String, Object> weixinBlackList = new LinkedHashMap<>();
                for (int cellNum = 0; cellNum < 2; cellNum++) {
                    XSSFCell xssfCell = xssfRow.getCell(cellNum);
                    weixinBlackList.put(WEIXIN_BLACK_LIST_COLUMN_NAME[cellNum], getValue(xssfCell));
                }
                result.add(weixinBlackList);
            }
        }
        return result;
    }

    private String getValue(XSSFCell xssfCell) {
        if (xssfCell == null) {
            return null;
        }
        if (xssfCell.getCellType() == Cell.CELL_TYPE_BOOLEAN) {
            return String.valueOf(xssfCell.getBooleanCellValue());
        } else if (xssfCell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
            DecimalFormat df = new DecimalFormat("0");
            return df.format(xssfCell.getNumericCellValue());
        } else {
            return String.valueOf(xssfCell.getStringCellValue());
        }
    }
}
