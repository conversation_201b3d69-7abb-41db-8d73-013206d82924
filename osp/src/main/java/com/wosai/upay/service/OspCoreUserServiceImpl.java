package com.wosai.upay.service;

import com.wosai.app.backend.api.service.IAccountService;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.reward.service.AccountService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class OspCoreUserServiceImpl implements OspCoreUserService {

    @Autowired
    private CoreUserService coreUserService;
    @Autowired
    private UserService userService;
    @Autowired
    private BusinessLogService businessLogService;
    @Autowired
    private IAccountService accountService;

    @Override
    public Map createAccount(Map request) {
        Map accountNew = coreUserService.createAccount(request);
        return accountNew;
    }

    @Override
    public void deleteAccount(Map request) {
        coreUserService.deleteAccount(request);
    }

    @Override
    public Map updateAccount(Map request) {
        return coreUserService.updateAccount(request);
    }

    @Override
    public Map modifyMerchantSuperAdminUsername(Map request) {
        String merchantUserId = BeanUtil.getPropString(request,"merchant_user_id");
        Map merchantUserOld = userService.getMerchantUser(merchantUserId);
        //获取account_id
        String accountId = BeanUtil.getPropString(merchantUserOld,"account_id");
        Map accountOld = userService.getAccount(accountId);
        //accountOld.put("merchant_user_id",merchantUserId);
        Map result = coreUserService.modifyMerchantSuperAdminUsername(request);
        //修改老板账号删除app里账号
        accountService.deleteByCellphone(BeanUtil.getPropString(request, "new_username",""));
        if(result!=null && result.containsKey("account_id")){
            String accountIdNew = BeanUtil.getPropString(result,"account_id","");
            Map accountNew = userService.getAccount(accountIdNew);
            //accountNew.put("merchant_user_id",merchantUserId)
            Map merchantUserNew = userService.getMerchantUserByAccountId(accountIdNew);
            String merchant_user_id = BeanUtil.getPropString(merchantUserNew,"id");
            accountOld.put("merchant_user_id",merchant_user_id);
            accountNew.put("merchant_user_id",merchant_user_id);
            Map businessRequest = HttpRequestUtil.getBusinessogRequest();
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, accountOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, accountNew,
                    BusinessLogUtil.LOG_PARAM_REQUEST, businessRequest,
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant_user",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "account",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_user_id"
                    )
            ));

            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantUserOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantUserNew,
                    BusinessLogUtil.LOG_PARAM_REQUEST, businessRequest,
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant_user",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_user",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id"
                    )
            ));

        }
        return result;
    }

    @Override
    public Map updateAccountPassword(Map request) {
        String username = BeanUtil.getPropString(request, "username");
        Map accountOld = userService.getAccountByUsername(username);
        String accountId = BeanUtil.getPropString(accountOld,"id");
        Map merchantUser = userService.getMerchantUserByAccountId(accountId);
        String merchantUserId = BeanUtil.getPropString(merchantUser,"id");
        String merchantId = BeanUtil.getPropString(merchantUser,"merchant_id");
        accountOld.put("merchant_id",merchantId);
        accountOld.put("merchant_user_id",merchantUserId);
        Map account = coreUserService.updateAccountPassword(request);
        Map accountNew = userService.getAccountByUsername(username);
        accountNew.put("merchant_id",merchantId);
        accountNew.put("merchant_user_id",merchantUserId);
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, accountOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, accountNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant_user",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "account",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_user_id"
                )
        ));

        return account;
    }

    
    @Override
    public Map getAccount(Map request) {
        return coreUserService.getAccount(request);
    }

    @Override
    public Map getAccountByUsername(Map request) {
        return coreUserService.getAccountByUsername(request);
    }

    @Override
    public Map getAccountByCellphone(Map request) {
        return coreUserService.getAccountByCellphone(request);
    }

    @Override
    public Map createVendorUser(Map request) {
        return coreUserService.createVendorUser(request);
    }

    @Override
    public void deleteVendorUser(Map request) {
        coreUserService.deleteVendorUser(request);
    }

    @Override
    public Map updateVendorUser(Map request) {
        return coreUserService.updateVendorUser(request);
    }

    @Override
    public Map getVendorUser(Map request) {
        return coreUserService.getVendorUser(request);
    }

    @Override
    public Map getVendorUserByAccountId(Map request) {
        return coreUserService.getVendorUserByAccountId(request);
    }

    @Override
    public void disableVendorUser(Map request) {
        coreUserService.disableVendorUser(request);
    }

    @Override
    public ListResult findVendorUsers(Map request) {
        return coreUserService.findVendorUsers(request);
    }

    @Override
    public Map createMerchantUser(Map request) {

        String account_id = BeanUtil.getPropString(request,"account_id");
        String merchant_id = BeanUtil.getPropString(request,"merchant_id");
        Map accountNew = userService.getAccount(account_id);
        Map merchantNew = coreUserService.createMerchantUser(request);
        accountNew.put("merchant_user_id",BeanUtil.getPropString(merchantNew,"id"));
        //创建老板账号删除app里账号
        if (BeanUtil.getPropString(request,"role","").equals("super_admin")){
            accountService.deleteByCellphone(BeanUtil.getPropString(accountNew, Account.CELLPHONE));
        }
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, null,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, accountNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant_user",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_ADD,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "account",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_user_id"
                )
        ));
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, null,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant_user",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_ADD,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_user",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID
                )
        ));
        return merchantNew;
    }

    @Override
    public void deleteMerchantUser(Map request) {
        String accountId = BeanUtil.getPropString(request,"account_id");
        String merchant_user_id = BeanUtil.getPropString(request,"merchant_user_id");
        Map accountOld = userService.getAccount(accountId);
        Map merchantOld = userService.getMerchantUser(merchant_user_id);

        if (WosaiMapUtils.isNotEmpty(accountOld)) {
            userService.deleteAccountTruly(accountId);
            accountOld.put("merchant_user_id", merchant_user_id);
            if (WosaiMapUtils.isNotEmpty(merchantOld)) {
                accountOld.put("name",BeanUtil.getPropString(merchantOld,"name"));
                accountOld.put("remark",BeanUtil.getPropString(merchantOld,"remark"));
                accountOld.put("role",BeanUtil.getPropString(merchantOld,"role"));
                accountOld.put("store_id",BeanUtil.getPropString(merchantOld,"store_id"));
                accountOld.put("merchant_id",BeanUtil.getPropString(merchantOld,"merchant_id"));
            }
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, accountOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, null,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant_user",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_DEL,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "account",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_user_id",
                            BizOpLog.OP_TIME, System.currentTimeMillis()
                    )
            ));
        }


        if (WosaiMapUtils.isNotEmpty(merchantOld)) {
            userService.deleteMerchantUser(merchant_user_id);
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, null,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant_user",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_DEL,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_user",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id",
                            BizOpLog.OP_TIME, System.currentTimeMillis()
                    )
            ));
        }

    }

    @Override
    public Map updateMerchantUser(Map request) {
        return coreUserService.updateMerchantUser(request);
    }

    @Override
    public Map getMerchantUser(Map request) {
        return coreUserService.getMerchantUser(request);
    }

    @Override
    public Map getFirstMerchantUser(Map request) {
        return coreUserService.getFirstMerchantUser(request);
    }

    @Override
    public Map getMerchantUserByAccountId(Map request) {
        return coreUserService.getMerchantUserByAccountId(request);
    }

    @Override
    public List getMerchantUserRoles(Map request) {
        return coreUserService.getMerchantUserRoles(request);
    }

    @Override
    public List getMerchantUserPermissions(Map request) {
        return coreUserService.getMerchantUserPermissions(request);
    }

    @Override
    public void disableMerchantUser(Map request) {
        coreUserService.disableMerchantUser(request);
    }

    @Override
    public ListResult findMerchantUsers(Map request) {
        return coreUserService.findMerchantUsers(request);
    }

    @Override
    public Map createSolicitorUser(Map request) {
        return coreUserService.createSolicitorUser(request);
    }

    @Override
    public void deleteSolicitorUser(Map request) {
        coreUserService.deleteSolicitorUser(request);
    }

    @Override
    public Map updateSolicitorUser(Map request) {
        return coreUserService.updateSolicitorUser(request);
    }

    @Override
    public Map getSolicitorUser(Map request) {
        return coreUserService.getSolicitorUser(request);
    }

    @Override
    public Map getSolicitorUserByAccountId(Map request) {
        return coreUserService.getSolicitorUserByAccountId(request);
    }

    @Override
    public void disableSolicitorUser(Map request) {
        coreUserService.disableSolicitorUser(request);
    }

    @Override
    public ListResult findSolicitorUsers(Map request) {
        return coreUserService.findSolicitorUsers(request);
    }

    @Override
    public void deleteBoss(Map request) {

        String accountId = BeanUtil.getPropString(request,"account_id");
        Map accountOld = userService.getAccount(accountId);
        String merchant_user_id = BeanUtil.getPropString(request,"merchant_user_id");
        Map merchantOld = userService.getMerchantUser(merchant_user_id);
        Map bossOld = accountOld;
        bossOld.put("name",BeanUtil.getPropString(merchantOld,"name"));
        bossOld.put("remark",BeanUtil.getPropString(merchantOld,"remark"));
        bossOld.put("role",BeanUtil.getPropString(merchantOld,"role"));
        bossOld.put("store_id",BeanUtil.getPropString(merchantOld,"store_id"));
        bossOld.put("merchant_id",BeanUtil.getPropString(merchantOld,"merchant_id"));
        coreUserService.deleteBoss(request);
        accountService.deleteByCellphone(MapUtils.getString(accountOld, Account.CELLPHONE));

        Map accountNew = userService.getAccountByUsername(accountId);
        Map merchantNew = userService.getMerchantUser(merchant_user_id);

        if(accountNew == null && merchantNew == null) {
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, bossOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, null,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant_user",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_DEL,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "account",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                    )
            ));
        }
    }
}
