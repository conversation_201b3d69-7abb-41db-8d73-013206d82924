package com.wosai.upay.service.translate;

import com.wosai.data.util.CollectionUtil;

import java.util.Map;

/**
 * Created by chenyu on 2018/11/26.
 */
public class MapRule extends Rule<Object,Object> {
    public static final Map MERCHANT_INVOICE_ORDER_MODE = CollectionUtil.hashMap(
        1,"自动",
            2,"手动"
    );
    // '状态，0:初始化（未确认前），1：生效中，2、未达标、3：已达标（未退款），4:达标并退款、5达标退款失败 6 退款中
    public static final Map MERCHANT_INVOICE_ORDER_STATUS = CollectionUtil.hashMap(
            0,"未提交",
            1,"申请中",
            2,"已寄送",
            3,"已合并",
            4,"数据生成中",
            5,"无需开票"
    );
    public static final Map MERCHANT_INVOICE_ORDER_TAX_TYPE = CollectionUtil.hashMap(
            1,"增值税普通发票",
            2,"增值税专用发票"
    );

    public MapRule(Map<Object, Object> map) {
        this.map = map;
    }

    private Map<Object,Object> map;

    @Override
    public Object translate(Object value) {
        if (map != null) {
            return map.get(value);
        }
        return null;
    }

}
