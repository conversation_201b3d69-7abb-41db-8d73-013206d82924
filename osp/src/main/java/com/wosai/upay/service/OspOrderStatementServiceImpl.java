package com.wosai.upay.service;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.sales.bean.IpLocation;
import com.wosai.sales.service.IpLocationQueryService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Group;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.GroupUserMerchantAuth;
import com.wosai.upay.service.queryCheck.TradeQueryCheck;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.service.ExportService;
import com.wosai.upay.transaction.service.OrderService;
import com.wosai.upay.transaction.service.TaskLogService;
import com.wosai.upay.transaction.service.TransactionService;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.OrderUtil;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by majiahui
 */
@Service
public class OspOrderStatementServiceImpl implements OspOrderStatementService {
    @Autowired
    private OrderService orderService;
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private TransactionService transactionService;
    @Autowired
    private TranslateService translateService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    private ExportService exportService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private GroupService groupService;
    @Autowired
    private UserService userService;
    @Autowired
    private IpLocationQueryService ipLocationQueryService;
    /**
     * 导出对账单数据，最大支持导出多长时间
     * 31天
     */
    public static final long EXPORT_TRANSACTION_MAX_TIME = 31 * 24 * 60 * 60 * 1000l;
    /**
     * 导出对账单限制最大任务数的时间范围
     */
    public static final long EXPORT_RUNNING_LIMIT_TIME = 7 * 24 * 60 * 60 * 1000l;
    /**
     * 导出对账单限制时间范围的最大任务数
     */
    public static final long EXPORT_RUNNING_LIMIT_COUNT = 50l;

    public static final String DAY_SDF_PATTERN_YYYYMMDD = "yyyyMMdd";


    @Override
    public List getTransactionListByOrderSn(Map request) {
        String orderSn = BeanUtil.getPropString(request, "order_sn");
        List list = transactionService.getTransactionListByOrderSn(orderSn);
        if (list != null && list.size() != 0) {
            translateService.translateOrderOperatorNames(list, BeanUtil.getPropString(list.get(0), ConstantUtil.KEY_MERCHANT_ID));
        }
        return list;
    }

    @Override
    public ListResult findTaskApplyLogs(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        long sevenDaysAgo = new Date().getTime() - EXPORT_RUNNING_LIMIT_TIME;
        if (pageInfo.getDateStart() == null || pageInfo.getDateStart() < sevenDaysAgo){
            pageInfo.setDateStart(sevenDaysAgo);
        }
        ListResult result = taskLogService.findTaskApplyLogs(pageInfo, request);
        if (result != null && result.getRecords() != null){
            for(Map task : result.getRecords()){
                addUserNameInfo(task);
            }
        }
        return result;
    }

    @Override
    public Map getTaskApplyLog(Map request) {
        String taskApplyLogId = BeanUtil.getPropString(request, "taskApplyLogId");
        if (StringUtil.empty(taskApplyLogId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出任务id不可为空!");
        }
        Map task = taskLogService.getTaskApplyLog(taskApplyLogId);
        addUserNameInfo(task);
        return task;
    }

    /**
     * 添加用户姓名信息
     * @param task
     */
    private void addUserNameInfo(Map task){
        String user_id = BeanUtil.getPropString(task, StatementTaskLog.USER_ID);
        if (StringUtil.empty(user_id)){
            Map account = null;
            try {
                account = userService.getAccount(user_id);
            }catch (Exception e){
            }
            task.put("user_name", BeanUtil.getPropString(account, Account.USERNAME));
            task.put("user_nickname", BeanUtil.getPropString(account, Account.NICKNAME));
        }
    }

    @Override
    public void deleteTaskApplyLog(Map request) {
        String taskApplyLogId = BeanUtil.getPropString(request, "taskApplyLogId");
        if (StringUtil.empty(taskApplyLogId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出任务id不可为空!");
        }
//        Map taskApplyLog = getTaskApplyLog(request);
//        if (taskApplyLog == null) {
//            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出任务不存在!");
//        }
//        if (StatementTaskLog.APPLY_STATUS_RUNNING.contains(BeanUtil.getPropInt(taskApplyLog, StatementTaskLog.APPLY_STATUS))) {
//            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出任务正在执行中不允许删除!");
//        }
        taskLogService.deleteTaskApplyLog(taskApplyLogId);
    }

    private Map getQueryOrderParams(Map request) {
        Map queryFilter = CollectionUtil.hashMap(
                "store_name", BeanUtil.getPropString(request, "store_name"),
                "store_sn", BeanUtil.getPropString(request, "store_sn"),
                "store_id", BeanUtil.getPropString(request, "store_id"),
                "merchant_id", BeanUtil.getPropString(request, "merchant_id"),
                "merchant_sn", BeanUtil.getPropString(request, "merchant_sn"),
                "merchant_name", BeanUtil.getPropString(request, "merchant_name"),
                "payway", BeanUtil.getPropString(request, "payway"),
                "sub_payway", BeanUtil.getPropString(request, "sub_payway"),
                "status", BeanUtil.getPropString(request, "status"),
                "min_total_amount", BeanUtil.getPropString(request, "min_total_amount"),
                "max_total_amount", BeanUtil.getPropString(request, "max_total_amount"),
                "order_sn", BeanUtil.getPropString(request, "order_sn"),
                "client_sn", BeanUtil.getPropString(request, "client_sn"),
                "trade_no", BeanUtil.getPropString(request, "trade_no"),
                "terminal_sn", BeanUtil.getPropString(request, "terminal_sn"),
                "provider", BeanUtil.getPropString(request, "provider"),
                "device_fingerprint", BeanUtil.getPropString(request, Terminal.DEVICE_FINGERPRINT),
                "date_start", BeanUtil.getPropLong(request, "date_start"),
                "date_end", BeanUtil.getPropLong(request, "date_end"),
                "timeZone", BeanUtil.getPropString(request, "timeZone"),
                "upayQueryType", BeanUtil.getPropInt(request, "upayQueryType")

        );
        //client_sn 新增 商户终端号
        String merchant_client_sn = BeanUtil.getPropString(request, "merchant_client_sn");
        if (!StringUtil.empty(merchant_client_sn)) {
            ListResult tmp = terminalService.findTerminals(new PageInfo(1, 1), CollectionUtil.hashMap("client_sn", merchant_client_sn));
            if (tmp != null && tmp.getRecords() != null && tmp.getRecords().size() > 0) {
                String merchant_id = (String) tmp.getRecords().get(0).get("merchant_id");
                queryFilter.put("merchant_id", merchant_id);
            } else {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户外部终端号不存在！");
            }
        }
        //支付源订单号
        String channel_trade_no = BeanUtil.getPropString(request, "channel_trade_no");
        if (!StringUtil.empty(channel_trade_no)){
            String orderSn = OrderUtil.getOrderSnByChannelTradeNo(channel_trade_no);
            if (StringUtil.empty(orderSn)){
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "源交易订单号没有在系统中找到！");
            }
            queryFilter.put("order_sn", orderSn);
        }
        return queryFilter;
    }

    @Override
    public ListResult findOrders(Map request) {
        TradeQueryCheck.timeSpanCheck(request);
        return orderService.getOrderList(PageInfoUtil.extractPageInfo(request), getQueryOrderParams(request));
    }

    @Override
    public Map createExportOrderTask(Map<String, Object> request) {
        String user_id = BeanUtil.getPropString((Map) HttpRequestUtil.getSession().getAttribute("osp_account"), DaoConstants.ID);
        //系统检查是否可以导出
        allowExport(user_id, "order");

        //参数检查
        long date_start = BeanUtil.getPropLong(request, "date_start");
        long date_end = BeanUtil.getPropLong(request, "date_end");
        if (date_start <= 0 || date_end <= 0 || date_start > date_end) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "订单导出时间有误");
        }
        String timeZoneStr = BeanUtil.getPropString(request, "timeZone");
        int upayQueryType = BeanUtil.getPropInt(request, "upayQueryType");
        return exportService.createExportStatementTask(CollectionUtil.hashMap(
                StatementTaskLog.TYPE, StatementTaskLog.TYPE_ORDER,
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_SP,
                StatementTaskLog.TITLE, getExportTitle(date_start, date_end, "SP", StatementTaskLog.TYPE_ORDER, timeZoneStr, upayQueryType),
                StatementTaskLog.USER_ID, user_id
        ), getQueryOrderParams(request));
    }

    @Override
    public Map createExportTransactionTask(Map<String, Object> request) {
        String user_id = BeanUtil.getPropString((Map) HttpRequestUtil.getSession().getAttribute("osp_account"), DaoConstants.ID);
        //系统检查是否可以导出
        allowExport(user_id, "transaction");
        String merchant_name = "";

        boolean exportHistoricalTransaction = BeanUtil.getPropBoolean(request,"historical", false);

        //参数检查
        long date_start = BeanUtil.getPropLong(request, "date_start");
        long date_end = BeanUtil.getPropLong(request, "date_end");
        if (date_start <= 0 || date_end <= 0 || date_start > date_end || date_end - date_start > EXPORT_TRANSACTION_MAX_TIME) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "对账单导出时间超过最大限制");
        }
        if (request == null) {
            request = new HashedMap();
        }
        //是否有自定义参数
        boolean hasParams = false;

        //门店号维度
        List<String> store_sns = (List) BeanUtil.getProperty(request, "store_sns");
        if (store_sns != null && store_sns.size() == 1) {
            List<String> store_ids = new ArrayList<>();
            for (String store_sn : store_sns) {
                Map store = storeService.getStoreByStoreSn(store_sn);
                if (store == null) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("找不到该门店[%s]，请重新输入", store_sn));
                }
                store_ids.add(BeanUtil.getPropString(store, DaoConstants.ID));
            }
            if (store_ids.size() > 0){
                request.put("store_id", store_ids.get(0));
                List<String> merchant_ids = getMerchantIdsByStoreIds(store_ids);
                if (merchant_ids.size() > 0){
                    request.put("merchant_id", merchant_ids.get(0));
                }
                hasParams = true;
            }
        }
        if (store_sns != null && store_sns.size() > 1) {
            List<String> store_ids = new ArrayList<>();
            for (String store_sn : store_sns) {
                Map store = storeService.getStoreByStoreSn(store_sn);
                if (store == null) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("找不到该门店[%s]，请重新输入", store_sn));
                }
                store_ids.add(BeanUtil.getPropString(store, DaoConstants.ID));
            }
            if (store_ids.size() > 0){
                request.put("store_ids", store_ids);
                List<String> merchant_ids = getMerchantIdsByStoreIds(store_ids);
                if (merchant_ids.size() > 0){
                    request.put("merchant_id", merchant_ids);
                }
                hasParams = true;
            }
        }

        //商户号维度
        List<String> merchant_sns = (List) BeanUtil.getProperty(request, "merchant_sns");
        if (merchant_sns != null && merchant_sns.size() == 1) {
            List<String> merchant_ids = new ArrayList<>();
            for (String merchant_sn : merchant_sns) {
                Map merchant = merchantService.getMerchantBySn(merchant_sn);
                if (merchant == null) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("找不到该商户[%s]，请重新输入", merchant_sn));
                }
                merchant_name = StringUtil.empty(merchant_name) ? BeanUtil.getPropString(merchant, Merchant.NAME) : (merchant_name + "," + BeanUtil.getPropString(merchant, Merchant.NAME));
                merchant_ids.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
            }
            if (merchant_ids.size() > 0) {
                request.put("merchant_id", merchant_ids.get(0));
                hasParams = true;
            }
        }
        if (merchant_sns != null && merchant_sns.size() > 1) {
            List<String> merchant_ids = new ArrayList<>();
            for (String merchant_sn : merchant_sns) {
                Map merchant = merchantService.getMerchantBySn(merchant_sn);
                if (merchant == null) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("找不到该商户[%s]，请重新输入", merchant_sn));
                }
                merchant_name = StringUtil.empty(merchant_name) ? BeanUtil.getPropString(merchant, Merchant.NAME) : (merchant_name + "," + BeanUtil.getPropString(merchant, Merchant.NAME));
                merchant_ids.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
            }
            if (merchant_ids.size() > 0) {
                request.put("merchant_ids", merchant_ids);
                hasParams = true;
            }
        }

        int exportType = exportHistoricalTransaction? StatementTaskLog.TYPE_HISTORICAL_TRANSACTION : StatementTaskLog.TYPE_TRANSACTION;

        //集团账号维度
        String group_sn = BeanUtil.getPropString(request, "group_sn");
        if (!StringUtil.empty(group_sn)) {
            Map group = groupService.getGroupBySn(group_sn);
            if (group == null) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("找不到该集团[%s]，请重新输入", group_sn));
            }
            merchant_name = BeanUtil.getPropString(group, Group.NAME);
            //获取集团账户下商户id集合
            final List<Map> groupUserMerchantAuths = groupService.getGroupUserMerchantAuths(CollectionUtil.hashMap(
                    GroupUserMerchantAuth.GROUP_ID, BeanUtil.getPropString(group, DaoConstants.ID)
            ));
            if (groupUserMerchantAuths == null || groupUserMerchantAuths.size() < 1) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("该集团[%s]下没有商户，请重新输入", group_sn));
            }
            List<String> merchant_ids = new ArrayList<String>() {{
                for (Map groupUserMerchantAuth : groupUserMerchantAuths) {
                    add(BeanUtil.getPropString(groupUserMerchantAuth, GroupUserMerchantAuth.MERCHANT_ID));
                }
            }};
            exportType = exportHistoricalTransaction ? StatementTaskLog.TYPE_HISTORICAL_GROUP_TRANSACTION : StatementTaskLog.TYPE_TRANSACTION_GROUP;
            request.put("group_name", merchant_name);
            request.put("group_sn", group_sn);
            request.put("merchant_ids", merchant_ids);
            hasParams = true;
        }
        if (!hasParams) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出参数为空");
        }
        String timeZoneStr = BeanUtil.getPropString(request, "timeZone");
        int upayQueryType = BeanUtil.getPropInt(request, "upayQueryType");
        return exportService.createExportStatementTask(CollectionUtil.hashMap(
                StatementTaskLog.TYPE, exportType,
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_SP,
                StatementTaskLog.TITLE, getExportTitle(date_start, date_end, merchant_name, StatementTaskLog.TYPE_TRANSACTION, timeZoneStr, upayQueryType),
                StatementTaskLog.USER_ID, user_id
        ), request);
    }

    /**
     * 根据订单号查询对应的交易ip和交易城市
     *
     * @param request
     * @return
     */
    @Override
    public Map getClientIpAndCityByOrderSn(Map<String, Object> request) {
        String orderSn = BeanUtil.getPropString(request, "order_sn");
        List<Map<String, Object>> list = transactionService.getTransactionListByOrderSn(orderSn);

        if (list != null && list.size() != 0) {
            for (Map result : list) {
                if (30 != WosaiMapUtils.getInteger(result, "type").intValue()) {//非交易成功的，跳过
                    continue;
                }
                Map extra = WosaiMapUtils.getMap(result, "extra_params");
                if (extra == null || !extra.containsKey("client_ip")) {
                    return null;
                }
                String client_ip = WosaiMapUtils.getString(extra, "client_ip");
                IpLocation ipLocation = ipLocationQueryService.ipLocationQuery(client_ip);
                return CollectionUtil.hashMap(
                        "client_ip", client_ip,
                        "province", null == ipLocation? null : ipLocation.getProvince(),
                        "city", null == ipLocation? null : ipLocation.getCity());
            }
        }
        return null;


    }

    private String getExportTitle(long time_begin, long time_end, String merchant_name, int type, String timeZoneStr, int upayQueryType) {
        SimpleDateFormat simpleDateFormat =  new SimpleDateFormat(DAY_SDF_PATTERN_YYYYMMDD);
        if(!StringUtils.isBlank(timeZoneStr)){
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone(timeZoneStr));
        }

        String begin = simpleDateFormat.format(time_begin);
        String end = simpleDateFormat.format(time_end);
        String title = begin + "_" + end;
        if(upayQueryType == 1){
            title = "银行卡" + title;
        }
        if (type == StatementTaskLog.TYPE_ORDER) {
            title = "订单" + title;
        } else if (type == StatementTaskLog.TYPE_TRANSACTION) {
            title = "对账单" + title;
        }
        return merchant_name + title;
    }

    /**
     * 检查是否允许导出
     */
    private void allowExport(String user_id, String type) {
        //检查用户七日内正在执行的任务数是否小于50
        long now = new Date().getTime();
        long sevenDaysAgo = now - EXPORT_RUNNING_LIMIT_TIME;
        ListResult taskResult = taskLogService.findTaskApplyLogs(new PageInfo(1, 10, sevenDaysAgo, now), CollectionUtil.hashMap(
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_SP,
                StatementTaskLog.USER_ID, user_id,
                StatementTaskLog.APPLY_STATUSES, StatementTaskLog.APPLY_STATUS_RUNNING
        ));
        if (taskResult != null && taskResult.getTotal() >= EXPORT_RUNNING_LIMIT_COUNT) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("当前已有%s个对账单任务在生成中,请等待任务完成再来操作", EXPORT_RUNNING_LIMIT_COUNT));
        }
    }


    /**
     * 根据商户sn集合获取商户id集合
     *
     * @param merchant_sns
     * @return
     */
    List<String> getMerchantIdsBySns(List<String> merchant_sns) {
        List<String> merchant_ids = new ArrayList<>();
        if (merchant_sns != null && merchant_sns.size() > 0) {
            ListResult result = merchantService.findMerchants(new PageInfo(1, merchant_sns.size()), CollectionUtil.hashMap(
                    "merchant_sns", merchant_sns
            ));
        }
        return merchant_ids;
    }

    /**
     * 根据门店sn集合获取门店id集合
     *
     * @param store_sns
     * @return
     */
    List<String> getStoreIdsBySns(List<String> store_sns) {
        List<String> store_ids = new ArrayList<>();
        if (store_sns != null && store_sns.size() > 0) {
            for (String store_sn : store_sns) {
                store_ids.add(BeanUtil.getPropString(storeService.getStoreByStoreSn(store_sn), DaoConstants.ID));
            }
        }
        return store_ids;
    }

    /**
     * 根据门店Id集合获取商户id集合
     *
     * @param store_ids
     * @return
     */
    List<String> getMerchantIdsByStoreIds(List<String> store_ids) {
        List<String> merchant_ids = new ArrayList<>();
        if (store_ids != null && store_ids.size() > 0) {
            for (String store_id : store_ids) {
                Map store = storeService.getStore(store_id);
                if (store != null && !merchant_ids.contains(BeanUtil.getPropString(store, Store.MERCHANT_ID))) {
                    merchant_ids.add(BeanUtil.getPropString(store, Store.MERCHANT_ID));
                }
            }
        }
        return merchant_ids;
    }
}
