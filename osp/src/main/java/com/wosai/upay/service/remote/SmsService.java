package com.wosai.upay.service.remote;

/**
 * Created by kay on 16/11/3.
 */
public interface SmsService {
    /**
     * 提现通过或者失败发送短信
     * @param withdrawId
     * @param status
     * @return
     */
    boolean sendWithdrawSms(String withdrawId, Long status);

    /**
     * 发送短信验证码
     * @param cellphone
     * @param type
     * @return
     */
    boolean sendSmsAuthCode(String cellphone, String type);

    boolean validateAuthCode(String cellphone, String authCode, String type);

    /**
     * 发送门店码首次开通提醒短信
     * @param storeId
     * @return
     */
    boolean sendBindQrcodeSms(String storeId);
}
