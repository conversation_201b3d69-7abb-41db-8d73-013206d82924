package com.wosai.upay.service;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.wosai.app.backend.api.service.IAccountService;
import com.wosai.app.backend.api.service.ISettingsService;
import com.wosai.app.dto.*;
import com.wosai.app.service.PushConfigService;
import com.wosai.sales.addin.bean.BeanUtils;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.wosai.data.util.CollectionUtil.hashMap;

@Service
public class OspPushServiceImpl implements OspPushService {

    @Autowired
    private PushConfigService pushConfigService;
    @Autowired
    private ISettingsService iSettingsService;
    @Autowired
    private IAccountService iAccountService;

    private static final String TYPE_TERMINAL = "terminal";
    private static final String TYPE_OPERATOR = "operator";
    private static final Map DEFAULT_AUDIT_TYPE = hashMap("audit_type", 0);

    @Override
    public PageResult<StorePushInfo> getStoreConfig(Map storeConfig) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(storeConfig);
        PageStoreConfigReq pageStoreConfigReq = (PageStoreConfigReq) new PageStoreConfigReq()
                .setAccount_id(MapUtils.getString(storeConfig, "account_id"))
                .setStore_name(MapUtils.getString(storeConfig, "store_name"))
                .setPage(pageInfo.getPage())
                .setPage_size(pageInfo.getPageSize());
        return pushConfigService.getStoreConfig(pageStoreConfigReq);
    }

    @Override
    public PageResult<TerminalPushInfo> getStoreTerminalConfig(Map storeTerminalConfig) {
        String type = MapUtils.getString(storeTerminalConfig, "type", TYPE_TERMINAL);
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(storeTerminalConfig);
        String accountId = MapUtils.getString(storeTerminalConfig, "account_id");
        String storeId = MapUtils.getString(storeTerminalConfig, "store_id");
        String terminalName = MapUtils.getString(storeTerminalConfig, "terminal_name");

        if (TYPE_TERMINAL.equals(type)) {
            PageTerminalConfigReq configReq = (PageTerminalConfigReq) new PageTerminalConfigReq()
                    .setAccount_id(accountId)
                    .setStore_id(storeId)
                    .setTerminal_name(terminalName)
                    .setPage(pageInfo.getPage())
                    .setPage_size(pageInfo.getPageSize());
            return pushConfigService.getTerminalConfig(configReq);
        }

        // type != terminal时, 请求 getOperatorConfig
        PageOperatorConfigReq operatorConfigReq = (PageOperatorConfigReq) new PageOperatorConfigReq()
                .setAccount_id(accountId)
                .setStore_id(storeId)
                .setOperator_name(terminalName)
                .setPage(pageInfo.getPage())
                .setPage_size(pageInfo.getPageSize());

        PageResult<OperatorPushInfo> operatorConfig = pushConfigService.getOperatorConfig(operatorConfigReq);
        List<TerminalPushInfo> terminalPushInfoList = Lists.transform(operatorConfig.getRecords(), new Function<OperatorPushInfo, TerminalPushInfo>() {
            @Override
            public TerminalPushInfo apply(OperatorPushInfo operatorInfo) {
                TerminalPushInfo terminalInfo = new TerminalPushInfo();
                terminalInfo.setTerminal_id(operatorInfo.getOperator_account_id());
                terminalInfo.setTerminal_name(operatorInfo.getOperator_name());
                terminalInfo.setTerminal_type("APP");
                terminalInfo.setTerminal_type_name("手机");
                terminalInfo.setTerminal_sn(operatorInfo.getCellphone());
                terminalInfo.setOpen(operatorInfo.getOpen());
                terminalInfo.setCtime(operatorInfo.getCtime());
                return terminalInfo;
            }
        });
        return new PageResult<>(operatorConfig.getTotal(), terminalPushInfoList);
    }

    @Override
    public Map getUserSetting(Map param) {
        Map account = iAccountService.getAccountByCriteria(hashMap("account_id", MapUtils.getString(param, "account_id")));
        String operatorId = BeanUtils.getProperty(account, "data[0].id");
        if (StringUtils.isEmpty(operatorId)) {
            return DEFAULT_AUDIT_TYPE;
        }
        Map<String, String> userSettings = iSettingsService.getUserSettings(operatorId);
        return hashMap("audio_type", MapUtils.getIntValue(userSettings, "push_sound"));
    }
}
