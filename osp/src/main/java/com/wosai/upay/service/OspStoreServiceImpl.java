package com.wosai.upay.service;

import com.google.common.collect.ImmutableMap;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.sales.core.model.Stores;
import com.wosai.sales.core.service.IKeeperService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.wosai.upay.common.util.ConstantUtil.KEY_SN;

@Service
public class OspStoreServiceImpl implements OspStoreService {

    @Autowired
    private StoreService storeService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private SupportService supportService;
    @Autowired
    private BusinessLogService businessLogService;
    @Autowired
    private IKeeperService keeperService;

    @Override
    public Map createStore(Map<String, Object> request) {
        return storeService.createStore(request);
    }

    @Override
    public void deleteStore(Map<String, Object> request) {
        storeService.deleteStore(BeanUtil.getPropString(request, DaoConstants.ID));
    }

    @Override
    public void deleteStoreBySn(Map<String, Object> request) {
        storeService.deleteStore(BeanUtil.getPropString(request, ConstantUtil.KEY_SN));
    }

    @Override
    public Map updateStore(Map<String, Object> request) {

        String storeId = BeanUtil.getPropString(request,"id");
        Map storeOld = storeService.getStore(storeId);
        Map storeNew = storeService.updateStore(request);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE,storeOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER,storeNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS,CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE,"store",
                        BizOpLog.OP_TYPE,BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME,"store",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID,"id"
                )
        ));
        return storeNew;
    }

    @Override
    public void disableStore(Map<String, Object> request) {
        String storeId = BeanUtil.getPropString(request, DaoConstants.ID);
        storeService.disableStore(storeId);
        deleteCache(storeId);
    }

    @Override
    public void enableStore(Map<String, Object> request) {
        String storeId = BeanUtil.getPropString(request, DaoConstants.ID);
        storeService.enableStore(storeId);
        deleteCache(storeId);
    }

    @Override
    public void closeStore(Map<String, Object> request) {
        String storeId = BeanUtil.getPropString(request, DaoConstants.ID);
        storeService.closeStore(storeId);
        deleteCache(storeId);
    }

    @Override
    public Map getStore(Map<String, Object> request) {
        return storeService.getStore(BeanUtil.getPropString(request, DaoConstants.ID));
    }

    @Override
    public Map getStoreByClientSn(Map<String, Object> request) {
        return storeService.getStoreByClientSn(BeanUtil.getPropString(request, Store.MERCHANT_ID), BeanUtil.getPropString(request, Store.CLIENT_SN));
    }

    @Override
    public ListResult getStoreListByMerchantId(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return storeService.getStoreListByMerchantId(merchantId, pageInfo, request);
    }

    @Override
    public ListResult findStores(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return storeService.findStores(pageInfo, request);
    }

    @Override
    public Map importStores(Map<String, Object> request) {
        return null;
    }

    @Override
    public List<Map<String,Object>> getStoreKeepers(Map<String, Object> request) {
        return keeperService.findKeepers(request.get("store_id").toString(), Stores.class,true);
    }

    private void deleteCache(String storeId){
        Map store = storeService.getStoreByStoreId(storeId);
        Map merchant = merchantService.getMerchantByMerchantId(BeanUtil.getPropString(store, "merchant_id"));
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
    }
}
