package com.wosai.upay.service;

import com.alibaba.fastjson.JSONObject;
import com.wosai.alipay.risk.service.HealthPointService;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.common.utils.WosaiJsonUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pub.alipay.authinto.model.Store;
import com.wosai.pub.alipay.authinto.service.AlipayStoreService;
import com.wosai.pub.alipay.authinto.service.StoreService;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.MapUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.SheetUtil;
import com.wosai.upay.util.TaskApplyLogUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalTime;
import java.util.*;

@Service
public class OspAlipayServiceImpl implements OspAlipayService {
    private static final Logger logger = LoggerFactory.getLogger(OspAlipayServiceImpl.class);
    @Autowired
    private AlipayStoreService alipayStoreService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private BusinessLogService businessLogService;

    @Autowired
    private HealthPointService healthPointService;

    @Autowired
    LogService logService;

    private static final String SESSION_IMPORT_HEALTH_POINT = "importHealthPoint_task";

    @Override
    public Map getStoreInfo(Map request) {
        String storeId=BeanUtil.getPropString(request,"storeId");
        logger.info("getStoreInfo params: storeId:{}",storeId);
        Map result =new HashMap();
        try {
             Store store = storeService.getStore(storeId);
             if(store==null){
                 return null;
             }
             result.put("storeID",store.getShouqianbaStoreId());
             result.put("storeName",store.getDetails().getFullName());
             result.put("merchantId",store.getParentId());
             result.put("shopId",store.getDetails().getAlipayShopId());
             StringBuilder shopName=new StringBuilder();
             if(store.getDetails().getMainShopName()!=null){
                 shopName.append(store.getDetails().getMainShopName());
                 if(store.getDetails().getBranchShopName()!=null){
                     shopName.append("(").append(store.getDetails().getBranchShopName()).append(")");
                 }
             }
             result.put("shopName",shopName.toString());
             //绑定状态 false为未绑定,true为绑定
             if(store.getDetails().getAlipayStoreInfo()==null || WosaiStringUtils.isEmpty(store.getDetails().getAlipayShopId())){
                result.put("bindStatus",false);
             }else{
                result.put("bindStatus",true);
             }
             //口碑绑定模式,0为支付宝直连，1为口碑外链
             if(store.getBindType().equals(Store.BindType.ALIPAY_DIRECT)){
                 result.put("bindType",0);
             }
             else{
                 result.put("bindType",1);
             }
        }catch(Throwable e){
            logger.error("call alipay authinto service exception:",e);
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "调用支付宝授权服务异常:"+e.getMessage());
        }
        logger.info("getStoreInfo params: storeId:{}.result:{}",storeId, WosaiJsonUtils.toJSON(result));
        return result;
    }

    @Override
    public Map getMerchantToken(Map request) {
        String merchangId=BeanUtil.getPropString(request,"merchantId");
        logger.info("getMerchantToken params:{}",request);
        try{
            Store authInfo= storeService.getStore(merchangId);
            if(authInfo==null){
                throw new UpayException(UpayException.CODE_UNKNOWN_ERROR,"商户不存在");
            }
            if (authInfo.getAlipayOauth()==null){
                throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR,"商户授权信息不存在");
            }
            String token=(String)authInfo.getAlipayOauth().getAppAuthToken();
            logger.info("getMerchantToken params:{},result:{}",merchangId,token);
           Map result= new HashMap();
           result.put("token",token);
           return result;
        }catch(Throwable e){
            logger.info("getMerchantToken fail,exception:",e);
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR,"查询商户token异常:"+e.getMessage());
        }
    }

    @Override
    public Map getMerchantAppId(Map request) {
        String merchantId=BeanUtil.getPropString(request,"merchantId");
        logger.info("getMerchantAppId params:{}",merchantId);
        try{
            Store authInfo= storeService.getStore(merchantId);
            if(authInfo==null){
                throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR,"商户不存在");
            }
            if (authInfo.getAlipayOauth()==null){
                throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR,"商户授权信息不存在");
            }
            String appId=(String)authInfo.getAlipayOauth().getAppId();
            logger.info("getMerchantAppId params:{},result:{}",merchantId,appId);
            Map result= new HashMap();
            result.put("appId",appId);
            return result;
        }catch(Throwable e){
            logger.info("getMerchantAppId fail,exception:",e);
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR,"查询商户app_id异常:"+e.getMessage());
        }
    }

    @Override
    public Map modifyBindType(Map request) {
        String storeId=BeanUtil.getPropString(request,"storeId");
        int bindType=BeanUtil.getPropInt(request,"bindType");
        logger.info("modifyBindType params: storeId:{},bindType:{}",storeId,bindType);
        if(bindType!=0 && bindType!=1){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER,"绑定方式参数无效");
        }
        Map storeOld=null;
        Map storeNew=null;
        try{
            storeOld=storeService.getStoreInfo(storeId);
            storeService.modifyBindType(storeId,bindType==0? false:true);
            storeNew=storeService.getStoreInfo(storeId);
        }catch(Throwable e){
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR,"调用修改绑定类型远程服务异常:"+e.getMessage());
        }
        logger.info("modifyBindType params: storeId:{},bindType:{}",storeId,bindType);
        Map result= new HashMap();
        result.put("result",true);
        //记录业务日志
        try {
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, CollectionUtil.hashMap(
                            "id", storeId,
                            "bindType", ((int) storeOld.get("bindType")) == 0 ? "支付宝直连模式" : "口碑外链模式"
                    ),
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, CollectionUtil.hashMap(
                            "id", storeId,
                            "bindType", ((int) storeNew.get("bindType")) == 0 ? "支付宝直连模式" : "口碑外链模式"
                    ),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id",
                            BizOpLog.BUSINESS_FUNCTION_CODE, "1000061",
                            BizOpLog.REMARK, "修改口碑门店绑定模式"
                    )
            ));
        }catch (Throwable e){
            logger.error("recode businesslog exception:",e);
        }
        return result;
    }

    @Override
    public Map unbindAuthorize(Map request) {
        String merchangId=BeanUtil.getPropString(request,"merchantId");
        logger.info("unbindAuthorize params: merchantId:{}",merchangId);
        String token=null;
        Store store=null;
        try{
            token=alipayStoreService.getAppAuthToken(merchangId);
            store=alipayStoreService.unbindAlipayAuth(merchangId,null,true);
            if(store==null){
                throw new UpayException(UpayException.CODE_UNKNOWN_ERROR,"解绑授权失败，商户不存在或未授权，商户ID:"+merchangId);
            }
        }catch(Throwable e){
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR,"调用解除授权远程服务异常:"+e.getMessage());
        }
        logger.info("unbindAuthorize params: merchantId:{},result:true",merchangId);
        Map result= new HashMap();
        result.put("result",true);
        try {
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, CollectionUtil.hashMap(
                        "id", merchangId,
                        "alipay_oauth_info", token
                ),
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, CollectionUtil.hashMap(
                        "id", merchangId,
                        "alipay_oauth_info", null
                ),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id",
                        BizOpLog.BUSINESS_FUNCTION_CODE,"1000051",
                        BizOpLog.REMARK, "解绑支付宝授权"
                )
            ));
        }catch (Throwable e){
            logger.error("recode businesslog exception:",e);
        }
        return result;
    }

    @Override
    public Map unbindShop(Map request) throws Exception{
        String storeId=BeanUtil.getPropString(request,"storeId");
        logger.info("unbindStore params:[ storeId:{}]",storeId);
        Store store=storeService.getStore(storeId);
        if(null==store){
            logger.error("unbindStore params:[ storeId:{}],exception:{}",storeId,"收钱吧门店不存在");
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "收钱吧门店不存在,门店id:"+storeId);
        }
        String shopId=store.getDetails().getAlipayShopId();
        String result=alipayStoreService.unbindAlipayShopId(store.getDetails().getAlipayShopId());
        if (!"解绑成功".equals(result)) {
            logger.error("unbindStore params:[ storeId:{}],exception:{}",storeId,"解绑失败");
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, result);
        }
        logger.info("unbindShop params: storeId:{},result:true",storeId);
        Map resultMap= new HashMap();
        resultMap.put("result",true);
        try {
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, CollectionUtil.hashMap(
                            "id", storeId,
                            "alipayShopId", shopId
                    ),
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, CollectionUtil.hashMap(
                            "id", storeId,
                            "alipayShopId", null
                    ),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id",
                            BizOpLog.BUSINESS_FUNCTION_CODE, "1000062",
                            BizOpLog.REMARK, "解绑口碑门店"
                    )
            ));
        }catch (Throwable e){
            logger.error("recode businesslog exception:",e);
        }
        return resultMap;
    }

    @Override
    public Map importGetHealthPoint(MultipartFile file) {
        //file转为excel
        Workbook workbook = getExcelFromFile(file);
        if (workbook == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel格式解析异常");
        }
        /**
         * 解析成list
         */
        final List<Map> merchantes_request = extraDataFromExcelOfHealthPoint(workbook);
        if (merchantes_request == null || merchantes_request.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "空excel没有数据");
        }

        if (merchantes_request.size() > 150) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过150条");
        }

        Map result = importGetHealthPointDetail(merchantes_request);
        return result;
    }

    private Map importGetHealthPointDetail(List<Map> merchantes) {
        String userId = "";
        String userName = "";
        try {
            Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
            userId = BeanUtil.getPropString(user, DaoConstants.ID);
            userName = BeanUtil.getPropString(user, Account.USERNAME);

        } catch (Exception e) {
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "请先登录");
        }


        int total = merchantes.size();
        //没有通过校验（条件1、2、3、4）
        List<Map> merchants_unValid = new ArrayList<>();
        //通过检验的
        List<Map> merchant_success = new ArrayList<>();
        //校验
        for (int i = 0; i < merchantes.size(); i++) {
            String merchant_sn = BeanUtil.getPropString(merchantes.get(i), Merchant.SN, "");
            //商户号为空
            if (StringUtil.empty(merchant_sn)) {
                merchantes.get(i).put("result", "商户号为空");
                merchantes.get(i).put("success", "false");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //去重
            if (merchant_success.contains(merchant_sn)) {
                merchantes.get(i).put("result", "excel中重复商户");
                merchantes.get(i).put("success", "false");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //通过校验的
            merchant_success.add(merchantes.get(i));
        }

        List<Map> merchant_result = healthPointService.getHealthPointByImport(merchant_success);
        merchant_result.addAll(merchants_unValid);
        merchants_unValid.clear();
        merchant_success.clear();
        for (Map map : merchant_result) {
            if (MapUtils.getBooleanValue(map , "success")) {
                merchant_success.add(map);
            } else {
                merchants_unValid.add(map);
            }
        }

        //保存导入结果到任务
        Map<String, Object> result = new HashedMap();
        result.put("total", total);
        result.put("success", total - merchants_unValid.size());
        result.put("failure", merchants_unValid.size());
        result.put("detail",merchant_result);

        Map params = CollectionUtil.hashMap(
                "userId", userId,
                "userName", userName
        );

        //记录日志
        logger.info("健康分批量查询日志记录request[{}],params[{}],result[{}]", merchant_result, params, result);
        Map taskApplyLog = this.createTaskApplyLog(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_IMPORT_GET_HEALTH_POINT,
                TaskApplyLog.APPLY_SYSTEM, 2,
                TaskApplyLog.APPLY_DATE, new java.sql.Date(new Date().getTime()),
                TaskApplyLog.APPLY_RESULT, JSONObject.toJSONString(result),
                TaskApplyLog.PAYLOAD, JSONObject.toJSONString(params),
                TaskApplyLog.USER_ID, userId,
                TaskApplyLog.APPLY_STATUS, 2
        ));
        logger.info("健康分批量查询日志记录taskApplyLog[{}]", BeanUtil.getPropString(taskApplyLog, DaoConstants.ID));
        HttpRequestUtil.getSession().setAttribute(SESSION_IMPORT_HEALTH_POINT, BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID));
        result.put("taskApplyLogId",BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID));

        return result;

    }

    @Override
    public void getImportGetHealthPoint(Map<String, Object> request, HttpServletResponse response) throws IOException {
        Map payload = getTaskResult(request, SESSION_IMPORT_HEALTH_POINT);
        List<Map> details = new ArrayList<>();
        if (payload == null || !payload.containsKey("detail")) {
            details.add(CollectionUtil.hashMap("sn", "", "result", "无数据"));
        } else {
            details = (List<Map>) BeanUtil.getProperty(payload, "detail");
        }
        HSSFWorkbook workbook = buildExcelDetail(details, "批量查询支付宝健康分", Arrays.asList("商户号", "身份证号" , "营业执照号" , "银行卡号" , "支付宝健康分"), Arrays.asList("sn" , "certNo" , "businessLicenseNo", "bankCardNo", "result"));
        String fileName = LocalTime.now() + "-importResults.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }

    private Map getTaskResult(Map request, String sessionKey) {
        try {
            String logId = "";
//                    (String) HttpRequestUtil.getSession().getAttribute(sessionKey);
            if (StringUtils.isEmpty(logId)) {
                logId = MapUtils.getString(request , "taskApplyLogId");
            }
            logger.info("健康分批量查询日志记录logId = ",logId);
            if (logId != null) {
                Map log = logService.getTaskApplyLog(logId);
                if (log != null) {
                    return (Map) JSONObject.parse(MapUtils.getString(log,TaskApplyLog.APPLY_RESULT));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private List<Map> extraDataFromExcelOfHealthPoint(Workbook hssfWorkbook) {
        List<Map> merchantInfos = new ArrayList<Map>();
        try {
            // 循环Sheet
            Sheet hssfSheet = hssfWorkbook.getSheetAt(0);
            if (hssfSheet == null) {
                return null;
            }
            // 循环行Row
            for (int rowNum = 1; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                Row hssfRow = hssfSheet.getRow(rowNum);
                if (hssfRow == null) {
                    continue;
                }
                Cell cellCode = hssfRow.getCell(0);
                cellCode.setCellType(HSSFCell.CELL_TYPE_STRING);
                Cell cellCode1 = hssfRow.getCell(1);
                if (null != cellCode1) {
                    cellCode1.setCellType(HSSFCell.CELL_TYPE_STRING);
                }
                Cell cellCode2 = hssfRow.getCell(2);
                if (null != cellCode2) {
                    cellCode2.setCellType(HSSFCell.CELL_TYPE_STRING);
                }
                Cell cellCode3 = hssfRow.getCell(3);
                if (null != cellCode3) {
                    cellCode3.setCellType(HSSFCell.CELL_TYPE_STRING);
                }
                String merchantSn = cellCode.getStringCellValue().replaceAll(" ", "");
                if (merchantSn.indexOf(".") != -1) {
                    merchantSn = merchantSn.substring(0, merchantSn.indexOf(".")); // 防止解析为小数，去掉0
                }
                if (!StringUtil.empty(merchantSn)) {
                    merchantInfos.add(CollectionUtil.hashMap(
                            "sn", merchantSn,
                            "certNo", toString(cellCode1),
                            "businessLicenseNo", toString(cellCode2),
                            "bankCardNo", toString(cellCode3)
                    ));
                }
            }
            return merchantInfos;
        } catch (Exception e) {
            logger.error("解析强批量查询健康分excel失败", e);
            return null;
        }
    }


    @SuppressWarnings("unchecked")
    private Workbook getExcelFromFile(MultipartFile file) {
        if (file == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "必须上传文件");
        }
        String fileName = file.getOriginalFilename();
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex == -1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        String type = fileName.substring(lastIndex + 1, fileName.length()).toLowerCase();
        if (!"xls".equals(type) && !"xlsx".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        InputStream is = null;
        Workbook workbook = null;
        try {
            is = file.getInputStream();
            if ("xls".equals(type)) {
                workbook = new HSSFWorkbook(is);
            } else if ("xlsx".equals(type)) {
                workbook = new XSSFWorkbook(is);
            }
        } catch (Exception e) {
            logger.error("excel格式解析不支持", e);
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel格式解析不支持");
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                }
            }
        }
        return workbook;
    }

    private int toInt(Object obj, int default_value) {
        if (obj == null) {
            return default_value;
        }
        try {
            String s = obj.toString();
            if (s.indexOf(".") > 0) {
                s = s.substring(0, s.indexOf(".")); // 防止解析为小数，去掉0
            }
            int value = Integer.valueOf(s);
            return value;
        } catch (Exception e) {
            return default_value;
        }
    }

    private String toString(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    private Map createTaskApplyLog(Map<String, Object> request) {
        try {
            return logService.createTaskApplyLog(request);
        } catch (Exception e) {
            logger.error("save TaskApplyLog error,param [{}]", request, e);
        }
        return CollectionUtil.hashMap();
    }


    private HSSFWorkbook buildExcelDetail(List<Map> list, String sheetName, List<String> headers, List<String> keys) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet(sheetName);
        buildExcelDetail(sheet, headers, list, keys);
        return workbook;
    }

    private void buildExcelDetail(HSSFSheet sheet, List<String> headers, List<Map> list, List<String> keys) {
        SheetUtil sheetUtil = new SheetUtil(sheet);
        if (headers != null) {
            sheetUtil.appendRow(headers);
        }
        if (list != null) {
            for (Map map : list) {
                List values = new ArrayList();
                if (keys != null) {
                    for (String key : keys) {
                        values.add(BeanUtil.getProperty(map, key));
                    }
                } else {
                    map = map == null ? new HashMap() : map;
                    Set<String> mKeys = map.keySet();
                    for (String key : mKeys) {
                        values.add(BeanUtil.getProperty(map, key));
                    }
                }
                sheetUtil.appendRow(values);
            }
        }
    }
}
