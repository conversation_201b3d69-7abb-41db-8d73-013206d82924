package com.wosai.upay.service;


import com.wosai.assistant.response.*;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.common.validation.PropNotEmpty;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 推广者相关接口
 *
 * <AUTHOR>
public interface OspOrganizationService {

    Page<String> findByUserAndOrganization(Map<String, Object> request);

    /**
     * 获取初始化额度,merchantSn，status
     * @param request
     * @return
     */
    TradeParam getInitMerchantTradeValidateParam(Map<String, Object> request);


    /**
     * 获取商户的渠道,merchantId
     * @param request
     * @return Channel:
     *           String channelCode;
     *          Category level2Category;（枚举D/S/A/I/K）未知状态null
     *          D: 直营
     *          S: 兼职
     *          A: 代理
     *          I: 行业合作
     *          K: 大客户
     */
    Channel getChannelByMerchantId(Map<String, Object> request);

    /**
     * 获取organizationBean信息,  request:name;String parentId（通过parentId一层层向下）,boolean isTop;(是否是顶级),boolean queryChildren;  pageInfo
     * @param request
     * @return:String id;String parentId;String name;(上海),String nameChain;（直营/上海）,String code;（00002）,String codeChain;（00001/00002）
     */
    Page<OrganizationBean> getOrganization(Map<String, Object> request);

    UserBean getUserById(Map<String, Object> request);

    Map<String, UserBean> getUserByMerchantIds(Map<String, Object> request);

    ListResult findUsersByNameLike(Map<String, Object> request);

    ListResult findUsers(Map<String, Object> request);

    /**
     * 查询组织架构树
     * @param request
     */
    List<Map<String, Object>> getOrganizationTree(Map request);

    /**
     * 查询组织详情
     * @param request
     * @return
     */
    Map<String, Object> getOrganizationDetail(Map request) throws IOException;

    /**
     * 更新组织
     * @param request
     * @return
     */
    Map<String, Object> updateOrganization(Map<String, Object> request) throws IOException;
}
