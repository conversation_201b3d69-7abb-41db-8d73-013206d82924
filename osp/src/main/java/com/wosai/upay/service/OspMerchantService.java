package com.wosai.upay.service;

import com.wosai.common.validation.PropIsMap;
import com.wosai.upay.bank.model.MerchantBusinessLicense;
import com.wosai.upay.bank.model.Request;
import com.wosai.upay.bean.Order;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.core.model.CurrencyFeerate;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.helper.CorePlatformsValidated;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 接口定义，参数校验规则定义在接口方法上，校验失败的错误提示可以支持i18n，具体做法是在src/main/resources/ValidationMessages.properties资源文件中定义错误提示的键值对，然后在这里引用错误提示键。
 * 本地化的资源文件加后缀，例如ValidationMessages_zh_CN.properties。
 *
 * <AUTHOR>
@CorePlatformsValidated
public interface OspMerchantService {

    /**
     * 根据storeId启用商户.
     *
     * @param request id                  UUID
     * @return
     */
    void enableMerchant(Map<String, Object> request);

    /**
     * 根据storeId禁用商户.
     *
     * @param request id                  UUID
     * @return
     */
    void disableMerchant(Map<String, Object> request);

    /**
     * 根据storeId关闭商户.
     *
     * @param request id                  UUID
     * @return
     */
    void closeMerchant(Map<String, Object> request);

    /**
     * 查询商户
     *
     * @param request merchant_id
     *                merchant_name
     *                merchant_sn
     * @return
     */
    ListResult findMerchants(Map request);

    /**
     * 查询商户禁用原因
     * @param request
     * @return
     */
    List<Map> getMerchantDisableReasons(Map request);
    /**
     * 导出商户信息
     *
     * @param request
     * @return
     */
    String exportMerchants(Map request);

    /**
     * 根据商户sn获取商户信息
     *
     * @param request merchant_sn
     * @return
     */
    Map getMerchantBySn(Map request);

    /**
     * 根据商户id获取商户信息
     *
     * @param request merchant_sn
     * @return
     */
    Map getMerchant(Map request);


    /**
     * 查询商户基本信息及照片相关信息
     *
     * @param request
     * @return
     */
    public Map queryApplicationBaseByCondition(Map request);


    /**
     * 编辑商户基本信息及照片相关信息
     */

    public Map updateApplicationBase(Map request);


    /**
     * 创建商户银行账户
     *
     * @param request
     */
    public Map createMerchantBankAccount(Map request);


    /**
     * 查询商户银行账号
     *
     * @param request
     * @return
     */
    public Map queryMerchantBankAccount(Map request);


    /**
     * 编辑商户接口信息
     *
     * @param request
     */

    public Map updateMerchant(@PropNotEmpty(value = "id", message = "{value}不可为空") Map request) throws Exception;


    /**
     * 获取商户开发者参数
     *
     * @param request merchant_id
     * @return
     */
    Map getMerchantDeveloper(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
            })
                    Map request);

    /**
     * 获取商户正式的配置参数
     *
     * @param request merchant_id
     *                payway
     * @return
     */
    Map getMerchantConfigFormalParams(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空"),
                    @PropNotEmpty(value = MerchantConfig.PAYWAY, message = "{value} 支付方式不能为空"),
            })
                    Map request);

    /**
     * 配置支付宝b2c c2b的 正式参数
     * merchant_Id
     * partner
     * app_key
     */
    void updateAlipayV1TradeParams(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
            })
                    Map request);

    /**
     * 更新商户支付宝2.0 b2c c2b支付参数
     * merchant_id
     * "fee_rate": "0.6",
     * "app_auth_token": "xxx"
     */
    void updateAlipayV2TradeParams(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
            })
                    Map request);

    /**
     * 更新微信b2c c2b 正式支付参数
     * merchant_id
     * weixin_sub_appid
     * weixin_sub_mch_id
     */
    void updateWeixinTradeParams(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
            })
                    Map request);


    void updateWeixinHKTradeParams(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
            }) Map request);

    /**
     * 更新微信wap 正式支付参数
     * merchant_id
     * weixin_sub_appid
     * weixin_sub_mch_id
     */
    public void updateWeixinWapTradeParams(Map request);

    /**
     * 获取商户收款通道是否开通，是否正式，费率， 正式参数等信息
     *
     * @param request merchant_id
     * @return
     */
    List getAnalyzedMerchantConfigs(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
            })
                    Map request);

    /**
     * 获取商户支付通道,以及支付通道的配置参数
     *
     * @param request merchant_id
     * @return
     */
    Map getMerchantConfigByMerchantIdAndPayway(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
            })
                    Map request);


    /**
     * 编辑收款通道 是否开通以及费率信息
     *
     * @param request merchant_id
     *                payway
     *                b2c_formal
     *                b2c_satus
     *                b2c_fee_rate
     *                c2b_formal
     *                c2b_fee_rate
     *                c2b_status
     *                wap_formal
     *                wap_status
     *                wap_fee_rate
     */
    void updateMerchantConfigStatusAndFeeRate(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空"),
                    @PropNotEmpty(value = MerchantConfig.PAYWAY, message = "{value} 支付方式不能为空"),
            })
                    Map request);


    /**
     * 新增外币费率
     *
     * @param request
     */
    Map createCurrencyFeeRateMappingRules(@PropNotEmpty.List({
            @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID),
            @PropNotEmpty(value = MerchantConfig.PAYWAY),
            @PropNotEmpty(value = "sub_payway"),
            @PropNotEmpty(value = "currency_codes_pairs"),
            @PropNotEmpty(value = "fee_rate")
    }) Map request);


    /**
     * 修改外币费率
     *
     * @param request
     */
    Map updateCurrencyFeerate(@PropNotEmpty.List({
            @PropNotEmpty(value = CurrencyFeerate.MERCHANT_ID),
            @PropNotEmpty(value = ConstantUtil.KEY_ID),
            @PropNotEmpty(value = CurrencyFeerate.FEE_RATE),
    }) Map request);

    /**
     * 获取商户基本的交易校验信息 如商户限额
     *
     * @param request merchant_id
     * @return
     */
    Map getMerchantTradeValidateParams(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空"),
            }) Map request);

    /***
     * 修改商户基本的交易校验信息 如商户限额
     *
     * @param request merchant_id
     *                store_daily_max_sum_of_trans
     *                merchant_daily_max_sum_of_trans
     */
    void updateMerchantTradeValidateParams(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空"),
            })
                    Map request);

    /**
     * 设置商户当日支付方式限额
     * @param request
     *  merchant_id
     *  payway
     *  sub_payway_config map {sub_payway: sum} sub_payway为空字符串时，表示设置payway的限额, 值全部为字符串
     */
    void updateMerchantDailyPaywayMaxSumOfTrans(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空"),
                    @PropNotEmpty(value = Order.PAYWAY, message = "{payway} 支付方式不能为空"),
            })
                    Map request);

    /***
     * 批量修改商户基本的交易校验信息 如商户限额
     *
     * @param file merchant_id
     *             store_daily_max_sum_of_trans
     *             merchant_daily_max_sum_of_trans
     */
    Map batchUpdateMerchantConfig(MultipartFile file);

    /**
     * 查询导入修改配置结果
     *
     * @param request
     * @return
     */
    public void getBatchUpdateMerchantConfigDetail(Map<String, Object> request, HttpServletResponse response) throws IOException;

    /***
     * 查询商户的用户
     *
     * @param request phone
     */
    public ListResult findMerchantUsers(Map request);

    /***
     * 删除商户的用户
     *
     * @param request merchant_user_id
     *                account_id
     */
    void deleteMerchantUser(Map request);

    /**
     * 获取商户用户门店授权
     *
     * @param request merchant_user_id    商户用户id
     *                store_id            门店id
     *                deleted
     * @return
     */
    List<Map> getMerchantUserStoreAuths(Map request);


    /**
     * 获取商户用户部门授权
     *
     * @param request merchant_id
     *                merchant_user_id    商户用户id
     *                department_id            门店id
     *                deleted
     * @return
     */
    List<Map> getMerchantUserDepartmentAuths(Map request);

    /**
     * 分页查询商户用户门店授权
     *
     * @param request merchant_user_id    商户用户id
     *                store_id            门店id
     *                deleted
     * @return
     */
    ListResult findMerchantUserStoreAuths(Map request);

    /**
     * 批量修改提现方式
     *
     * @param request merchant_user_id    商户用户id
     *                store_id            门店id
     *                deleted
     * @return
     */

    List<Map> batchUpdateWithdrawMode(Map request);

    /**
     * 查询商户余额
     *
     * @param request merchant_id    商户用户id
     * @return
     */

    long getBalance(Map request);

    Map getLakalaTradeParams(Map request);

    void updateLakalaTradeParams(Map request);

    /**
     * 更改银行卡真实性验证状态
     *
     * @param request
     */
    void updateBankAccountVerifyStatus(Map request);

    /**
     * 获取威富通的参数信息
     *
     * @param request merchant_id
     * @return citicbank_group_no    二级商户号
     * citicbank_group_name  二级商户名
     * citicbank_mch_id      威富通商户号
     * citicbank_mch_pay_id  支付宝三级商户号
     */
    Map getWftTradeParams(Map request);


    /**
     * 重新绑定银行卡 - 通过merchantId查找并进行重新绑定.
     *
     * @param request merchant_id
     *                holder         账户持有人名称
     *                id_type        账户持有人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；
     *                identity      账户持有人证件编号
     *                number        账号
     *                bank_name      开户银行名称
     *                branch_name      分支行名称
     *                type        账户类型：1：个人账户；2：企业账户
     */
    @Deprecated
    void rebindMerchantBankAccount(Map request);


    /**
     * 修改银行卡信息
     *
     * @param request merchant_id
     *                holder_id_front_photo    开户人证件照正面照片
     *                holder_id_back_photo    开户人证件照背面照片
     *                tax_payer_id        工商税务号
     *                cellphone          和账号绑定的手机号
     *                city            分支行所在城市
     */
    @Deprecated
    void updateBankAccountEdgeInfo(Map request);

    /**
     * 修改银行卡信息
     *
     * @param request merchant_id
     *                holder         账户持有人名称
     *                id_type        账户持有人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；
     *                identity      账户持有人证件编号
     *                number        账号
     *                bank_name      开户银行名称
     *                branch_name      分支行名称
     *                type        账户类型：1：个人账户；2：企业账户
     *                <p>
     *                holder_id_front_photo    开户人证件照正面照片
     *                holder_id_back_photo    开户人证件照背面照片
     *                tax_payer_id        工商税务号
     *                cellphone          和账号绑定的手机号
     *                city            分支行所在城市
     */
    @Deprecated
    void updateMerchantBankAccountInfo(Map request);

    /***
     * 查询商户是否有商户后台退款权限
     *
     * @param request
     */
    long merchantAvailableMspRefund(Map<String, Object> request);

    /***
     * 修改商户在商户后台退款权限
     *
     * @param request
     */
    void updateMerchantMspRefundPermission(Map request);

    /***
     * 获取商户所有的历史活跃度等级
     *
     * @param request
     */
    ListResult getMerchantAllActiveLevelByMerchantId(Map request);

    /**
     * 执行换卡
     *
     * @param request
     */
    @Deprecated
    Map allowChangeMerchantBankAccount(Map request);

    /**
     * 执行换卡
     *
     * @param request
     */
    @Deprecated
    void changeMerchantBankAccount(Map request);


    /**
     * 修改不需要送拉卡拉信息
     *
     * @param merchantBankAccount
     * @return
     */
    boolean updateBankOtherMessage(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id", message = "{value}merchant_id不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}不能为空")
    }) Map merchantBankAccount);


    boolean updateBusinessLicenseInfo(@PropNotEmpty(value = "merchant_id", message = "merchant_id不能为空") Map businessLicense);


    MerchantBusinessLicense getMerchantBusinessLicense(@PropNotEmpty(value = "merchant_id", message = "merchant_id不能为空")Map request);


    /**
     * 换卡前置校验接口
     *
     * @param params
     * @return
     */
    Map checkoutAllowChangeCard(@PropNotEmpty.List({
            @PropNotEmpty(value = Request.CHANGE_TYPE, message = "{value}请求类型不能为空"),
            @PropNotEmpty(value = "merchant_id", message = "{value}merchant_id不能为空"),
            @PropNotEmpty(value = "number", message = "{value} 银行卡号不能为空")
    }) Map params);

    /**
     * 对公异名换卡
     *
     * @param merchantBankAccount
     * @return
     */
    Map bindBusinessHolderMerchantBankAccount(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id", message = "{value}merchant_id不能为空"),
            @PropNotEmpty(value = "bank_card_image", message = "{value} 开户许可证不能为空"),
            @PropNotEmpty(value = "holder", message = "{value}开户许可证名称不能为空"),
            @PropNotEmpty(value = "bank_card_status", message = "{value} 开户许可证状态不能为空"),
            @PropNotEmpty(value = "number", message = "{value} 银行卡号不能为空"),
            @PropNotEmpty(value = "bank_name", message = "{value}开户银行名称不能为空"),
            @PropNotEmpty(value = "id_type", message = "{value}证件类型不能为空"),
            @PropNotEmpty(value = "holder_id_front_photo", message = "{value}开户人证件照正面照片不能为空"),
            @PropNotEmpty(value = "holder_id_back_photo", message = "{value}开户人证件照背面照片不能为空"),
            @PropNotEmpty(value = "holder_id_front_ocr_status", message = "{value}开户人证件照正面照片ocr状态不能为空"),
            @PropNotEmpty(value = "holder_id_back_ocr_status", message = "{value}开户人证件照背面照片ocr状态不能为空"),
            @PropNotEmpty(value = "legal_person_name", message = "{value}法人姓名不能为空"),
            @PropNotEmpty(value = "identity", message = "{value}证件编号不能为空"),
            @PropNotEmpty(value = "id_validity", message = "{value}证件有效期不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}备注不能为空")
    }) Map merchantBankAccount);


    /**
     * 对私异名换卡
     *
     * @param merchantBankAccount
     * @return
     */
    Map bindPrivateHolderMerchantBankAccount(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id", message = "{value}merchant_id不能为空"),
            @PropNotEmpty(value = "bank_card_image", message = "{value} 银行卡照片不能为空"),
            @PropNotEmpty(value = "bank_card_status", message = "{value} 银行卡照片状态不能为空"),
            @PropNotEmpty(value = "number", message = "{value} 银行卡号不能为空"),
            @PropNotEmpty(value = "bank_name", message = "{value}开户银行名称不能为空"),
            @PropNotEmpty(value = "id_type", message = "{value}证件类型不能为空"),
            @PropNotEmpty(value = "holder_id_front_photo", message = "{value}开户人证件照正面照片不能为空"),
            @PropNotEmpty(value = "holder_id_back_photo", message = "{value}开户人证件照背面照片不能为空"),
            @PropNotEmpty(value = "holder_id_front_ocr_status", message = "{value}开户人证件照正面照片ocr状态不能为空"),
            @PropNotEmpty(value = "holder_id_back_ocr_status", message = "{value}开户人证件照背面照片ocr状态不能为空"),
            @PropNotEmpty(value = "holder", message = "{value}开户人名称不能为空"),
            @PropNotEmpty(value = "identity", message = "{value}证件编号不能为空"),
            @PropNotEmpty(value = "id_validity", message = "{value}证件有效期不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}备注不能为空")
    }) Map merchantBankAccount);


    /**
     * 同名换卡
     *
     * @param merchantBankAccount
     * @return
     */
    Map bindMerchantBankAccount(@PropNotEmpty.List({
            @PropNotEmpty(value = Request.TYPE, message = "{value}请求类型不能为空"),
            @PropNotEmpty(value = "merchant_id", message = "{value}merchant_id不能为空"),
            @PropNotEmpty(value = "bank_card_image", message = "{value} 银行卡照片不能为空"),
            @PropNotEmpty(value = "bank_card_status", message = "{value} 银行卡照片审核状态不能为空"),
            @PropNotEmpty(value = "number", message = "{value} 银行卡号不能为空"),
            @PropNotEmpty(value = "bank_name", message = "{value}开户银行名称不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}备注不能为空")
    }) Map merchantBankAccount);

    /**
     * 切换现有银行卡
     *
     * @return
     */
    Map replaceMerchantBankAccount(@PropNotEmpty.List({
            @PropNotEmpty(value = "id", message = "{value}预存卡id不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}备注不能为空")
    }) Map merchantBankAccountPre);

    /**
     * 修改预存卡
     *
     * @param merchantBankAccountPre
     * @return
     */
    Map updateMerchantBankAccount(@PropNotEmpty.List({
            @PropNotEmpty(value = "id", message = "{value}预存卡id不能为空"),
            @PropNotEmpty(value = Request.TYPE, message = "{value}换卡类型不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}备注不能为空")
    }) Map merchantBankAccountPre);


    /**
     * 查询预存卡状态
     *
     * @return
     */
    Map getMerchantBankAccountPre(@PropNotEmpty.List({
            @PropNotEmpty(value = "id", message = "{value}预存卡id不能为空")
    }) Map<String, Object> request);

    /**
     * 分页查询商户银行预存信息
     *
     * @param
     * @param request merchant_id
     *                type
     *                holder
     *                id_type
     *                identity
     *                tax_payer_id
     *                number
     *                verify_status
     *                bank_name
     *                branch_name
     *                city
     *                cellphone
     * @return
     */
    ListResult findMerchantBankAccounts(Map<String, Object> request);

    /**
     * 删除预存银行卡数据
     *
     * @param
     */
    void deletedMerchantBankAccountPre(@PropNotEmpty.List({
            @PropNotEmpty(value = "id", message = "{value}预存卡id不能为空"),
            @PropNotEmpty(value = Request.KEY_REMARK, message = "{value}备注不能为空")
    }) Map<String, Object> request);


    /**
     * 根据Id查询商户银行卡信息
     *
     * @param
     * @return
     */
    Map getMerchantBankAccountByMerchantId(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id", message = "{value}商户id不能为空")
    }) Map<String, Object> request);


    /**
     * 校验是否可以变更银行卡
     *
     * @param request
     * @return
     */
    @Deprecated
    Map<String, Object> allowChangeMerchantWithdrawMode(Map<String, Object> request);

    /**
     * 更改商户提现方式
     *
     * @param request
     */
    void updateMerchantWithdrawMode(Map<String, Object> request);

    /**
     * 开通、关闭信用卡
     */
    void updateMerchantIsLimitCreditCard(@PropNotEmpty.List({
            @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空"),
            @PropNotEmpty(value = MerchantConfig.PAYWAY, message = "{value} 支付方式不能为空"),
            @PropNotEmpty(value = TransactionParam.ALLOW_CREDIT_PAY, message = "{value} 信用卡状态不能为空")
    }) Map<String, Object> request);


    /**
     * 查询商户创建门店权限
     *
     * @param request
     */
    boolean getCreateStoreLimit(@PropNotEmpty.List({
            @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "商户{value} 不能为空"),
    }) Map<String, Object> request);

    /**
     * 开通/关闭 商户创建门店权限
     *
     * @param request
     */
    void updateCreateStoreLimit(@PropNotEmpty.List({
            @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "商户{value}不能为空"),
    }) Map<String, Object> request);


    /**
     * 获取微信公众号主体信息
     *
     * @param request
     * @return
     */
    Map getMerchantWeixinChannelMessage(Map request);

    /**
     * 获取公众号取消状态
     *
     * @param request
     * @return
     */
    Map getCustomAppidApplyByParams(Map request);


    /**
     * 取消关注公众号
     *
     * @param request
     * @return
     */
    Map updateMerchantNoWeixinAppid(Map request);

    /**
     * 回切商户至普通通道或者
     */
    Map rollbackMerchantWeixinConfig(Map request);

    /**
     * 调敏感词接口校验商户名、商户经营名称
     *
     * @param request
     * @return
     */
    Map validMerchantName(@PropNotEmpty.List({
            @PropNotEmpty(value = Merchant.NAME, message = "{value} 商户名称不能为空"),
            @PropNotEmpty(value = Merchant.BUSINESS_NAME, message = "{value} 商户经营名称不能为空")
    }) Map request);

    /**
     * 校验支付宝及拉卡拉黑名单接口
     *
     * @param request
     * @return
     */
    Map validMerchantBlacklist(Map request);

    /**
     * 更改商户历史交易退款配置
     *
     * @param request
     */
    Map updateMerchantHistoryTradeRefundFlag(@PropNotEmpty.List({
            @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
    }) Map request);

    /**
     * 更改商户预授权配置
     *
     * @param request
     */
    Map updateMerchantDeposit(@PropNotEmpty.List({
            @PropNotEmpty(value = ConstantUtil.KEY_MERCHANT_ID, message = "{value} 商户id不能为空")
    })
                              @PropIsMap(value = TransactionParam.DEPOSIT, nullable = true, emptyable = true, message = "{value}扩展参数必须为Map格式")
                                      Map request);

    Map getGiftCardStatus(Map request);

    void updateGiftCardStatus(Map request);

    /**
     * 获取支付宝收款账户id
     *
     * @param request
     * @return
     */
    Map getAlipaySellerId(Map request);

    /**
     * 更新支付宝收款账户id
     *
     * @param request
     * @return
     */
    Map updateAlipaySellerId(Map request);

    /**
     * 根据merchantId开启商户收款权限.
     *
     * @param request
     */
    void openMerchantPay(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id", message = "{value}商户id不能为空")
    }) Map<String, Object> request);

    /**
     * 根据merchantId关闭商户收款权限.
     *
     * @param request
     */
    void closeMerchantPay(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id", message = "{value}商户id不能为空")
    }) Map<String, Object> request);


    /**
     * 根据merchantId查找商户收款功能的状态.
     *
     * @param request
     * @return 商户收款功能的权限状态
     */
    Integer queryMerchantPayStatus(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id", message = "{value}商户id不能为空")
    }) Map<String, Object> request);
}
