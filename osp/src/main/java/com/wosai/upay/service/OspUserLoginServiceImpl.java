package com.wosai.upay.service;

import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.sdk.support.CommonRedisOps;
import com.wosai.sp.service.OspUserService;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.GroupUser;
import com.wosai.upay.core.model.user.OspUser;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.HttpRequestUtil;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Map;

import static org.apache.http.util.TextUtils.isEmpty;


@Service
public class OspUserLoginServiceImpl implements OspUserLoginService {
    private static final Logger logger = LoggerFactory.getLogger(OspUserLoginService.class);

    private static final String SESSION_USER = "osp_account"; //存储在session中的user信息
    protected static final String SESSION_USERNAME = "_username"; //session中存用户名的

    @Autowired
    private UserService userService;
    @Autowired
    private CommonRedisOps redisOps;

    @Autowired
    private OspUserService ospUserService;
    @Autowired
    private GroupService groupService;

    @Override
    public Map getUserInfo(Map<String, Object> request) {
        //osp前端发起做了一次 session用户数据的同步.
        this.syncSession(request);

        Object accountId = getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        Object username = getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        if (accountId == null) {
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
        }
        Map account = (Map) getSession().getAttribute(SESSION_USER);
        //缓存中accountId与更新后的accountId不一致，需要更新缓存
        if (account != null && !BeanUtil.getPropString(account, ConstantUtil.KEY_ACCOUNT_ID).equals(accountId)) {
            account = null;
        }
        // 获取新sp_user系统中的用户数据
        Map user = ospUserService.getUserInfo(CollectionUtil.hashMap(OspUser.ACCOUNT_ID, accountId));
        if (user == null || MapUtils.getInteger(user, com.wosai.sp.model.OspUser.STATUS) != 1) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "运营平台无此用户或者此用户被禁用");
        }

        String userId = BeanUtil.getPropString(user, ConstantUtil.KEY_ID);
        Map groupUser = groupService.getGroupUser(userId);

        if (account == null || account.size() <= 0) {
            account = CollectionUtil.hashMap(
                    ConstantUtil.KEY_ACCOUNT_ID, accountId,
                    "user_id", BeanUtil.getPropString(user, ConstantUtil.KEY_ID),
                    Account.USERNAME, username,
                    "role", BeanUtil.getPropString(user, "role"),
                    "auth", BeanUtil.getProperty(user, "auth")
            );

            if (MapUtils.isNotEmpty(groupUser)) {
                account.put("group_id", BeanUtil.getPropString(groupUser, GroupUser.GROUP_ID));
            }
            getSession().setAttribute(SESSION_USER, account);
        } else {
            account.put("role", BeanUtil.getPropString(user, "role"));
            account.put("auth", BeanUtil.getProperty(user, "auth"));
        }
        return account;
    }


    //通过 osp后端随机生成的token 与 osp前端页面 做一次用户数据的session同步.(osp前端页面发起)
    private void syncSession(Map<String, Object> request) {
        String sessionId = HttpRequestUtil.getCookieValueByName(OspCasService.SESSIONID);
        if (!isEmpty(sessionId)) {
            String userInfo = redisOps.get(sessionId);
            logger.info("syncSession" + userInfo);
            HttpSession session = getSession();
            if (isEmpty(userInfo)) {
                session.setAttribute(CommonLoginService.SESSION_USERNAME, null);
                session.setAttribute(CommonLoginService.SESSION_ACCOUNT_ID, null);
            } else {
                Map userInfoMap = JSON.parseObject(userInfo);
                session.setAttribute(CommonLoginService.SESSION_USERNAME, userInfoMap.get(CommonLoginService.SESSION_USERNAME));
                session.setAttribute(CommonLoginService.SESSION_ACCOUNT_ID, userInfoMap.get(CommonLoginService.SESSION_ACCOUNT_ID));
            }
        }
    }


    private HttpSession getSession() {
        return HttpRequestUtil.getSession();
    }

    private HttpServletRequest getRequest() {
        return HttpRequestUtil.getRequest();
    }


}
