package com.wosai.upay.service;

import com.wosai.bsm.loanbackend.model.UserInfo;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.validation.PropNotEmpty;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.wosai.upay.common.util.ConstantUtil.KEY_MERCHANT_ID;

/**
 * @author: wangchao
 * 2018/5/17 0017
 */
public interface OspLoanService {

    /**
     * 获取用户的贷款信息
     * @param request
     * @return
     */
    Map getUserLoanInfo(Map request);

    /**
     * 是否在合作方黑名单
     * @param request
     * @return
     */
    Map isInProviderBlacklist(Map request);

    /**
     * 更新用户信息
     * @param request
     * @return
     */
    @Deprecated
    Map updateUserInfo(@PropNotEmpty.List({
            @PropNotEmpty(value = "userId"),
            @PropNotEmpty(value = "merchantId"),
            @PropNotEmpty(value = UserInfo.CELLPHONE),
            @PropNotEmpty(value = UserInfo.IDENTITY),
    })Map request);

    /**
     * [该接口已废弃]修改白名单状态
     * @param request open 对应值 open, close, not_open
     * @return
     */
    @Deprecated
    Map<String, Object> changeOpen(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchantId", message = "{value} 商户id不能为空"),
            @PropNotEmpty(value = "open",message = "{value} 资金方开放状态不能为空")
    })Map request);

    /**
     * [该接口已废弃]切换资金方
     * @param request
     * @return
     */
    @Deprecated
    Map<String, Object> changeProvider(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchantId", message = "{value} 商户id不能为空"),
            @PropNotEmpty(value = "provider",message = "{value} 资金方provider不能为空")
    })Map request);

    /**
     * 修改开放状态或对接方 文档：https://confluence.wosai-inc.com/pages/viewpage.action?pageId=1182198#id-%E8%B4%B7%E6%AC%BEsp%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3-%E4%BF%AE%E6%94%B9%E8%B4%B7%E6%AC%BE%E5%BC%80%E6%94%BE%E7%8A%B6%E6%80%81%E6%88%96%E5%AF%B9%E6%8E%A5%E6%96%B9%E3%80%902019.02.21%E3%80%91
     * @param request  merchantId
     *                 operate open, close, not_open
     *                 provider open时必须传provider
     * @return
     */
    @Deprecated
    Map<String, Object> changeOpenOrProvider(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchantId", message = "{value} 商户id不能为空"),
            @PropNotEmpty(value = "operate", message = "{value} 操作不能为空")
    })Map request);

    /**
     *
     * @param request
     * @return
     */
    List<Map> findUserMultiMerchantInfos(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchantId", message = "{value} 商户id不能为空")
    })Map request);

    /**
     * 获取分组信息
     * @param request
     * @return
     */
    Map getBatches(Map request);

    /**
     * 设置多个用户的分组信息
     * @param batch 分组名称
     * @param file id文件，每行一个。
     * @return
     */
    Map setBatch(MultipartFile file, String batch);

    /**
     * 分页查询贷款申请记录.
     *
     * param pageInfo
     * param queryFilter provider            贷款对接方code
     *                    loan_company_code   贷款公司/资金方code
     *                    user_id             贷款借款人id
     *                    apply_id            贷款申请ID
     *                    merchant_id         商户id，冗余
     *                    current_status      当前状态（收钱吧）
     *                    current_status_provider当前状态（对接方）
     *                    apply_balance       申请金额
     *                    apply_period        申请贷款周期
     *                    approve_balance     审批金额
     *                    approve_period      审批贷款周期
     *                    pay_rate            还款利率
     *                    rate_type           计息方式 1:先息后本 2:等额本息
     *                    refuse_reason       拒绝描述
     *                    apply_date          申请日期
     *                    approve_date        审批日期
     *                    lend_date           第一次放款日期
     *                    deleted             已删除：0否1是
     * @return
     */
    Map findApplyRecords(@PropNotEmpty.List({
            @PropNotEmpty(value = "pageInfo"),
            @PropNotEmpty(value = "queryFilter")
    })Map request);

    /**
     * 分页查询贷款还款计划.
     *
     * param pageInfo
     * param queryFilter provider            贷款对接方code
     *                    loan_company_code   贷款公司/资金方code
     *                    user_id             贷款借款人id
     *                    merchant_id         商户id，冗余
     *                    apply_id            贷款申请ID
     *                    lend_id             贷款放款ID
     *                    refund_id           贷款还款ID
     *                    refund_period       还款期数序号
     *                    pre_refund_date     预计还款日期
     *                    refund_date         真实还款日期
     *                    status              还款状态
     *                    overdue             当前是否逾期 0:否 1:是
     *                    once_overdue        本次还款是否曾经有过逾期 0:否 1:是
     *                    rate_type           计息方式 1:先息后本 2:等额本息
     *                    repay_type          还款方式 1:银行代扣 2:主动还款
     *                    refund_bank_name    还款银行卡
     *                    refund_bank_card    还款银行卡号
     *                    deleted             已删除：0否1是
     * @return
     */
    Map findRefundRecords(@PropNotEmpty.List({
            @PropNotEmpty(value = "pageInfo"),
            @PropNotEmpty(value = "queryFilter")
    })Map request);

    /**
     * 贷款业务常量.
     *
     * @return
     */
    Map getBusinessConstants();

    /**
     * 批量切资金方
     * @param file id/sn文件，每行一个。
     * @return
     */
    Map batchChangeProvider(MultipartFile file, Map request) throws IOException;
}
