package com.wosai.upay.service;


import com.wosai.business.audit.model.AuditConstant;
import com.wosai.business.audit.model.AuditRecord;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.merchant.Bean.ExportTask;
import com.wosai.operation.bean.MerchantInvoiceConfig;
import com.wosai.operation.bean.MerchantInvoiceOrder;
import com.wosai.operation.service.MerchantInvoiceService;
import com.wosai.merchant.service.ExportTaskService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.helper.CommonUtil;
import com.wosai.upay.helper.StringUtil;
import com.wosai.upay.service.translate.*;
import com.wosai.upay.user.api.util.DateUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.exception.UpayException.CODE_INVALID_PARAMETER;


/**
 * Created by chenyu on 2018/5/30.
 */
@SuppressWarnings("unchecked")
@Service
public class OspMerchantInvoiceServiceImpl implements OspMerchantInvoiceService {

    private static final Logger logger = LoggerFactory.getLogger(OspMerchantInvoiceService.class);

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantInvoiceService merchantInvoiceService;
    @Autowired
    private ExportTaskService exportTaskService;

    @Override
    public ListResult findMerchantInvoiceConfig(Map request) {
        request.put(MerchantInvoiceConfig.MERCHANT_TYPE, MerchantInvoiceConfig.MERCHANT_TYPE_SINGLE);
        return merchantInvoiceService.findMerchantInvoiceConfig(
                PageInfoUtil.extractPageInfo(request), request
        );
    }

    @Override
    public Map addMerchantInvoiceConfigList(Map request) {
        String merchantSnsStr = MapUtils.getString(request, "sns");
        String[] snArray = StringUtil.full2Half(merchantSnsStr)
                .replaceAll(" ", "").split(",");

        List<String> sns = Arrays.stream(snArray).distinct().collect(Collectors.toList());
        if (sns.size() < 1 || sns.size() > 10) {
            throw new UpayException(CODE_INVALID_PARAMETER, "一次只能添加1~10个商户");
        }

        List<String> errorSns = new ArrayList<>();
        for (String sn : sns) {
            Map map = merchantService.getMerchantBySn(sn);
            if (CollectionUtils.isEmpty(map)) {
                errorSns.add(sn);
                continue;
            }
            try {
                merchantInvoiceService.enableMerchant(CollectionUtil.hashMap(
                        MerchantInvoiceConfig.MERCHANT_ID, MapUtils.getString(map, ConstantUtil.KEY_ID),
                        MerchantInvoiceConfig.MERCHANT_TYPE, MerchantInvoiceConfig.MERCHANT_TYPE_SINGLE,
                        MerchantInvoiceConfig.CREATOR_ID, getUserIdAndName()[0],
                        MerchantInvoiceConfig.CREATOR_NAME, getUserIdAndName()[1]
                ));
            } catch (Exception e) {
                logger.error(e.getMessage());
                errorSns.add(sn);
            }
        }

        Map result = new HashMap();
        result.put("succ_num", sns.size() - errorSns.size());
        result.put("error_num", errorSns.size());
        result.put("error_sns", errorSns);
        result.put("repeat_num", snArray.length - sns.size());
        return result;
    }

    @Override
    public void updateMerchantInvoiceConfig(Map request) {
        request.put(MerchantInvoiceOrder.APPLY_USER_ID, getUserIdAndName()[0]);
        request.put(MerchantInvoiceOrder.APPLY_USER_NAME, getUserIdAndName()[1]);
        request.put(AuditRecord.APPLY_SYSTEM, AuditConstant.APPLY_SYSTEM_SP);
        merchantInvoiceService.submit(request);
    }

    @Override
    public void deleteTask(Map request) {
        String id = MapUtils.getString(request, ConstantUtil.KEY_ID);
        exportTaskService.deleteTask(id);
    }

    @Override
    public void createTask(Map request) {
        List<Map> tasks = CommonUtil.convertToList(MapUtils.getObject(request, "tasks"));
        String date = DateUtil.formatDate(new Date(), "yyyy-MM-dd hh时mm分ss秒");
        List<String> merchantIds = CommonUtil.getValues(tasks, "merchant_id");
        exportTaskService.createTask(CollectionUtil.hashMap(
                ExportTask.TYPE, ExportTask.TYPE_MERCHANT_INVOICE_CONFIG_INFO,
                ExportTask.NAME, date,
                ExportTask.PARAM, CollectionUtil.hashMap(
                        "merchant_ids", merchantIds
                ))
        );
    }

    @Override
    public void confirmImportFee(Map request) {
        String month = DateFormatUtils.format(DateUtils.addMonths(new Date(),-1),"yyyyMM");
        merchantInvoiceService.confirmImportFee(Integer.parseInt(month));
    }

    @Override
    public void confirmMail(Map request) {
        merchantInvoiceService.sendInvoice();
    }

    @Override
    public ListResult findTask(Map request) {
       return exportTaskService.findTask(PageInfoUtil.extractPageInfo(request), request);
    }

    @Override
    public void exportHistoryFee(Map params, HttpServletResponse response) throws IOException {
        params.put("pageSize", 1000);
        List<Map> result = merchantInvoiceService.findHistoryFee(params);
        Map<String, Rule> rules = new HashMap();
        rules.put("total_amount", new NumberRule(2, BigDecimal.ROUND_HALF_UP, 0.01D));
        rules.put("total_fee", new NumberRule(2, BigDecimal.ROUND_HALF_UP, 0.01D));

        Workbook workbook = SheetUtils.buildExcel("手续费", result,
                CollectionUtil.hashMap(
                        MerchantInvoiceOrder.MERCHANT_SN,"商户号",
                        MerchantInvoiceOrder.MERCHANT_NAME,"商户名称",
                        "total_amount","交易总额",
                        "total_fee","手续费",
                        "month","月份"
                ), rules);
        String fileName = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_invoice.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }

    @Override
    public void exportInvoiceFee(Map params, HttpServletResponse response) throws IOException {
        PageInfo pageInfo = new PageInfo(1,2000);

        ListResult result = merchantInvoiceService.findMerchantInvoiceOrders(pageInfo,params);
        List<Map> data = result.getRecords();

        List<String> merchantIds = CommonUtil.getValues(data,MerchantInvoiceOrder.MERCHANT_ID);
        ListResult configResult = merchantInvoiceService.findMerchantInvoiceConfig(pageInfo,CollectionUtil.hashMap(
                "merchant_ids",merchantIds
        ));
        List<Map> configs = configResult.getRecords();
        Map<String, Map> configMap = CommonUtil.convert(configs, MerchantInvoiceOrder.MERCHANT_ID);

        //通过 export_merged_order判断 ,是否合并数据
        if (MapUtils.getIntValue(params, "export_merged_order") == 1) {
            exportInvoiceOrder(mergedInvoiceOrder(data), configMap, response);
        } else {
            exportInvoiceOrder(data, configMap, response);
        }
    }

    private void exportInvoiceOrder(List<Map> orderData, Map<String, Map> configMap, HttpServletResponse response) throws IOException {
        for (Map order : orderData) {
            addExtraInfo2InvoiceOrder(configMap, order);
        }
        Workbook workbook = generateInvoiceExcelWorkbook(orderData);
        String fileName = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "_invoice.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }

    private void addExtraInfo2InvoiceOrder(Map configMap, Map order) {
        String merchantId = MapUtils.getString(order, MerchantInvoiceOrder.MERCHANT_ID);
        order.putAll(BeanUtil.getPart(configMap.get(merchantId), Arrays.asList(
                MerchantInvoiceConfig.CONTACT_NAME, MerchantInvoiceConfig.CONTACT_CELLPHONE, MerchantInvoiceConfig.ADDRESS
        )));
        //增加字段
        Map bankAccountInfo = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        String accountName = MapUtils.getString(bankAccountInfo, "holder");
        String invoiceName = MapUtils.getString(order, MerchantInvoiceOrder.INVOICE_NAME);
        if (accountName != null && invoiceName != null) {
            order.put("holder", accountName);
            order.put("is_consistent", invoiceName.equals(accountName) ? "是" : "否");
        } else {
            order.put("holder", accountName);
            order.put("is_consistent", "否");
        }
    }

    private Workbook generateInvoiceExcelWorkbook(List<Map> data) {
        DateRule dateRule = new DateRule();
        Map<String, Rule> rules = new HashMap();
        rules.put(MerchantInvoiceOrder.TAX_TYPE, new MapRule(MapRule.MERCHANT_INVOICE_ORDER_TAX_TYPE));
        rules.put(MerchantInvoiceOrder.MODE, new MapRule(MapRule.MERCHANT_INVOICE_ORDER_MODE));
        rules.put(MerchantInvoiceOrder.STATUS, new MapRule(MapRule.MERCHANT_INVOICE_ORDER_STATUS));
        rules.put(MerchantInvoiceOrder.AMOUNT, new NumberRule(2, BigDecimal.ROUND_HALF_UP, 0.01D));
        rules.put(MerchantInvoiceOrder.SUBMIT_TIME, dateRule);
        Workbook workbook = SheetUtils.buildExcel("手续费开票", data,
                CollectionUtil.hashMap(
                        MerchantInvoiceOrder.MERCHANT_SN,"商户号",
                        MerchantInvoiceOrder.MERCHANT_NAME,"商户名称",
                        MerchantInvoiceConfig.CONTACT_NAME,"收件人",
                        MerchantInvoiceConfig.CONTACT_CELLPHONE,"收件电话",
                        MerchantInvoiceConfig.ADDRESS,"收件地址",
                        MerchantInvoiceOrder.TAX_TYPE,"发票类型",
                        MerchantInvoiceOrder.AMOUNT,"发票金额",
                        MerchantInvoiceOrder.ORDER_PERIOD,"金额计算周期",
                        MerchantInvoiceOrder.SN,"开票编号",
                        MerchantInvoiceOrder.INVOICE_NAME,"开票抬头",
                        "holder", "结算户名",
                        "is_consistent", "是否一致",
                        MerchantInvoiceOrder.STATUS,"开票状态",
                        MerchantInvoiceOrder.MODE,"开票方式"
                ), rules);
        return workbook;
    }

    private List<Map> mergedInvoiceOrder(List<Map> orders) {
        Map<String, Map<String, Map>> mergeByMerchantSn = new HashMap<>();
        for (Map order : orders) {
            String merchantSn = MapUtils.getString(order, MerchantInvoiceConfig.MERCHANT_SN);
            String merchantStatus = MapUtils.getString(order, MerchantInvoiceConfig.STATUS);
            //把不同商户的第一个 order 记录下来
            if (!mergeByMerchantSn.containsKey(merchantSn)) {
                //按商户号分
                Map<String, Map> mergeByMerchantStatus = new HashMap<>();
                mergeByMerchantStatus.put(merchantStatus, order);
                mergeByMerchantSn.put(merchantSn, mergeByMerchantStatus);
            } else {
                Map mergeByMerchantStatus = MapUtils.getMap(mergeByMerchantSn, merchantSn);
                if (!mergeByMerchantStatus.containsKey(merchantStatus)) {
                    //按状态分
                    mergeByMerchantStatus.put(merchantStatus, order);
                } else {
                    Map mergeOrder = (Map) mergeByMerchantStatus.get(merchantStatus);
                    // 相同状态合并金额
                    long merchantAmount = MapUtils.getLongValue(order, MerchantInvoiceOrder.AMOUNT);
                    long totalAmount = Math.addExact(merchantAmount, MapUtils.getLongValue(mergeOrder, MerchantInvoiceOrder.AMOUNT));
                    mergeOrder.put(MerchantInvoiceOrder.AMOUNT, totalAmount);
                    //开票编号 sn
                    String invoiceSns = MapUtils.getString(mergeOrder, MerchantInvoiceOrder.SN);
                    String invoiceSn = MapUtils.getString(order, MerchantInvoiceOrder.SN);
                    mergeOrder.put(MerchantInvoiceOrder.SN, invoiceSns + ", " + invoiceSn);
                    //金额计算周期合并
                    String orderPeriods = MapUtils.getString(mergeOrder, MerchantInvoiceOrder.ORDER_PERIOD);
                    String orderPeriod = MapUtils.getString(order, MerchantInvoiceOrder.ORDER_PERIOD);
                    mergeOrder.put(MerchantInvoiceOrder.ORDER_PERIOD, orderPeriods + ", " + orderPeriod);
                }
            }
        }
        List<Map> mergedOrderData = new ArrayList<>();
        Collection<Map<String, Map>> values = mergeByMerchantSn.values();
        for (Map<String, Map> mergeByStatus : values) {
            Collection<Map> ordersData = mergeByStatus.values();
            mergedOrderData.addAll(ordersData);
        }
        return mergedOrderData;
    }

    @Override
    public ListResult findMerchantInvoiceOrders(Map params) {
        return merchantInvoiceService.findMerchantInvoiceOrders(PageInfoUtil.extractPageInfo(params), params);
    }

    @Override
    public void updateOrder(Map request) {
        merchantInvoiceService.updateOrder(request);
    }

    @Override
    public Map getSummaryConfig(Map request) {
        String merchantId = MapUtils.getString(request, MerchantInvoiceConfig.MERCHANT_ID);
        return merchantInvoiceService.getSummaryConfig(merchantId);
    }

    @Override
    public void disableMerchant(Map request) {
        merchantInvoiceService.disableMerchant(request);
    }

    @Override
    public void enableMerchant(Map request) {
        request.put(MerchantInvoiceConfig.CREATOR_ID, getUserIdAndName()[0]);
        request.put(MerchantInvoiceConfig.CREATOR_NAME, getUserIdAndName()[1]);
        merchantInvoiceService.enableMerchant(request);
    }

    private String[] getUserIdAndName() {
        HttpSession session = HttpRequestUtil.getSession();
        Object accountId = session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        Object username = session.getAttribute(CommonLoginService.SESSION_USERNAME);

        return new String[]{String.valueOf(accountId), String.valueOf(username)};
    }
}
