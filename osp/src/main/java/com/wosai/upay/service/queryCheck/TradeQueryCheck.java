package com.wosai.upay.service.queryCheck;

import com.google.common.collect.ImmutableSet;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.exception.UpayException;
import java.util.Map;
import static com.wosai.upay.common.util.ConstantUtil.KEY_DATE_END;
import static com.wosai.upay.common.util.ConstantUtil.KEY_DATE_START;

public class TradeQueryCheck {

    /**
     *
     * 31天
     */
    public static final long ONE_MONTH_TIME_MILLIS = 31 * 24 * 60 * 60 * 1000l;


    /**
     * 1 天
     */
    public static final long ONE_DAY_TIME_MILLIS = 24 * 60 * 60 * 1000l;



    private static final ImmutableSet<String> CAN_ONE_MONTH_QUERY = ImmutableSet.of("store_name"
            , "store_sn"
            , "store_id"
            , "merchant_id"
            , "merchant_sn"
            , "merchant_name"
            , "terminal_sn"
            , "terminal_name"
            , "merchant_client_sn"
            , Terminal.DEVICE_FINGERPRINT);


    private static final ImmutableSet<String> CAN_QUERY = ImmutableSet.of("order_sn"
            , "client_sn"
            , "trade_no"
            , "transaction_sn"
            , "channel_trade_no"
            , "client_sn"
            , "trade_no");


    /**
     *
     * @param request
     */
    public static void timeSpanCheck(Map request){
        //查询时间区间不限制
        for(String str : CAN_QUERY){
            if(request.get(str) != null){
                return;
            }
        }
        //查询时间区间1个月
        boolean isOneMonthQuery = false;
        for(String str : CAN_ONE_MONTH_QUERY){
            if(request.get(str) != null){
                isOneMonthQuery = true;
                break;
            }
        }
        long currentTimeMillis = System.currentTimeMillis();
        Long  startTimeMillis = BeanUtil.getPropLong(request, KEY_DATE_START, currentTimeMillis);
        Long endTimeMillis = BeanUtil.getPropLong(request, KEY_DATE_END, currentTimeMillis);
        if(isOneMonthQuery){
            if(endTimeMillis - startTimeMillis > ONE_MONTH_TIME_MILLIS){
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户维度查询,时间限制为一个月");
            }
        }else{
            if(endTimeMillis - startTimeMillis > ONE_DAY_TIME_MILLIS){
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "全量查询,时间限制为一天");
            }
        }
    }
}
