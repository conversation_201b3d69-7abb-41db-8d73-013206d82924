package com.wosai.upay.service;

import com.wosai.assistant.request.OrganizationRequest;
import com.wosai.assistant.response.*;
import com.wosai.assistant.service.MerchantRpcService;
import com.wosai.assistant.service.OrganizationRpcService;
import com.wosai.assistant.service.UserRpcService;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.sales.core.model.Organization;
import com.wosai.sales.core.model.OrganizationConfig;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.sales.core.service.UserService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/5/8.
 */
@Service
public class OspOrganizationServiceImpl implements OspOrganizationService {
    private static final Logger logger = LoggerFactory.getLogger(OspOrganizationServiceImpl.class);
    @Autowired
    UserRpcService userRpcService;
    @Autowired
    MerchantRpcService merchantRpcService;
    @Autowired
    OrganizationRpcService organizationRpcService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private BusinessLogService businessLogService;
    @Autowired
    UserService userService;


    @Override
    public Page<String> findByUserAndOrganization(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return merchantRpcService.findByUserAndOrganization(pageInfo,request);
    }

    @Override
    public TradeParam getInitMerchantTradeValidateParam(Map<String, Object> request) {
        String merchantSn = BeanUtil.getPropString(request,"merchantSn");
        int status = BeanUtil.getPropInt(request,"status");
        if (StringUtil.empty(merchantSn)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER,"merchantSn不可为空");
        }
        return organizationRpcService.getInitMerchantTradeValidateParam(merchantSn,status);
    }

    @Override
    public Channel getChannelByMerchantId(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request,"merchantId");
        if (StringUtil.empty(merchantId)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER,"merchantId不可为空");
        }
        return organizationRpcService.getChannelByMerchantId(merchantId);
    }

    @Override
    public Page<OrganizationBean> getOrganization(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        OrganizationRequest or = new OrganizationRequest();
        or.setName(BeanUtil.getPropString(request,"name"));
        or.setParentId(BeanUtil.getPropString(request,"parentId"));
        or.setQueryChildren(BeanUtil.getPropBoolean(request,"queryChildren",false));
        or.setTop(BeanUtil.getPropBoolean(request,"isTop",false));
        return organizationRpcService.getOrganization(or,pageInfo);
    }

    @Override
    public UserBean getUserById(Map<String, Object> request) {
        String id = BeanUtil.getPropString(request,"id");
        if (StringUtil.empty(id)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER,"id不可为空");
        }
        return userRpcService.getUserById(id);
    }

    @Override
    public Map<String, UserBean> getUserByMerchantIds(Map<String, Object> request) {
        if (request == null || !request.containsKey("merchantIds")){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER,"merchantIds不可为空");
        }
        List<String> merchantIds = null;
        try {
            merchantIds = (List<String>) request.get("merchantIds");
        }catch (Exception e){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER,"merchantIds格式有误");
        }
        if (merchantIds == null || merchantIds.size() < 1){
            return null;
        }
        return userRpcService.getUserByMerchantIds(merchantIds);
    }

    @Override
    public ListResult findUsersByNameLike(Map<String, Object> request) {
        if (request == null || !request.containsKey("organization_user_name")){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER,"organization_user_name不可为空");
        }
       String name = BeanUtil.getPropString(request,"organization_user_name");
       if(!StringUtil.empty(name)){
           List<UserBean> list = userRpcService.findUsersByNameLike(name);
           List<Map> maps = new ArrayList<>();
           for(UserBean userBean : list){
               maps.add(bean2Map(userBean));
           }
           return new ListResult(maps.size(),maps);
       }
       return null;
    }

    @Override
    public ListResult findUsers(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        request.remove("page");
        request.remove("page_size");
        return userService.findUsers(pageInfo, request);
    }

    @Override
    public List<Map<String, Object>> getOrganizationTree(Map request) {
        return organizationService.getOrganizationTree(BeanUtil.getPropString(request, "rootId"));
    }

    @Override
    public Map<String, Object> getOrganizationDetail(Map request) throws IOException{
        Map result = organizationService.getOrganization(BeanUtil.getPropString(request, DaoConstants.ID));
        String risk_configs = BeanUtil.getPropString(result, OrganizationConfig.RISK_CONFIGS);
        if (!StringUtil.empty(risk_configs)){
            result.put(OrganizationConfig.RISK_CONFIGS, JacksonUtil.toBean(risk_configs, List.class));
        }
        return result;
    }

    @Override
    public Map<String, Object> updateOrganization(Map<String, Object> request) throws IOException{
        String id = BeanUtil.getPropString(request, DaoConstants.ID);
        if (StringUtil.empty(id)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER,"id参数不可为空!");
        }
        List risk_configs = (List) BeanUtil.getProperty(request, OrganizationConfig.RISK_CONFIGS);
        if (risk_configs != null){
            request.put(OrganizationConfig.RISK_CONFIGS, JacksonUtil.toJsonString(risk_configs));
        }
        Map before = getOrganizationDetail(request);
        organizationService.updateOrganization(request);
        Map after = getOrganizationDetail(request);
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, before,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, after,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "organizations",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "organizations",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id"
                )
        ));
        return after;
    }


    /**
     * 将bean转为map
     * @param bean
     * @return
     */
    public Map<String,Object> bean2Map(Object bean){
        if(bean == null){
            return null;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(bean.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();

                // 过滤class属性
                if (!key.equals("class")) {
                    // 得到property对应的getter方法
                    Method getter = property.getReadMethod();
                    Object value = getter.invoke(bean);
                    map.put(key, value);
                }

            }
        } catch (Exception e) {
            return null;
        }
        return map;
    }
}
