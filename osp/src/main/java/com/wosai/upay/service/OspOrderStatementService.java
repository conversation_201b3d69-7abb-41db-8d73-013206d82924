package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.helper.CorePlatformsValidated;

import java.util.List;
import java.util.Map;

/**
 * Created by jianfree on 31/3/16.
 */
@CorePlatformsValidated
public interface OspOrderStatementService {


    List getTransactionListByOrderSn(Map request);


    /**
     * 获取导出任务集合
     *
     * @param request
     * @return
     */
    ListResult findTaskApplyLogs(Map request);

    /**
     * 获取单个导出任务
     *
     * @param request
     * @return
     */
    Map getTaskApplyLog(Map request);

    /**
     * 删除一个导出任务
     *
     * @param request
     */
    void deleteTaskApplyLog(Map request);

    /**
     * 订单查询
     *
     * @param request merchant_id
     *                merchant_sn
     *                merchant_name
     *                store_id
     *                store_sn
     *                store_name
     *                payway
     *                sub_payway
     *                status
     *                min_total_amount
     *                max_total_amount
     *                order_sn
     *                client_sn
     *                trade_no
     *                terminal_sn
     * @return
     */
    ListResult findOrders(Map request);

    /**
     * 创建订单导出任务
     * @param request
     * @return
     */
    Map createExportOrderTask(Map<String, Object> request);

    /**
     * 创建对账单导出任务
     *
     * @param request
     * @return
     */
    Map createExportTransactionTask(Map<String, Object> request);


    /**
     * 根据订单号查询对应的交易ip和交易城市
     *
     * @param request
     * @return
     */
    Map getClientIpAndCityByOrderSn(Map<String, Object> request);
}
