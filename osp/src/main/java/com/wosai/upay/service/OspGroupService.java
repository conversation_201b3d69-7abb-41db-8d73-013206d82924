package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 集团.
 *
 * <AUTHOR>
 */
public interface OspGroupService {

    /**
     * 创建集团.
     *
     * @param request 如果传account_id的话则会创建一个超级管理员的集团用户，如果传auth_merchant_ids则保存管理的商户授权
     */
    Map createGroup(Map request);

    /**
     * 根据groupId启用集团.
     *
     * @param request id
     */
    void enableGroup(Map request);

    /**
     * 根据groupId禁用集团.
     *
     * @param request id
     */
    void disableGroup(Map request);

    /**
     * 根据groupId关闭集团.
     *
     * @param request id
     */
    void closeGroup(Map request);

    /**
     * 根据groupId删除集团.
     *
     * @param request id
     */
    void deleteGroup(Map request);

    /**
     * 根据groupSn删除集团.
     *
     * @param request sn
     */
    void deleteGroupBySn(Map request);

    /**
     * 修改集团.
     *
     * @param request
     */
    Map updateGroup(Map request);

    /**
     * 根据groupId获取集团.
     *
     * @param request id
     * @return
     */
    Map getGroup(Map request);

    /**
     * 根据groupSn获取集团.
     *
     * @param request sn
     * @return
     */
    Map getGroupBySn(Map request);

    /**
     * 分页查询集团.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                sn                  集团编号
     *                name                集团名称
     *                alias               集团简称
     *                industry
     *                status
     *                contact_name        联系人姓名
     *                contact_phone       联系固定电话号码
     *                contact_cellphone   联系移动电话号码
     *                client_sn           集团外部集团号
     *                deleted
     *                manager_merchant_names    传yes时会把管辖的商户名称查出来使用半角逗号拼成属性manager_merchant_names并返回
     *                merchant_sn         管辖商户编号
     *                merchant_name       管辖商户名称  模糊查询
     *                username            集团账号
     * @return
     */
    ListResult findGroups(Map request);

    /**
     * 创建集团账号.
     *
     * @param request
     */
    Map createAccount(Map request);

    /**
     * 修改集团账号密码.
     *
     * @param request username
     *                password
     * @return
     */
    Map updateAccountPassword(Map request);

    /**
     * 创建集团用户.
     *
     * @param request
     */
    Map createGroupUser(Map request);

    /**
     * 根据groupUserId启用集团用户.
     *
     * @param request id
     */
    void enableGroupUser(Map request);

    /**
     * 根据groupUserId禁用集团用户.
     *
     * @param request id
     */
    void disableGroupUser(Map request);

    /**
     * 根据groupUserId删除集团用户.
     *
     * @param request id
     */
    void deleteGroupUser(Map request);

    /**
     * 修改集团用户.
     *
     * @param request
     */
    Map updateGroupUser(Map request);

    /**
     * 根据groupUserId获取集团用户.
     *
     * @param request id
     * @return
     */
    Map getGroupUser(Map request);

    /**
     * 根据accountId获取集团用户.
     *
     * @param request account_id
     * @return
     */
    Map getGroupUserByAccountId(Map request);

    /**
     * 分页查询集团用户.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                name                名称
     *                group_id            集团id
     *                account_id          account表id
     *                role                角色/职务，super_admin：超级管理员，admin：管理员
     *                merchant_auth       商户权限，1：所有商户，2：授权商户
     *                status              状态：0：禁用；1:正常
     *                deleted
     * @return
     */
    ListResult findGroupUsers(Map request);

    /**
     * 保存集团用户商户授权.
     *
     * @param request group_user_id
     *                merchant_ids 商户ids多个以半角逗号分隔
     */
    List<Map> saveGroupUserMerchantAuth(Map request);

    /**
     * 导入集团管辖商户(需要商户sn和商户名称同数据库中的一致).
     *
     * @param groupUserId
     * @param merchantInfos 商户信息(包括商户sn、商户名称)
     * @return
     */
    List<Map> importGroupUserMerchantAuth(String groupUserId, List<Map> merchantInfos);

    /**
     * 导入集团管辖商户.
     *
     * @param file
     */
    void importGroupMerchantAuth(MultipartFile file);

    /**
     * 获取导入集团管辖商户处理详情.
     *
     * @param request 空
     * @return
     */
    Map getImportGroupMerchantAuthPayload(Map request);

    /**
     * 获取导入集团管辖商户结果.
     *
     * @param request 空
     * @return
     */
    Map getImportGroupMerchantAuthResult(Map request);

    /**
     * 导出导入集团管辖商户失败详情.
     *
     * @param params
     * @param response
     * @throws IOException
     */
    void getImportGroupMerchantAuthFailDetail(Map params, HttpServletResponse response) throws IOException;

    /**
     * 根据groupUserMerchantAuthId删除集团用户商户权限.
     *
     * @param request id 权限id
     */
    void deleteGroupUserMerchantAuth(Map request);

    /**
     * 根据groupUserMerchantAuthId获取集团用户商户权限.
     *
     * @param request id
     * @return
     */
    Map getGroupUserMerchantAuth(Map request);

    /**
     * 获取集团用户商户授权.
     *
     * @param request group_id            集团id
     *                group_user_id       集团用户id
     *                merchant_id         商户id
     *                merchant_ids        商户id数组
     *                merchant_sn         商户编号
     *                merchant_name       商户名称  模糊查询
     *                deleted
     * @return
     */
    List<Map> getGroupUserMerchantAuths(Map request);

    /**
     * 分页查询集团用户商户权限.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                group_id            集团id
     *                group_user_id       集团用户id
     *                merchant_id         商户id
     *                merchant_ids        商户id数组
     *                merchant_sn         商户编号
     *                merchant_name       商户名称  模糊查询
     *                deleted
     * @return
     */
    ListResult findGroupUserMerchantAuths(Map request);

    /**
     * 集团导出.
     *
     * @param params
     * @param response
     */
    void exportGroups(Map params, HttpServletResponse response) throws IOException;

    /***
     * 修改集团在商户后台退款权限
     *  @param request
     *
     */
    void updateGroupMspRefundPermission(Map request);

}
