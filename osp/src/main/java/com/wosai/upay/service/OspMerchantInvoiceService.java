package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.helper.CorePlatformsValidated;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created by chenyu on 2018/5/30.
 */
@CorePlatformsValidated
public interface OspMerchantInvoiceService {

    ListResult findMerchantInvoiceConfig(Map request);

    Map addMerchantInvoiceConfigList(@PropNotEmpty.List({
            @PropNotEmpty(value = "sns", message = "{value}不能为空"),
    }) Map request);

    void updateMerchantInvoiceConfig(Map request);

    void deleteTask(Map request);

    void createTask(Map request);

    void confirmImportFee(Map request);

    void confirmMail(Map request);

    ListResult findTask(Map request);

    void exportHistoryFee(Map params, HttpServletResponse response) throws IOException;

    void exportInvoiceFee(Map params, HttpServletResponse response) throws IOException;

    ListResult findMerchantInvoiceOrders(Map params);

    void updateOrder(Map request);

    Map getSummaryConfig(Map request);

    void disableMerchant(Map request);

    void enableMerchant(Map request);
}
