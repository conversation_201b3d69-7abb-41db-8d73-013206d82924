package com.wosai.upay.service;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.googlecode.jsonrpc4j.ProxyUtil;
import com.wosai.app.backend.api.bean.user.User;
import com.wosai.app.backend.api.service.IAccountService;
import com.wosai.app.backend.api.service.IAppGatedRuleService;
import com.wosai.app.push.api.bean.AudioDTO;
import com.wosai.app.push.api.bean.LocationDTO;
import com.wosai.app.push.api.bean.MsgDTO;
import com.wosai.app.push.api.service.IPushMessageService;
import com.wosai.app.push.api.template.PushTemplate;
import com.wosai.assistant.response.Page;
import com.wosai.assistant.response.UserBean;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.risk.bean.RiskSubjectBean;
import com.wosai.risk.constants.RiskSubjectTypeEnum;
import com.wosai.risk.service.IRiskQueryService;
import com.wosai.shouqianba.withdrawservice.exception.WithdrawBizErrorException;
import com.wosai.shouqianba.withdrawservice.model.*;
import com.wosai.shouqianba.withdrawservice.service.*;
import com.wosai.upay.clearance.exception.BizException;
import com.wosai.upay.clearance.model.Withdraw;
import com.wosai.upay.clearance.service.ClearanceService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.constant.WithdrawConstant;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.helper.CommonUtil;
import com.wosai.upay.helper.ExcelCreator;
import com.wosai.upay.helper.ResponseHelper;
import com.wosai.upay.rest.ast.ASTHelper;
import com.wosai.upay.service.remote.NoticeService;
import com.wosai.upay.service.remote.WithdrawService;
import com.wosai.upay.util.*;
import com.wosai.upay.wallet.model.EventLog;
import com.wosai.upay.wallet.model.MerchantLimitAuthorization;
import com.wosai.upay.wallet.service.MerchantLimitAuthorizationService;
import com.wosai.upay.wallet.service.WalletService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpCookie;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.InvalidParameterException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;


/**
 * Created by kay on 16/11/2.
 */
@SuppressWarnings("unchecked")
public class OspWithdrawServiceImpl implements OspWithdrawService {
    @Autowired
    MerchantService merchantService;
    @Autowired
    LogService logService;
    @Autowired
    IAppGatedRuleService appGatedRuleService;
    @Autowired
    com.wosai.shouqianba.withdrawservice.service.WithdrawService withdrawRealTimeService;
    @Autowired
    WithdrawConfigService withdrawConfigService;
    @Autowired
    IPushMessageService pushMessageService;
    @Autowired
    IAccountService accountService;
    @Autowired
    PushService pushService;
    @Autowired
    WithdrawCloseSmsNoticeService withdrawCloseSmsNoticeService;
    @Autowired
    OspOrganizationService ospOrganizationService;
    @Autowired
    private OspTaskService ospTaskService;
    @Autowired
    private SupportService supportService;
    @Autowired
    private BusinessLogService businessLogService;
    @Autowired
    private WithdrawNoticeLogService withdrawNoticeLogService;
    @Autowired
    private NoticeRuleService noticeRuleService;
    @Autowired
    private WalletService walletService;
    @Autowired
    private OssFileUploader ossFileUploader;
    @Autowired
    private MerchantWithdrawConfigService merchantWithdrawConfigService;
    @Autowired
    private IRiskQueryService riskQueryService;
    @Autowired
    private D1WithdrawBatchService d1WithdrawBatchService;
    @Autowired
    protected ASTHelper astHelper;
    @Autowired
    private OspUserLoginService loginService;
    @Autowired
    private ClearanceService clearanceService;
    @Autowired
    private WithdrawNotice withdrawNotice;
    @Autowired
    private MerchantLimitAuthorizationService merchantLimitAuthorizationService;
    @Autowired
    private CompensationService compensationService;

    private static ExecutorService pushExecutors = Executors.newFixedThreadPool(10);

    private static final Logger logger = LoggerFactory.getLogger(OspWithdrawServiceImpl.class);

    private WithdrawService withdrawService; //backend upay项目提供的接口
    private NoticeService noticeService;     //backend upay项目提供的接口
    private String shouqianbaServer;
    private static final String SHOUQIANBA_COOKIE = "shouqianbaCookie"; //调用收钱吧某些接口时，需要登录

    private static final String SESSION_IMPORT_MERCHANT_DRAW_REALTIME = "importDrawRealTime_task";
    private static final String SESSION_IMPORT_MERCHANT_FORCE_CLEAR = "importForceClear_task";

    private static final String BASE_DIR = "osp/withdraw/";
    private static final String ENDPOINT_URL = "http://wosai-statics.oss-cn-hangzhou.aliyuncs.com";

    public static final int MAX_LOG_THREAD_COUNT = 2; //最多同时有多少个线程执行
    public static final int MAX_SMS_THREAD_COUNT = 2;

    public static final String SMS_STATUS = "sms_status";
    private ThreadPoolExecutor withdrawExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(MAX_LOG_THREAD_COUNT + MAX_SMS_THREAD_COUNT);
    private ThreadPoolExecutor importExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(2);

    public static ThreadPoolExecutor noticeExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(10);

    private static ThreadPoolExecutor withdrawExportExecutor =(ThreadPoolExecutor) Executors.newFixedThreadPool(1);


    private static final String[] COLUMN_NAME = new String[]{
            MerchantD1WithdrawBatch.MERCHANT_NO,
            MerchantD1WithdrawBatch.REMARK,
            MerchantD1WithdrawBatch.BATCH_NAME
    };

    private static final int  MERCHANT_CREATE_TIME_NOT_REACH_STANDARD = 10;

    public static final int RISK_MERCHANT =11;

    /**
     * D0灰度appId
     */
    private static final String APP_GATE_WITHDRAW_REALTIME_APPID = "9";
    /**
     * D0灰度ruleId
     */
    private static final String APP_GATE_WITHDRAW_REALTIME_RULEID = "20170327";


    public OspWithdrawServiceImpl(String backendUpayUrl, String shouqianbaServer) {
        this.shouqianbaServer = shouqianbaServer;
        JsonRpcHttpClient clientWithdraw = null;
        JsonRpcHttpClient clientNotice = null;
        try {
            clientWithdraw = new JsonRpcHttpClient(new URL(backendUpayUrl + "rpc/withdraw"));
            clientNotice = new JsonRpcHttpClient(new URL(backendUpayUrl + "rpc/notice"));
        } catch (MalformedURLException e) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        this.withdrawService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                WithdrawService.class,
                clientWithdraw);
        this.noticeService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                NoticeService.class,
                clientNotice);
    }

    @Override
    public ListResult getWithDrawList(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        if (request.containsKey("clearance_priority") && request.get("clearance_priority") != null) {
            long dateEnd = BeanUtil.getPropLong(request, "date_end");
            long dateStart = BeanUtil.getPropLong(request, "date_start");
            if (dateEnd - dateStart > DateTimeUtil.ONE_DAY_MILLIS * 3) {
                throw new BizException("查询打款批次时，时间跨度不可超过三天");
            }
        }
        if (request.containsKey("bank_name") && (request.get("bank_name") != null && !StringUtil.empty(String.valueOf(request.get("bank_name"))))) {
            long dateEnd = BeanUtil.getPropLong(request, "date_end");
            long dateStart = BeanUtil.getPropLong(request, "date_start");
            if (dateEnd - dateStart > DateTimeUtil.ONE_DAY_MILLIS * 3) {
                throw new BizException("查询银行名称，时间跨度不可超过三天");
            }
        }
        if (request.containsKey("ne_remark") && (request.get("ne_remark") != null && !StringUtil.empty(String.valueOf(request.get("ne_remark"))))) {
            long dateEnd = BeanUtil.getPropLong(request, "date_end");
            long dateStart = BeanUtil.getPropLong(request, "date_start");
            if (dateEnd - dateStart > DateTimeUtil.ONE_DAY_MILLIS * 3) {
                throw new BizException("查询备注，时间跨度不可超过三天");
            }
        }
        boolean isHistory = BeanUtil.getPropBoolean(request,"history");
        ListResult result;
        if(isHistory){
            request.remove("date_end");
            request.remove("date_start");
            request.remove("page");
            request.remove("page_size");
            request.remove("provider");
            request.remove("history");
            for (Object key:request.keySet()){
                if(!key.equals("merchant_sn")&&!key.equals("provider_mch_id")){
                    throw new IllegalArgumentException("历史数据不支持此查询选项");
                }
            }
            result = withdrawRealTimeService.findWithdrawsForHistory(pageInfo,request);
        }else{
            result = withdrawRealTimeService.findWithdraws(pageInfo, request);
        }
        Map batchPriorityNameMap = d1WithdrawBatchService.getBatchPriorityNameMap();

        if (CollectionUtils.isEmpty(result.getRecords()))
            return result;
        for (Map bactch : result.getRecords()) {
            bactch.put("history", isHistory);
            String clearancePriority = BeanUtil.getPropString(bactch, "clearance_priority");
            if (clearancePriority == null)
                continue;
            String batchName = BeanUtil.getPropString(batchPriorityNameMap, clearancePriority);
            bactch.put("clearance_priority", batchName);
        }

        return result;
    }

    private Map changeWithDrawStatus_old(String withdrawId, String username, String failedReason, long status) {
        if (status == 5) {
            Map result = (Map) this.delegateToShouqianba("Osp/lakalaReClearing/", CollectionUtil.hashMap(
                    "withDrawId", withdrawId,
                    "operator", username,
                    "remark", failedReason
            ));
            return result;
        }
        return null;
    }

    @Override
    public String changeWithDrawStatus(Map request) {
        String withdrawId = BeanUtil.getPropString(request, "withDrawId");
        long status = BeanUtil.getPropInt(request, "opCheckStatus");
        String failedReason = BeanUtil.getPropString(request, "failedReason");
        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        String username = BeanUtil.getPropString(user, Account.USERNAME);
        //检验更改状态是否符合时序
        this.drawTransferCheck(withdrawId, status);

        if (status == 5) {
            Map withdraw = withdrawRealTimeService.getWithdraw(withdrawId);
            String afterChangeStatus = "";
            if (BeanUtil.getPropInt(withdraw, "version", 0) >= 1000) {
                afterChangeStatus = withdrawRealTimeService.transfer(withdrawId, failedReason, username);
            } else {
                afterChangeStatus = BeanUtil.getPropString(changeWithDrawStatus_old(withdrawId, username, failedReason, status), "opCheckStatus", "");
                withdrawRealTimeService.updateWithdrawRemark(withdrawId, failedReason, username);
            }
            return afterChangeStatus;
        }
        //更改状态
        changeWithDrawOpCheckStatus(status, withdrawId, failedReason, username);
        return status + "";
    }

    @Override
    public void asyncBatchTransferForD1(List<Map> data) {
        if (data == null || data.size() == 0) {
            return;
        }
        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        String username = BeanUtil.getPropString(user, Account.USERNAME);
        List<Map<String, Object>> batchTransfer = new ArrayList<>();
        for (Map map : data) {
            batchTransfer.add(CollectionUtil.hashMap(
                    "withdraw_id", BeanUtil.getPropString(map, "withDrawId"),
                    "remark", BeanUtil.getPropString(map, "failedReason"),
                    "operator", username
            ));
        }
        withdrawRealTimeService.asyncBatchTransfer(batchTransfer);
    }

    private void changeWithDrawOpCheckStatus(long opCheckStatus, String withdrawId, String remark, String operator) {
        //提现审核中
        if (opCheckStatus == 1) {
            withdrawRealTimeService.withdrawOperatorReview(withdrawId, remark, operator);
        }
        //提现驳回
        else if (opCheckStatus == 2) {
            withdrawRealTimeService.withdrawOperatorBack(withdrawId, remark, operator);
        }
        //运营冻结资金
        else if (opCheckStatus == 3) {
            withdrawRealTimeService.withdrawOperatorFreeze(withdrawId, remark, operator);
        }
        //运营通过
        else if (opCheckStatus == 4) {
            withdrawRealTimeService.withdrawOperatorPass(withdrawId, remark, operator);
        }
        //财务通过
        else if (opCheckStatus == 5) {
            withdrawRealTimeService.withdrawFinancierPass(withdrawId, remark, operator);
        }
        //运营手动修改为打款成功
        else if (opCheckStatus == 6) {
            withdrawRealTimeService.withdrawOperatorTransferSuccess(withdrawId, remark, operator);
        }
        //手工修改为打款失败
        else if (opCheckStatus == 7) {
            withdrawRealTimeService.withdrawOperatorTransferFailed(withdrawId, remark, operator);
        }
    }

    @Override
    public Map updateWithdrawRemark(Map request) {
        String withdrawId = BeanUtil.getPropString(request, "withDrawId");
        String remark = BeanUtil.getPropString(request, "remark");
        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        withdrawRealTimeService.updateWithdrawRemark(withdrawId, remark, BeanUtil.getPropString(user, Account.USERNAME));
        return withdrawRealTimeService.getWithdraw(withdrawId);
    }


    @Override
    public Map exportWithdrawList(Map requestParams, HttpServletResponse response) {
        if(withdrawExportExecutor.getActiveCount() == 1){
            Map<String,Object> returnResult = new HashMap<>();
            returnResult.put("success",false);
            returnResult.put("errorMsg","前一次导出任务还在执行,请稍后再导出");
            return returnResult;
        }
        requestParams = (requestParams == null) ? new HashMap<>():requestParams;
        boolean history = BeanUtil.getPropBoolean(requestParams, "history", false);
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(requestParams);
        boolean isEncrypt = BeanUtil.getPropBoolean(requestParams, "isEncrypt", true);
        if(history){
            requestParams.remove("date_end");
            requestParams.remove("date_start");
            requestParams.remove("page");
            requestParams.remove("page_size");
            requestParams.remove("provider");
            requestParams.remove("history");
            requestParams.remove("isEncrypt");
            for (Object key:requestParams.keySet()){
                if(!key.equals("merchant_sn")&&!key.equals("provider_mch_id")){
                    throw new IllegalArgumentException("历史数据不支持此选项");
                }
            }
            if(requestParams.size() == 0){
                throw new IllegalArgumentException("请输入商户号或拉卡拉商户号");
            }
        }
        final Map<String,Object> params = requestParams;
        final Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_WITHDRAW_LIST_DOWNLOAD
        ));
        Map<String, Object> userInfo = loginService.getUserInfo(new HashMap<>());
        withdrawExportExecutor.submit(new Runnable() {
            @Override
            public void run() {
                logger.info("数据导出开始");
                Long startTime = System.currentTimeMillis();
                List<Map> records = new ArrayList<>();
                //提现记录10000条分5次请求
                if(history){
                    for (int i = 0; i < 20;i++) {
                        pageInfo.setPage(i + 1);
                        pageInfo.setPageSize(100);
                        ListResult result = withdrawRealTimeService.findWithdrawsForHistory(pageInfo, params);
                        records.addAll(result.getRecords());
                    }
                }else{
                    for (int i = 0; i < 5;i++){
                        pageInfo.setPage(i+1);
                        pageInfo.setPageSize(2000);
                        ListResult result = withdrawRealTimeService.findWithdraws(pageInfo, params);
                        records.addAll(result.getRecords());
                    }
                }
                logger.info("接口耗时:"+(System.currentTimeMillis()-startTime));
                Set<String> merchantIds = new HashSet<>();
                for (Map record : records) {
                    String merchantId = MapUtils.getString(record, Withdraw.MERCHANT_ID);
                    merchantIds.add(merchantId);
                }
                logger.info("处理结果:");
                Map<String, Object> queryFilter = new HashMap();
                queryFilter.put("merchant_ids", merchantIds);
                ListResult merchants = merchantService.findMerchants(new PageInfo(1, merchantIds.size()), queryFilter);
                List<Map> merchantRecords = merchants.getRecords();
                Map<String, Map> merchant = CommonUtil.convert(merchantRecords, ConstantUtil.KEY_ID);
                for (Map record : records) {
                    String merchantId = MapUtils.getString(record, Withdraw.MERCHANT_ID);
                    String merchantSn = MapUtils.getString(MapUtils.getMap(merchant, merchantId), Merchant.SN);
                    record.put("merchant_sn", merchantSn);
                }
                logger.info("获取商户信息");
                logger.info("记录获取成功");
                HSSFWorkbook workbook = null;
                try {
                    workbook = ExcelCreator.exportWithdrawsExcel(records, isEncrypt);
                } catch (Exception e) {
                    logger.error("excel写入错误:"+e.getMessage(),e);
                    Map<String,Object> exportWithdrawResult = new HashMap<>();
                    exportWithdrawResult.put("结果","失败");
                    String taskId = BeanUtil.getPropString(task , DaoConstants.ID);
                    ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                            TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                            TaskApplyLog.APPLY_RESULT, exportWithdrawResult));
                    return;
                }
                String fileExt = "xls";
                try{
                    String fullName = uploadStatementToOSS(fileExt,workbook);
                    logger.info("获取的文件全名为:"+fullName);
                    String taskId = BeanUtil.getPropString(task , DaoConstants.ID);
                    Map<String,Object> exportWithdrawResult = new HashMap<>();
                    exportWithdrawResult.put("downloadResultUrl",fullName);
                    logger.info("taskId:"+taskId);
                    Map<String,Object> taskResult = ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                            TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                            TaskApplyLog.APPLY_RESULT, exportWithdrawResult));
                    logger.info("更新后任务结果:"+taskResult);
                    logger.info("整体耗时:"+(System.currentTimeMillis()-startTime));
                }catch (Exception ex){
                    logger.error("uploadStatementToOSS error is:",ex);
                    Map<String,Object> exportWithdrawResult = new HashMap<>();
                    exportWithdrawResult.put("结果","失败");
                    String taskId = BeanUtil.getPropString(task , DaoConstants.ID);
                    ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                            TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                            TaskApplyLog.APPLY_RESULT, exportWithdrawResult));
                }
            }
        });
        Map<String,Object> returnResult = ResponseHelper.getResponse(ResponseHelper.SUCCESS_CODE,ResponseHelper.SUCCESS_MSG);
        Map<String,Object> data = new HashMap<>();
        data.put("success",true);
        returnResult = ResponseHelper.addRecord(returnResult, data);
        return returnResult;
    }

    @Override
    public List batchChangeWithDrawStatus(Map request) {
        List<Map> batchTransferD1New = new ArrayList<>();
        List<Map> result = new LinkedList<Map>();
        List<Map> widthdraws = (List<Map>) request.get("data");
        for (Map widthdraw : widthdraws) {
            String withdrawId = BeanUtil.getPropString(widthdraw, "withDrawId");
            try {
                long opCheckStatus = BeanUtil.getPropLong(widthdraw, "opCheckStatus");
                Map oldWithdra = withdrawRealTimeService.getWithdraw(withdrawId);
                //非D0提现  不允许失败到提交打款操作
                if (!(BeanUtil.getPropInt(oldWithdra, "withdraw_mode") == 1 && BeanUtil.getPropInt(oldWithdra, "type") == 1)
                        && BeanUtil.getPropLong(oldWithdra, "op_check_status") == 7
                        && opCheckStatus == 5 && BeanUtil.getPropInt(oldWithdra, Withdraw.PROVIDER) == Withdraw.PROVIDER_LAKALA) {
                    throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "非D0拉卡拉提现,打款失败状态不允许提交打款操作");
                }
                //前端设置opCheckStatus为5 为重新发起打款操作 通过reTransfer标志区分重新打款和设置为等待打款结果
                if (BeanUtil.getPropLong(oldWithdra, "op_check_status") == 9 && opCheckStatus == 5) {
                    if(BeanUtil.getPropBoolean(widthdraw,"reTransfer",true)){
                        widthdraw.put("opCheckStatus", 7);
                        changeWithDrawStatus(widthdraw);
                    }else{
                        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
                        String username = BeanUtil.getPropString(user, Account.USERNAME);
                        withdrawRealTimeService.withdrawFinancierPass(withdrawId, "", username);
                        continue;
                    }
                }

                widthdraw.put("opCheckStatus", opCheckStatus);
                if (!(BeanUtil.getPropInt(oldWithdra, "withdraw_mode") == 1 && BeanUtil.getPropInt(oldWithdra, "type") == 1)
                        && opCheckStatus == 5 && BeanUtil.getPropInt(oldWithdra, "version", 0) >= 1000) {
                    if(BeanUtil.getPropBoolean(widthdraw,"reTransfer",true)){
                        batchTransferD1New.add(widthdraw);
                    }
                } else {
                    String data = this.changeWithDrawStatus(widthdraw);
                    if(opCheckStatus == 7) {
                        //手动设置为打款失败 进行推送通知
                        pushExecutors.submit(new Runnable() {
                            @Override
                            public void run() {
                                logger.info("withdrawId:"+withdrawId);
                                withdrawNotice.pushWithdrawNotice(withdrawId);
                            }
                        });
                    }
                    result.add(CollectionUtil.hashMap(
                            "withDrawId", withdrawId,
                            "code", UpayException.CODE_SUCCESS,
                            "msg", "succ",
                            "data", data
                    ));
                }
            } catch (Exception e) {
                result.add(CollectionUtil.hashMap(
                        "withDrawId", withdrawId,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                ));
            }
        }

        try {
            asyncBatchTransferForD1(batchTransferD1New);
        } catch (Exception e) {
            logger.error("批量d1打款异常request[{}], batchTransfer[{}]", request, batchTransferD1New, e);
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, e.getMessage());
        }
        return result;
    }

    private Map withdrawVerifyOfLakala_old(Map request) {
        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        request.put("operator", BeanUtil.getPropString(user, Account.USERNAME));
        Map result = (Map) this.delegateToShouqianba("Osp/withdrawVerifyOfLakala/", request);
        return result;
    }

    @Override
    public String withdrawVerifyOfLakala(Map request) {
        String withdrawId = BeanUtil.getPropString(request, "withDrawId");
        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        String username = BeanUtil.getPropString(user, Account.USERNAME);
        if (StringUtil.empty(withdrawId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "withDrawId不可为空!");
        }
        Map withdraw = withdrawRealTimeService.getWithdraw(withdrawId);
        int opCheckStatus = MapUtils.getIntValue(withdraw, "op_check_status");
        List<Integer> noNeed = Arrays.asList(5, 7, 9);
        if (!(noNeed.contains(opCheckStatus))) {
            return null;
        }
        if (BeanUtil.getPropInt(withdraw, "version", 0) >= 1000) {
            return withdrawRealTimeService.updateTransferStatusByQueryProvider(withdrawId, "", username);
        }
        return BeanUtil.getPropString(withdrawVerifyOfLakala_old(request), "opCheckStatus", "");
    }

    @Override
    public List<Map> batchWithdrawVerifyOfLakala(Map request) {
        List<String> ids = (ArrayList<String>) request.get("ids");
        List<Map> result = new LinkedList<>();
        try {
            Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
            String username = BeanUtil.getPropString(user, Account.USERNAME);
            logger.info("batchWithdrawVerifyOfLakala username:{}", username);
        }catch (Exception ex){
            logger.info("batchWithdrawVerifyOfLakala:{}",Arrays.asList(ex.getStackTrace()));
        }
        for (String id : ids) {
            try {
                String data = clearanceService.updateTransferStatusByQueryProviderForSp(id);
                result.add(CollectionUtil.hashMap(
                        "withDrawId", id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", data
                ));
            } catch (Exception e) {
                result.add(CollectionUtil.hashMap(
                        "withDrawId", id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage()
                ));
            }
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getWithdrawOperationLog(Map<String, Object> request) {
        String withDrawId = BeanUtil.getPropString(request, "withDrawId");
        if (StringUtil.empty(withDrawId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "withDrawId参数必传!");
        }
        if (!request.containsKey("order_by")) {
            List orderBy = new LinkedList();
            orderBy.add(CollectionUtil.hashMap(
                    "field", ConstantUtil.KEY_CTIME,
                    "order", OrderBy.OrderType.DESC
            ));
            request.put("order_by", orderBy);
        }
        List<Map<String, Object>> results = clearanceService.getWithdrawLogs(withDrawId, request);
        if (results != null && results.size() > 0) {
            for (Map withdrawLog : results) {
                List<Map> pushWays = withdrawNoticeLogService.getWithdrawNoticeLogsByWithdrawLogId(BeanUtil.getPropString(withdrawLog, DaoConstants.ID, ""));
                String pushWay = "";
                if (pushWays != null && pushWays.size() > 0) {
                    pushWay = "推送";
                    for (Map map : pushWays) {
                        if (BeanUtil.getPropInt(map, WithdrawNoticeLog.PUSH_TYPE) == WithdrawNoticeLog.PUSH_TYPE_SMS) {
                            pushWay = "短信 +" + pushWay;
                            break;
                        }
                    }
                }
                withdrawLog.put("push_way", pushWay);
            }
        }
        return results;
    }


    @Override
    public Map batchChangeCompensationStatus(Map<String, Object> request) {
        Map<String, Object> failure = new HashMap<>();
        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        String operator = MapUtils.getString(user, Account.USERNAME);
        final String remark = BeanUtil.getPropString(request, "remark");
        int op_type = BeanUtil.getPropInt(request, "op_type", -1);
        if (op_type != 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "只能进行D0赔付操作");
        }
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        final int inOrDecreaseNum = BeanUtil.getPropInt(request, "inOrDecreaseNum");

        List<String> ids = CommonUtil.convertToList(request.get("records"));
        List<String> merchant_ids = CommonUtil.convertToList(request.get("merchant_ids"));
        List<String> pushModelList = CommonUtil.convertToList(request.get(PushTask.PUSH_MODEL_LIST));

        if (CollectionUtils.isEmpty(ids) || CollectionUtils.isEmpty(merchant_ids) || merchant_ids.size() != ids.size()) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "提现记录id和商户id集合参数不匹配");
        }
        //创建任务

        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_COMPENSATION
        ));
        final String task_apply_log_id = BeanUtil.getPropString(task, DaoConstants.ID);

        List<String> success = new ArrayList<>();
        List<String> successWithdrawIds = new ArrayList<>();
        List<Map> failureDetail = new ArrayList<>();
        for (int i = 0; i < ids.size(); i++) {
            String id = ids.get(i);
            try {
                request.put("id", id);
                changeCompensationStatusForD0FreeCount(op_type, id, remark, operator);
                success.add(merchant_ids.get(i));
                successWithdrawIds.add(id);
            } catch (Exception e) {
                failureDetail.add(CollectionUtil.hashMap(
                        "withDrawId", id,
                        "msg", e.getMessage()
                ));
            }
        }

        logger.info("超时赔付详情,request[{}],result[{}]", request, failureDetail);
        //记录赔付日志
        List failureDetailTask = failureDetail;
        if (failureDetailTask.size() > 20) {
            failureDetailTask = failureDetail.subList(0, 20);
            failureDetailTask.add(CollectionUtil.hashMap(
                    "withDrawId", "...",
                    "data", "错误数过多不全部显示"
            ));
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap(
                        "超时赔付总数", ids.size(),
                        "超时赔付成功总数", success.size(),
                        "超时赔付失败总数", failureDetail.size(),
                        "超时赔付失败详情", failureDetailTask
                )
        ));
        if (success.size() > 0) {
            final List success_copy = success;
            final Map httpSession = HttpRequestUtil.getBusinessogRequest();
            noticeExecutor.submit(new Runnable() {
                @Override
                public void run() {
                    compensationAndD0FreeCount(success_copy, successWithdrawIds, inOrDecreaseNum, remark, task_apply_log_id, httpSession, pushModelList);
                }
            });
        }

        return CollectionUtil.hashMap("taskApplyLogId", task_apply_log_id, "task_apply_log_id", task_apply_log_id);
    }

    @Override
    public Map getCompensationAmount(Map<String, Object> request) {
        String withdrawId = BeanUtil.getPropString(request, "withdraw_id");
        return compensationService.calculateCompensationInfo(withdrawId);
    }

    @Override
    public Map d1CompensationForMoney(Map<String, Object> request) {
        String withdrawId = BeanUtil.getPropString(request, "withdraw_id");
        List<String> pushModelList = CommonUtil.convertToList(request.get(PushTask.PUSH_MODEL_LIST));
        Map<String, Object> compensationRequest = new HashMap<>();
        compensationRequest.put(CompensationConstant.WITHDRAW_ID, withdrawId);
        Map<String, Object> pushData = new HashMap<>();
        pushData.put(PushTask.PUSH_WITHDRAW_ID_LIST, Collections.singletonList(withdrawId));
        pushData.put(PushTask.PUSH_CONFIG_KEY, PushTask.PUSH_CONFIG_D1_COMPENSATION);
        pushData.put(PushTask.PUSH_MODEL_LIST, pushModelList);
        compensationRequest.put(CompensationConstant.COMPENSATION_PUSH_DATA, pushData);
        Map<String, Object> platform = new HashMap<>();
        platform.put("remark",BeanUtil.getPropString(request,"remark"));
        platform.put("platform", "SP");
        platform.put("operator", HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID));
        compensationService.launchCompensationApply(compensationRequest, platform);
        return null;
    }

    /**
     * 赠送免费次数并发送通知
     *
     * @param merchantIds
     * @param inOrDecreaseNum
     * @param remark
     * @param task_apply_log_id
     */
    private void compensationAndD0FreeCount(List<String> merchantIds,List<String> withdrawIds, int inOrDecreaseNum, String remark, String task_apply_log_id, Map businessRequest,List<String> pushModelList) {
        List<Map> result = batchUpdateDrawRealTimeFreeCountWithIds(merchantIds, inOrDecreaseNum, remark, task_apply_log_id, businessRequest);
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<String> successWithdrawIds = new ArrayList<>();
        if (result.size() > 0) {
            for (int i = 0; i < result.size(); i++) {
                Map map = result.get(i);
                if (BeanUtil.getPropInt(map, "code") == UpayException.CODE_SUCCESS) {
                    successWithdrawIds.add(withdrawIds.get(i));
                }
            }
        }
        //推送
        Map<String, Object> extraPushData = new HashMap<>();
        extraPushData.put(PushTask.COMPENSATE_D0_NUM, inOrDecreaseNum);
        pushService.pushTaskForSpAsync(CollectionUtil.hashMap(
                PushTask.PUSH_WITHDRAW_ID_LIST, successWithdrawIds,
                PushTask.TASK_APPLY_LOG_ID, task_apply_log_id,
                PushTask.PUSH_CONFIG_KEY, PushTask.PUSH_CONFIG_D0_COMPENSATION,
                PushTask.PUSH_MODEL_LIST, pushModelList,
                PushTask.PUSH_WITHDRAW_EX_DATA,extraPushData
        ));
    }

    /**
     * 发送到收钱吧api去执行
     * 比如提现相关接口
     *
     * @param path
     * @param request
     * @return
     */

    private Object delegateToShouqianba(String path, Map request) {
        RestTemplate restTemplate = restTemplateUtil.getRestTemplate();
        ObjectMapper om = new ObjectMapper();
        String shouqianba = (String) HttpRequestUtil.getSession().getAttribute("shouqianbaCookie");
        if (StringUtil.empty(shouqianba)) {
            loginToShouqianbaAndSaveCookieToSession();
        }
        shouqianba = (String) HttpRequestUtil.getSession().getAttribute("shouqianbaCookie");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("cookie", shouqianba);
        ResponseEntity<Map> response = this.postForm(restTemplate, this.shouqianbaServer + path, request, httpHeaders, Map.class);
        try {
            logger.info("DelegateToShouqianba: path is " + path + " request is: " + om.writeValueAsString(request) + "  response is: " + om.writeValueAsString(response));
        } catch (Exception e) {
            //do nothing
        }
        if (response.getStatusCode() == HttpStatus.OK &&
                response.getBody() != null && BeanUtil.getPropLong(response.getBody(), "code") == 10000) {
            return response.getBody().get("data");
        } else {
            if (response.getBody() != null && BeanUtil.getPropLong(response.getBody(), "code") == 11000) {
                HttpRequestUtil.getSession().removeAttribute(SHOUQIANBA_COOKIE);
            }
            String errMsg = BeanUtil.getPropString(response.getBody(), "msg");
            int errCode;
            if (StringUtil.empty(BeanUtil.getPropString(response.getBody(), "code"))) {
                errCode = UpayException.CODE_UNKNOWN_ERROR;
            } else {
                errCode = BeanUtil.getPropInt(response.getBody(), "code");
            }
            throw new UpayException(errCode, errMsg);
        }
    }


    private void changeCompensationStatusForD0FreeCount(int op_type, String withdrawId, String remark, String operator) {
        if (op_type == 1) {
            withdrawRealTimeService.withdrawCompensateFinished(withdrawId, remark, "admin");
            withdrawRealTimeService.updateWithdrawCompensateRemark(withdrawId, remark, operator);
        }
    }

    private void changeCompensationStatusForMoney(int op_type, String withdrawId, String remark, String operator, long compensation_amount) {
        if (op_type < 0 || op_type > 2) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "赔付操作不存在");
        }
        if (StringUtil.empty(withdrawId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "提现id不可为空!");
        }
        //改为超时不赔付状态
        if (op_type == 0) {
            withdrawRealTimeService.withdrawDelayedNotCompensate(withdrawId, remark, operator);
        }
        //改为超时赔付状态
        else if (op_type == 1) {
            if (compensation_amount < 100 || compensation_amount > 10000) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "赔付金额在1~100元之间!");
            }
            withdrawRealTimeService.withdrawDelayedCompensate(withdrawId, remark, operator, compensation_amount);
        }
        //改为赔付完成状态
        else if (op_type == 2) {
            withdrawRealTimeService.withdrawCompensateFinished(withdrawId, remark, operator);
        }
    }

    public static double getMoneyYuan(long money) {
        BigDecimal bMoney = new BigDecimal(money).divide(new BigDecimal(100));
        return bMoney.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 提取校验
     * 按打款时序流程来
     * 逻辑参考http://wiki.wosai-inc.com/pages/viewpage.action?pageId=2820229 时序图
     *
     * @param withdrawId
     */
    private Boolean drawTransferCheck(String withdrawId, long opCheckStatus) {
        //提现审核中 1, 提现驳回 2 ，运营冻结资金 3 ，运营通过 4，财务通过 5 ，运营手动修改为打款成功 6 //运营手动修改为打款成功, 7  //手工修改为打款失败
        if (StringUtil.empty(withdrawId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "打款纪录不存在");
        }
        Map drawRecord = withdrawRealTimeService.getWithdraw(withdrawId);
        if (drawRecord == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "打款纪录：" + withdrawId + "，不存在");
        }
        int withDrawMode = BeanUtil.getPropInt(drawRecord, "withdraw_mode");
        int type = BeanUtil.getPropInt(drawRecord, "type");

        //只允许d1（非d0） 驳回，withdrwa_modes!=1
        //        mode type
        //标准提现  1  0
        //智能提现  2  0
        //D0 提现  1  1
        if (opCheckStatus == 2 && (withDrawMode == 1 && type == 1)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "只允许D1进行驳回");
        }

        boolean legal = false;
        int oldStatus = BeanUtil.getPropInt(drawRecord, "op_check_status");
        int provider = BeanUtil.getPropInt(drawRecord, "provider");
        //拉卡拉
        if (provider == 2) {
            switch (oldStatus) {
                case 1:
                    legal = (opCheckStatus == 2 || opCheckStatus == 3 || opCheckStatus == 4);
                    break;
                case 3:
                    legal = (opCheckStatus == 1 || opCheckStatus == 2);
                    break;
                case 4:
                    legal = (opCheckStatus == 3 || opCheckStatus == 5 || opCheckStatus == 2);
                    break;
                case 5:
                    legal = (opCheckStatus == 6 || opCheckStatus == 7 || opCheckStatus == 8 || opCheckStatus == 9 || opCheckStatus == 5);
                    break;
                case 6:
                    legal = (opCheckStatus == 7);
                    break;
                case 7:
                    legal = (opCheckStatus == 5 || opCheckStatus == 6 || opCheckStatus == 8);
                    break;
                case 9:
                    legal = (opCheckStatus == 5 || opCheckStatus == 6 || opCheckStatus == 7);
                    break;
                case 10:
                    legal = (opCheckStatus == 6 || opCheckStatus == 7 || opCheckStatus == 8);
                default:
                    break;
            }
        }else if(provider==4){
            switch (oldStatus) {
                case 1:
                    legal = (opCheckStatus == 2 || opCheckStatus == 3 || opCheckStatus == 4);
                    break;
                case 3:
                    legal = (opCheckStatus == 1 || opCheckStatus == 2);
                    break;
                case 4:
                    legal = (opCheckStatus == 3 || opCheckStatus == 5 || opCheckStatus == 2);
                    break;
                case 5:
                    legal = (opCheckStatus == 6 || opCheckStatus == 7 || opCheckStatus == 8 || opCheckStatus == 9 || opCheckStatus == 5);
                    break;
                case 6:
                    legal = (opCheckStatus == 7);
                    break;
                case 7:
                    legal = (opCheckStatus == 5 || opCheckStatus == 6 || opCheckStatus == 8);
                    break;
                case 9:
                    legal = (opCheckStatus == 5 || opCheckStatus == 6 || opCheckStatus == 7);
                    break;
                default:
                    break;
            }
        }
        if (!legal) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "提现状态修改不合法");
        }
        return legal;
    }

    private static <T> ResponseEntity<T> postForm(RestTemplate template, String url, Map<String, String> params, Map<String, List<String>> extraHeaders, Class<T> clazz) {
        MultiValueMap<String, String> restParams = new LinkedMultiValueMap<String, String>();
        for (Map.Entry<String, String> e : params.entrySet()) {
            restParams.add(e.getKey(), e.getValue());
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Collections.singletonList(MediaType.ALL));
        if (extraHeaders != null) {
            for (Map.Entry<String, List<String>> header : extraHeaders.entrySet()) {
                headers.put(header.getKey(), header.getValue());
            }
        }
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<MultiValueMap<String, String>>(restParams, headers);

        return template.postForEntity(url, request, clazz);
    }

    private void loginToShouqianbaAndSaveCookieToSession() {
        ObjectMapper objectMapper = new ObjectMapper();
        RestTemplate restTemplate = restTemplateUtil.getRestTemplate();
        String username = HttpRequestUtil.getSession().getAttribute("osp_username") + "";
        //login to shouqianba ,填写固定用户名密码，传递正式用户
        Map userInfo = CollectionUtil.hashMap(
                "realname", username,
                "username", "wosaioperationserviceplatform",
                "password", "d60aaad4fd5201d7c8350165dd00c8e3"
        );
        ResponseEntity<Map> response = this.postForm(restTemplate, this.shouqianbaServer + "Osp/login", userInfo, null, Map.class);
        if (response.getStatusCode() == HttpStatus.OK
                && !StringUtil.empty(BeanUtil.getPropString(response.getHeaders(), "set-cookie"))
                && BeanUtil.getPropLong(response.getBody(), "code") == 10000) {
            String cookieString = "";
            for (String cookieStr : response.getHeaders().get("set-cookie")) {
                HttpCookie cookie = HttpCookie.parse(cookieStr).get(0);
                cookieString = cookieString + cookie.getName() + "=" + cookie.getValue() + ";";
            }
            HttpRequestUtil.getSession().setAttribute(SHOUQIANBA_COOKIE, cookieString);
        } else {
            try {
                String errMsg = objectMapper.writeValueAsString(response.getBody());
                throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "调用收钱吧登录接口失败:"
                        + errMsg);
            } catch (JsonProcessingException e) {
                throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "调用收钱吧登录接口失败:" + "解析response body异常");
            }
        }
    }

    @Override
    public ListResult getWithDrawRealTimeList(Map<String, Object> request) {
        PageInfo pageInfo = pageInfo = PageInfoUtil.extractPageInfo(request);
        if (pageInfo == null) {
            pageInfo = new PageInfo(1, 10, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        }
        //增加推广者
        String organizationId = BeanUtil.getPropString(request, "organizationId");
        long total = 0;
        if (!StringUtil.empty(organizationId)) {
            Page<String> merchants = ospOrganizationService.findByUserAndOrganization(request);
            if (request.containsKey("organizationId")) {
                request.remove("organizationId");
            }
            //取第一页数据
            pageInfo.setPage(1);
            if (merchants != null && merchants.getData() != null && merchants.getData().size() > 0) {
                request.put("merchant_ids", merchants.getData());
                total = merchants.getTotalNumber();
            } else {
                return new ListResult(0, new ArrayList<Map>());
            }
        }
        //增加手机号、商户名查询
        String merchant_name = BeanUtil.getPropString(request, "merchant_name");
        String contact_cellphone = BeanUtil.getPropString(request, Merchant.CONTACT_CELLPHONE);
        if (!StringUtil.empty(merchant_name) || !StringUtil.empty(contact_cellphone)) {
            final ListResult merchants = merchantService.findMerchants(pageInfo, request);
            //取第一页数据
            pageInfo.setPage(1);
            //使用商户id集合去配置表查询配置
            if (merchants != null && merchants.getRecords() != null && merchants.getRecords().size() > 0) {
                final List<String> merchant_ids = new ArrayList<String>() {
                    {
                        for (Map map : merchants.getRecords()) {
                            add(BeanUtil.getPropString(map, DaoConstants.ID));
                        }
                    }
                };
                request.put("merchant_ids", merchant_ids);
                if (!StringUtil.empty(merchant_name)) {
                    request.remove("merchant_name");
                }
                if (!StringUtil.empty(contact_cellphone)) {
                    request.remove(Merchant.CONTACT_CELLPHONE);
                }
                total = merchants.getTotal();
            } else {
                return new ListResult(0, new ArrayList<Map>());
            }
        }
        //开户行查询
        String bank_name = BeanUtil.getPropString(request, MerchantBankAccount.BANK_NAME);
        if (!StringUtil.empty(bank_name)) {
            final ListResult merchant_banks = merchantService.findMerchantBankAccounts(pageInfo, request);
            //取第一页数据
            pageInfo.setPage(1);
            //使用商户id集合去配置表查询配置
            if (merchant_banks != null && merchant_banks.getRecords() != null && merchant_banks.getRecords().size() > 0) {
                final List<String> merchant_ids = new ArrayList<String>() {
                    {
                        for (Map map : merchant_banks.getRecords()) {
                            add(BeanUtil.getPropString(map, DaoConstants.ID));
                        }
                    }
                };
                request.put("merchant_ids", merchant_ids);
                if (!StringUtil.empty(bank_name)) {
                    request.remove(MerchantBankAccount.BANK_NAME);
                }
                total = merchant_banks.getTotal();
            } else {
                return new ListResult(0, new ArrayList<Map>());
            }
        }

        ListResult result = withdrawConfigService.findMerchantWithdrawrealtimeConfigs(pageInfo, request);
        if (result != null && result.getRecords() != null && result.getRecords().size() > 0) {
            List<String> mercahntIds = new ArrayList<>();
            for (Map map : result.getRecords()) {
                mercahntIds.add(BeanUtil.getPropString(map, MerchantWithdrawrealtimeConfig.MERCHANT_ID));
            }
            //补全渠道信息
            Map<String, UserBean> organinfos = ospOrganizationService.getUserByMerchantIds(CollectionUtil.hashMap("merchantIds", mercahntIds));
            //补全商户名
            final ListResult merchants = mercahntIds.size() == 0 ? new ListResult(0, new ArrayList<Map>()) : merchantService.findMerchants(new PageInfo(1, mercahntIds.size()), CollectionUtil.hashMap("merchant_ids", mercahntIds));
            final Map merchant_names = new HashMap() {
                {
                    for (Map map : merchants.getRecords()) {
                        put(BeanUtil.getPropString(map, DaoConstants.ID), BeanUtil.getPropString(map, Merchant.NAME));
                    }
                }
            };
            if (result != null && result.getRecords() != null && result.getRecords().size() > 0) {
                for (Map map : result.getRecords()) {
                    map.put("organization", BeanUtil.getProperty(organinfos, BeanUtil.getPropString(map, DaoConstants.ID)));
                    map.put("merchant_name", BeanUtil.getPropString(merchant_names, BeanUtil.getPropString(map, MerchantWithdrawrealtimeConfig.MERCHANT_ID)));
                }
            }
        }
        if (result != null && result.getTotal() < total) {
            result.setTotal(total);
        }
        return result;
    }

    @Override
    public Map batchImportDrawRealTime(MultipartFile file, final String noticeCode, final String noticeSubCode) {
        if (file == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "必须上传文件");
        }
        if (StringUtil.empty(noticeCode) || StringUtil.empty(noticeSubCode)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "noticeCode与noticeSubCode必传");
        }
        String fileName = file.getOriginalFilename();
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex == -1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        String type = fileName.substring(lastIndex + 1, fileName.length()).toLowerCase();
        if (!"xls".equals(type) && !"xlsx".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }

        InputStream is = null;
        Workbook workbook = null;
        try {
            is = file.getInputStream();
            if ("xls".equals(type)) {
                workbook = new HSSFWorkbook(is);
            } else if ("xlsx".equals(type)) {
                workbook = new XSSFWorkbook(is);
            }
        } catch (Exception e) {
            logger.error("excel格式解析不支持", e);
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel格式解析不支持");
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                }
            }
        }
        if (workbook == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel格式解析异常");
        }

        /**
         * 解析成list
         */
        final List<Map> merchantes_request = extraDataFromExcel(workbook);
        if (merchantes_request == null || merchantes_request.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "空excel没有商户数据");
        }
        if (merchantes_request.size() > 5000) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过5000条");
        }

        //创建任务
        Map taskApplyLog = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_IMPORT_D0
        ));

        final String taskId = BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        //异步执行导入操作
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                try {
                    batchImportDrawRealTimeTask(merchantes_request, taskId, noticeCode, noticeSubCode, businessRequest);
                } catch (Exception e) {
                    logger.error("importDrawRealTime() error", e);
                    ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                            TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                            TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "导入过程中发生错误")));
                }
            }
        };
        try {
            withdrawExecutor.submit(runnable);
        } catch (Exception e) {
            logger.error("submit importDrawRealTime() to threadPool error", e);
            ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                    TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "提交导入异步任务发生错误")));
        }

        HttpRequestUtil.getSession().setAttribute(SESSION_IMPORT_MERCHANT_DRAW_REALTIME, taskId);
        return CollectionUtil.hashMap("taskId", taskId, "task_apply_log_id", taskId);
    }


    @Override
    public Map importDrawRealTimeConfig(MultipartFile file) {
        List<String> titles = Arrays.asList(
                ConstantUtil.KEY_MERCHANT_SN,
                MerchantWithdrawrealtimeConfig.REMAINDER_NUMBER,
                Withdraw.REMARK,
                MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW,
                MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW,
                MerchantWithdrawrealtimeConfig.FEE_RATE,
                MerchantWithdrawrealtimeConfig.MIN_FEE,
                MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT

        );
        List<List<Map>> datas = ExcelUtil.getListFromExcelFile(file, titles);
        if (datas == null || datas.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }
        List<Map> data = datas.get(0);
        if (data == null || data.size() < 2) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }
        //去除标题
        data.remove(0);
        if (data.size() > 5000) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过5000条");
        }

        //创建任务
        Map taskApplyLog = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_CHANGE_MERCHANT_D0_CONFIG
        ));

        final String taskId = BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        final List dataCopy = data;

        //异步执行导入操作
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                try {
                    batchImportDrawRealTimeConfigTask(dataCopy, taskId, businessRequest);
                } catch (Exception e) {
                    logger.error("importDrawRealTime() error", e);
                    ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                            TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                            TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "导入过程中发生错误")));
                }
            }
        };

        try {
            withdrawExecutor.submit(runnable);
        } catch (Exception e) {
            logger.error("submit importDrawRealTimeConfig() to threadPool error", e);
            ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                    TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "提交导入异步任务发生错误")));
        }
        return CollectionUtil.hashMap("taskId", taskId, "task_apply_log_id", taskId);
    }

    /**
     * 导入逻辑
     * 1.去除不合法商户(根据条件判断:商户绑定银行卡为对私且在支持D0银行卡列表中)
     * 2.分批次调用灰度，去除灰度添加失败的商户(灰度单次上限500)
     * 3.增加商户D0免费次数
     * 4.将excel每条记录标记成功、失败原因上传阿里服务器
     *
     * @param merchantes
     */
    private Map batchImportDrawRealTimeConfigTask(List<Map> merchantes, String taskId, Map businessRequest) {

        int total = merchantes.size();
        //没有通过校验
        List<Map> merchants_unValid = new ArrayList<>();
        List<String> success_sns = new ArrayList<>();

        //失败商户号
        List<String> errorSns = new ArrayList<>();


        //校验
        for (int i = 0; i < merchantes.size(); i++) {
            //sn为空
            if (StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), ConstantUtil.KEY_MERCHANT_SN))) {
                merchantes.get(i).put("errmsg", "sn为空");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //remark为空
            if (StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), "remark")) || BeanUtil.getPropString(merchantes.get(i), "remark").length() > 200) {
                merchantes.get(i).put("errmsg", "备注为空或大于200个字符");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //excel中重复
            if (success_sns.contains(BeanUtil.getPropString(merchantes.get(i), ConstantUtil.KEY_MERCHANT_SN))) {
                merchantes.get(i).put("errmsg", "excel中重复");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //无操作
            if (BeanUtil.getPropInt(merchantes.get(i), MerchantWithdrawrealtimeConfig.REMAINDER_NUMBER, 0) == 0 &&
                    BeanUtil.getPropInt(merchantes.get(i), MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW, 0) == 0  &&
                    BeanUtil.getPropString(merchantes.get(i), MerchantWithdrawrealtimeConfig.FEE_RATE, "").equals("")&&
                    BeanUtil.getPropInt(merchantes.get(i), MerchantWithdrawrealtimeConfig.MIN_FEE, 0) == 0&&
                    BeanUtil.getPropInt(merchantes.get(i), MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT, 0) == 0&&
                    BeanUtil.getPropInt(merchantes.get(i), MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW, 0) == 0) {
                merchantes.get(i).put("errmsg", "无操作");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //通过校验
            success_sns.add(BeanUtil.getPropString(merchantes.get(i), ConstantUtil.KEY_MERCHANT_SN));
        }
        //商户信息补全
        final ListResult merchantInfos = success_sns.size() == 0 ? new ListResult(0, new ArrayList<Map>()) : withdrawConfigService.findMerchantWithdrawrealtimeConfigs(new PageInfo(1, success_sns.size()), CollectionUtil.hashMap("merchant_sns", success_sns));
        Map merchantInfosMap = new HashMap() {{
            for (Map map : merchantInfos.getRecords()) {
                put(BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_SN), map);
            }
        }};
        long now = new Date().getTime();
        for (int i = 0; i < merchantes.size(); i++) {
            String merchantSn = BeanUtil.getPropString(merchantes.get(i), ConstantUtil.KEY_MERCHANT_SN);
            Map merchantD0 = (Map) BeanUtil.getProperty(merchantInfosMap, merchantSn);
            final String merchantId = BeanUtil.getPropString(merchantD0, ConstantUtil.KEY_MERCHANT_ID);
            //商户不在D0白名单
            if (merchantD0 == null || merchantD0.isEmpty()) {
                merchantes.get(i).put("errmsg", "商户不在D0白名单");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //通知不存在
            String notice_code = BeanUtil.getPropString(merchantes.get(i), NoticeRule.CODE, "");
            String notice_sub_code = BeanUtil.getPropString(merchantes.get(i), NoticeRule.SUB_CODE, "");
            Map notice = (StringUtil.empty(notice_code) || StringUtil.empty(notice_sub_code)) ? null : noticeRuleService.getNoticeRuleByCodeAndSubCode(notice_code, notice_sub_code);
            if (!StringUtil.empty(notice_code) && notice == null) {
                merchantes.get(i).put("errmsg", "通知不存在");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            Map<String,Object> merchant = merchantes.get(i);

            String errmsg = "";
            String remark = BeanUtil.getPropString(merchant,Withdraw.REMARK);
            String id = BeanUtil.getPropString(merchantD0, DaoConstants.ID);

            Map<String,Object> update = new HashMap<>();
            int singleMinLimit = BeanUtil.getPropInt(merchantD0,MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW);
            int singleMaxLimit = BeanUtil.getPropInt(merchantD0,MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW);
            if (!StringUtil.empty(BeanUtil.getPropString(merchant, MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW))) {
                singleMinLimit = BeanUtil.getPropInt(merchant, MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW, 0);
                if (singleMinLimit <= 0) {
                    merchantes.get(i).put("errmsg", "每日最小提现金额大于0");
                    merchants_unValid.add(merchantes.get(i));
                    merchantes.remove(i);
                    i--;
                    continue;
                } else {
                    update.put(MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW, singleMinLimit);
                }
            }
            if (!StringUtil.empty(BeanUtil.getPropString(merchant, MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW))) {
                singleMaxLimit = BeanUtil.getPropInt(merchant, MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW, 0);
                if (singleMaxLimit <= 0) {
                    merchantes.get(i).put("errmsg", "每日最大提现金额大于0");
                    merchants_unValid.add(merchantes.get(i));
                    merchantes.remove(i);
                    i--;
                    continue;
                } else {
                    update.put(MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW, singleMaxLimit);
                }
            }

            if (singleMaxLimit < singleMinLimit) {
                merchantes.get(i).put("errmsg", "每日最大提现金额大于每日最小提现金额");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            if (!StringUtil.empty(BeanUtil.getPropString(merchant, MerchantWithdrawrealtimeConfig.FEE_RATE))) {
                String feeRate = BeanUtil.getPropString(merchant, MerchantWithdrawrealtimeConfig.FEE_RATE);
                double feeRateDouble = Double.parseDouble(feeRate);
                if (feeRateDouble < 0 || feeRateDouble > 1) {
                    merchantes.get(i).put("errmsg", "费率填写错误");
                    merchants_unValid.add(merchantes.get(i));
                    merchantes.remove(i);
                    i--;
                    continue;
                } else {
                    update.put(MerchantWithdrawrealtimeConfig.FEE_RATE, feeRate);
                }
            }
            if (!StringUtil.empty(BeanUtil.getPropString(merchant, MerchantWithdrawrealtimeConfig.MIN_FEE))) {
                int minFee = BeanUtil.getPropInt(merchant, MerchantWithdrawrealtimeConfig.MIN_FEE, 0);
                if (minFee <= 0) {
                    merchantes.get(i).put("errmsg", "每笔最少手续费大于0");
                    merchants_unValid.add(merchantes.get(i));
                    merchantes.remove(i);
                    i--;
                    continue;
                } else {
                    update.put(MerchantWithdrawrealtimeConfig.MIN_FEE, minFee);
                }
            }

            if (!StringUtil.empty(BeanUtil.getPropString(merchant, MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT))) {
                int dayLimitWithdrawCount = BeanUtil.getPropInt(merchant, MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT, 0);
                if (dayLimitWithdrawCount <= 0) {
                    merchantes.get(i).put("errmsg", "每天最少提现次数大于0");
                    merchants_unValid.add(merchantes.get(i));
                    merchantes.remove(i);
                    i--;
                    continue;
                } else {
                    update.put(MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT, dayLimitWithdrawCount);
                }
            }
            boolean ok = true;
            //增减免费次数
            int remainder_number = BeanUtil.getPropInt(merchantes.get(i), MerchantWithdrawrealtimeConfig.REMAINDER_NUMBER, 0);
            if (remainder_number != 0) {
                try {
                    if (remainder_number > 0) {
                        withdrawConfigService.increaseWithDrawRealtimeFreeCountById(id, remainder_number, remark);
                    } else {
                        withdrawConfigService.decreaseWithDrawRealtimeFreeCountById(id, Math.abs(remainder_number), remark);
                    }
                    errmsg = errmsg + " 修改免费次数成功 ";
                    if (notice != null) {
                        pushService.pushTaskAsynchronous(CollectionUtil.hashMap(
                                PushTask.MERCHANT_IDS, new ArrayList<String>() {{
                                    add(merchantId);
                                }},
                                PushTask.NOTICE_RULE, notice
                        ));
                        errmsg = errmsg + " 并推送 ";
                    }
                    Map merchantD0New = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchantId);
                    //记录业务日志
                    businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                            BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantD0,
                            BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantD0New,
                            BusinessLogUtil.LOG_PARAM_REQUEST, businessRequest,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                    BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                    BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_BATCH_MODIFY,
                                    BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_withdrawrealtime_config",
                                    BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                                    BizOpLog.OP_TIME, now,
                                    BizOpLog.BUSINESS_FUNCTION_CODE, "1000028",
                                    BizOpLog.REMARK, remark
                            )
                    ));
                } catch (Exception e) {
                    logger.error("excel批量导入修改D0免费次数失败, request[{}]", merchantes.get(i), e);
                    errmsg = errmsg + e.getMessage();
                    ok = false;
                }
            }
            if(update.keySet().size()>0){
                update.put(DaoConstants.ID,id);
                update.put(MerchantWithdrawrealtimeConfig.REMARK,remark);
                try {
                    Map merchantD0New = withdrawConfigService.updateMerchantWithdrawrealtimeConfig(update);
                    businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                            BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantD0,
                            BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantD0New,
                            BusinessLogUtil.LOG_PARAM_REQUEST, businessRequest,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                    BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                    BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_BATCH_MODIFY,
                                    BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_withdrawrealtime_config",
                                    BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                                    BizOpLog.OP_TIME, now,
                                    BizOpLog.BUSINESS_FUNCTION_CODE, "1000083",
                                    BizOpLog.REMARK, remark
                            )
                    ));
                }catch (Exception ex){
                    logger.error("excel批量导入修改D0配置失败, request[{}]", merchantes.get(i), ex);
                    errmsg = errmsg + ex.getMessage();
                    ok = false;
                }

            }
            if (!ok) {
                merchantes.get(i).put("errmsg", errmsg);
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
        }

        //保存导入结果到任务
        logger.info("导入D0白名单配置结果 总数[{}],非法详情[{}],导入成功详情[{}]", total, merchants_unValid, merchantes);
        String excelDownloadUrl = uploadExcelResult(merchants_unValid, merchantes,
                Arrays.asList("商户sn", "错误原因"),
                Arrays.asList(ConstantUtil.KEY_MERCHANT_SN, "errmsg")
        );

        Map<String, Object> result = new HashedMap();
        //设置下载地址
        result.put("downloadResultUrl", excelDownloadUrl);
        result.put("导入总数", total);
        result.put("导入失败总数", merchants_unValid.size());
        if (merchants_unValid.size() > 20) {
            merchants_unValid = merchants_unValid.subList(0, 20);
            merchants_unValid.add(CollectionUtil.hashMap(
                    "msg", "过多不再显示.........."
            ));
        }
        result.put("导入失败详情", merchants_unValid);
        result.put("导入成功总数", merchantes.size());

        ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                TaskApplyLog.APPLY_RESULT, result));

        return result;
    }

    private Map createTaskApplyLog(Map<String, Object> request) {
        try {
            return logService.createTaskApplyLog(request);
        } catch (Exception e) {
            logger.error("save TaskApplyLog error,param [{}]", request, e);
        }
        return CollectionUtil.hashMap();
    }


    private String toString(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    private int toInt(Object obj) {
        if (obj == null) {
            return 0;
        }
        try {
            String s = obj.toString();
            if (s.indexOf(".") > 0) {
                s = s.substring(0, s.indexOf(".")); // 防止解析为小数，去掉0
            }
            int value = Integer.valueOf(s);
            return value < 0 ? 0 : value;
        } catch (Exception e) {
            return 0;
        }
    }

    private int toInt(Object obj, int default_value) {
        if (obj == null) {
            return default_value;
        }
        try {
            String s = obj.toString();
            if (s.indexOf(".") > 0) {
                s = s.substring(0, s.indexOf(".")); // 防止解析为小数，去掉0
            }
            int value = Integer.valueOf(s);
            return value;
        } catch (Exception e) {
            return default_value;
        }
    }

    private Integer toInteger(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            String s = obj.toString();
            if (s.trim().length() == 0) {
                return null;
            }
            if (s.indexOf(".") > 0) {
                s = s.substring(0, s.indexOf(".")); // 防止解析为小数，去掉0
            }
            int value = Integer.valueOf(s);
            return value < 0 ? 0 : value;
        } catch (Exception e) {
            return null;
        }
    }

    private Integer toMultiply100Integer(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            String s = obj.toString();
            if (s.trim().length() == 0) {
                return null;
            }
            int idx = s.indexOf(".");
            int len = s.length();
            if (idx > 0) {
                s = s.substring(0, idx) + s.substring(idx + 1, (len - idx - 1) > 2 ? (idx + 3) : len);
            } else {
                s = s + "00";
            }
            int value = Integer.valueOf(s);
            return value < 0 ? 0 : value;
        } catch (Exception e) {
            return null;
        }
    }

    private List<Map> extraDataFromExcel(Workbook hssfWorkbook) {
        List<Map> merchantInfos = new ArrayList<Map>();
        try {
            // 循环Sheet
            Sheet hssfSheet = hssfWorkbook.getSheetAt(0);
            if (hssfSheet == null) {
                return null;
            }
            // 循环行Row
            for (int rowNum = 1; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                Row hssfRow = hssfSheet.getRow(rowNum);
                if (hssfRow == null) {
                    continue;
                }
                String merchantSn = toString(hssfRow.getCell(0)).replaceAll(" ", "");
                if (merchantSn.indexOf(".") != -1) {
                    merchantSn = merchantSn.substring(0, merchantSn.indexOf(".")); // 防止解析为小数，去掉0
                }
                int freeCount = toInt(hssfRow.getCell(1));
                if (!StringUtil.empty(merchantSn)) {
                    merchantInfos.add(CollectionUtil.hashMap(
                            MerchantWithdrawrealtimeConfig.MERCHANT_SN, merchantSn,
                            MerchantWithdrawrealtimeConfig.REMAINDER_NUMBER, freeCount,
                            MerchantWithdrawrealtimeConfig.REMARK, toString(hssfRow.getCell(2)).replaceAll(" ", ""),
                            MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW, toMultiply100Integer(hssfRow.getCell(3)),
                            MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW, toMultiply100Integer(hssfRow.getCell(4)),
                            MerchantWithdrawrealtimeConfig.FEE_RATE, toString(hssfRow.getCell(5)).replaceAll(" ", ""), // TODO 类型
                            MerchantWithdrawrealtimeConfig.SINGLE_MIN_FEE_WITHDRAW, toMultiply100Integer(hssfRow.getCell(6)),
                            MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT, toInteger(hssfRow.getCell(7))
                    ));
                }
            }
            return merchantInfos;
        } catch (Exception e) {
            logger.error("解析D0白名单excel失败", e);
            return null;
        }
    }

    /**
     * 导入逻辑
     * 1.去除不合法商户(根据条件判断:商户绑定银行卡为对私且在支持D0银行卡列表中)
     * 2.分批次调用灰度，去除灰度添加失败的商户(灰度单次上限500)
     * 3.增加商户D0免费次数
     * 4.将excel每条记录标记成功、失败原因上传阿里服务器
     *
     * @param merchantes
     */
    private Map batchImportDrawRealTimeTask(List<Map> merchantes, String taskId, final String noticeCode, final String noticeSubCode, Map businessRequest) {

        int total = merchantes.size();
        //没有通过校验
        List<Map> merchants_unValid = new ArrayList<>();
        //重复的商户sn
        List<Map> merchants_repeat = new ArrayList<>();

        //失败商户号
        List<String> errorSns = new ArrayList<>();

        //去重
        for (int i = 0; i < merchantes.size(); i++) {
            for (int j = i + 1; j < merchantes.size(); j++) {
                if (BeanUtil.getPropString(merchantes.get(i), "merchant_sn").equals(BeanUtil.getPropString(merchantes.get(j), "merchant_sn"))) {
                    merchantes.get(j).put("errmsg", "excel中重复商户");
                    merchants_repeat.add(merchantes.get(j));
                    merchantes.remove(j);
                    j--;
                }
            }
        }
        //商户信息补全
        final List<Map> merchantes_copy = merchantes;
        List<String> merchantSns = new ArrayList<String>() {
            {
                for (Map map : merchantes_copy) {
                    add(BeanUtil.getPropString(map, "merchant_sn"));
                }
            }
        };
        final ListResult merchantInfos = merchantSns.size() == 0 ? new ListResult(0, new ArrayList<Map>()) : merchantService.findMerchants(new PageInfo(1, merchantSns.size()), CollectionUtil.hashMap("merchant_sns", merchantSns));
        Map merchantInfosMap = new HashMap() {{
            for (Map map : merchantInfos.getRecords()) {
                put(BeanUtil.getPropString(map, Merchant.SN), map);
            }
        }};
        for (int i = 0; i < merchantes.size(); i++) {
            String merchantId = BeanUtil.getPropString(merchantInfosMap.get(BeanUtil.getPropString(merchantes.get(i), "merchant_sn")), DaoConstants.ID);
            if (StringUtil.empty(merchantId)) {
                merchantes.get(i).put("errmsg", "商户sn不存在");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            merchantes.get(i).put("merchant_id", merchantId);
        }


        //检查商户信息
        final List<Map> merchantes_copy2 = merchantes;
        List<String> validMerchantIds = new ArrayList<String>() {
            {
                for (Map map : merchantes_copy2) {
                    add(BeanUtil.getPropString(map, "merchant_id"));
                }
            }
        };

        //检查商户风控等级是否可以开通D0
        final List<Map> riskMerchants = validMerchantIds.size() == 0 ? new ArrayList<Map>() : riskQueryService.queryRisksByBizIdsAndType(validMerchantIds, RiskSubjectTypeEnum.MERCHANT.getCode());
        Map riskMerchantsMap = new HashMap() {{
            for (Map riskMerchant : riskMerchants) {
                put(BeanUtil.getPropString(riskMerchant, RiskSubjectBean.SUBJECT_BIZ_ID), riskMerchant);
            }
        }};
        for (int i = 0; i < merchantes.size(); i++) {
            String merchant_id = BeanUtil.getPropString(merchantes.get(i), ConstantUtil.KEY_MERCHANT_ID);
            Map riskMerchant = (Map) BeanUtil.getProperty(riskMerchantsMap, merchant_id);
            int status = BeanUtil.getPropInt(riskMerchant, RiskSubjectBean.RISK_STATE_D0, -1);
            if (status == 0) {
                validMerchantIds.remove(merchant_id);
                merchantes.get(i).put("errmsg", "商户风控配置不允许开通D0");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
        }


        //检查商户银行卡信息
        int next = 0;
        for (int begin = 0; begin < validMerchantIds.size(); ) {
            next = begin + 200;
            next = next >= validMerchantIds.size() ? validMerchantIds.size() : next;
            List<String> subMerchantIds = validMerchantIds.subList(begin, next);
            begin = next;
            try {
                Map validMerchantBank = withdrawRealTimeService.validWithDrawRealTimeMerchantBankInfos(subMerchantIds);
                if (BeanUtil.getPropInt(validMerchantBank, "error") > 0) {
                    Map checkBanks = (Map) validMerchantBank.get("errorDetail");
                    for (int i = 0; i < merchantes.size(); i++) {
                        if (checkBanks.containsKey(BeanUtil.getPropString(merchantes.get(i), "merchant_id"))) {
                            merchantes.get(i).put("errmsg", BeanUtil.getPropString(checkBanks, BeanUtil.getPropString(merchantes.get(i), "merchant_id")));
                            merchants_unValid.add(merchantes.get(i));
                            merchantes.remove(i);
                            i--;
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("调用批量检查商户银行卡信息口错误subMerchantIds[{}]", subMerchantIds, e);
            }
        }


        //灰度处理失败的数据从merchantes中移除并初始化参数,补全D0信息
        long importTime = new Date().getTime();
        for (int i = 0; i < merchantes.size(); i++) {
            if (errorSns.contains(BeanUtil.getPropString(merchantes.get(i), "merchant_sn"))) {
                merchantes.remove(i);
                i--;
                continue;
            }
            merchantes.get(i).put(MerchantWithdrawrealtimeConfig.REMARK, "");
            merchantes.get(i).put(MerchantWithdrawrealtimeConfig.EXTRA, "");
            merchantes.get(i).put(MerchantWithdrawrealtimeConfig.IMPORT_TIME, importTime);
            merchantes.get(i).put(MerchantWithdrawrealtimeConfig.USE_NUMBER, 0);
        }
        //将merchantes保存进merchant_withdrawrealtime_config
        //分批导入 200一次
        List<Map> merchantLogs = new ArrayList<>();

        next = 0;
        for (int begin = 0; begin < merchantes.size(); ) {
            next = begin + 200;
            next = next >= merchantes.size() ? merchantes.size() : next;
            List<Map> subMerchants = merchantes.subList(begin, next);
            begin = next;
            try {
                merchantLogs.addAll(withdrawConfigService.batchCreateMerchantWithdrawRealtimeConfig(subMerchants));
            } catch (Exception e) {
                logger.error("调用批量创建D0白名单接口错误subMerchants[{}]", subMerchants, e);
            }
        }

        List<Map> merchantLogs_success = new ArrayList<>();
        long op_time = new Date().getTime();
        for (Map map : merchantLogs) {
            if (BeanUtil.getPropBoolean(map, "success", false)) {
                merchantLogs_success.add(map);
                //记录业务日志
                businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                        BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, null,
                        BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, withdrawConfigService.getMerchantWithdrawrealtimeConfigByMerchantSn(BeanUtil.getPropString(map, "merchant_sn")),
                        BusinessLogUtil.LOG_PARAM_REQUEST, businessRequest,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_IMPORT,
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_withdrawrealtime_config",
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                                BizOpLog.OP_TIME, op_time
                        )
                ));
            } else {
                for (int i = 0; i < merchantes.size(); i++) {
                    if (BeanUtil.getPropString(merchantes.get(i), "merchant_sn").equals(BeanUtil.getPropString(map, "merchant_sn"))) {
                        merchantes.get(i).put("errmsg", BeanUtil.getPropString(map, "errmsg"));
                        merchants_repeat.add(merchantes.get(i));
                        merchantes.remove(i);
                        i--;
                        break;
                    }

                }
            }
        }


        //保存导入结果到任务
        logger.info("导入D0白名单结果 总数[{}],非法详情[{}],重复详情[{}],导入成功详情[{}]", total, merchants_unValid, merchants_repeat, merchantes);
        String excelDownloadUrl = uploadD0ImportResult(merchants_unValid, merchants_repeat, merchantes);

        Map<String, Object> result = new HashedMap();
        //设置下载地址
        result.put("downloadResultUrl", excelDownloadUrl);
        result.put("导入总数", total);
        result.put("导入失败总数", merchants_unValid.size());
        if (merchants_unValid.size() > 20) {
            merchants_unValid = merchants_unValid.subList(0, 20);
            merchants_unValid.add(CollectionUtil.hashMap(
                    "msg", "过多不再显示.........."
            ));
        }
        result.put("导入失败详情", merchants_unValid);
        result.put("重复总数", merchants_repeat.size());
        if (merchants_repeat.size() > 20) {
            merchants_repeat = merchants_repeat.subList(0, 20);
            merchants_repeat.add(CollectionUtil.hashMap(
                    "msg", "过多不再显示.........."
            ));
        }
        result.put("重复详情", merchants_repeat);
        result.put("导入成功总数", merchantes.size());

        ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                TaskApplyLog.APPLY_STATUS, merchantes.size() > 0 ? TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE : TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                TaskApplyLog.APPLY_RESULT, result));


        List<String> merchantIds = new ArrayList<String>();
        // 对导入成功的发送通知
        for (Map sMerchant : merchantes) {
            merchantIds.add(BeanUtil.getPropString(sMerchant, ConstantUtil.KEY_MERCHANT_ID));
        }
        pushService.pushTaskAsynchronous(CollectionUtil.hashMap(PushTask.TASK_APPLY_LOG_ID, taskId,
                PushTask.MERCHANT_IDS, merchantIds,
                PushTask.NOTICE_CODE, noticeCode,
                PushTask.NOTICE_SUB_CODE, noticeSubCode));

        return result;
    }

    private String uploadD0ImportResult(List<Map> merchants_unValid, List<Map> merchants_repeat, List<Map> merchants_success) {
        try {
            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet failureSheet = workbook.createSheet("失败详情");
            List<String> failureHeaders = new ArrayList<String>() {{
                add("商户sn");
                add("失败原因");
            }};
            List<String> failureField = new ArrayList<String>() {{
                add("merchant_sn");
                add("errmsg");
            }};
            buildExcelDetail(failureSheet, failureHeaders, merchants_repeat, failureField);
            buildExcelDetail(failureSheet, null, merchants_unValid, failureField);

            HSSFSheet successSheet = workbook.createSheet("成功详情");
            buildExcelDetail(successSheet, null, merchants_success, null);
            return uploadStatementToOSS("xls", workbook);
        } catch (Exception e) {

        }
        return "";
    }

    private String uploadExcelResult(List<Map> merchants_unValid, List<Map> merchants_success, List<String> headers, List<String> fields) {
        try {
            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet failureSheet = workbook.createSheet("失败详情");
            buildExcelDetail(failureSheet, headers, merchants_unValid, fields);
            HSSFSheet successSheet = workbook.createSheet("成功详情");
            buildExcelDetail(successSheet, null, merchants_success, null);
            return uploadStatementToOSS("xls", workbook);
        } catch (Exception e) {

        }
        return "";
    }

    private String uploadStatementToOSS(String ext, HSSFWorkbook workbook) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        byte[] content = bos.toByteArray();
        bos.close();
        String fullName = "portal/statement/sp_import_result/" + new Date().getTime() + "." + ext;
        ByteArrayInputStream bais = new ByteArrayInputStream(content);
        //todo 开个保存报表的bucket
        ossFileUploader.uploadIfNotExists(OssFileUploader.IMAGE_BUCKET_NAME, fullName, bais, content.length);
        return fullName;
    }

    @Override
    public void getImportDrawRealTimeLog(Map<String, Object> request, HttpServletResponse response) throws IOException {
        Map payload = getImportDrawRealTimePayload(request);
        List<Map> errors = new ArrayList<>();
        if (payload == null) {
            errors.add(CollectionUtil.hashMap("merchant_sn", "", "errmsg", "导入商户错误过多，无法保存，已记录在日志中，请查看相关日志"));
        } else {
            errors.addAll((List<Map>) payload.get("repeatDetail"));
            errors.addAll((List<Map>) payload.get("unValidDetail"));
            errors.addAll((List<Map>) payload.get("importFalureDetail"));
        }

        HSSFWorkbook workbook = buildImportDrawRealTimeFailDetail(errors);
        String fileName = new Date().getTime() + "-importFail.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }

    public Map getImportDrawRealTimePayload(Map request) {
        try {
            String logId = (String) HttpRequestUtil.getSession().getAttribute(SESSION_IMPORT_MERCHANT_DRAW_REALTIME);
            if (logId != null) {
                Map log = logService.getTaskApplyLog(logId);
                if (log != null) {
                    return (Map) log.get(TaskApplyLog.PAYLOAD);
                }
            }
        } catch (Exception e) {
        }
        return null;
    }

    private HSSFWorkbook buildImportDrawRealTimeFailDetail(List<Map> list) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("导入失败数据");
        SheetUtil sheetUtil = new SheetUtil(sheet);

        List<String> columnArr = Arrays.asList(
                "merchant_sn", "errmsg"
        );
        Map nameMap = CollectionUtil.hashMap("merchant_sn", "商户sn", "errmsg", "失败原因");
        List headers = new ArrayList();
        for (String column : columnArr) {
            headers.add(nameMap.get(column));
        }
        sheetUtil.appendRow(headers);
        for (Map map : list) {
            List values = new ArrayList();
            for (String column : columnArr) {
                if ("merchant_sn".equals(column)) {
                    values.add(BeanUtil.getProperty(map, column));
                } else {
                    values.add(BeanUtil.getProperty(map, column));
                }
            }
            sheetUtil.appendRow(values);
        }
        return workbook;
    }

    @Override
    public Map batchDeleteDrawRealTime(final Map<String, Object> request) {
        final Map notice = getNoticeRule(request);
        if (notice == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则不存在!");
        }
        final String remark = BeanUtil.getPropString(request, "remark");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        Map select_all_params = (Map) BeanUtil.getProperty(request, "select_all_params");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            merchant_ids = getWithdrawrealtimeIdsByParams(select_all_params);
        }
        if (merchant_ids == null || merchant_ids.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "删除D0数目为0");
        }
        final List<String> merchant_ids_copy = merchant_ids;
        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_REMOVE_D0,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final String task_apply_log_id = BeanUtil.getPropString(task, DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        noticeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchDeleteDrawRealTimeWithIdsTask(merchant_ids_copy, remark, notice, task_apply_log_id, businessRequest);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }

    /**
     * 批量禁用D0状态
     *
     * @param merchant_ids
     * @param remark
     * @param noticeRule
     * @param task_apply_log_id
     * @return
     */
    private void batchDeleteDrawRealTimeWithIdsTask(List<String> merchant_ids, String remark, final Map noticeRule, final String task_apply_log_id, Map businessRequest) {
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> falureDetail = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        long op_time = new Date().getTime();
        for (String merchant_id : merchant_ids) {
            try {
                Map merchantD0Old = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchant_id);
                withdrawConfigService.deleteMerchantWithdrawrealtimeConfig(CollectionUtil.hashMap(
                        MerchantWithdrawrealtimeConfig.MERCHANT_ID, merchant_id
                ));
                successIds.add(merchant_id);
                //记录业务日志
                businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                        BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantD0Old,
                        BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, null,
                        BusinessLogUtil.LOG_PARAM_REQUEST, businessRequest,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_BATCH_DEL,
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_withdrawrealtime_config",
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                                BizOpLog.OP_TIME, op_time
                        )
                ));
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                falureDetail.add(falure);
            }
        }

        if (falureDetail.size() > 20) {
            falureDetail = falureDetail.subList(0, 20);
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"
            ));
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap(
                        "批量移除D0名单总数", merchant_ids.size(),
                        "批量移除D0名单成功数", successIds.size(),
                        "批量移除D0名单失败数", merchant_ids.size() - successIds.size(),
                        "批量移除D0名单失败详情", falureDetail
                )
        ));
        //发送通知
        pushService.pushTask(CollectionUtil.hashMap(
                PushTask.NOTICE_RULE, noticeRule,
                PushTask.TASK_APPLY_LOG_ID, task_apply_log_id,
                PushTask.MERCHANT_IDS, successIds
        ));
    }

    private void deleteMerchantWithdrawrealtimeConfig(String merchant_id, String remark, String disnable_reason, String disnable_time_begin, String disnable_time_end, int disable_way) {
        withdrawConfigService.disableMerchantWithdrawrealtimeConfig(CollectionUtil.hashMap(
                MerchantWithdrawrealtimeConfig.MERCHANT_ID, merchant_id,
                MerchantWithdrawrealtimeConfig.REMARK, remark,
                MerchantWithdrawrealtimeConfig.DISABLE_REASON, disnable_reason,
                MerchantWithdrawrealtimeConfig.DISABLE_WAY, disable_way,
                MerchantWithdrawrealtimeConfig.DISABLE_TIME_BEGIN, disnable_time_begin,
                MerchantWithdrawrealtimeConfig.DISABLE_TIME_END, disnable_time_end
        ));
    }

    @Override
    public Map updateDrawRealTimeFreeCount(Map<String, Object> request) {
        String merchant_id = BeanUtil.getPropString(request, "merchant_id");
        String remark = BeanUtil.getPropString(request, "remark");
        int inOrDecreaseNum = BeanUtil.getPropInt(request, "inOrDecreaseNum", 0);
        if (StringUtil.empty(merchant_id)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "merchant_id不可为空");
        }
        Map merchantWithdrawrealtimeConfig = null;
        Map merchantWithdrawrealtimeConfigOld = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchant_id);
        if (merchantWithdrawrealtimeConfigOld == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "该商户没有D0的权限!");
        }
        int afterUpdate = BeanUtil.getPropInt(merchantWithdrawrealtimeConfigOld, MerchantWithdrawrealtimeConfig.REMAINDER_NUMBER);
        afterUpdate = inOrDecreaseNum < 0 ? (afterUpdate - Math.abs(inOrDecreaseNum)) : (afterUpdate + inOrDecreaseNum);
        if (afterUpdate < 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "D0免费次数不可修改为负数!");
        }
        //减少免费次数
        if (inOrDecreaseNum < 0) {
            inOrDecreaseNum = Math.abs(inOrDecreaseNum);
            merchantWithdrawrealtimeConfig = withdrawConfigService.decreaseWithDrawRealtimeFreeCount(merchant_id, inOrDecreaseNum);
        }
        //增加免费次数
        else if (inOrDecreaseNum > 0) {
            merchantWithdrawrealtimeConfig = withdrawConfigService.increaseWithDrawRealtimeFreeCount(merchant_id, inOrDecreaseNum);
        }

        if (!StringUtil.empty(remark)) {
            merchantWithdrawrealtimeConfig = withdrawConfigService.modifyWithDrawRealtimeRemark(merchant_id, remark);
        }
        return merchantWithdrawrealtimeConfig;
    }

    private void updateDrawRealTimeFreeCount(String merchant_id, String remark, int inOrDecreaseNum) {
        if (inOrDecreaseNum == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "增加减少免费次数为0，无需变动");
        }
        //减少免费次数
        if (inOrDecreaseNum < 0) {
            inOrDecreaseNum = Math.abs(inOrDecreaseNum);
            withdrawConfigService.decreaseWithDrawRealtimeFreeCount(merchant_id, inOrDecreaseNum);
        }
        //增加免费次数
        else {
            withdrawConfigService.increaseWithDrawRealtimeFreeCount(merchant_id, inOrDecreaseNum);
        }
        withdrawConfigService.modifyWithDrawRealtimeRemark(merchant_id, remark);
    }


    @Override
    public Map batchUpdateDrawRealTimeFreeCount(Map<String, Object> request) {
        final int inOrDecreaseNum = BeanUtil.getPropInt(request, "inOrDecreaseNum");
        if (inOrDecreaseNum == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "增减免费次数不可为空或0!");
        }
        final String remark = BeanUtil.getPropString(request, "remark");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        Map select_all_params = (Map) BeanUtil.getProperty(request, "select_all_params");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            merchant_ids = getWithdrawrealtimeIdsByParams(select_all_params);
        }
        if (merchant_ids == null || merchant_ids.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "修改D0免费次数数目为0");
        }
        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_UPDATE_D0_FREECOUNT,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final List<String> merchant_ids_copy = merchant_ids;
        final String task_apply_log_id = BeanUtil.getPropString(task, DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        noticeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchUpdateDrawRealTimeFreeCountWithIds(merchant_ids_copy, inOrDecreaseNum, remark, task_apply_log_id, businessRequest);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }

    /**
     * 批量修改D0免费次数
     *
     * @param merchant_ids
     * @param inOrDecreaseNum
     * @param remark
     * @return
     */
    private List<Map> batchUpdateDrawRealTimeFreeCountWithIds(List<String> merchant_ids, int inOrDecreaseNum, String remark, final String task_apply_log_id, Map businessRequest) {
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> result = new ArrayList<>();
        List<Map> falureDetail = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        long op_time = new Date().getTime();
        for (String merchant_id : merchant_ids) {
            try {
                Map merchantD0Old = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchant_id);
                updateDrawRealTimeFreeCount(merchant_id, remark, inOrDecreaseNum);
                Map merchantD0New = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchant_id);
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", ""
                ));
                successIds.add(merchant_id);
                //记录业务日志
                businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                        BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantD0Old,
                        BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantD0New,
                        BusinessLogUtil.LOG_PARAM_REQUEST, businessRequest,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_BATCH_MODIFY,
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_withdrawrealtime_config",
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                                BizOpLog.OP_TIME, op_time
                        )
                ));
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                result.add(falure);
                falureDetail.add(falure);
            }
        }
        if (falureDetail.size() > 20) {
            falureDetail = falureDetail.subList(0, 20);
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"
            ));
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap(
                        "批量修改D0免费次数总数", result.size(),
                        "批量修改D0免费次数成功数", successIds.size(),
                        "批量修改D0免费次数失败数", result.size() - successIds.size(),
                        "批量修改D0免费次数失败详情", falureDetail
                )
        ));
        return result;
    }

    /**
     * 根据请求参数获取d0配置merchant_id集合
     *
     * @param params
     * @return
     */
    private List<String> getWithdrawrealtimeIdsByParams(Map params) {
        List<Map> d0s = new ArrayList<>();
        try {
            long page = 1;
            long pageSize = 500;
            params.put("page_size", pageSize);
            params.put("page", 1);
            ListResult lr = getWithDrawRealTimeList(params);
            if (lr != null && lr.getRecords() != null && lr.getRecords().size() > 0) {
                d0s.addAll(lr.getRecords());
            }
            long currentSize = pageSize;
            while (lr != null && currentSize < lr.getTotal()) {
                page++;
                params.put("page", page);
                lr = getWithDrawRealTimeList(params);
                if (lr != null && lr.getRecords() != null && lr.getRecords().size() > 0) {
                    d0s.addAll(lr.getRecords());
                }
                currentSize = currentSize + pageSize;
            }
        } catch (Exception e) {
            logger.error("全选修改D0免费次数异常,入参params[{}]", params, e);
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "全选修改D0免费次数系统异常!");
        }
        List<String> ids = new ArrayList<>();
        for (Map d0 : d0s) {
            ids.add(BeanUtil.getPropString(d0, MerchantWithdrawrealtimeConfig.MERCHANT_ID));
        }
        return ids;
    }

    @Override
    public Map updateWithDrawRulerConfig(Map<String, Object> request) {
        logger.info("更新配置规则入参[{}]", request);
        String type = BeanUtil.getPropString(request, "type");
        if (StringUtil.empty(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "规则类型必传");
        }
        //D0
        if (type.equals(WithdrawRule.TYPE_WITHDRAW_REALTIME_FEE_RATE_RULE)) {
            String validDay = BeanUtil.getPropString(request, WithdrawRule.VALID_DAY, WithdrawRule.VALID_DAY_EVERY_DAY);
            String validTimeBegin = BeanUtil.getPropString(request, WithdrawRule.VALID_TIME_BEGIN);
            String validTimeEnd = BeanUtil.getPropString(request, WithdrawRule.VALID_TIME_END);
            String feeRate = BeanUtil.getPropString(request, WithdrawRule.FEE_RATE);
            long minFee = BeanUtil.getPropLong(request, WithdrawRule.MIN_FEE);
            long singleMinLimitWithdraw = BeanUtil.getPropLong(request, WithdrawRule.SINGLE_MIN_LIMIT_WITHDRAW);
            long singleMaxLimitWithdraw = BeanUtil.getPropLong(request, WithdrawRule.SINGLE_MAX_LIMIT_WITHDRAW);
            int dayLimitWithDrawCount = BeanUtil.getPropInt(request, WithdrawRule.DAY_LIMIT_WITHDRAW_COUNT);
            long singleMinFeeWithdraw = BeanUtil.getPropInt(request, WithdrawRule.SINGLE_MIN_FEE_WITHDRAW);

            if (StringUtil.empty(validTimeBegin) || StringUtil.empty(validTimeEnd) || StringUtil.empty(feeRate)
                    || StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.SINGLE_MIN_LIMIT_WITHDRAW))
                    || StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.SINGLE_MAX_LIMIT_WITHDRAW))
                    || StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.DAY_LIMIT_WITHDRAW_COUNT))
                    || StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.MIN_FEE))
                    || StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.SINGLE_MIN_FEE_WITHDRAW))
                    ) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "规则参数缺失!");
            }
            Map before = withdrawRealTimeService.getWithDrawRuleConfigByType(type);
            Map after = withdrawRealTimeService.modifyWithDrawRuleConfig(CollectionUtil.hashMap(
                    "type", type,
                    "rule", CollectionUtil.hashMap(
                            WithdrawRule.VALID_DAY, validDay,
                            WithdrawRule.VALID_TIME_BEGIN, validTimeBegin,
                            WithdrawRule.VALID_TIME_END, validTimeEnd,
                            WithdrawRule.FEE_RATE, feeRate,
                            WithdrawRule.MIN_FEE, minFee,
                            WithdrawRule.SINGLE_MIN_LIMIT_WITHDRAW, singleMinLimitWithdraw,
                            WithdrawRule.SINGLE_MAX_LIMIT_WITHDRAW, singleMaxLimitWithdraw,
                            WithdrawRule.DAY_LIMIT_WITHDRAW_COUNT, dayLimitWithDrawCount,
                            WithdrawRule.SINGLE_MIN_FEE_WITHDRAW, singleMinFeeWithdraw
                    )
            ));
            sendBusinesLogGeneralRule(before, after);
            return after;
        }
        //standard  intelligence
        else if (type.equals(WithdrawRule.TYPE_WITHDRAW_STANDARD_FEE_RATE_RULE) || type.equals(WithdrawRule.TYPE_WITHDRAW_INTELLIGENCE_FEE_RATE_RULE)) {
            String validDay = BeanUtil.getPropString(request, WithdrawRule.VALID_DAY, WithdrawRule.VALID_DAY_EVERY_DAY);
            long singleMinLimitWithdraw = BeanUtil.getPropLong(request, WithdrawRule.SINGLE_MIN_LIMIT_WITHDRAW);
            long singleMaxLimitWithdrawForPrivate = BeanUtil.getPropLong(request, WithdrawRule.SINGLE_MAX_LIMIT_WITHDRAW_FOR_PRIVATE);
            long singleMaxLimitWithdrawForPublic = BeanUtil.getPropLong(request, WithdrawRule.SINGLE_MAX_LIMIT_WITHDRAW_FOR_PUBLIC);
            int dayLimitWithDrawCount = BeanUtil.getPropInt(request, WithdrawRule.DAY_LIMIT_WITHDRAW_COUNT);
            if (StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.SINGLE_MIN_LIMIT_WITHDRAW))
                    || StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.SINGLE_MAX_LIMIT_WITHDRAW_FOR_PRIVATE))
                    || StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.SINGLE_MAX_LIMIT_WITHDRAW_FOR_PUBLIC))
                    || StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.DAY_LIMIT_WITHDRAW_COUNT))) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "规则参数缺失!");
            }
            Map before = withdrawRealTimeService.getWithDrawRuleConfigByType(type);
            Map after = withdrawRealTimeService.modifyWithDrawRuleConfig(CollectionUtil.hashMap(
                    "type", type,
                    "rule", CollectionUtil.hashMap(
                            WithdrawRule.VALID_DAY, validDay,
                            WithdrawRule.SINGLE_MIN_LIMIT_WITHDRAW, singleMinLimitWithdraw,
                            WithdrawRule.SINGLE_MAX_LIMIT_WITHDRAW_FOR_PRIVATE, singleMaxLimitWithdrawForPrivate,
                            WithdrawRule.SINGLE_MAX_LIMIT_WITHDRAW_FOR_PUBLIC, singleMaxLimitWithdrawForPublic,
                            WithdrawRule.DAY_LIMIT_WITHDRAW_COUNT, dayLimitWithDrawCount
                    )
            ));
            sendBusinesLogGeneralRule(before, after);
            return after;
        }
        //orther
        else if (type.equals(WithdrawRule.TYPE_WITHDRAW_OTHER_FEE_RATE_RULE)) {
            boolean intelligenceAllowStandard = BeanUtil.getPropBoolean(request, WithdrawRule.INTELLIGENCE_ALLOW_STANDARD);
            if (StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.INTELLIGENCE_ALLOW_STANDARD))) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "规则参数缺失!");
            }
            Map before = withdrawRealTimeService.getWithDrawRuleConfigByType(type);
            Map after = withdrawRealTimeService.modifyWithDrawRuleConfig(CollectionUtil.hashMap(
                    "type", type,
                    "rule", CollectionUtil.hashMap(
                            WithdrawRule.INTELLIGENCE_ALLOW_STANDARD, intelligenceAllowStandard
                    )
            ));
            sendBusinesLogGeneralRule(before, after);
            return after;
        }
        throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "规则参数没有该type!");
    }

    @Override
    public Map addWithDrawSystemRuleConfig(Map<String, Object> request) {
        long withdraw_disable_begin_time = BeanUtil.getPropLong(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BEGIN_TIME);
        long withdraw_disable_end_time = BeanUtil.getPropLong(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_END_TIME);
        final List<String> withdraw_disable_types = (List) BeanUtil.getProperty(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_TYPES);
        final List<String> withdraw_disable_providers = (List) BeanUtil.getProperty(request, WithdrawRule.WITHDRAW_DISABLE_PROVIDERS);
        String withdraw_disable_desc = BeanUtil.getPropString(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_DESC);
        Map before = withdrawRealTimeService.getWithDrawRuleConfigByType(WithdrawRule.TYPE_WITHDRAW_SYSTEM_DISABLE_RULE);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> beforeRuleList = (List<Map<String, Object>>) BeanUtil.getProperty(before, "rule");
        if (beforeRuleList == null) beforeRuleList = new ArrayList<>();
        Map<String, Object> newRule = CollectionUtil.hashMap(
                DaoConstants.ID, CrudUtil.randomUuid(),
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BEGIN_TIME, withdraw_disable_begin_time,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_END_TIME, withdraw_disable_end_time,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_TYPES, withdraw_disable_types,
                WithdrawRule.WITHDRAW_DISABLE_PROVIDERS,withdraw_disable_providers,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_DESC, withdraw_disable_desc
        );
        modifySystemRuleConfigCheck(beforeRuleList, newRule);
        beforeRuleList.add(newRule);
        Map after = withdrawRealTimeService.modifyWithDrawRuleConfig(before);
        beforeRuleList.remove(newRule);
        sendBusinesLogGeneralRule(new HashMap(), request);
        return after;
    }

    @Override
    public Map deleteWithDrawSystemRuleConfig(Map<String,Object> request) {
        String id = BeanUtil.getPropString(request,DaoConstants.ID);
        Map before = withdrawRealTimeService.getWithDrawRuleConfigByType(WithdrawRule.TYPE_WITHDRAW_SYSTEM_DISABLE_RULE);
        @SuppressWarnings("unchecked")
        List<Map<String,Object>> beforeRuleList = (List<Map<String, Object>>) BeanUtil.getProperty(before,"rule");
        if(beforeRuleList == null) return null;
        Map<String,Object> deletedMap = new HashMap<>();
        for (int i = beforeRuleList.size() -1; i>=0; i--){
            if(id.equals(BeanUtil.getPropString(beforeRuleList.get(i),DaoConstants.ID))){
                deletedMap = beforeRuleList.get(i);
                beforeRuleList.remove(i);
                break;
            }
        }
        withdrawRealTimeService.modifyWithDrawRuleConfig(before);
        sendBusinesLogGeneralRule(deletedMap,new HashMap());
        return null;
    }

    @Override
    public Map updateWithDrawSystemRuleConfig(Map<String, Object> request) {
        long withdraw_disable_begin_time = BeanUtil.getPropLong(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BEGIN_TIME);
        long withdraw_disable_end_time = BeanUtil.getPropLong(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_END_TIME);
        final List<String> withdraw_disable_types = (List) BeanUtil.getProperty(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_TYPES);
        final List<String> withdraw_disable_providers = (List) BeanUtil.getProperty(request, WithdrawRule.WITHDRAW_DISABLE_PROVIDERS);
        String withdraw_disable_desc = BeanUtil.getPropString(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_DESC);
        Map before = withdrawRealTimeService.getWithDrawRuleConfigByType(WithdrawRule.TYPE_WITHDRAW_SYSTEM_DISABLE_RULE);
        String id = BeanUtil.getPropString(request,DaoConstants.ID);
        @SuppressWarnings("unchecked")
        List<Map<String,Object>> beforeRuleList = (List<Map<String, Object>>) BeanUtil.getProperty(before,"rule");
        if(beforeRuleList == null) return addWithDrawSystemRuleConfig(request);
        Map<String,Object> updatedMap = new HashMap<>();
        Map<String,Object> newRule = CollectionUtil.hashMap(
                DaoConstants.ID,id,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BEGIN_TIME, withdraw_disable_begin_time,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_END_TIME, withdraw_disable_end_time,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_TYPES, withdraw_disable_types,
                WithdrawRule.WITHDRAW_DISABLE_PROVIDERS,withdraw_disable_providers,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_DESC, withdraw_disable_desc
        );
        modifySystemRuleConfigCheck(beforeRuleList,newRule);
        for (int i = 0; i < beforeRuleList.size(); i++) {
            if (id.equals(BeanUtil.getPropString(beforeRuleList.get(i), DaoConstants.ID))) {
                updatedMap = beforeRuleList.get(i);
                beforeRuleList.set(i, newRule);
                break;
            }
        }
        Map after = withdrawRealTimeService.modifyWithDrawRuleConfig(before);
        sendBusinesLogGeneralRule(updatedMap,request);
        return after;
    }

    private void modifySystemRuleConfigCheck(List<Map<String,Object>> beforeRuleList,Map<String,Object> newRule){
        long withdraw_disable_begin_time = BeanUtil.getPropLong(newRule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BEGIN_TIME);
        long withdraw_disable_end_time = BeanUtil.getPropLong(newRule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_END_TIME);
        final List<String> withdraw_disable_types = (List) BeanUtil.getProperty(newRule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_TYPES);
        final List<String> withdraw_disable_providers = (List) BeanUtil.getProperty(newRule, WithdrawRule.WITHDRAW_DISABLE_PROVIDERS);
        if (withdraw_disable_types == null) throw new InvalidParameterException("withdraw_disable_types不可为空");
        for (Map<String, Object> rule : beforeRuleList) {
            if (BeanUtil.getPropString(rule, DaoConstants.ID).equals(BeanUtil.getPropString(newRule, DaoConstants.ID, ""))) {
                continue;
            }
            long beforeWithdrawDisableBeginTime = BeanUtil.getPropLong(rule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BEGIN_TIME);
            long beforeWithdrawDisableEndTime = BeanUtil.getPropLong(rule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_END_TIME);
            final List<String> beforeWithdrawDisableTypes = (List) BeanUtil.getProperty(rule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_TYPES);
            final List<String> beforeWithdrawDisableProviders = (List) BeanUtil.getProperty(rule, WithdrawRule.WITHDRAW_DISABLE_PROVIDERS);
            if (beforeWithdrawDisableBeginTime > withdraw_disable_end_time || beforeWithdrawDisableEndTime < withdraw_disable_begin_time) {
                continue;
            }
            for (String type : withdraw_disable_types) {
                if (beforeWithdrawDisableTypes.contains(type) && intersection(beforeWithdrawDisableProviders, withdraw_disable_providers)) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "禁用时间段与已有配置重合，请重新设置!");
                }
            }
        }
    }

    private boolean intersection(List<String> list1,List<String> list2){
        if(list1 == null || list2 == null){
            return false;
        }
        for (String var1:list1){
            if(list2.contains(var1)){
                return true;
            }
        }
        return false;
    }

    @Override
    public Map addWithDrawBankSystemRuleConfig(Map<String, Object> request) {
        long withdraw_disable_begin_time = BeanUtil.getPropLong(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_BEGIN_TIME);
        long withdraw_disable_end_time = BeanUtil.getPropLong(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_END_TIME);
        final List<String> withdraw_disable_types = (List) BeanUtil.getProperty(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_TYPES);
        String bankName = BeanUtil.getPropString(request, Withdraw.BANK_NAME);
        String withdraw_disable_bank_desc = BeanUtil.getPropString(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_DESC);
        final List<String> withdraw_disable_providers = (List) BeanUtil.getProperty(request, WithdrawRule.WITHDRAW_DISABLE_PROVIDERS);
        if (StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_BEGIN_TIME))
                || StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_END_TIME))
                || bankName == null || StringUtil.empty(bankName)
                || withdraw_disable_types == null || withdraw_disable_types.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "规则参数缺失!");
        }

        List<String> withdrawDisableNotifyWay = (List<String>) MapUtil.getObject(request, WithdrawRule.WITHDRAW_DISABLE_NOTIFY_WAY);
        long withdrawNotifyDatetime = MapUtil.getLongValue(request, WithdrawRule.WITHDRAW_DISABLE_NOTIFY_DATETIME, 0L);

        Map before = withdrawRealTimeService.getWithDrawRuleConfigByType(WithdrawRule.TYPE_WITHDRAW_SYSTEM_DISABLE_BANK_RULE);
        @SuppressWarnings("unchecked")
        List<Map<String,Object>> beforeRuleList = (List<Map<String, Object>>) BeanUtil.getProperty(before,"rule");
        if(beforeRuleList == null) beforeRuleList = new ArrayList<>();
        String id = CrudUtil.randomUuid();
        Map newRule = CollectionUtil.hashMap(
                DaoConstants.ID, id,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_BEGIN_TIME, withdraw_disable_begin_time,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_END_TIME, withdraw_disable_end_time,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_TYPES, withdraw_disable_types,
                WithdrawRule.WITHDRAW_DISABLE_PROVIDERS,withdraw_disable_providers,
                Withdraw.BANK_NAME, bankName,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_DESC, withdraw_disable_bank_desc,
                WithdrawRule.WITHDRAW_DISABLE_NOTIFY_DATETIME, withdrawNotifyDatetime,
                WithdrawRule.WITHDRAW_DISABLE_NOTIFY_WAY, withdrawDisableNotifyWay
        );
        modifyWithDrawBankSystemRuleConfigCheck(beforeRuleList,newRule);
        beforeRuleList.add(newRule);
        before.put(WithdrawRule.WITHDRAW_DISABLE_RULE_ID, id);
        Map after = withdrawRealTimeService.modifyWithDrawRuleConfig(before);
        before.remove(WithdrawRule.WITHDRAW_DISABLE_RULE_ID);
        beforeRuleList.remove(newRule);
        sendBusinesLogGeneralRule(before, after);
        return after;
    }

    @Override
    public Map deleteWithDrawBankSystemRuleConfig(Map<String,Object> request) {
        String id = BeanUtil.getPropString(request,DaoConstants.ID);
        Map before = withdrawRealTimeService.getWithDrawRuleConfigByType(WithdrawRule.TYPE_WITHDRAW_SYSTEM_DISABLE_BANK_RULE);
        @SuppressWarnings("unchecked")
        List<Map<String,Object>> beforeRuleList = (List<Map<String, Object>>) BeanUtil.getProperty(before,"rule");
        if(beforeRuleList == null) return null;
        Map<String,Object> deletedMap = new HashMap<>();
        for (int i = beforeRuleList.size() -1; i>=0; i--){
            if(id.equals(BeanUtil.getPropString(beforeRuleList.get(i),DaoConstants.ID))){
                deletedMap = beforeRuleList.get(i);
                beforeRuleList.remove(i);
                break;
            }
        }
        withdrawRealTimeService.modifyWithDrawRuleConfig(before);
        sendBusinesLogGeneralRule(deletedMap,new HashMap());
        return null;
    }

    @Override
    public Map updateWithDrawBankSystemRuleConfig(Map<String, Object> request) {
        Map before = withdrawRealTimeService.getWithDrawRuleConfigByType(WithdrawRule.TYPE_WITHDRAW_SYSTEM_DISABLE_BANK_RULE);
        @SuppressWarnings("unchecked")
        List<Map<String,Object>> beforeRuleList = (List<Map<String, Object>>) BeanUtil.getProperty(before,"rule");
        if(beforeRuleList == null) return addWithDrawSystemRuleConfig(request);
        Map<String,Object> updatedMap = new HashMap<>();
        long withdraw_disable_begin_time = BeanUtil.getPropLong(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_BEGIN_TIME);
        long withdraw_disable_end_time = BeanUtil.getPropLong(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_END_TIME);
        final List<String> withdraw_disable_types = (List) BeanUtil.getProperty(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_TYPES);
        final List<String> withdraw_disable_providers = (List) BeanUtil.getProperty(request, WithdrawRule.WITHDRAW_DISABLE_PROVIDERS);
        String bankName = BeanUtil.getPropString(request, Withdraw.BANK_NAME);
        String id= BeanUtil.getPropString(request, DaoConstants.ID);
        String withdraw_disable_bank_desc = BeanUtil.getPropString(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_DESC);
        if (StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_BEGIN_TIME))
                || StringUtil.empty(BeanUtil.getPropString(request, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_END_TIME))
                || bankName == null || StringUtil.empty(bankName)
                || withdraw_disable_types == null || withdraw_disable_types.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "规则参数缺失!");
        }

        List<String> withdrawDisableNotifyWay = (List<String>) MapUtil.getObject(request, WithdrawRule.WITHDRAW_DISABLE_NOTIFY_WAY);
        long withdrawNotifyDatetime = MapUtil.getLongValue(request, WithdrawRule.WITHDRAW_DISABLE_NOTIFY_DATETIME, 0L);

        Map newRule = CollectionUtil.hashMap(
                DaoConstants.ID,id,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_BEGIN_TIME, withdraw_disable_begin_time,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_END_TIME, withdraw_disable_end_time,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_TYPES, withdraw_disable_types,
                WithdrawRule.WITHDRAW_DISABLE_PROVIDERS,withdraw_disable_providers,
                Withdraw.BANK_NAME, bankName,
                WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_DESC, withdraw_disable_bank_desc,
                WithdrawRule.WITHDRAW_DISABLE_NOTIFY_DATETIME, withdrawNotifyDatetime,
                WithdrawRule.WITHDRAW_DISABLE_NOTIFY_WAY, withdrawDisableNotifyWay
        );
        modifyWithDrawBankSystemRuleConfigCheck(beforeRuleList,newRule);
        for (int i = 0; i < beforeRuleList.size(); i++) {
            if (id.equals(BeanUtil.getPropString(beforeRuleList.get(i), DaoConstants.ID))) {
                updatedMap = beforeRuleList.get(i);
                beforeRuleList.set(i, newRule);
                break;
            }
        }
        before.put(WithdrawRule.WITHDRAW_DISABLE_RULE_ID, id);
        Map after = withdrawRealTimeService.modifyWithDrawRuleConfig(before);
        before.remove(WithdrawRule.WITHDRAW_DISABLE_RULE_ID);
        sendBusinesLogGeneralRule(updatedMap,request);
        return after;
    }

    private void modifyWithDrawBankSystemRuleConfigCheck(List<Map<String,Object>> beforeRuleList,Map<String,Object> newRule){
        long withdraw_disable_begin_time = BeanUtil.getPropLong(newRule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_BEGIN_TIME);
        long withdraw_disable_end_time = BeanUtil.getPropLong(newRule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_END_TIME);
        final List<String> withdraw_disable_types = (List) BeanUtil.getProperty(newRule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_TYPES);
        final List<String> withdraw_disable_providers = (List) BeanUtil.getProperty(newRule, WithdrawRule.WITHDRAW_DISABLE_PROVIDERS);
        String newBankName = BeanUtil.getPropString(newRule, Withdraw.BANK_NAME);

        String [] newBankNameList = newBankName.split(",");

        if(withdraw_disable_types == null) throw new InvalidParameterException("withdraw_disable_types不可为空");
        for (Map<String,Object> rule:beforeRuleList){
            if(BeanUtil.getPropString(rule,DaoConstants.ID).equals(BeanUtil.getPropString(newRule,DaoConstants.ID,""))){
                continue;
            }
            boolean bankNameConflict = false;
            List<String> beforeBankList = Arrays.asList(BeanUtil.getPropString(rule, Withdraw.BANK_NAME,"").split(","));
            for (String bankName : newBankNameList) {
                if (beforeBankList.contains(bankName)) {
                    bankNameConflict = true;
                    break;
                }
            }
            if(!bankNameConflict) continue;
            long beforeWithdrawDisableBeginTime = BeanUtil.getPropLong(rule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_BEGIN_TIME);
            long beforeWithdrawDisableEndTime = BeanUtil.getPropLong(rule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_END_TIME);
            final List<String> beforeWithdrawDisableTypes = (List) BeanUtil.getProperty(rule, WithdrawRule.WITHDRAW_SYSTEM_DISABLE_BANK_TYPES);
            final List<String> beforeWithdrawDisableProviders = (List) BeanUtil.getProperty(rule, WithdrawRule.WITHDRAW_DISABLE_PROVIDERS);
            if (beforeWithdrawDisableBeginTime > withdraw_disable_end_time || beforeWithdrawDisableEndTime < withdraw_disable_begin_time) {
                continue;
            }
            for (String type : withdraw_disable_types) {
                if (beforeWithdrawDisableTypes.contains(type) && intersection(beforeWithdrawDisableProviders, withdraw_disable_providers)) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "禁用时间段与已有配置重合，请重新设置!");
                }
            }
        }
    }


    private void pushWithdrawSystemCloseInfo(Map noticeRule, final String task_apply_log_id, List<String> withdraw_disable_types) {
        List<String> merchant_ids = new ArrayList<>();
        if (withdraw_disable_types != null && withdraw_disable_types.contains(WithdrawRule.TYPE_WITHDRAW_REALTIME_FEE_RATE_RULE)) {
            merchant_ids = getAllD0merchantIds();
        }
        //获取D0集合
        pushService.pushTaskAsynchronous(CollectionUtil.hashMap(
                PushTask.MERCHANT_IDS, merchant_ids,
                PushTask.TASK_APPLY_LOG_ID, task_apply_log_id,
                PushTask.NOTICE_RULE, noticeRule
        ));

    }

    /**
     * 获取所有D0商户id集合
     *
     * @return
     */
    private List<String> getAllD0merchantIds() {
        List<String> merchantIds = new ArrayList<>();
        try {
            int page = 1;
            PageInfo pageInfo = new PageInfo(1, 2000);
            ListResult result = withdrawConfigService.findMerchantWithdrawrealtimeConfigs(pageInfo, null);
            long total = 0;
            long current = 0;
            if (result != null && result.getRecords() != null && result.getRecords().size() > 0) {
                total = result.getTotal();
                current = result.getRecords().size();
                for (Map map : result.getRecords()) {
                    merchantIds.add(BeanUtil.getPropString(map, MerchantWithdrawrealtimeConfig.MERCHANT_ID));
                }
            }
            while (current < total) {
                page++;
                pageInfo.setPage(page);
                result = withdrawConfigService.findMerchantWithdrawrealtimeConfigs(pageInfo, null);
                if (result != null && result.getRecords() != null && result.getRecords().size() > 0) {
                    total = result.getTotal();
                    current = current + result.getRecords().size();
                    for (Map map : result.getRecords()) {
                        merchantIds.add(BeanUtil.getPropString(map, MerchantWithdrawrealtimeConfig.MERCHANT_ID));
                    }
                }
            }
        } catch (Exception e) {
            logger.error("系统异常", e);
        }
        return merchantIds;
    }

    @Override
    public Map createOrganizationWithDrawRulerConfig(Map<String, Object> request) {
        String organizationId = BeanUtil.getPropString(request, "organizationId");
        Map rule = getOrganizationWithDrawRulerConfig(CollectionUtil.hashMap(
                "organizationId", organizationId
        ));
        if (rule != null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "渠道D0规则已配置，请使用编辑");
        } else {
            return updateOrganizationWithDrawRulerConfig(request);
        }
    }

    @Override
    public Map updateOrganizationWithDrawRulerConfig(Map<String, Object> request) {
        logger.info("更新配置规则入参[{}]", request);
        String organizationId = BeanUtil.getPropString(request, "organizationId");
        if (StringUtil.empty(organizationId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "渠道id不能为空!");
        }
        String validDay = BeanUtil.getPropString(request, WithdrawRule.VALID_DAY);
        String validTimeBegin = BeanUtil.getPropString(request, WithdrawRule.VALID_TIME_BEGIN);
        String validTimeEnd = BeanUtil.getPropString(request, WithdrawRule.VALID_TIME_END);
        String feeRate = BeanUtil.getPropString(request, WithdrawRule.FEE_RATE);
        String minFee = BeanUtil.getPropString(request, WithdrawRule.MIN_FEE);
        String singleMinLimitWithdraw = BeanUtil.getPropString(request, WithdrawRule.SINGLE_MIN_LIMIT_WITHDRAW);
        String singleMaxLimitWithdraw = BeanUtil.getPropString(request, WithdrawRule.SINGLE_MAX_LIMIT_WITHDRAW);
        String dayLimitWithdrawCount = BeanUtil.getPropString(request, WithdrawRule.DAY_LIMIT_WITHDRAW_COUNT);
        String singleMinFeeWithdraw = BeanUtil.getPropString(request, WithdrawRule.SINGLE_MIN_FEE_WITHDRAW);
        String remainder_number = BeanUtil.getPropString(request, WithdrawRule.REMAINDER_NUMBER);

        Map updateMap = CollectionUtil.hashMap();
        //每日可提现次数
        if (!StringUtil.empty(dayLimitWithdrawCount)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT, Long.valueOf(dayLimitWithdrawCount));
        } else {
            updateMap.put(MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT, null);
        }
        //单笔最多可提现金额
        if (!StringUtil.empty(singleMaxLimitWithdraw)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW, Long.valueOf(singleMaxLimitWithdraw));
        } else {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW, null);
        }
        //单笔最少可提现金额
        if (!StringUtil.empty(singleMinLimitWithdraw)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW, Long.valueOf(singleMinLimitWithdraw));
        } else {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW, null);
        }
        //单笔最少手续费提现金额
        if (!StringUtil.empty(singleMinFeeWithdraw)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MIN_FEE_WITHDRAW, Long.valueOf(singleMinFeeWithdraw));
        } else {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MIN_FEE_WITHDRAW, null);
        }
        //费率
        if (!StringUtil.empty(feeRate)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.FEE_RATE, feeRate);
        } else {
            updateMap.put(MerchantWithdrawrealtimeConfig.FEE_RATE, null);
        }
        //最低手续费
        if (!StringUtil.empty(minFee)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.MIN_FEE, Long.valueOf(minFee));
        } else {
            updateMap.put(MerchantWithdrawrealtimeConfig.MIN_FEE, null);
        }
        //可提现日
        if (!StringUtil.empty(validDay)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_DAY, validDay);
        } else {
            updateMap.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_DAY, null);
        }
        //可提现开始时间
        if (!StringUtil.empty(validTimeBegin)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_BEGIN, validTimeBegin);
        } else {
            updateMap.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_BEGIN, null);
        }
        //可提现结束时间
        if (!StringUtil.empty(validTimeEnd)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_END, validTimeEnd);
        } else {
            updateMap.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_END, null);
        }
        //D0免费次数
        if (!StringUtil.empty(remainder_number)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.REMAINDER_NUMBER, remainder_number);
        } else {
            updateMap.put(MerchantWithdrawrealtimeConfig.REMAINDER_NUMBER, null);
        }
        Map before = getOrganizationWithDrawRulerConfig(CollectionUtil.hashMap(
                "organizationId", organizationId
        ));
        Map after = withdrawRealTimeService.modifyOrganizationWithDrawRuleConfig(organizationId, updateMap);
        sendBusinesLogGeneralRule(before, after);
        return after;
    }

    @Override
    public List<Map> batchCreateOrganizationWithDrawRulerConfig(Map request) {
        List<String> organizationIds = (List) BeanUtil.getProperty(request, "organizationIds");
        if (organizationIds == null || organizationIds.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "渠道集合为空!");
        }
        request.remove("organizationIds");
        List<Map> result = new ArrayList<>();
        long op_time = new Date().getTime();
        StringBuilder existsRuleOrganizations = new StringBuilder();
        // 先进行校验
        for (String id : organizationIds) {
//            try {
            Map genulOldDetail = getOrganizationWithDrawRulerConfig(CollectionUtil.hashMap(
                    "organizationId", id
            ));
            if (genulOldDetail != null) {
                existsRuleOrganizations.append(", ").append(BeanUtil.getPropString(withdrawConfigService.getOrganization(id), "name"));
            }
        }
        if (existsRuleOrganizations.length() > 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "渠道(" + existsRuleOrganizations.substring(2) + ")D0规则已配置");
        }
        // 校验通过进行创建
        for (String id : organizationIds) {
            Map genulOld = CollectionUtil.hashMap(
                    "type", WithdrawRule.TYPE_WITHDRAW_ORGANIZATION_FEE_RATE_RULE,
                    "rule", null
            );


            request.put("organizationId", id);
            Map data = updateOrganizationWithDrawRulerConfig(request);


            Map genulNewDetail = getOrganizationWithDrawRulerConfig(CollectionUtil.hashMap(
                    "organizationId", id
            ));
            genulNewDetail.put("organizationId", id);
            Map genulNew = CollectionUtil.hashMap(
                    "type", WithdrawRule.TYPE_WITHDRAW_ORGANIZATION_FEE_RATE_RULE,
                    "rule", genulNewDetail
            );


            result.add(CollectionUtil.hashMap(
                    "organizationId", id,
                    "code", UpayException.CODE_SUCCESS,
                    "msg", "succ",
                    "data", data
            ));
            //记录业务日志
            int op_type = genulOld == null ? BizOpLog.OP_TYPE_ADD : BizOpLog.OP_TYPE_BATCH_MODIFY;
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, genulOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, genulNew,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "general_rule",
                            BizOpLog.OP_TYPE, op_type,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "general_rule",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "type",
                            BizOpLog.OP_TIME, op_time
                    )
            ));
//            }catch (Exception e){
//                result.add(CollectionUtil.hashMap(
//                        "organizationId", id,
//                        "code", UpayException.CODE_UNKNOWN_ERROR,
//                        "msg", e.getMessage(),
//                        "data", ""
//                ));
//            }
        }
        return result;
    }

    @Override
    public Map batchUpdateMerchantWithDrawRulerConfig(Map<String, Object> request) {
        logger.info("更新配置规则入参[{}]", request);
        String remark = BeanUtil.getPropString(request, "remark");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不能为空!");
        }
        Map rule = getMerchantD0Rule(request);
        rule.put(MerchantWithdrawrealtimeConfig.REMARK, remark);
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        Map select_all_params = (Map) BeanUtil.getProperty(request, "select_all_params");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            merchant_ids = getWithdrawrealtimeIdsByParams(select_all_params);
        }
        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_UPDATE_D0_RULE,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final List<String> merchant_ids_copy = merchant_ids;
        final Map rule_copy = rule;
        final String task_apply_log_id = BeanUtil.getPropString(task, DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        noticeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchUpdateMerchantWithDrawRulerConfigWithIds(merchant_ids_copy, rule_copy, task_apply_log_id, businessRequest);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }

    /**
     * 批量修改D0规则
     *
     * @param merchant_ids
     * @param rule
     * @return
     */
    private List<Map> batchUpdateMerchantWithDrawRulerConfigWithIds(List<String> merchant_ids, Map rule, final String task_apply_log_id, Map businessRequest) {
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> result = new ArrayList<>();
        List<Map> falureDetail = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        long op_time = new Date().getTime();
        for (String merchant_id : merchant_ids) {
            try {
                rule.put(MerchantWithdrawrealtimeConfig.MERCHANT_ID, merchant_id);
                Map merchantD0Old = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchant_id);
                Map merchantD0New = updateMerchantWithdrawRulerConfig(rule);
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", null
                ));
                successIds.add(merchant_id);
                //记录业务日志
                businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                        BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantD0Old,
                        BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantD0New,
                        BusinessLogUtil.LOG_PARAM_REQUEST, businessRequest,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_BATCH_MODIFY,
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_withdrawrealtime_config",
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                                BizOpLog.OP_TIME, op_time
                        )
                ));
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                result.add(falure);
                falureDetail.add(falure);
            }
        }
        if (falureDetail.size() > 20) {
            falureDetail = falureDetail.subList(0, 20);
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"
            ));
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap(
                        "批量修改D0规则总数", result.size(),
                        "批量修改D0规则成功数", successIds.size(),
                        "批量修改D0规则失败数", result.size() - successIds.size(),
                        "批量修改D0规则失败详情", falureDetail
                )
        ));
        return result;
    }

    private Map updateMerchantWithdrawRulerConfig(Map merchantConfig) {
        return withdrawConfigService.updateWithDrawRealtimeRule(merchantConfig);
    }

    private Map getMerchantD0Rule(Map request) {
        Map updateMap = CollectionUtil.hashMap();
        if (request == null) {
            return updateMap;
        }
        String dayLimitWithdrawCount = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT);
        String singleMaxLimitWithdraw = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW);
        String singleMinLimitWithdraw = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW);
        String singleMinFeeWithdraw = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.SINGLE_MIN_FEE_WITHDRAW);
        String feeRate = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.FEE_RATE);
        String minFee = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.MIN_FEE);
        String validDay = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.EXTRA_VALID_DAY);
        String validTimeBegin = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_BEGIN);
        String validTimeEnd = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_END);
        //每日可提现次数
        if (!StringUtil.empty(dayLimitWithdrawCount)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT, Long.valueOf(dayLimitWithdrawCount));
        } else if (request.containsKey(MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.DAY_LIMIT_WITHDRAW_COUNT, null);
        }
        //单笔最多可提现金额
        if (!StringUtil.empty(singleMaxLimitWithdraw)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW, Long.valueOf(singleMaxLimitWithdraw));
        } else if (request.containsKey(MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MAX_LIMIT_WITHDRAW, null);
        }
        //单笔最少可提现金额
        if (!StringUtil.empty(singleMinLimitWithdraw)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW, Long.valueOf(singleMinLimitWithdraw));
        } else if (request.containsKey(MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MIN_LIMIT_WITHDRAW, null);
        }
        //单笔最少手续费提现金额
        if (!StringUtil.empty(singleMinFeeWithdraw)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MIN_FEE_WITHDRAW, Long.valueOf(singleMinFeeWithdraw));
        } else if (request.containsKey(MerchantWithdrawrealtimeConfig.SINGLE_MIN_FEE_WITHDRAW)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.SINGLE_MIN_FEE_WITHDRAW, null);
        }
        //费率
        if (!StringUtil.empty(feeRate)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.FEE_RATE, feeRate);
        } else if (request.containsKey(MerchantWithdrawrealtimeConfig.FEE_RATE)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.FEE_RATE, null);
        }
        //最低手续费
        if (!StringUtil.empty(minFee)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.MIN_FEE, Long.valueOf(minFee));
        } else if (request.containsKey(MerchantWithdrawrealtimeConfig.MIN_FEE)) {
            updateMap.put(MerchantWithdrawrealtimeConfig.MIN_FEE, null);
        }
        Map extra = new HashedMap();
        //可提现日
        if (!StringUtil.empty(validDay)) {
            extra.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_DAY, validDay);
        } else if (request.containsKey(MerchantWithdrawrealtimeConfig.EXTRA_VALID_DAY)) {
            extra.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_DAY, null);
        }
        //可提现开始时间
        if (!StringUtil.empty(validTimeBegin)) {
            extra.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_BEGIN, validTimeBegin);
        } else if (request.containsKey(MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_BEGIN)) {
            extra.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_BEGIN, null);
        }
        //可提现结束时间
        if (!StringUtil.empty(validTimeEnd)) {
            extra.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_END, validTimeEnd);
        } else if (request.containsKey(MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_END)) {
            extra.put(MerchantWithdrawrealtimeConfig.EXTRA_VALID_TIME_END, null);
        }
        updateMap.put(MerchantWithdrawrealtimeConfig.EXTRA, extra);
        return updateMap;
    }


    private void sendBusinesLogGeneralRule(Map before, Map after) {

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, before,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, after,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "general_rule",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "general_rule",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "type"
                )
        ));
    }

    @Override
    public Map getWithDrawRulerConfig(Map<String, Object> request) {
        String type = BeanUtil.getPropString(request, "type");
        if (StringUtil.empty(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "规则类型必传");
        }
        return withdrawRealTimeService.getWithDrawRuleConfigByType(type);
    }

    @Override
    public Map getOrganizationWithDrawRulerConfig(Map<String, Object> request) {
        String organizationId = BeanUtil.getPropString(request, "organizationId");
        if (StringUtil.empty(organizationId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "渠道id必传");
        }
        return withdrawRealTimeService.getOrganizationWithDrawRuleConfig(organizationId);
    }

    @Override
    public ListResult findOrganizationWithDrawRuleConfigs(Map<String, Object> request) {
        return withdrawRealTimeService.findOrganizationWithDrawRuleConfigs(PageInfoUtil.extractPageInfo(request), request);
    }

    @Override
    public void deleteOrganizationWithDrawRulerConfig(Map<String, Object> request) {
        String organizationId = BeanUtil.getPropString(request, "organizationId");
        if (StringUtil.empty(organizationId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "渠道id必传");
        }

        Map genulOldDetail = getOrganizationWithDrawRulerConfig(CollectionUtil.hashMap(
                "organizationId", organizationId
        ));
        Map genulOld = CollectionUtil.hashMap(
                "type", WithdrawRule.TYPE_WITHDRAW_ORGANIZATION_FEE_RATE_RULE,
                "rule", genulOldDetail
        );

        withdrawRealTimeService.deleteOrganizationWithDrawRuleConfig(organizationId);

        Map genulNew = CollectionUtil.hashMap(
                "type", WithdrawRule.TYPE_WITHDRAW_ORGANIZATION_FEE_RATE_RULE,
                "rule", CollectionUtil.hashMap(
                        "organizationId", organizationId
                )
        );

        //记录商户日志
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, genulOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, genulNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "general_rule",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_DEL,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "general_rule",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "type",
                        BizOpLog.OP_TIME, new Date().getTime()
                )
        ));
    }

    private Double getPropDouble(Object object, String propName) {
        Object value = BeanUtil.getNestedProperty(object, propName);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (Exception e) {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public Map merchantAvailableWithdrawRealtime(Map<String, Object> request) {
        String merchant_id = BeanUtil.getPropString(request, "merchant_id");
        if (StringUtil.empty(merchant_id)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户id不可为空");
        }
        List<Map> avables = withdrawRealTimeService.getMerchantAvailableWithdrawMode(merchant_id);
        if (avables != null) {
            for (Map map : avables) {
                if (BeanUtil.getPropInt(map, "withdraw_mode") == 1 && BeanUtil.getPropInt(map, "type") == 1) {
                    if (BeanUtil.getPropBoolean(map, "enabled")) {
                        map.put("allow", true);
                    } else {
                        map.put("allow", false);
                    }
                    return map;
                }
            }
        }
        return CollectionUtil.hashMap(
                "allow", false,
                "remainder_number", 0
        );
    }

    @Override
    public Map merchantAvailableWithdraw(Map<String, Object> request) {
        String merchant_id = BeanUtil.getPropString(request, "merchant_id");
        if (StringUtil.empty(merchant_id)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户id不可为空");
        }
        Map map = merchantWithdrawConfigService.getMerchantWithdrawConfigByMerchantId(merchant_id);
        if (map != null && !map.isEmpty()) {
            return CollectionUtil.hashMap(
                    "allow", false
            );
        }
        return CollectionUtil.hashMap(
                "allow", true
        );
    }


    /***
     * 1.获取商户ID
     * 2.调用灰度服务
     * @param request
     * @return
     */
    @Override
    public Map batchModifySmsNotify(Map request) {
        //需要导入灰度的集合
        List<String> merchantsIds = new ArrayList<>();
        //成功导入的集合
        List<String> successMerchantsIds = new ArrayList<>();
        //商户id，sn映射
        Map<String, String> merchant_id_sns = new HashMap<>();
        //商户sn，id映射
        Map<String, String> merchant_sn_ids = new HashMap<>();
        //事变详情列表
        List<Map> errorList = new ArrayList<>();
        //前端传入的sn集合
        List<String> merchant_sns = (List<String>) request.get("merchant_sns");
        String smsStatus = BeanUtil.getPropString(request, SMS_STATUS);
        //merchant_sns单次500条
        if (merchant_sns.size() > 1000) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "merchant_sns只允许批量修改1000个!");
        }
        if (!Arrays.asList("0", "1").contains(smsStatus)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "smsStatus非法!");
        }
        //获取id
        ListResult result = merchantService.findMerchants(new PageInfo(1, 1000), CollectionUtil.hashMap(
                "merchant_sns", merchant_sns
        ));
        if (result != null && result.getRecords() != null && result.getRecords().size() > 0) {
            for (Map merchant : result.getRecords()) {
                String withdrawMode = BeanUtil.getPropString(merchant, "withdraw_mode");
                merchant_id_sns.put(BeanUtil.getPropString(merchant, DaoConstants.ID, ""), BeanUtil.getPropString(merchant, Merchant.SN, ""));
                merchant_sn_ids.put(BeanUtil.getPropString(merchant, Merchant.SN, ""), BeanUtil.getPropString(merchant, DaoConstants.ID, ""));
                //关闭时候，判断非智能提现
//                if (smsStatus.equals("1") || (smsStatus.equals("0") && withdrawMode.equals("1"))) {
//                    merchantsIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID, ""));
//                } else {
//                    errorList.add(CollectionUtil.hashMap(
//                            "sn", BeanUtil.getPropString(merchant, Merchant.SN, ""),
//                            "message", "商户提现方式为智能提现，不允许关闭提现短信通知!"
//                    ));
//                }

                //去除智能提现判断
                merchantsIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID, ""));
            }
        }
        //不存在商户sn
        for (String importSn : merchant_sns) {
            if (!merchant_sn_ids.containsKey(importSn)) {
                errorList.add(CollectionUtil.hashMap(
                        "sn", importSn,
                        "message", "商户不存在!"
                ));
            }
        }
        //调用灰度服务
        int next = 0;
        for (int begin = 0; begin < merchantsIds.size(); ) {
            next = begin + 500;
            next = next >= merchantsIds.size() ? merchantsIds.size() : next;
            List<String> subMerchantIds = merchantsIds.subList(begin, next);
            begin = next;
            //调用灰度导入
            Map failResult = null;
            try {
                if (smsStatus.equals("0")) {
                    failResult = appGatedRuleService.linkeAppBizRuleWithResult("15", subMerchantIds, "20170622");
                } else if (smsStatus.equals("1")) {
                    failResult = appGatedRuleService.deleteAppBizRuleWithResult("15", subMerchantIds);
                }
            } catch (Exception e) {
                logger.error("导入数据,appId[{}],ruleId[{}],merchantSns[{}]", "15", "302", subMerchantIds, e);
                for (String merchantId : subMerchantIds) {
                    errorList.add(CollectionUtil.hashMap(
                            "sn", merchant_id_sns.get(merchantId),
                            "message", "灰度服务接口异常,异常信息[" + e.getMessage() + "]"
                    ));
                }
                continue;
            }

            //返回数据一定存在
            List<String> missList = null;
            try {
                missList = (List<String>) failResult.get("missList");
            } catch (Exception e) {
            }
            if (missList == null || missList.size() == 0) {
                successMerchantsIds.addAll(subMerchantIds);
            } else {
                for (String merchantId : subMerchantIds) {
                    if (!missList.contains(merchantId)) {
                        successMerchantsIds.add(merchantId);
                    } else {
                        errorList.add(CollectionUtil.hashMap(
                                "sn", merchant_id_sns.get(merchantId),
                                "message", smsStatus.equals("0") ? "灰度接口返回失败，可能原因商户已在灰度名单!" : "灰度接口返回失败，可能原因商户不在灰度名单!"
                        ));
                    }
                }
            }
        }


        if (smsStatus.equals("0")) {
            //异步发送短信
            final List successMerchantsIds_copy = successMerchantsIds;
            sendSmsInGeryService(successMerchantsIds_copy);
        }
        return CollectionUtil.hashMap(
                "total", successMerchantsIds.size() + errorList.size(),
                "success", successMerchantsIds.size(),
                "falure", errorList.size(),
                "falure_detail", errorList
        );
    }

    /***
     *
     */
    public void sendSmsInGeryService(final List<String> merchantIds) {
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                //调用短信接口
                for (String merchantId : merchantIds) {
                    try {
                        boolean sendSmsSuccess = pushService.sendSms(merchantId, CollectionUtil.hashMap(
                                "template", "Eztf4",
                                "vars", CollectionUtil.hashMap(),
                                "throwException", true,
                                "exceptionMsg", "关闭短信提醒第一次发送文案短信失败"
                        ));
                        //记录日志
                        withdrawCloseSmsNoticeService.createWithdrawCloseSmsNotice(CollectionUtil.hashMap(
                                "merchant_id", merchantId,
                                "send_type", 0,
                                "send_status", (sendSmsSuccess == false ? 1 : 0)
                        ));
                    } catch (Exception e) {
                        logger.error("发送短信sendSmsInGeryService异常merchantId[{}]", merchantId, e);
                    }
                }
            }
        };
        try {
            withdrawExecutor.submit(runnable);
        } catch (Exception e) {
            logger.error("发送短信sendSmsInGeryService提交到线程池异常", e);
        }
    }


    /**
     * app推送D0通知
     *
     * @param infos
     */
    private void app_push_d0_message(List<Map> infos) {
        if (infos != null && infos.size() > 0) {
            String title = "提现实时到账功能已开通";
            String body = "尊敬的收钱吧特约商户，鉴于您在收钱吧良好的使用记录，已为您开通提现实时到账功能，详见收钱吧APP提现页。";

            //推送模板
            PushTemplate pushTemplate = new PushTemplate();

            //推送信息
            MsgDTO msgDTO = new MsgDTO();
            msgDTO.setBody(body);
            msgDTO.setTitle(title);
            pushTemplate.setMsg(msgDTO);

            //推送文件信息
            AudioDTO audioDTO = new AudioDTO();
            pushTemplate.setAudio(audioDTO);

            //推送点击定位
            LocationDTO locationDTO = new LocationDTO();
            locationDTO.setDest("wosaishouqianba://fund/wallet");
            locationDTO.setType("app");
            pushTemplate.setLocation(locationDTO);


            //公告模板
            Map notice = CollectionUtil.hashMap(
                    "title", title,
                    "content", body,
                    "author", "web-platforms-osp-d0"
            );

            int index = 1;
            for (Map map : infos) {
                logger.info("开始推送[{}/{}]", index, infos.size());
                index++;
                try {
                    String merchant_id = BeanUtil.getPropString(map, "merchant_id");
                    if (StringUtil.empty(merchant_id)) {
                        logger.info("D0白名单推送app信息map[{}]没有商户信息", map);
                        continue;
                    }
                    // 推送
                    String operatorId = "";
                    String phone = "";
                    try {
                        operatorId = accountService.getSuperAdminIdByMerchantId(merchant_id);
                        Map result = accountService.getSimpleAccountByIdList(Arrays.asList(operatorId));
                        logger.info("resul[{}]", result);
                        if (result != null && BeanUtil.getPropInt(result, "code", -1) == 10000 && result.containsKey("data")) {
                            List<User> users = JSONArray.parseArray(JacksonUtil.toJsonString(result.get("data")), User.class);
                            if (users != null && users.size() > 0) {
                                phone = users.get(0).getCellphone();
                            }
                        }
                    } catch (Exception e) {
                        logger.error("D0白名单推送app信息获取操作者失败map[{}]", map, e);
                        continue;
                    }

                    if (StringUtil.empty(operatorId)) {
                        logger.info("D0白名单推送app信息获取操作者信息为空map[{}]", map);
                        continue;
                    }
                    //推送信息
                    try {
                        pushMessageService.pushMessageToOperatorList(pushTemplate, Arrays.asList(operatorId));
                    } catch (Exception e) {
                        logger.error("D0白名单推送app信息推送异常map[{}],oeprator[{}],pushTemplate[{}]", map, operatorId, pushTemplate, e);
                    }
                    //推送公告
                    try {
                        Set<String> sets = new HashSet<>();
                        sets.add(phone);
                        noticeService.createNotice(notice, sets);
                    } catch (Exception e) {
                        logger.error("D0白名单推送公告信息推送异常map[{}],phone[{}],notice[{}]", map, phone, notice, e);
                    }
                } catch (Exception e) {
                    logger.error("D0白名单推送app信息业务异常map[{}]", map, e);
                }

                //等待一秒
                try {
                    Thread.sleep(500);
                } catch (Exception e) {
                }
            }
        }
    }


    @Override
    public Map merchantSmsCloseStatus(Map request) {
        String merchant_id = BeanUtil.getPropString(request, "merchant_id");
        Map<String, Object> checkResult = null;
        try {
            checkResult = appGatedRuleService.checkGatedRule("15", merchant_id, 302);
            Map<String, Object> data = WosaiMapUtils.getMap(checkResult, "data", null);
            String payloadString = WosaiMapUtils.getString(data, "payload", null);
            if (WosaiStringUtils.isNotEmpty(payloadString)) {
                checkResult = new ObjectMapper().readValue(payloadString, HashMap.class);
            }
        } catch (Exception e) {
            logger.error("mercahntSmsCloseStatus服务错误", e);
            checkResult = CollectionUtil.hashMap(
                    "show", false,
                    "push", true
            );
        }
        return checkResult;
    }

    @Override
    public Map getNoticeRule(Map request) {
        String code = BeanUtil.getPropString(request, NoticeRule.CODE);
        if (StringUtil.empty(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码不能为空");
        }
        if (!WithdrawUtil.NOTICE_RULE_CODES.contains(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码非系统定义code");
        }
        return noticeRuleService.getNoticeRuleByCodeAndSubCode(code, BeanUtil.getPropString(request, NoticeRule.SUB_CODE));
    }

    @Override
    public void deleteNoticeRule(Map request) {
        String code = BeanUtil.getPropString(request, NoticeRule.CODE);
        if (StringUtil.empty(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码不能为空");
        }
        if (!WithdrawUtil.NOTICE_RULE_CODES.contains(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码非系统定义code");
        }
        noticeRuleService.deleteNoticeRuleByCodeAndSubCode(code, BeanUtil.getPropString(request, NoticeRule.SUB_CODE));
    }

    @Override
    public Map updateNoticeRule(Map request) {
        String code = BeanUtil.getPropString(request, NoticeRule.CODE);
        if (StringUtil.empty(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码不能为空");
        }
        if (!WithdrawUtil.NOTICE_RULE_CODES.contains(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码非系统定义code");
        }
        return noticeRuleService.updateNoticeRule(request);
    }

    @Override
    public Map createNoticeRule(Map request) {
        String code = BeanUtil.getPropString(request, NoticeRule.CODE);
        if (StringUtil.empty(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码不能为空");
        }
        if (!WithdrawUtil.NOTICE_RULE_CODES.contains(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码非系统定义code");
        }
        return noticeRuleService.createNoticeRule(request);
    }

    @Override
    public ListResult findNoticeRules(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return noticeRuleService.findRules(pageInfo, request);
    }

    @Override
    public List<Map> getNoticeRules(Map request) {
        String code = BeanUtil.getPropString(request, NoticeRule.CODE);
        if (StringUtil.empty(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码不能为空");
        }
        if (!WithdrawUtil.NOTICE_RULE_CODES.contains(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码非系统定义code");
        }
        return noticeRuleService.getRulesBycode(code);
    }

    @Override
    public List<Map> getNoticeRuleCodes(Map request) {
        return WithdrawUtil.NOTICE_RULE_CODES_DESC;
    }

    @Override
    public Map batchDisableMerchantwithdrawrealtime(Map request) {
//        final Map notice = getNoticeRule(request);
//        if (notice == null) {
//            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则不存在!");
//        }
        final String remark = BeanUtil.getPropString(request, "remark");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        Map select_all_params = (Map) BeanUtil.getProperty(request, "select_all_params");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            merchant_ids = getWithdrawrealtimeIdsByParams(select_all_params);
        }
        if (merchant_ids == null || merchant_ids.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "禁用D0数目为0");
        }
        final String disable_time_begin = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.DISABLE_TIME_BEGIN);
        final String disable_time_end = BeanUtil.getPropString(request, MerchantWithdrawrealtimeConfig.DISABLE_TIME_END);

        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_DISABLE_D0_STATUS,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final List<String> merchant_ids_copy = merchant_ids;
        final String task_apply_log_id = BeanUtil.getPropString(task, DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        Object obj = BeanUtil.getProperty(request, "disable_reasons");
        List<Integer> disableReasons = null;
        if(obj != null) {
            disableReasons = (List<Integer>) obj;
        }
        List<Integer> finalDisableReasons = disableReasons;
        noticeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchDisableMerchantwithdrawrealtimeWithIds(merchant_ids_copy, remark, disable_time_begin, disable_time_end, MerchantWithdrawrealtimeConfig.DISABLE_WAY_SP, task_apply_log_id, businessRequest, finalDisableReasons);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }

    /**
     * 批量禁用D0状态
     *
     * @param merchant_ids
     * @param remark
     * @param disnable_time_begin
     * @param disnable_time_end
     * @param disable_way
     * @return
     */
    private List<Map> batchDisableMerchantwithdrawrealtimeWithIds(List<String> merchant_ids, String remark,  String disnable_time_begin, String disnable_time_end, int disable_way, final String task_apply_log_id, Map businessRequest,List<Integer> disableReasons) {
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> result = new ArrayList<>();
        List<Map> falureDetail = new ArrayList<>();
//        String disnable_reason = BeanUtil.getPropString(noticeRule, NoticeRule.APP_TIP);
        List<String> successIds = new ArrayList<>();
        long op_time = new Date().getTime();
        for (String merchant_id : merchant_ids) {
            try {
                Map merchantD0Old = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchant_id);
                disableMerchantWithdrawrealtimeConfig(merchant_id, remark, "", disnable_time_begin, disnable_time_end, disable_way,disableReasons);
                Map merchantD0New = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchant_id);
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", null
                ));
                successIds.add(merchant_id);
                //记录业务日志
                businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                        BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantD0Old,
                        BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantD0New,
                        BusinessLogUtil.LOG_PARAM_REQUEST, businessRequest,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_BATCH_MODIFY,
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_withdrawrealtime_config",
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                                BizOpLog.OP_TIME, op_time
                        )
                ));
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                result.add(falure);
                falureDetail.add(falure);
            }
        }
        if (falureDetail.size() > 20) {
            falureDetail = falureDetail.subList(0, 20);
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"
            ));
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap(
                        "批量禁用D0状态总数", result.size(),
                        "批量禁用D0状态成功数", successIds.size(),
                        "批量禁用D0状态失败数", result.size() - successIds.size(),
                        "批量禁用D0状态失败详情", falureDetail
                )
        ));
//        //发送通知
//        final List<String> successIds_copy = successIds;
//        noticeExecutor.submit(new Runnable() {
//            @Override
//            public void run() {
//                pushService.pushTask(CollectionUtil.hashMap(
//                        PushTask.NOTICE_RULE, noticeRule,
//                        PushTask.TASK_APPLY_LOG_ID, task_apply_log_id,
//                        PushTask.MERCHANT_IDS, successIds_copy
//                ));
//            }
//        });
        return result;
    }

    private void disableMerchantWithdrawrealtimeConfig(String merchant_id, String remark, String disnable_reason, String disnable_time_begin, String disnable_time_end, int disable_way,List<Integer> disableReasons) {
        withdrawConfigService.disableMerchantWithdrawrealtimeConfig(CollectionUtil.hashMap(
                MerchantWithdrawrealtimeConfig.MERCHANT_ID, merchant_id,
                MerchantWithdrawrealtimeConfig.REMARK, remark,
                MerchantWithdrawrealtimeConfig.DISABLE_REASON, disnable_reason,
                MerchantWithdrawrealtimeConfig.DISABLE_WAY, disable_way,
                MerchantWithdrawrealtimeConfig.DISABLE_TIME_BEGIN, disnable_time_begin,
                MerchantWithdrawrealtimeConfig.DISABLE_TIME_END, disnable_time_end
        ),disableReasons);
    }

    @Override
    public Map batchEnableMerchantwithdrawrealtime(Map request) {
        final Map notice = getNoticeRule(request);
        if (notice == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则不存在!");
        }
        final String remark = BeanUtil.getPropString(request, "remark");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        Map select_all_params = (Map) BeanUtil.getProperty(request, "select_all_params");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            merchant_ids = getWithdrawrealtimeIdsByParams(select_all_params);
        }
        if (merchant_ids == null || merchant_ids.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "启用D0数目为0");
        }
        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_ENABLE_D0_STATUS,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final List<String> merchant_ids_copy = merchant_ids;
        final String task_apply_log_id = BeanUtil.getPropString(task, DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        noticeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchEnableMerchantwithdrawrealtimeWithIds(merchant_ids_copy, remark, MerchantWithdrawrealtimeConfig.DISABLE_WAY_SP, task_apply_log_id, notice, businessRequest);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }

    /**
     * 批量禁用D0状态
     *
     * @param merchant_ids
     * @param remark
     * @param disable_way
     * @return
     */
    private List<Map> batchEnableMerchantwithdrawrealtimeWithIds(List<String> merchant_ids, String remark, int disable_way, final String task_apply_log_id, final Map noticeRule, Map businessRequest) {
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> result = new ArrayList<>();
        List<Map> falureDetail = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        long op_time = new Date().getTime();
        for (String merchant_id : merchant_ids) {
            try {
                Map merchantD0Old = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchant_id);
                checkAndEnableMerchantWithdrawRealTimeConfig(merchant_id, remark, disable_way);
                Map merchantD0New = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchant_id);
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", null
                ));
                successIds.add(merchant_id);
                //记录业务日志
                businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                        BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantD0Old,
                        BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantD0New,
                        BusinessLogUtil.LOG_PARAM_REQUEST, businessRequest,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_BATCH_MODIFY,
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_withdrawrealtime_config",
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                                BizOpLog.OP_TIME, op_time
                        )
                ));
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                result.add(falure);
                falureDetail.add(falure);
            }
        }
        if (falureDetail.size() > 20) {
            falureDetail = falureDetail.subList(0, 20);
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"
            ));
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap(
                        "批量启用D0状态总数", result.size(),
                        "批量启用D0状态成功数", successIds.size(),
                        "批量启用D0状态失败数", result.size() - successIds.size(),
                        "批量启用D0状态失败详情", falureDetail
                )
        ));
        //发送通知
        final List<String> successIds_copy = successIds;
        noticeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                pushService.pushTask(CollectionUtil.hashMap(
                        PushTask.NOTICE_RULE, noticeRule,
                        PushTask.TASK_APPLY_LOG_ID, task_apply_log_id,
                        PushTask.MERCHANT_IDS, successIds_copy
                ));
            }
        });
        return result;
    }

    private void checkAndEnableMerchantWithdrawRealTimeConfig(String merchant_id, String remark, int disable_way) {
        withdrawConfigService.checkAndEnableMerchantWithdrawRealTimeConfig(CollectionUtil.hashMap(
                MerchantWithdrawrealtimeConfig.MERCHANT_ID, merchant_id,
                MerchantWithdrawrealtimeConfig.REMARK, remark,
                MerchantWithdrawrealtimeConfig.DISABLE_WAY, disable_way
        ));
    }


    @Override
    public Map batchImportForceClearMerchants(MultipartFile file) {
        String userId = "";
        String userName = "";
        try {
            Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
            userId = BeanUtil.getPropString(user, DaoConstants.ID);
            userName = BeanUtil.getPropString(user, Account.USERNAME);

        } catch (Exception e) {
            throw new UpayException(UpayException.CODE_NEED_LOGIN, "请先登录");
        }

        //file转为excel
        Workbook workbook = getExcelFromFile(file);
        if (workbook == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel格式解析异常");
        }

        /**
         * 解析成list
         */
        final List<Map> merchantes_request = extraDataFromExcelOfForceClearMerchants(workbook);
        if (merchantes_request == null || merchantes_request.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "空excel没有商户数据");
        }

        if (merchantes_request.size() > 500) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过500条");
        }

        for(Map merchant : merchantes_request){
            int merchantStatus = BeanUtil.getPropInt(merchant, Merchant.STATUS);
            if(merchantStatus == Merchant.STATUS_ENABLED || merchantStatus == Merchant.STATUS_DISABLED){
               throw new UpayException(UpayException.CODE_INVALID_PARAMETER,"不支持进行批量禁用、开启商户");
            }
        }

        Map params = CollectionUtil.hashMap(
                "userId", userId,
                "userName", userName
        );
        Map result = importOfForceClearMerchants(merchantes_request, params);
        //记录日志
        logger.info("强制结算日志结果日志记录request[{}],params[{}],result[{}]", merchantes_request, params, result);
        Map taskApplyLog = this.createTaskApplyLog(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, 6,
                TaskApplyLog.APPLY_SYSTEM, 2,
                TaskApplyLog.APPLY_DATE, new java.sql.Date(new Date().getTime()),
                TaskApplyLog.PAYLOAD, result,
                TaskApplyLog.USER_ID, userId,
                TaskApplyLog.APPLY_STATUS, 2
        ));
        logger.info("强制结算日志结果日志记录taskApplyLog[{}]", BeanUtil.getPropString(taskApplyLog, DaoConstants.ID));
        HttpRequestUtil.getSession().setAttribute(SESSION_IMPORT_MERCHANT_FORCE_CLEAR, BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID));
        return result;
    }

    private void updateTaskApplyLogStatus(String id, int status, Object resultUrl) {
        Map taskApplyLog = logService.getTaskApplyLog(id);
        taskApplyLog.put(TaskApplyLog.APPLY_STATUS, status);
        if (resultUrl != null) {
            taskApplyLog.put(TaskApplyLog.APPLY_RESULT, resultUrl);
        }
        logService.updateTaskApplyLog(taskApplyLog);
    }

    private List<Map> extraDataFromExcelOfForceClearMerchants(Workbook hssfWorkbook) {
        List<Map> merchantInfos = new ArrayList<Map>();
        try {
            // 循环Sheet
            Sheet hssfSheet = hssfWorkbook.getSheetAt(0);
            if (hssfSheet == null) {
                return null;
            }
            // 循环行Row
            for (int rowNum = 1; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                Row hssfRow = hssfSheet.getRow(rowNum);
                if (hssfRow == null) {
                    continue;
                }
                String merchantSn = toString(hssfRow.getCell(0)).replaceAll(" ", "");
                if (merchantSn.indexOf(".") != -1) {
                    merchantSn = merchantSn.substring(0, merchantSn.indexOf(".")); // 防止解析为小数，去掉0
                }
                if (!StringUtil.empty(merchantSn)) {
                    merchantInfos.add(CollectionUtil.hashMap(
                            "sn", merchantSn,
                            "status", toInt(hssfRow.getCell(1), -100),
                            "forceClear", toInt(hssfRow.getCell(2), 2),
                            "withdrawFrozen", toInt(hssfRow.getCell(3), 2),
                            "merchantWithdraw", toInt(hssfRow.getCell(4), 3),
                            "remark", toString(hssfRow.getCell(5)),
                            MerchantWithdrawConfig.DISABLE_REASON, toString(hssfRow.getCell(6))
                    ));
                }
            }
            return merchantInfos;
        } catch (Exception e) {
            logger.error("解析强制结算excel失败", e);
            return null;
        }
    }

    private Workbook getExcelFromFile(MultipartFile file) {
        if (file == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "必须上传文件");
        }
        String fileName = file.getOriginalFilename();
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex == -1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        String type = fileName.substring(lastIndex + 1, fileName.length()).toLowerCase();
        if (!"xls".equals(type) && !"xlsx".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        InputStream is = null;
        Workbook workbook = null;
        try {
            is = file.getInputStream();
            if ("xls".equals(type)) {
                workbook = new HSSFWorkbook(is);
            } else if ("xlsx".equals(type)) {
                workbook = new XSSFWorkbook(is);
            }
        } catch (Exception e) {
            logger.error("excel格式解析不支持", e);
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel格式解析不支持");
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                }
            }
        }
        return workbook;
    }

    /**
     * 导入逻辑
     * 1.去除重复的商户和商户号为空的
     * 2.去除无备注信息商户
     * 3.去除无变更状态且不需强制结算商户
     * 4.去除商户不存在信息
     * 5.导入
     * @param merchantes
     */
    private Map importOfForceClearMerchants(List<Map> merchantes, Map params) {
        String username = BeanUtil.getPropString(params, "userName");
        int total = merchantes.size();
        //没有通过校验（条件1、2、3、4）
        List<Map> merchants_unValid = new ArrayList<>();
        //通过检验的
        List<String> merchant_success = new ArrayList<>();
        //没有通过校验
        List merchant_status = Arrays.asList(Merchant.STATUS_CLOSED, Merchant.STATUS_ENABLED, Merchant.STATUS_DISABLED);

        //校验
        for (int i = 0; i < merchantes.size(); i++) {
            String merchant_sn = BeanUtil.getPropString(merchantes.get(i), Merchant.SN, "");
            //商户号为空
            if (StringUtil.empty(merchant_sn)) {
                merchantes.get(i).put("errmsg", "商户号为空");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //备注信息不存在
            if (StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), "remark"))) {
                merchantes.get(i).put("errmsg", "商户备注信息不存在");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //去重
            if (merchant_success.contains(merchant_sn)) {
                merchantes.get(i).put("errmsg", "excel中重复商户");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }

            //无操作
            if (!merchant_status.contains(BeanUtil.getPropInt(merchantes.get(i), Merchant.STATUS)) &&
                    BeanUtil.getPropInt(merchantes.get(i), "forceClear") != 1 &&
                    BeanUtil.getPropInt(merchantes.get(i), "withdrawFrozen") != 1 &&
                    !Arrays.asList(1, 2).contains(BeanUtil.getPropInt(merchantes.get(i), "merchantWithdraw"))) {
                merchantes.get(i).put("errmsg", "无操作");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }

            //1为关闭商户余额转出功能 需要有文案
            if (BeanUtil.getPropInt(merchantes.get(i), "merchantWithdraw") == 1 && StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), MerchantWithdrawConfig.DISABLE_REASON))) {
                merchantes.get(i).put("errmsg", "禁用商户提现操作、无禁用文案");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }

            //通过校验的
            merchant_success.add(merchant_sn);

        }
        //商户信息补全
        final ListResult merchantInfos = merchant_success.size() == 0 ? new ListResult(0, new ArrayList<Map>()) : merchantService.findMerchants(new PageInfo(1, merchant_success.size()), CollectionUtil.hashMap("merchant_sns", merchant_success));
        Map merchantInfosMap = new HashMap() {{
            for (Map map : merchantInfos.getRecords()) {
                put(BeanUtil.getPropString(map, Merchant.SN), map);
            }
        }};
        for (int i = 0; i < merchantes.size(); i++) {
            if (!merchantInfosMap.containsKey(BeanUtil.getPropString(merchantes.get(i), Merchant.SN))) {
                merchantes.get(i).put("errmsg", "商户sn不存在");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
        }

        Map httpReques = HttpRequestUtil.getBusinessogRequest();
        long now = new Date().getTime();

        //执行状态和提现操作
        for (int i = 0; i < merchantes.size(); i++) {
            String merchantSn = BeanUtil.getPropString(merchantes.get(i), Merchant.SN);
            Map merchant = (Map) BeanUtil.getProperty(merchantInfosMap, merchantSn);
            int status = BeanUtil.getPropInt(merchantes.get(i), Merchant.STATUS);
            String remark = BeanUtil.getPropString(merchantes.get(i), "remark");
            boolean forceClear = BeanUtil.getPropInt(merchantes.get(i), "forceClear") == 1;
            String merchant_id = BeanUtil.getPropString(merchant, DaoConstants.ID);
            int merchanStatus = BeanUtil.getPropInt(merchant, Merchant.STATUS);
            long balance = getBalance(merchant_id);
            boolean ok = true;
            int withdrawFrozen = BeanUtil.getPropInt(merchantes.get(i), "withdrawFrozen");
            int merchantWithdraw = BeanUtil.getPropInt(merchantes.get(i), "merchantWithdraw");
            String errorMsg = "";
            String op_id = BusinessLogUtil.uuid();
            //强制更改状态
            if (merchant_status.contains(status) && status != merchanStatus) {
                try {
                    if (status == Merchant.STATUS_CLOSED) {
                        merchantService.closeMerchant(merchant_id);
                    } else if (status == Merchant.STATUS_ENABLED) {
                        merchantService.enableMerchant(merchant_id);
                    } else if (status == Merchant.STATUS_DISABLED) {
                        merchantService.disableMerchant(merchant_id);
                    }
                    //清理交易缓存
                    supportService.removeCachedParams(merchantSn);
                    errorMsg = errorMsg + "状态：success ";
                } catch (Exception e) {
                    logger.error("强制结算修改商户转改失败", e);
                    errorMsg = errorMsg + "状态：" + e.getMessage();
                    ok = false;
                }
            } else if (merchant_status.contains(status) && status == merchanStatus) {
                errorMsg = errorMsg + "状态：状态已是" + status + " ";
            } else {
                errorMsg = errorMsg + "状态：无操作 ";
            }

            //强制结算
            if (forceClear && balance > 0) {
                try {
                    Map withdrawResult = withdrawRealTimeService.submitWithdraw(CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchant_id,
                            "amount", balance,
                            "withdraw_mode", 3,
                            "type", 0,
                            "needAllowWithdraw", false
                    ));
                    String withdraw_id = BeanUtil.getPropString(withdrawResult, DaoConstants.ID);
                    if (StringUtil.empty(withdraw_id)) {
                        errorMsg = errorMsg + "结算：" + withdrawResult + " ";
                        ok = false;
                    } else {
                        try {
                            withdrawRealTimeService.updateWithdrawRemark(withdraw_id, remark, username);
                        } catch (Exception e) {
                            logger.error("更改提现备注失败withdraw_id[{}],remark[{}],username[{}]", withdraw_id, remark, username, e);
                        }
                        errorMsg = errorMsg + "结算：success ";
                    }
                } catch (Exception e) {
                    errorMsg = errorMsg + " 结算：" + e.getMessage();
                    ok = false;
                }
            } else if (forceClear && balance <= 0) {
                errorMsg = errorMsg + " 结算：商户余额已为0 ";
            } else {
                errorMsg = errorMsg + " 结算：无操作 ";
            }

            //冻结审核中提现
            if (withdrawFrozen == 1) {
                try {
                    int size = 0;
                    ListResult result = withdrawRealTimeService.findWithdraws(new PageInfo(1, 100), CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchant_id,
                            "op_check_status", 1
                    ));
                    if (result != null && result.getRecords() != null && result.getRecords().size() > 0) {
                        size = size + result.getRecords().size();
                        for (Map withdraw : result.getRecords()) {
                            changeWithDrawOpCheckStatus(3, BeanUtil.getPropString(withdraw, DaoConstants.ID), remark, username);
                        }
                    }
                    result = withdrawRealTimeService.findWithdraws(new PageInfo(1, 100), CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchant_id,
                            "op_check_status", 4
                    ));
                    if (result != null && result.getRecords() != null && result.getRecords().size() > 0) {
                        size = size + result.getRecords().size();
                        for (Map withdraw : result.getRecords()) {
                            changeWithDrawOpCheckStatus(3, BeanUtil.getPropString(withdraw, DaoConstants.ID), remark, username);
                        }
                    }
                    errorMsg = errorMsg + " 冻结成功:" + size + "个 ";
                } catch (Exception e) {
                    errorMsg = errorMsg + "冻结失败：" + e.getMessage();
                    ok = false;
                }
            } else {
                errorMsg = errorMsg + "冻结：无操作 ";
            }

            //开启商户余额转出功能
            if (merchantWithdraw == 2) {
                try {
                    allowWalletTurnOut(new HashMap<String, Object>(){{
                        put(MerchantWithdrawConfig.MERCHANT_ID, merchant_id);
                        put(MerchantWithdrawConfig.REMARK, remark);
                    }});
                } catch (Exception e) {
                    errorMsg = errorMsg + "禁用余额转出失败：" + e.getMessage();
                    ok = false;
                }
            } else if (merchantWithdraw == 1) {
                //关闭商户余额转出功能
                try {
                    Map<String,Object> forbidMap = new HashMap<>();
                    forbidMap.put("app_hint", BeanUtil.getPropString(merchantes.get(i), MerchantWithdrawConfig.DISABLE_REASON));
                    forbidMap.put(MerchantWithdrawConfig.REMARK, remark);
                    forbidMap.put(MerchantWithdrawConfig.MERCHANT_ID, merchant_id);
                    forbidWalletTurnOut(forbidMap);
                } catch (Exception e) {
                    errorMsg = errorMsg + "启用余额转出失败：" + e.getMessage();
                    ok = false;
                }
            } else {
                errorMsg = errorMsg + "余额转出：无操作 ";
            }


            //记录商户日志
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchant,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantService.getMerchant(merchant_id),
                    BusinessLogUtil.LOG_PARAM_REQUEST, httpReques,
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID,
                            BizOpLog.REMARK, remark,
                            BizOpLog.OP_ID, op_id
                    )
            ));
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchant_id,
                            "balance", balance
                    ),
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchant_id,
                            "balance", getBalance(merchant_id)
                    ),
                    BusinessLogUtil.LOG_PARAM_REQUEST, httpReques,
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "wallet",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, ConstantUtil.KEY_MERCHANT_ID,
                            BizOpLog.REMARK, remark,
                            BizOpLog.OP_ID, op_id
                    )
            ));
            if (!ok) {
                merchantes.get(i).put("errmsg", errorMsg);
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
        }

        //失败列表去除多余key
        //for (int i = 0; i < merchants_unValid.size(); i++) {
//            merchants_unValid.get(i).remove(Merchant.STATUS);
//            merchants_unValid.get(i).remove("forceClear");
//            merchants_unValid.get(i).remove("remark");
        //}

        //保存导入结果到任务
        Map<String, Object> result = new HashedMap();
        result.put("total", total);
        result.put("success", total - merchants_unValid.size());
        result.put("failure", merchants_unValid.size());
        result.put("failureDetail", merchants_unValid);
        return result;
        //result.put("failureDetail", merchants_unValid);
//        try {
//            updateTaskApplyLogStatus(BeanUtil.getPropString(params, "task_apply_log_id"), 2 , result);
//        }catch (Exception e){
//            updateTaskApplyLogStatus(BeanUtil.getPropString(params, "task_apply_log_id"), 2 , result);
//            logger.error("强制结算保存结果异常, 结果[{}]", result ,e);
//        }
    }

    private long getBalance(String merchant_id) {
        try {
            return walletService.getBalance(merchant_id);
        } catch (Exception e) {
            logger.error("获取商户余额异常merchant_id[{}]", merchant_id, e);
        }
        return 0;
    }

    @Override
    public void getImportForceClearMerchantsDetail(Map<String, Object> request, HttpServletResponse response) throws IOException {
        Map payload = getTaskPayload(request, SESSION_IMPORT_MERCHANT_FORCE_CLEAR);
        List<Map> errors = new ArrayList<>();
        if (payload == null || !payload.containsKey("failureDetail")) {
            errors.add(CollectionUtil.hashMap("merchant_sn", "", "errmsg", "导入商户错误过多，无法保存，已记录在日志中，请查看相关日志"));
        } else {
            errors = (List<Map>) BeanUtil.getProperty(payload, "failureDetail");
        }
        HSSFWorkbook workbook = buildExcelDetail(errors, "强制结算失败商户", Arrays.asList("商户号", "失败原因"), Arrays.asList("sn", "errmsg"));
        String fileName = new Date().getTime() + "-importFail.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }

    private Map getTaskPayload(Map request, String sessionKey) {
        try {
            String logId = (String) HttpRequestUtil.getSession().getAttribute(sessionKey);
            if (logId != null) {
                Map log = logService.getTaskApplyLog(logId);
                if (log != null) {
                    return (Map) log.get(TaskApplyLog.PAYLOAD);
                }
            }
        } catch (Exception e) {
        }
        return null;
    }

    private HSSFWorkbook buildExcelDetail(List<Map> list, String sheetName, List<String> headers, List<String> keys) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet(sheetName);
        buildExcelDetail(sheet, headers, list, keys);
        return workbook;
    }

    private void buildExcelDetail(HSSFSheet sheet, List<String> headers, List<Map> list, List<String> keys) {
        SheetUtil sheetUtil = new SheetUtil(sheet);
        if (headers != null) {
            sheetUtil.appendRow(headers);
        }
        if (list != null) {
            for (Map map : list) {
                List values = new ArrayList();
                if (keys != null) {
                    for (String key : keys) {
                        values.add(BeanUtil.getProperty(map, key));
                    }
                } else {
                    map = map == null ? new HashMap() : map;
                    Set<String> mKeys = map.keySet();
                    for (String key : mKeys) {
                        values.add(BeanUtil.getProperty(map, key));
                    }
                }
                sheetUtil.appendRow(values);
            }
        }
    }

    @Override
    public Map getMerchantAnalyzedD1WithdrawBatch(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        return d1WithdrawBatchService.getMerchantAnalyzedD1WithdrawBatch(merchantId);
    }

    @Override
    public Map getD1WithdrawBatch(Map request) {
        String batchId = BeanUtil.getPropString(request, ConstantUtil.KEY_ID);
        return d1WithdrawBatchService.getD1WithdrawBatch(batchId);
    }

    @Override
    @SuppressWarnings("unchecked")
    public ListResult findD1WithdrawBatchs(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return d1WithdrawBatchService.findD1WithdrawBatchs(pageInfo, request);
    }

    @Override
    public Map updateD1WithdrawBatch(Map request) {
        String batchId = BeanUtil.getPropString(request, ConstantUtil.KEY_ID);
        if (StringUtil.empty(batchId)) {
            return d1WithdrawBatchService.createD1WithdrawBatch(request);
        }
        return d1WithdrawBatchService.updateD1WithdrawBatch(request);
    }

    @Override
    public void deleteD1WithdrawBatch(Map request) {
        String batchId = BeanUtil.getPropString(request, "batch_id");
        d1WithdrawBatchService.deleteD1WithdrawBatch(batchId);
    }

    /*****************************批量导入优先白名单代码开始************************************/
    @Override
    public Map importPriorMerchantWhiteList(final MultipartFile multipartFile) {
        // 导入优先批次商户白名单任务
        Map taskApplyLog = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_IMPORT_D1_PRIOR_MERCHANTS,
                TaskApplyLog.APPLY_SYSTEM, TaskApplyLogUtil.APPLY_SYSTEM_SP
        ));
        final String taskApplyLogId = BeanUtil.getPropString(taskApplyLog, DaoConstants.ID);

        final Map businessLogRequest = HttpRequestUtil.getBusinessogRequest();

        importExecutor.submit(new Runnable() {
            @Override
            public void run() {
                importMerchantWhiteList(multipartFile, taskApplyLogId, businessLogRequest);
            }
        });
        return CollectionUtil.hashMap("task_id", taskApplyLogId, "task_apply_log_id", taskApplyLogId);
    }

    private void importMerchantWhiteList(MultipartFile multipartFile, String taskApplyLogId, Map businessLogRequest) {
        // 校验上传文件
        validUploadFile(multipartFile);

        File file = new File(System.getProperty("java.io.tmpdir") + "/" + CrudUtil.randomUuid() + multipartFile.getName());
        // 解析文件并保存到数据库中
        List<Map<String, Object>> merchantWhiteList;
        try {
            // 读取并解析文件
            multipartFile.transferTo(file);
            merchantWhiteList = readXlsx(file);
        } catch (Exception e) {
            throw new WithdrawBizErrorException("文件解析失败: " + e.getMessage() + ", 请重试");
        } finally {
            if (file.exists()) {
                // 上传后需要删除掉
                file.delete();
            }
        }

        if (!WosaiCollectionUtils.isEmpty(merchantWhiteList)) {
            // 记录旧名单中原有的商户ID
            Map<String, Object> changeMerchants = checkMerchantsInOldBatch(merchantWhiteList);
            // 删除数据库原有记录并记录商户日志
            deleteD1WhiteListAndSendLog(businessLogRequest);

            int fail = 0;
            Map<String, Object> failMerchants = new HashMap<>();
            for (Map<String, Object> entity : merchantWhiteList) {
                String merchantNo = BeanUtil.getPropString(entity, MerchantD1WithdrawBatch.MERCHANT_NO);
                Map<String, Object> merchant =
                        merchantService.getMerchantByMerchantSn(merchantNo);
                if (MapUtils.isEmpty(merchant)) {
                    fail++;
                    logger.warn("商户号为:[{}]的商户为空", merchantNo);
                    failMerchants.put(merchantNo, "不存在该商户");
                    continue;
                }
                String merchantId = BeanUtil.getPropString(merchant, "id");
                String batchName = BeanUtil.getPropString(entity, MerchantD1WithdrawBatch.BATCH_NAME);
                String batchId = BeanUtil.getPropString(
                        d1WithdrawBatchService.getD1WithdrawBatchByBatchName(batchName), "id"
                );
                if (StringUtil.empty(batchId)) {
                    fail++;
                    logger.warn("商户号为:[{}]的批次号为空,请检查批次名称:[{}]", merchantNo, batchName);
                    failMerchants.put(merchantNo, "查询不到名称为[" + batchName + "]的批次号");
                    continue;
                }

                String remark = BeanUtil.getPropString(entity, MerchantD1WithdrawBatch.REMARK);
                Map<String, Object> d1Merchant = CollectionUtil.hashMap(
                        MerchantD1WithdrawBatch.MERCHANT_ID, merchantId,
                        MerchantD1WithdrawBatch.BATCH_ID, batchId,
                        "batch_name", batchName,
                        MerchantD1WithdrawBatch.REMARK, remark
                );

                try {
                    Map<String, Object> oldD1Merchant = (Map<String, Object>) BeanUtil.getProperty(changeMerchants, merchantId);
                    d1WithdrawBatchService.createMerchantD1WithdrawBatch(d1Merchant);
                    int opType;
                    if (MapUtils.isEmpty(oldD1Merchant) || Objects.equals(oldD1Merchant.get("batch_name"), d1Merchant.get("batch_name"))) {
                        opType = BizOpLog.OP_TYPE_ADD;
                        oldD1Merchant = null;
                    } else {
                        opType = BizOpLog.OP_TYPE_MODIFY;
                    }
                    //记录商户日志
                    businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                            BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, oldD1Merchant,
                            BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, d1Merchant,
                            BusinessLogUtil.LOG_PARAM_REQUEST, businessLogRequest,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                    BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                    BizOpLog.OP_TYPE, opType,
                                    BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_d1_withdraw_batch",
                                    BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                                    BizOpLog.REMARK, remark,
                                    BizOpLog.OP_ID, BusinessLogUtil.uuid()
                            )
                    ));
                } catch (Exception e) {
                    fail++;
                    logger.warn("商户号为[{}]导入失败:{}", merchantNo, e.getMessage());
                    failMerchants.put(merchantNo, e.getMessage());
                }
            }
            ospTaskService.updateTask(CollectionUtil.hashMap(
                    DaoConstants.ID, taskApplyLogId,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                    TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap(
                            "导入商户数", merchantWhiteList.size(),
                            "导入成功商户数", merchantWhiteList.size() - fail,
                            "导入失败商户数", fail,
                            "失败详情", failMerchants
                    )
            ));
        }
    }

    private Map<String, Object> checkMerchantsInOldBatch(List<Map<String, Object>> newBatchMerchants) {
        Map<String, Object> oldBatchMerchants = new HashMap<>();
        for (Map<String, Object> newMerchant : newBatchMerchants) {
            String merchantNo = BeanUtil.getPropString(newMerchant, MerchantD1WithdrawBatch.MERCHANT_NO);
            Map<String, Object> merchant = merchantService.getMerchantByMerchantSn(merchantNo);
            String merchantId = BeanUtil.getPropString(merchant, "id");
            Map<String, Object> oldMerchant = d1WithdrawBatchService.getMerchantD1WithdrawBatchByMerchantId(merchantId);
            if (!MapUtils.isEmpty(oldMerchant)) {
                oldBatchMerchants.put(merchantId, oldMerchant);
            }
        }
        return oldBatchMerchants;
    }

    private void deleteD1WhiteListAndSendLog(Map businessLogRequest) {
        PageInfo pageInfo = new PageInfo(1, 999, 1511923134000L, DateTimeUtil.getOneDayEnd(System.currentTimeMillis()));
        ListResult oldD1WihteListResult = d1WithdrawBatchService.findMerchantD1WithdrawBatchs(pageInfo, new HashMap());
        if (oldD1WihteListResult == null) {
            return;
        }
        List<Map> oldD1WihteList = oldD1WihteListResult.getRecords();
        while (!WosaiCollectionUtils.isEmpty(oldD1WihteList)) {
            for (Map oldD1White : oldD1WihteList) {
                String id = BeanUtil.getPropString(oldD1White, "id");
                d1WithdrawBatchService.deleteMerchantD1WithdrawBatch(id);
                //记录商户日志
                businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                        BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, oldD1White,
                        BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, null,
                        BusinessLogUtil.LOG_PARAM_REQUEST, businessLogRequest,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_DEL,
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_d1_withdraw_batch",
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                                BizOpLog.REMARK, "系统自动删除",
                                BizOpLog.OP_ID, BusinessLogUtil.uuid()
                        )
                ));

            }
            oldD1WihteList = d1WithdrawBatchService.findMerchantD1WithdrawBatchs(pageInfo, new HashMap()).getRecords();
        }
    }

    private void validUploadFile(MultipartFile file) {
        if (file == null) {
            throw new WithdrawBizErrorException("导入白名单失败, 文件上传为空");
        }
        String fileName = file.getOriginalFilename();
        if (StringUtil.empty(fileName) || (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx"))) {
            throw new WithdrawBizErrorException("文件类型错误，支持上传.xls .xlsx格式文件");
        }
    }

    private List<Map<String, Object>> readXlsx(File file) throws Exception {
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(file);
        List<Map<String, Object>> result = new LinkedList<>();
        // 循环工作表Sheet
        for (int numSheet = 0; numSheet < xssfWorkbook.getNumberOfSheets(); numSheet++) {
            XSSFSheet xssfSheet = xssfWorkbook.getSheetAt(numSheet);
            if (xssfSheet == null) {
                continue;
            }

            boolean title = true;
            // 循环行Row
            for (int rowNum = 0; rowNum <= xssfSheet.getLastRowNum(); rowNum++) {
                XSSFRow xssfRow = xssfSheet.getRow(rowNum);
                if (xssfRow == null || title) {
                    title = false;
                    continue;
                }

                // 循环列Cell
                Map<String, Object> merchantBatch = new HashMap<>();
                for (int cellNum = 0; cellNum < 3; cellNum++) {
                    XSSFCell xssfCell = xssfRow.getCell(cellNum);
                    merchantBatch.put(COLUMN_NAME[cellNum], getValue(xssfCell));
                }
                result.add(merchantBatch);
            }
        }
        return result;
    }

    @SuppressWarnings("static-access")
    private String getValue(XSSFCell xssfCell) {
        if (xssfCell == null) {
            return null;
        }
        if (xssfCell.getCellType() == xssfCell.CELL_TYPE_BOOLEAN) {
            return String.valueOf(xssfCell.getBooleanCellValue());
        } else if (xssfCell.getCellType() == xssfCell.CELL_TYPE_NUMERIC) {
            DecimalFormat df = new DecimalFormat("0");
            return df.format(xssfCell.getNumericCellValue());
        } else {
            return String.valueOf(xssfCell.getStringCellValue());
        }
    }

    /*****************************批量导入优先白名单代码结束************************************/

    /**
     * 查询禁用原因
     * @param request
     * @return
     */
    public List<Map<String, Object>> queryMerchantDisableReason(Map request){
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        return withdrawConfigService.queryMerchantDisableReason(merchantId);
    }

    /**
     * 查询商户d0入口是否可见
     * @param request
     * @return
     */
    public boolean queryMerchantD0EntranceVisible(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        return withdrawConfigService.queryMerchantD0EntranceVisible(merchantId);
    }

    /**
     * 获取商户节假日大额提现拆分白名单
     * @param request
     * @return
     */
    public Map getLargeAmountWithdrawSpConfig(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        return withdrawConfigService.getLargeAmountWithdrawSpConfig(merchantId);
    }

    /**
     * 商户关闭节假日大额提现拆分白名单设置
     * @param request
     */
    public void closeLargeAmountWithdrawSpConfig(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        HttpSession session = HttpRequestUtil.getSession();
        Map<String, String> user = CollectionUtil.hashMap(
                "op_user_id",session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID),
                "op_user_name",session.getAttribute(CommonLoginService.SESSION_USERNAME)
        );
        logger.info("关闭大额提现拆分,request[{}],user:[{}]", request, user);
        withdrawConfigService.closeLargeAmountWithdrawSpConfig(merchantId,user,"SP");
    }

    /**
     * 商户激活假日大额提现拆分白名单设置
     * @param request
     */
    public void enableLargeAmountWithdrawSpConfig(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        HttpSession session = HttpRequestUtil.getSession();
        Map<String, String> user = CollectionUtil.hashMap(
                "op_user_id",session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID),
                "op_user_name",session.getAttribute(CommonLoginService.SESSION_USERNAME)
        );
        logger.info("开启大额提现拆分,request[{}],user:[{}]", request, user);
        withdrawConfigService.enableLargeAmountWithdrawSpConfig(merchantId,user,"SP");
    }

    /**
     * 查询禁用原因
     * @param request
     */
    public Map queryD0DisableReason(Map request) {
        Integer id = BeanUtil.getPropInt(request, ConstantUtil.KEY_ID);
        return withdrawConfigService.queryD0DisableReason(id);
    }

    @Override
    public Map queryMerchantD1Config(Map<String,Object> request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        return withdrawConfigService.getMerchantD1WithdrawConfig(merchantId);
    }

    @Override
    public void addMerchantD1Config(Map<String, Object> request) {
        withdrawConfigService.addMerchantD1WithdrawConfig(request);
    }

    @Override
    public void updateMerchantD1Config(Map<String, Object> request) {
        withdrawConfigService.updateMerchantD1WithdrawConfig(request);
    }

    @Override
    public List<Map<String, Object>> getAllD1BatchList() {
        return d1WithdrawBatchService.getAllD1WithdrawBatch();
    }

    @Override
    public void withdrawOperatorTransferWaitLakalaRepairPay(Map<String, Object> request) {
        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        String username = BeanUtil.getPropString(user, Account.USERNAME);
        String withdrawId = BeanUtil.getPropString(request,DaoConstants.ID);
        clearanceService.withdrawOperatorWaitLakalaReTransfer(withdrawId,"申请补划款",username);
    }

    @Override
    public Map batchDeleteMerchantD0DisableReason(MultipartFile file) {
        if (file == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "必须上传文件");
        }
        String fileName = file.getOriginalFilename();
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex == -1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        String type = fileName.substring(lastIndex + 1).toLowerCase();
        if (!"xls".equals(type) && !"xlsx".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        InputStream is = null;
        Workbook workbook;
        try {
            is = file.getInputStream();
            if ("xls".equals(type)) {
                workbook = new HSSFWorkbook(is);
            } else{
                workbook = new XSSFWorkbook(is);
            }
        } catch (Exception e) {
            logger.error("excel格式解析不支持", e);
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel格式解析不支持");
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException ignored) {}
            }
        }
        //解析成list
        final List<Map> merchant_request = extraDeleteD0DisableDataFromExcel(workbook);
        if (merchant_request == null || merchant_request.size() <= 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "空excel没有商户数据");
        }
        if (merchant_request.size() > 2000) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过2000条");
        }

        //创建任务
        Map taskApplyLog = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_DELETE_MERCHANT_D0_DISABLE_REASON
        ));

        final String taskId = BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID);

        String userId = String.valueOf(HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID));
        String username = String.valueOf(HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME));
        //异步执行导入操作
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                try {
                    batchDeleteMerchantD0DisableReason(merchant_request, taskId, userId, username);
                } catch (Exception e) {
                    logger.error("importDrawRealTime() error", e);
                    ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                            TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                            TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "导入过程中发生错误")));
                }
            }
        };
        try {
            withdrawExecutor.submit(runnable);
        } catch (Exception e) {
            logger.error("submit importDrawRealTime() to threadPool error", e);
            ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                    TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "提交导入异步任务发生错误")));
        }

        return CollectionUtil.hashMap("taskId", taskId, "task_apply_log_id", taskId);
    }

    @Override
    public void forbidWalletTurnOut(Map<String, Object> map) {
        String merchantId = BeanUtil.getPropString(map,"merchant_id");
        String remark=BeanUtil.getPropString(map,"remark");
        String appHint = BeanUtil.getPropString(map,"app_hint");
        if(StringUtil.empty(appHint)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "提示文案不容许为空");
        }
        Set<Integer> forbidType = new HashSet<Integer>(){{
            add(EventLog.TYPE_DRAW);
            add(EventLog.TYPE_BUY_FINANCIAL_PRODUCT);
            add(EventLog.TYPE_FINANCIAL_MANUAL_BUY);
        }};

        merchantLimitAuthorizationService.batchAddMerchantLimitAuthorization(merchantId,forbidType,appHint);
        //记录商户日志
        String userId = String.valueOf(HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID));
        String username = String.valueOf(HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME));
        String functionCode = "1000102";
        String opId = BusinessLogUtil.uuid();
        Map businessLog = CollectionUtil.hashMap(
                BizOpLog.BUSINESS_OBJECT_CODE,"merchant",
                BizOpLog.BUSINESS_OBJECT_COLUMN_CODE,"merchant_limit_authorization#remarks",
                BizOpLog.BUSINESS_SYSTEM_CODE,BusinessLogUtil.BUSINESS_SYSTEM_CODE_SP,
                BizOpLog.BUSINESS_SYSTEM_VERSION,BusinessLogUtil.BUSINESS_SYSTEM_VERSION_SP,
                BizOpLog.BUSINESS_FUNCTION_CODE, functionCode,
                BizOpLog.OP_USER_ID, userId,
                BizOpLog.OP_USER_NAME, username,
                BizOpLog.REMARK, remark,
                BizOpLog.OP_COLUMN_VALUE_BEFORE,null,
                BizOpLog.OP_COLUMN_VALUE_AFTER, appHint,
                BizOpLog.OP_ID, opId,
                BizOpLog.OP_OBJECT_ID,merchantId,
                BizOpLog.OP_TIME,new Date().getTime(),
                BizOpLog.OP_TYPE,BizOpLog.OP_TYPE_MODIFY);
        businessLogService.createBizOpLog(businessLog);
    }

    @Override
    public void allowWalletTurnOut(Map<String, Object> map) {
        String merchantId = BeanUtil.getPropString(map,"merchant_id");
        String remark=BeanUtil.getPropString(map,"remark");
        Map<String,Object> before = getAllowWalletTurnOutStatus(map);
        Set<Integer> forbidType = new HashSet<Integer>(){{
            add(EventLog.TYPE_DRAW);
            add(EventLog.TYPE_BUY_FINANCIAL_PRODUCT);
            add(EventLog.TYPE_FINANCIAL_MANUAL_BUY);
        }};
        merchantLimitAuthorizationService.batchDeleteMerchantLimitAuthorization(merchantId,forbidType);
        //记录商户日志
        String userId = String.valueOf(HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID));
        String username = String.valueOf(HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME));
        String functionCode = "1000103";
        String opId = BusinessLogUtil.uuid();
        Map businessLog = CollectionUtil.hashMap(
                BizOpLog.BUSINESS_OBJECT_CODE,"merchant",
                BizOpLog.BUSINESS_OBJECT_COLUMN_CODE,"merchant_limit_authorization#remarks",
                BizOpLog.BUSINESS_SYSTEM_CODE,BusinessLogUtil.BUSINESS_SYSTEM_CODE_SP,
                BizOpLog.BUSINESS_SYSTEM_VERSION,BusinessLogUtil.BUSINESS_SYSTEM_VERSION_SP,
                BizOpLog.BUSINESS_FUNCTION_CODE, functionCode,
                BizOpLog.OP_USER_ID, userId,
                BizOpLog.OP_USER_NAME, username,
                BizOpLog.REMARK, remark,
                BizOpLog.OP_COLUMN_VALUE_BEFORE,BeanUtil.getPropString(before,"app_hint",""),
                BizOpLog.OP_COLUMN_VALUE_AFTER, null,
                BizOpLog.OP_ID, opId,
                BizOpLog.OP_OBJECT_ID,merchantId,
                BizOpLog.OP_TIME,new Date().getTime(),
                BizOpLog.OP_TYPE,BizOpLog.OP_TYPE_MODIFY);
        businessLogService.createBizOpLog(businessLog);
    }

    @Override
    public Map getAllowWalletTurnOutStatus(Map<String, Object> map) {
        String merchantId = BeanUtil.getPropString(map,"merchant_id");
        Map<String,Object> merchantLimitAuthorization = merchantLimitAuthorizationService.getMerchantAllLimitAuthorization(merchantId);
        if(merchantLimitAuthorization == null || merchantLimitAuthorization.isEmpty()){
            return new HashMap(){{
                put("forbid",false);
            }};
        }else{
            String limitTypes = BeanUtil.getPropString(merchantLimitAuthorization,MerchantLimitAuthorization.LIMIT_TYPES,"");
            List<String> limitTypeList = Arrays.asList(limitTypes.split(","));
            Set<Integer> forbidType = new HashSet<Integer>(){{
                add(EventLog.TYPE_DRAW);
                add(EventLog.TYPE_BUY_FINANCIAL_PRODUCT);
                add(EventLog.TYPE_FINANCIAL_MANUAL_BUY);
            }};
            for (int type:forbidType) {
                if (!limitTypeList.contains(type + "")) {
                    return new HashMap() {{
                        put("forbid", false);
                    }};
                }
            }
            Map<String,Object> result = new HashMap<>();
            Map<String,Object> forbidRemark = (Map<String, Object>) BeanUtil.getProperty(merchantLimitAuthorization,MerchantLimitAuthorization.REMARKS);
            result.put("forbid",true);
            result.put("app_hint",forbidRemark.get(EventLog.TYPE_DRAW+""));
            return result;
        }
    }

    private void batchDeleteMerchantD0DisableReason(List<Map> request,String taskId,String userId,String username){
        int totalCount = request.size();
        int successCount =0;
        int failedCount = 0;
        for (Map map:request){
            String merchantSn = BeanUtil.getPropString(map,MerchantWithdrawrealtimeConfig.MERCHANT_SN);
            int disableReasonId = BeanUtil.getPropInt(map,MerchantD0DisableReasonConfig.DISABLE_REASON_ID);
            String remark = BeanUtil.getPropString(map,MerchantWithdrawrealtimeConfig.REMARK);
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            if(merchant == null || merchant.isEmpty()){
                failedCount ++;
                continue;
            }
            String merchantId = BeanUtil.getPropString(merchant,DaoConstants.ID);
            try {
                withdrawConfigService.deleteMerchantDisableReason(merchantId, disableReasonId);
                withdrawConfigService.checkAndEnableMerchantWithdrawRealTimeConfig(new HashMap() {{
                    put(Withdraw.MERCHANT_ID, merchantId);
                    put(MerchantWithdrawrealtimeConfig.DISABLE_WAY, MerchantWithdrawrealtimeConfig.DISABLE_WAY_SP);
                }});
                int statusAfter = 0;
                Map realTimeConfig = withdrawConfigService.getMerchantWithdrawrealtimeConfig(merchantId);
                if(realTimeConfig != null && realTimeConfig.isEmpty()){
                    statusAfter = BeanUtil.getPropInt(realTimeConfig,MerchantWithdrawrealtimeConfig.STATUS);
                }
                String functionCode = "1000092";
                if(disableReasonId == MERCHANT_CREATE_TIME_NOT_REACH_STANDARD){
                    functionCode = "1000093";
                }
                Map<String, Object> businessLog = CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE,"merchant",
                        BizOpLog.BUSINESS_OBJECT_COLUMN_CODE,"merchant_withdrawrealtime_config#status",
                        BizOpLog.BUSINESS_SYSTEM_CODE,BusinessLogUtil.BUSINESS_SYSTEM_CODE_SP,
                        BizOpLog.BUSINESS_SYSTEM_VERSION,BusinessLogUtil.BUSINESS_SYSTEM_VERSION_SP,
                        BizOpLog.BUSINESS_FUNCTION_CODE, functionCode,
                        BizOpLog.OP_USER_ID, userId,
                        BizOpLog.OP_USER_NAME, username,
                        BizOpLog.REMARK, remark,
                        BizOpLog.OP_ID, BusinessLogUtil.uuid(),
                        BizOpLog.OP_OBJECT_ID,merchantId,
                        BizOpLog.OP_COLUMN_VALUE_BEFORE,0,
                        BizOpLog.OP_COLUMN_VALUE_AFTER,statusAfter,
                        BizOpLog.OP_TIME,new Date().getTime(),
                        BizOpLog.OP_TYPE,BizOpLog.OP_TYPE_MODIFY);
                businessLogService.createBizOpLog(businessLog);
                successCount ++;
            } catch (Exception ex) {
                failedCount ++;
                logger.info("batchDeleteMerchantD0DisableReason error,merchantId:{}",merchantId,ex);
            }
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, taskId,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap(
                        "删除禁用原因数量", totalCount,
                        "执行成功数量", successCount,
                        "执行失败数量", failedCount
                )
        ));
    }

    private List<Map> extraDeleteD0DisableDataFromExcel(Workbook workbook){
        List<Map> merchantInfos = new ArrayList<Map>();
        try {
            // 循环Sheet
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                return null;
            }
            // 循环行Row
            for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    continue;
                }
                String merchantSn = toString(row.getCell(0)).replaceAll(" ", "");
                if (merchantSn.contains(".")) {
                    merchantSn = merchantSn.substring(0, merchantSn.indexOf(".")); // 防止解析为小数，去掉0
                }
                String row1Str = toString(row.getCell(1)).replaceAll(" ", "");
                int disableReasonId;
                if(row1Str.equals("风险商户")){
                    disableReasonId = RISK_MERCHANT;
                }else if (row1Str.equals("入网时间未达标")){
                    disableReasonId = MERCHANT_CREATE_TIME_NOT_REACH_STANDARD;
                }else{
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel数据格式错误");
                }
                String remark = toString(row.getCell(2)).replaceAll(" ", "");
                if(!StringUtil.empty(merchantSn)){
                    merchantInfos.add(CollectionUtil.hashMap(
                            MerchantWithdrawrealtimeConfig.MERCHANT_SN, merchantSn,
                            MerchantD0DisableReasonConfig.DISABLE_REASON_ID, disableReasonId,
                            MerchantWithdrawrealtimeConfig.REMARK, remark
                    ));
                }
            }
            return merchantInfos;
        } catch (Exception e) {
            logger.error("解析删除禁用白名单excel失败", e);
            throw e;
        }
    }
}

