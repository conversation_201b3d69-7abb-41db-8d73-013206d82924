package com.wosai.upay.service;


import com.wosai.business.log.model.BizObjectColumn;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.business.log.service.BizFunctionService;
import com.wosai.business.log.service.BizObjectColumnService;
import com.wosai.business.log.service.BizObjectService;
import com.wosai.business.log.service.BizOpLogService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.util.BusinessLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Created by qizheming on 2017/6/5.
 */
@Service
public class BusinessLogServiceImpl implements BusinessLogService {
    public static final Logger logger = LoggerFactory.getLogger(BusinessLogServiceImpl.class);

    //生成固定线程池
    public static final int MAX_BUSINESS_LOG_THREAD_COUNT = 5; //最多同时有多少个线程执行
    public static ThreadPoolExecutor businessLogExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(MAX_BUSINESS_LOG_THREAD_COUNT);


    @Autowired
    private BizObjectColumnService bizObjectColumnService;
    @Autowired
    private BizObjectService bizObjectService;
    @Autowired
    private BizOpLogService bizOpLogService;
    @Autowired
    private BizFunctionService bizFunctionService;
    @Autowired
    MerchantService merchantService;
    @Autowired
    StoreService storeService;
    @Autowired
    TerminalService terminalService;

    @Override
    public ListResult findBizObjectColumns(Map<String,Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        ListResult result = bizObjectColumnService.findBizObjectColumns(pageInfo,request);
        return result;
    }

    @Override
    @Cacheable(value = "findBizObjectColumnsByObjectCode")
    public List<Map<String,Object>> findBizObjectColumnsByObjectCode(Map<String,Object>  request) {
        String business_object_column_name = BeanUtil.getPropString(request,"business_object_column_name","");
        List<Map<String,Object>> list = new ArrayList<>();
        if (request.containsKey("business_object_codes")){
            List<String> business_object_codes = (List<String>) request.get("business_object_codes");
            for (String business_object_code : business_object_codes){
                list.addAll(bizObjectColumnService.findBizObjectColumnsByObjectCode(business_object_code));
            }
        }else{
            list.addAll(bizObjectColumnService.findBizObjectColumnsByObjectCode(BeanUtil.getPropString(request,BizObjectColumn.BUSINESS_OBJECT_CODE)));
        }
        if (StringUtil.empty(business_object_column_name)){
            return list;
        }
        List<Map<String,Object>> result = new ArrayList<>();
        for(Map map : list){
            if (BeanUtil.getPropString(map,BizObjectColumn.NAME,"").contains(business_object_column_name)){
                result.add(CollectionUtil.hashMap(
                        "type","business_object_column",
                        "code",BeanUtil.getPropString(map,"code"),
                        "name",BeanUtil.getPropString(map,"name")
                ));
            }
        }
        Map<String,String> functions = bizFunctionService.findAllFunctions();
        Set<String> keys = functions.keySet();
        for (String key:keys) {
            if (BeanUtil.getPropString(functions,key,"").contains(business_object_column_name)){
                result.add(CollectionUtil.hashMap(
                        "type","business_function",
                        "code",key,
                        "name",BeanUtil.getPropString(functions,key)
                ));
            }
        }
        return result;
    }

    @Override
    public ListResult findBizObjects(Map<String,Object>  request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return bizObjectService.findBizObjects(pageInfo,request);
    }

    @Override
    @Cacheable(value = "findObjectColumns")
    public List<String> findObjectColumns(String bizObjectCode){
        List<String> columns = new ArrayList<>();
        List<Map<String,Object>> cols = findBizObjectColumnsByObjectCode(CollectionUtil.hashMap(
                BizObjectColumn.BUSINESS_OBJECT_CODE,bizObjectCode
        ));
        for(Map map_col : cols){
            columns.add(BeanUtil.getPropString(map_col, BizObjectColumn.CODE));
        }
        return columns;
    }

    @Override
    public Map createBizOpLog(Map bizOpLog) {
        return bizOpLogService.createBizOpLog(bizOpLog);
    }

    @Override
    public ListResult findBizOpLogs(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return bizOpLogService.findBizOpLogs(pageInfo,request);
    }

    @Override
    public Map getBizOpLogDetail(Map request) {
        Map result = bizOpLogService.getBizOpLogDetail(request);
        if (result != null && result.containsKey("change_list") ){
            List<Map> change_list = (List<Map>) result.get("change_list");
            Map store = null;
            Map merchant = null;
            for (int i = 0 ; i < change_list.size(); i++){
                //补全商户信息
                if (BeanUtil.getPropString(change_list.get(i),BizOpLog.BUSINESS_OBJECT_COLUMN_CODE,"").equals("merchant_id")){
                    String merchant_id = BeanUtil.getPropString(change_list.get(i),BizOpLog.OP_COLUMN_VALUE_AFTER,"");
                    merchant_id = StringUtil.empty(merchant_id)? BeanUtil.getPropString(change_list.get(i),BizOpLog.OP_COLUMN_VALUE_BEFORE,""):merchant_id;
                    merchant = merchantService.getMerchant(merchant_id);
                    change_list.remove(i);
                    i --;
                    change_list.add(CollectionUtil.hashMap(
                            "business_object_column_code","merchant_info",
                            "business_object_column_name","商户sn",
                            "op_column_value_before",BeanUtil.getPropString(merchant, Merchant.SN,""),
                            "op_column_value_after",BeanUtil.getPropString(merchant, Merchant.SN,"")
                            ));
                    change_list.add(CollectionUtil.hashMap(
                            "business_object_column_code","merchant_info",
                            "business_object_column_name","商户名称",
                            "op_column_value_before",BeanUtil.getPropString(merchant, Merchant.NAME,""),
                            "op_column_value_after",BeanUtil.getPropString(merchant, Merchant.NAME,"")
                    ));
                    continue;
                }
                //补全门店信息信息
                if (BeanUtil.getPropString(change_list.get(i),BizOpLog.BUSINESS_OBJECT_COLUMN_CODE,"").equals("store_id")){
                    String store_id = BeanUtil.getPropString(change_list.get(i),BizOpLog.OP_COLUMN_VALUE_AFTER,"");
                    store_id = StringUtil.empty(store_id)? BeanUtil.getPropString(change_list.get(i),BizOpLog.OP_COLUMN_VALUE_BEFORE,""):store_id;
                    store = storeService.getStore(store_id);
                    change_list.remove(i);
                    i --;
                    change_list.add(CollectionUtil.hashMap(
                            "business_object_column_code","store_info",
                            "business_object_column_name","门店sn",
                            "op_column_value_before",BeanUtil.getPropString(store, Store.SN,""),
                            "op_column_value_after",BeanUtil.getPropString(store, Store.SN,"")
                    ));
                    change_list.add(CollectionUtil.hashMap(
                            "business_object_column_code","store_info",
                            "business_object_column_name","门店名称",
                            "op_column_value_before",BeanUtil.getPropString(store, Store.NAME,""),
                            "op_column_value_after",BeanUtil.getPropString(store, Store.NAME,"")
                    ));
                    continue;
                }
                //补全终端信息
                if (BeanUtil.getPropString(change_list.get(i),BizOpLog.BUSINESS_OBJECT_COLUMN_CODE,"").equals("terminal_id")){
                    String terminal_id = BeanUtil.getPropString(change_list.get(i),BizOpLog.OP_COLUMN_VALUE_AFTER,"");
                    terminal_id = StringUtil.empty(terminal_id)? BeanUtil.getPropString(change_list.get(i),BizOpLog.OP_COLUMN_VALUE_BEFORE,""):terminal_id;
                    Map terminal = terminalService.getTerminal(terminal_id);
                    change_list.remove(i);
                    i --;
                    change_list.add(CollectionUtil.hashMap(
                            "business_object_column_code","terminal_info",
                            "business_object_column_name","终端sn",
                            "op_column_value_before",BeanUtil.getPropString(terminal, Terminal.SN,""),
                            "op_column_value_after",BeanUtil.getPropString(terminal, Terminal.SN,"")
                    ));
                    change_list.add(CollectionUtil.hashMap(
                            "business_object_column_code","terminal_info",
                            "business_object_column_name","终端名称",
                            "op_column_value_before",BeanUtil.getPropString(terminal, Terminal.NAME,""),
                            "op_column_value_after",BeanUtil.getPropString(terminal, Terminal.NAME,"")
                    ));
                    continue;
                }
            }
            //补全终端信息
            if (BeanUtil.getPropString(result,BizOpLog.BUSINESS_OBJECT_CODE,"").equals("terminal")){
                String terminal_id = BeanUtil.getPropString(result,BizOpLog.OP_OBJECT_ID,"");
                Map terminal = terminalService.getTerminal(terminal_id);
                change_list.add(CollectionUtil.hashMap(
                        "business_object_column_code","terminal_info",
                        "business_object_column_name","终端sn",
                        "op_column_value_before",BeanUtil.getPropString(terminal, Terminal.SN,""),
                        "op_column_value_after",BeanUtil.getPropString(terminal, Terminal.SN,"")
                ));
                change_list.add(CollectionUtil.hashMap(
                        "business_object_column_code","terminal_info",
                        "business_object_column_name","终端名称",
                        "op_column_value_before",BeanUtil.getPropString(terminal, Terminal.NAME,""),
                        "op_column_value_after",BeanUtil.getPropString(terminal, Terminal.NAME,"")
                ));
            }
        }
        return result;
    }

    /**
     * 异步发送业务日志
     * @param params
     */
    public void sendBusinessLog(final Map<String,Object> params){
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                try{
                    excuteSendLog_test(params);
                }catch (Exception e){
                    logger.error("发送业务日志excuteSendLog异常params[{}]",params,e);
                    return ;
                }
            }
        };
        try {
            businessLogExecutor.submit(runnable);
        }catch (Exception e){
            logger.error("发送业务日志提交到线程池异常params[{}]",params,e);
        }
    }



    private void excuteSendLog_test(Map<String,Object> params){
        List<Map> results = null;
        try{
            Map op_params = (Map) params.get(BusinessLogUtil.LOG_PARAM_OP_PARAMS);
            params.put(BusinessLogUtil.LOG_PARAM_OP_PARAMS_COLUMNS, SpringContextHolder.getBean(BusinessLogService.class).findObjectColumns(BeanUtil.getPropString(op_params,BizOpLog.BUSINESS_OBJECT_CODE)));
            results = BusinessLogUtil.buildBusinessLogSp(params);
        }catch (Exception e){
            logger.error("创建业务日志异常params[{}]",params,e);
            return ;
        }
        try{
            if (results == null || results.size() == 0){
                return ;
            }
            //发送业务日志
            for (Map map : results){
                try {
                    long time_begin = new Date().getTime();
                    logger.info("send business_log start time_begin[{}] :params[{}]",time_begin,map);
                    createBizOpLog(map);
                    long time_end = new Date().getTime();
                    logger.info("send business_log start time_begin[{}] time_end[{}] time_cost[{}]:params[{}]",time_begin,time_end,time_end-time_begin,map);
                }catch (Exception e){
                    logger.error("rpc保存日志error,map[{}]",map,e);
                }
            }
        }catch (Exception e){
            logger.error("发送业务日志异常params[{}]",params,e);
            return ;
        }
    }

}
