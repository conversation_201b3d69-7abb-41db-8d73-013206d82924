package com.wosai.upay.service.remote;

import java.util.Map;

/**
 * Created by kay on 16/10/11.
 */
public interface QrcodeService {

    /**
     * 绑定门店码 - 根据编号.
     *
     * @param
     * @return
     */
    Map bindQrcode(Map bindInfo);

    /**
     * 改变收款码状态.
     *
     * @param
     * @return
     */
    void changeQrcodeStatus(String qrcode);
    /**
     * 更新二维码名称.
     *
     * @param
     * @return
     */
    Map updateQrcodeName(String id, String name);
    /**
     * 更新二维码名称-根据qrcode.
     *
     * @param
     * @return
     */
    Map updateQrcodeNameByQrcode(String qrcode,String name);
    /**
     * 根据qrcode启用门店码.
     *
     * @param qrcode
     */
    void enableQrcodeByQrcode(String qrcode);
    /**
     * 根据qrcode禁用门店码.
     *
     * @param qrcode
     */
    void disableQrcodeByQrcode(String qrcode);
    /**
     * 解绑门店码.
     * @param qrcode
     */
    void unbindQrcodeByQrcode(String qrcode);

    /**
     * 根据qrcode获取门店码.
     *
     * @param qrcode
     * @return
     */
    Map getQrcodeByCode(String qrcode);


    /**
     * 根据发票码查询 开票端PC插件信息
     * @param qrCode
     * @return
     */
    Map getHemayunInvoiceTerminal(String qrCode);

    /**
     * 删除门店码配置
     * @param qrcodeId
     */
    void deleteQrcodeTagByQrcodeId(String qrcodeId);

}
