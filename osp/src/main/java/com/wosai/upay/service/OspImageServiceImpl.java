package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.oss.OssUrlEncrypt;
import com.wosai.risk.bean.BankCardOcrResult;
import com.wosai.risk.bean.Extra;
import com.wosai.risk.bean.OcrIDCard;
import com.wosai.risk.service.IOcrService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.restTemplateUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpSession;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

import static com.google.common.io.BaseEncoding.base64;
import static com.wosai.data.util.CollectionUtil.hashMap;

/**
 * @author: yinchengjian
 * @description:
 * @date: 2018/10/9
 * @modified By:
 */
@Service
public class OspImageServiceImpl implements OspImageService {

    private static Logger log = LoggerFactory.getLogger(OspImageServiceImpl.class);


    private static final String BASEURL = "http://images.wosaimg.com/";

    private static final String GAODEAPI = "http://restapi.amap.com/v3/geocode/regeo?key=94473cd11e459feedf76bf92c5079549&location={longitude},{latitude}&extensions=base";

    private static final long FILE_THRESHOLD = 5 * FileUtils.ONE_MB;
    private static final String IMAGE_PREFIX = "x-oss-process=";

    private static final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final String NO_VALID = "NO VALID";


    @Autowired
    private OssFileUploader ossFileUploader;

    @Autowired
    private IOcrService iOcrService;

    @Autowired
    private OssUrlEncrypt ossUrlEncrypt;

    @Override
    public Map uploadWithPoi(MultipartFile images, boolean watermarkFlag, String ocrType, double longitude, double latitude, String version,
                             boolean isTolerant) throws IOException {
        Map imageWithOcr = this.upload(images, watermarkFlag, ocrType, isTolerant);
        boolean poiFlag = longitude != 0 && latitude != 0;
        Map gaode = poiFlag ? restTemplateUtil.getRestTemplate().getForObject(GAODEAPI, Map.class, longitude, latitude) : Collections.emptyMap();
        String status = MapUtils.getString(gaode, "status");
        // regeocode.formatted_address 街道地址
        // regeocode.addressComponent.province/city/district 省市区地址
        Map address = "1".equals(status) ? (Map) BeanUtil.getNestedProperty(gaode, "regeocode.addressComponent") : Collections.emptyMap();
        Map result = hashMap(
                "province", MapUtils.getString(address, "province"),
                "city", MapUtils.getString(address, "city"),
                "district", MapUtils.getString(address, "district"),
                "address", BeanUtil.getPropString(gaode, "regeocode.formatted_address"),
                "time", System.currentTimeMillis(),
                "longitude", longitude,
                "latitude", latitude,
                "version", version,
                "system", "CRM");
        result.putAll(imageWithOcr);
        return result;
    }

    @Override
    public Map encrypt(Map url) throws UnsupportedEncodingException {
        if (url.containsKey("url")) {
            url.put("url", ossUrlEncrypt.encryptUrl(url.get("url").toString()));
        }
        return url;
    }

    public Map upload(MultipartFile image, boolean watermarkFlag, String ocrType, boolean isTolerant) throws IOException {
        String originName = image.getOriginalFilename();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        IOUtils.copy(image.getInputStream(), baos);
        byte[] content = baos.toByteArray();
        Map result = hashMap();
        String url = baseUpload(content, image.getContentType(), originName.substring(originName.lastIndexOf(".")), isPrivate(ocrType));
        if (!isTolerant) {
            this.checkOcrAndPut(url, ocrType, result);
        } else {
            try {
                this.checkOcrAndPut(url, ocrType, result);
            } catch (Exception e) {
            }
        }
        url = watermarkFlag ? buildWatermark(url) : url;

        // 如果文件太大,则增加字符限制
        if (content.length >= FILE_THRESHOLD) {
            url = compressUrl(url);
        }
        //图片加上了水印需要在解析一边重新生成授权签名,否则签名不正确无法访问
        url = ossUrlEncrypt.encryptUrl(url);
        result.put("photo", url);
        return result;
    }

    private boolean isPrivate(String ocrType) {
        return ("bankCard".equals(ocrType) || "front".equals(ocrType) || "back".equals(ocrType));
    }

    private String baseUpload(byte[] content, String contentType, String suffix, boolean isPrivate) {
        try {
            // calculate path
            String digest = toSHA1(content);
            String dirName = digest.substring(0, 2);
            String fullName = dirName + "/" + digest.substring(2) + suffix;

            ByteArrayInputStream stream = new ByteArrayInputStream(content);
            //contentType
            String url = ossFileUploader.uploadIfNotExists(OssFileUploader.IMAGE_BUCKET_NAME, fullName, stream, content.length, isPrivate);
            return StringUtils.isEmpty(url) ? (BASEURL + fullName) : url;
        } catch (Exception e) {
            log.error("upload image error", e);
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "上传文件失败" + e.getMessage());
        }
    }

    private static String compressUrl(String url) {
        return StringUtils.contains(url, IMAGE_PREFIX)
                ? url + ",image/resize,p_80"
                : url + "?x-oss-process=image/resize,p_80";
    }

    private String buildWatermark(String url) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = dateFormat.format(new Date());
        return new StringBuilder(url)
                .append("?x-oss-process=image/watermark,color_FFFFAA,x_50,y_100,")
                .append("text_")
                .append(base64().encode(("疯狂收钱吧" + time).getBytes())).toString();
    }

    private static String toSHA1(byte[] convertme) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("SHA-1");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return byteArrayToHexString(md.digest(convertme));
    }

    private static String byteArrayToHexString(byte[] b) {
        String result = "";
        for (int i = 0; i < b.length; i++) {
            result +=
                    Integer.toString((b[i] & 0xff) + 0x100, 16).substring(1);
        }
        return result;
    }

    private void checkOcrAndPut(String url, String ocrType, Map result) {
        if (ocrType == null) {
            return;
        }
        HttpSession session = HttpRequestUtil.getSession();
        Object accountId = session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        Map extra = hashMap(Extra.PLATFORM, "CRM", Extra.OPEATROR, accountId == null ? "111111" : accountId);
        if (StringUtils.equals(ocrType, "bankCard")) {
            BankCardOcrResult bankCardOcrResult = iOcrService.bankCardNoOcr(url, extra);
            result.put(BankCardOcrResult.BANK_CARD_NO, bankCardOcrResult.getBankCardNo());
            result.put(BankCardOcrResult.VALID_DATE, "");
            if (bankCardOcrResult.getValidDate() != null && !NO_VALID.equals(bankCardOcrResult.getValidDate())) {
                result.put(BankCardOcrResult.VALID_DATE, bankCardOcrResult.getValidDate());
            }
            return;
        }
        boolean isFront = "front".equals(ocrType);
        OcrIDCard ocrIdCard = iOcrService.identityIdCardOneSideByUrl(isFront, url, extra);
        // 上传身份证无效
        if (!ocrIdCard.isFrontSuccess() && !ocrIdCard.isBackendSuccess()) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "请上传有效的身份证照片");
        }
        if (isFront) {
            // 是否满18岁
            Period period = Period.between(LocalDate.parse(ocrIdCard.getBirth(), yyyyMMdd), LocalDate.now());
            if (period.getYears() < 18) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "开户人未满18岁，不允许入网，请更换");
            }
            result.put("name", ocrIdCard.getName());
            result.put("identity", ocrIdCard.getNum());
        } else {
            result.put("id_card_start_date", ocrIdCard.getStartDate());
            result.put("id_card_end_date", ocrIdCard.getEndDate());
            LocalDate endDate;
            try {
                endDate = LocalDate.parse(ocrIdCard.getEndDate(), yyyyMMdd);
            } catch (Exception e) {
                endDate = null;
            }
            // 身份证是否过期
            if (endDate != null && endDate.isBefore(LocalDate.now())) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "身份证已过期，请更换最新证件后重试");
            }
        }
    }
}
