package com.wosai.upay.service;


import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.helper.CorePlatformsValidated;

import java.util.List;
import java.util.Map;

/**
 * 接口定义，参数校验规则定义在接口方法上，校验失败的错误提示可以支持i18n，具体做法是在src/main/resources/ValidationMessages.properties资源文件中定义错误提示的键值对，然后在这里引用错误提示键。
 * 本地化的资源文件加后缀，例如ValidationMessages_zh_CN.properties。
 *
 * <AUTHOR>
@CorePlatformsValidated
public interface OspStoreService {

    /**
     * 创建Store.
     *
     * @param request
     */
    Map createStore(Map<String, Object> request);

    /**
     * 根据storeId删除Store.
     *
     * @param request id                  UUID
     */
    void deleteStore(Map<String, Object> request);

    /**
     * 根据storeSn删除Store.
     *
     * @param request sn
     */
    void deleteStoreBySn(Map<String, Object> request);

    /**
     * 修改Store.
     *
     * @param request
     */
    Map updateStore(Map<String, Object> request);

    /**
     * 根据storeId禁用Store.
     *
     * @param request id                  UUID
     * @return
     */
    void disableStore(Map<String, Object> request);

    /**
     * 根据storeId启用Store.
     *
     * @param request id                  UUID
     * @return
     */
    void enableStore(Map<String, Object> request);

    /**
     * 根据storeId关闭Store.
     *
     * @param request id                  UUID
     * @return
     */
    void closeStore(Map<String, Object> request);

    /**
     * 根据storeId获取Store.
     *
     * @param request id                  UUID
     * @return
     */
    Map getStore(Map<String, Object> request);

    /**
     * 根据clientSn获取Store.
     *
     * @param request merchant_id
     *                client_sn
     * @return
     */
    Map getStoreByClientSn(Map<String, Object> request);

    /**
     * 获取商户下面的门店.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                merchant_id
     * @return
     */
    ListResult getStoreListByMerchantId(Map<String, Object> request);

    /**
     * 分页查询Store.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                store_sn
     *                store_name
     *                sn                  编号
     *                name                门店名称
     *                status              门店状态
     *                rank                信用等级
     *                contact_phone       联系固定电话号码
     *                contact_cellphone   联系移动电话号码
     *                client_sn           商户外部门店号
     *                merchant_id         商户ID
     *                solicitor_id        推广渠道ID
     *                vendor_id           服务商ID
     *                deleted
     * @return
     */
    ListResult findStores(Map<String, Object> request);

    /**
     * 批量导入门店.
     *
     * @param request
     * @return
     */
    Map importStores(Map<String, Object> request);

    /**
     * 查询门店维护人。
     *
     * @param request store_id
     * @return
     */
    List<Map<String,Object>> getStoreKeepers(@PropNotEmpty.List({
            @PropNotEmpty(value = TransactionParam.STORE_ID, message = "{value} 门店id不能为空"),
    })Map<String, Object> request);

}
