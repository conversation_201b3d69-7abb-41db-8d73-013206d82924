package com.wosai.upay.service;

import com.google.common.collect.Lists;
import com.wosai.assistant.response.Page;
import com.wosai.assistant.response.UserBean;
import com.wosai.business.audit.model.*;
import com.wosai.business.audit.service.*;
import com.wosai.business.audit.service.AuditItemService;
import com.wosai.business.audit.service.AuditService;
import com.wosai.business.audit.service.CustomColumnsService;
import com.wosai.business.audit.service.ReplyTemplateService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.MapUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.HttpRequestUtil;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Array;
import java.util.*;

/**
 * Created by chenzhiping on 2017/8/21.
 */
@Service
public class BusinessAuditServiceImpl implements BusinessAuditService {

    private static final Logger logger = LoggerFactory.getLogger(BusinessAuditServiceImpl.class);

    @Autowired
    private ReplyTemplateService replyTemplateService;
    @Autowired
    private CustomColumnsService customColumnsService;
    @Autowired
    private AuditService auditService;
    @Autowired
    private AuditItemService auditItemService;
    @Autowired
    private AuditRecordService auditRecordService;
    @Autowired
    private OspOrganizationService ospOrganizationService;
    @Autowired
    private OspCoreUserService ospCoreUserService;
    @Autowired
    private MerchantService merchantService;

    /* *******************提示文案服务************************ */
    @Override
    public ListResult findReplyTemplateKinds(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return replyTemplateService.findReplyTemplateKinds(pageInfo, request);
    }

    @Override
    public Map getReplyTemplateKind(Map<String, Object> request) {
        String replyTemplateKindId = MapUtils.getString(request, "id");
        return replyTemplateService.getReplyTemplateKind(replyTemplateKindId);
    }

    @Override
    public Map getReplyTemplateKindByName(Map<String, Object> request) {
        String name = MapUtils.getString(request, ReplyTemplateKind.NAME);
        return replyTemplateService.getReplyTemplateKindByName(name);
    }

    @Override
    public void deleteReplyTemplateKindById(Map<String, Object> request) {
        String replyTemplateKindId = MapUtils.getString(request, "id");
        replyTemplateService.deleteReplyTemplateKind(replyTemplateKindId);
    }

    @Override
    public Map createReplyTemplateKind(Map<String, Object> request) {
        return replyTemplateService.createReplyTemplateKind(request);
    }

    @Override
    public Map updateReplyTemplateKind(Map<String, Object> request) {
        return replyTemplateService.updateReplyTemplateKind(request);
    }

    @Override
    public ListResult getReplyTemplates(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        ListResult listResult = replyTemplateService.findReplyTemplates(pageInfo, request);
        if (listResult == null || CollectionUtils.isEmpty(listResult.getRecords()))
            return new ListResult(0, new LinkedList<Map>() {
            });

        List<Map> records = listResult.getRecords();

        if (CollectionUtils.isEmpty(records)) return new ListResult(0, new LinkedList<Map>() {
        });
        for (Map record : records) {
            String kindId = MapUtils.getString(record, ReplyTemplate.KIND_ID);
            Map resultKind = replyTemplateService.getReplyTemplateKind(kindId);
            record.put("kind_info", resultKind);
        }

        return listResult;
    }

    @Override
    public Map getReplyTemplateById(Map<String, Object> request) {
        String replyTemplateId = MapUtils.getString(request, "id");
        Map result = replyTemplateService.getReplyTemplate(replyTemplateId);
        if (MapUtils.isEmpty(result)) return new HashMap() {
        };

        String kindId = MapUtils.getString(result, ReplyTemplate.KIND_ID);
        Map resultKind = replyTemplateService.getReplyTemplateKind(kindId);
        result.put("kind_info", resultKind);
        return result;
    }

    @Override
    public void deleteReplyTemplateById(Map<String, Object> request) {
        String replyTemplateId = MapUtils.getString(request, "id");
        replyTemplateService.deleteReplyTemplate(replyTemplateId);
    }

    @Override
    public Map createReplyTemplate(Map<String, Object> request) {
        return replyTemplateService.createReplyTemplate(request);
    }

    @Override
    public Map updateReplyTemplate(Map<String, Object> request) {
        return replyTemplateService.updateReplyTemplate(request);
    }

    /* *********************自定义字段服务*********************** */
    @Override
    public Map createCustomColumns(Map<String, Object> request) {
        return customColumnsService.createCustomColumns(request);
    }

    @Override
    public void deleteCustomColumns(Map<String, Object> request) {
        String customColumnsId = MapUtils.getString(request, "id");
        customColumnsService.deleteCustomColumns(customColumnsId);
    }

    @Override
    public Map updateCustomColumns(Map<String, Object> request) {
        return customColumnsService.updateCustomColumns(request);
    }

    @Override
    public Map getCustomColumnsById(Map<String, Object> request) {
        String customColumnsId = MapUtils.getString(request, DaoConstants.ID);
        return customColumnsService.getCustomColumns(customColumnsId);
    }

    @Override
    public ListResult findCustomColumns(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return customColumnsService.findCustomColumns(pageInfo, request);
    }


    /* **********************审批服务************************ */
    @Override
    public List getAuditSubjects() {
        return auditService.getAuditSubjects();
    }

    @Override
    public List getAuditObjects(Map<String, Object> request) {
        String subjectKey = MapUtils.getString(request, "subjectKey");
        return auditService.getAuditObjects(subjectKey);
    }

    @Override
    public List getAuditTypes(Map<String, Object> request) {
        String subjectKey = MapUtils.getString(request, "subjectKey");
        String objectKey = MapUtils.getString(request, "objectKey");
        return auditService.getAuditTypes(subjectKey, objectKey);
    }

    @Override
    public List getAuditFields(Map<String, Object> request) {
        String subjectKey = MapUtils.getString(request, "subjectKey");
        String objectKey = MapUtils.getString(request, "objectKey");
        String typeKey = MapUtils.getString(request, "typeKey");
        String fieldName = MapUtils.getString(request, "fieldName");
        return auditService.getAuditFields(subjectKey, objectKey, typeKey, fieldName);
    }


    /* **********************审批事项服务********************** */
    @Override
    public Map createAuditItem(Map<String, Object> request) {
        List<Map> fields = (List) BeanUtil.getProperty(request, AuditConstant.INPUT_CONTEXT_FIELDS);
        if (fields == null || fields.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "审批字段fields不可为空");
        }
        request.remove(AuditConstant.INPUT_CONTEXT_FIELDS);
        Map input_context = CollectionUtil.hashMap(AuditConstant.INPUT_CONTEXT_FIELDS, fields);
        request.put(AuditItem.INPUT_CONTEXT, input_context);
        return auditItemService.createAuditItem(request);
    }

    @Override
    public void deleteAuditItem(Map<String, Object> request) {
        String auditItemId = MapUtils.getString(request, DaoConstants.ID);
        auditItemService.deleteAuditItem(auditItemId);
    }

    @Override
    public Map updateAuditItem(Map<String, Object> request) {
        Object fields = BeanUtil.getProperty(request, AuditConstant.INPUT_CONTEXT_FIELDS);
        if (!(fields instanceof List)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "审批字段fields格式错误");
        }
        request.remove(AuditConstant.INPUT_CONTEXT_FIELDS);
        Map input_context = CollectionUtil.hashMap(AuditConstant.INPUT_CONTEXT_FIELDS, fields);
        request.put(AuditItem.INPUT_CONTEXT, input_context);
        return auditItemService.updateAuditItem(request);
    }

    @Override
    public Map getAuditItem(Map<String, Object> request) {
        String auditItemId = MapUtils.getString(request, DaoConstants.ID);
        return auditItemService.getAuditItem(auditItemId);
    }

    @Override
    public List getAuditItemNames() {
        return auditItemService.getAuditNames();
    }

    @Override
    public ListResult findAuditItems(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return auditItemService.findAuditItems(pageInfo, request);
    }



    /* **************审批记录接口服务*************** */

    @Override
    public Map createAuditRecord(Map<String, Object> request) {
        return auditRecordService.createAuditRecord(request);
    }

    @Override
    public void deleteAuditRecord(Map<String, Object> request) {
        String auditRecordId = MapUtils.getString(request, DaoConstants.ID);
        auditRecordService.deleteAuditRecord(auditRecordId);
    }

    @Override
    public void deleteAuditRecordBySn(Map<String, Object> request) {
        String auditRecordSn = MapUtils.getString(request, AuditRecord.SN);
        auditRecordService.deleteAuditRecordBySn(auditRecordSn);
    }

    @Override
    public Map updateAuditRecord(Map<String, Object> request) {
        return auditRecordService.updateAuditRecord(request);
    }

    @Override
    public Map getAuditRecord(Map<String, Object> request) {
        String auditRecordId = MapUtils.getString(request, "id");
        Map record = auditRecordService.getAuditRecord(auditRecordId);

        if (record != null) {
            String merchantId = BeanUtil.getPropString(record, AuditRecord.APPLY_OBJECT_ID);
            Map<String, UserBean> organizations = ospOrganizationService.getUserByMerchantIds(CollectionUtil.hashMap("merchantIds", Lists.newArrayList(merchantId)));
            Map merchantInfo = merchantService.getMerchant(BeanUtil.getPropString(record, AuditRecord.APPLY_OBJECT_ID));

            record.put("organization", organizations.get(merchantId));
            record.put("merchant_info", merchantInfo);
        }
        return record;
    }

    @Override
    public Map getAuditRecordBySn(Map<String, Object> request) {
        String auditRecordSn = MapUtils.getString(request, AuditRecord.SN);
        Map record = auditRecordService.getAuditRecordBySn(auditRecordSn);
        if (record != null) {
            record.put("merchant_info", merchantService.getMerchant(BeanUtil.getPropString(record, AuditRecord.APPLY_OBJECT_ID)));
        }
        return record;
    }

    @Override
    public ListResult findAuditRecords(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);

        /* 通过商户号精确查找 */
        String merchantSn = MapUtils.getString(request, "merchant_sn");
        if (!StringUtils.isEmpty(merchantSn)) {
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            request.put(AuditRecord.APPLY_OBJECT_ID, BeanUtil.getPropString(merchant, DaoConstants.ID));
        }
        /* 通过联系电话精确查找 */
        String cellphone = MapUtils.getString(request, "cellphone");
        if (!StringUtils.isEmpty(cellphone)) {
            Map userInfo = ospCoreUserService.getAccountByCellphone(request);
            String userId = BeanUtil.getPropString(userInfo, DaoConstants.ID);
            if (!StringUtils.isBlank(userId)) {
                request.put(AuditRecord.APPLY_USER_ID, userId);
            } else {
                return new ListResult(0, Collections.EMPTY_LIST);
            }
        }

        /* 通过推广者和推广者组织精确查找 */
        String organizationId = MapUtils.getString(request, "organizationId");
        String userId = MapUtils.getString(request, "userId");
        if (!StringUtils.isEmpty(organizationId) || !StringUtils.isEmpty(userId)) {
            /* 根据推广者后者推广者组织查询，返回商户ID */
            Map temp = CollectionUtil.hashMap(
                    "organizationId", MapUtils.getString(request, "organizationId"),
                    "userId", MapUtils.getString(request, "userId")
            );
            Page<String> merchants = ospOrganizationService.findByUserAndOrganization(temp);
            if (request.containsKey("organizationId")) {
                request.remove("organizationId");
            }
            if (request.containsKey("userId")) {
                request.remove("userId");
            }

            if (merchants != null && merchants.getData() != null && merchants.getData().size() > 0) {
                List<String> applyObjectIds = merchants.getData();
                request.put(AuditRecord.APPLY_OBJECT_IDS, applyObjectIds);
            } else {
                return new ListResult(0, Collections.EMPTY_LIST);
            }
        }
        request.put(AuditRecord.AUDIT_SUBJECT, request.remove("subjectKey"));
        ListResult result = auditRecordService.findAuditRecords(pageInfo, request);
        //补全商户信息
        if (result != null && result.getRecords() != null && result.getRecords().size() > 0) {
            List<String> merchantIds = new ArrayList<>();
            for (Map map : result.getRecords()) {
                merchantIds.add(BeanUtil.getPropString(map, AuditRecord.APPLY_OBJECT_ID));
            }
            //补全渠道信息
            Map<String, UserBean> organizations = ospOrganizationService.getUserByMerchantIds(CollectionUtil.hashMap("merchantIds", merchantIds));
            //补全商户
            final ListResult merchants = merchantIds.size() == 0 ? new ListResult(0, new ArrayList<Map>()) : merchantService.findMerchants(new PageInfo(1, merchantIds.size()), CollectionUtil.hashMap("merchant_ids", merchantIds));
            final Map merchantInfos = new HashMap() {
                {
                    for (Map map : merchants.getRecords()) {
                        put(BeanUtil.getPropString(map, DaoConstants.ID), map);
                    }
                }
            };
            for (Map map : result.getRecords()) {
                map.put("organization", BeanUtil.getProperty(organizations, BeanUtil.getPropString(map, AuditRecord.APPLY_OBJECT_ID)));
                map.put("merchant_info", BeanUtil.getProperty(merchantInfos, BeanUtil.getPropString(map, AuditRecord.APPLY_OBJECT_ID)));
            }
        }
        return result;
    }

    @Override
    public Map createAuditOperationLog(Map<String, Object> request) {
        return auditRecordService.createAuditOperationLog(request);
    }

    @Override
    public void deleteAuditOperationLog(Map<String, Object> request) {
        String auditOperationLogId = MapUtils.getString(request, DaoConstants.ID);
        auditRecordService.deleteAuditOperationLog(auditOperationLogId);
    }

    @Override
    public Map updateAuditOperationLog(Map<String, Object> request) {
        return auditRecordService.updateAuditOperationLog(request);
    }

    @Override
    public Map getAuditOperationLog(Map<String, Object> request) {
        String auditOperationLogId = MapUtils.getString(request, DaoConstants.ID);
        return auditRecordService.getAuditOperationLog(auditOperationLogId);
    }

    @Override
    public ListResult findAuditOperationLogs(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return auditRecordService.findAuditOperationLogs(pageInfo, request);
    }

    @Override
    public void auditRecordPass(Map<String, Object> request) {
        request.put(AuditRecord.OPERATOR_ID, HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID));
        request.put(AuditRecord.OPERATOR, HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME));
        auditRecordService.auditRecordPass(request);
    }

    @Override
    public void auditRecordReject(Map<String, Object> request) {
        request.put(AuditRecord.OPERATOR_ID, HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID));
        request.put(AuditRecord.OPERATOR, HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME));
        auditRecordService.auditRecordReject(request);
    }

    @Override
    public void auditRecordBackToWait(Map<String, Object> request) {
        request.put(AuditRecord.OPERATOR_ID, HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID));
        request.put(AuditRecord.OPERATOR, HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME));
        auditRecordService.auditRecordBackToWait(request);
    }

}
