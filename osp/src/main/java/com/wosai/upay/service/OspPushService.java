package com.wosai.upay.service;

import com.wosai.app.dto.PageResult;
import com.wosai.app.dto.StorePushInfo;
import com.wosai.app.dto.TerminalPushInfo;

import java.util.Map;

public interface OspPushService {

    PageResult<StorePushInfo> getStoreConfig(Map storeConfig);

    /**
     * 查询门店下收款设备/收银人员设置
     *
     * @param storeTerminalConfig type=terminal(收款设备) else(收银人员设置)
     */
    PageResult<TerminalPushInfo> getStoreTerminalConfig(Map storeTerminalConfig);

    /**
     * 查询账号语音设置
     *
     * @param param 期望聚合一些参数,暂时只有account_id字段
     * @return audit_type 播报类型：0-关闭,1-钱箱,2-语音
     */
    Map getUserSetting(Map param);

}
