package com.wosai.upay.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface OspCasService {

    String SESSIONID = "CAS-OSP-SESSION-ID";

    void login(HttpServletResponse response);

    void logout(HttpServletRequest request, HttpServletResponse response);

    void validate(HttpServletRequest request, HttpServletResponse response, String ticket);

    void invalidate(String sessionId);

    void redirect(HttpServletRequest request, HttpServletResponse response) throws IOException;
}
