package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.operation.activity.entity.Activity;
import com.wosai.operation.activity.entity.WhiteList;
import com.wosai.operation.activity.service.ActivityService;
import com.wosai.operation.activity.service.WhitelistService;
import com.wosai.operation.merchant.card.model.Card;
import com.wosai.operation.merchant.card.service.CardService;
import com.wosai.shouqianba.withdrawservice.model.MerchantWithdrawrealtimeConfig;
import com.wosai.shouqianba.withdrawservice.service.WithdrawConfigService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

@Service
public class OspCardsServiceImpl implements OspCardsService {
    Logger logger = LoggerFactory.getLogger(OspCardsServiceImpl.class);

    @Autowired
    private ActivityService activityService;
    @Autowired
    private WhitelistService whitelistService;
    @Autowired
    private WithdrawConfigService withdrawConfigService;
    @Autowired
    private OssFileUploader ossFileUploader;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private CardService cardService;
    @Autowired
    private OspTaskService ospTaskService;

    public static final int MAX_LOG_THREAD_COUNT = 2; //最多同时有多少个线程执行
    public static final int MAX_SMS_THREAD_COUNT = 2;
    ThreadPoolExecutor cardsExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(MAX_LOG_THREAD_COUNT + MAX_SMS_THREAD_COUNT);


    @Override
    public Map createActivity(Map request) {
        return activityService.createActivity(request);
    }

    @Override
    public ListResult findActivity(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return activityService.findActivity(pageInfo, request);
    }

    @Override
    public Map getActivityBySn(Map request) {
        String sn = BeanUtil.getPropString(request, "sn");
        if(StringUtil.empty(sn)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "sn不能为空");
        }

        return activityService.getActivityBySn(sn);
    }

    @Override
    public Map updateActivity(Map request) {
        return activityService.updateActivity(request);
    }

    @Override
    public Map auditActivity(Map request) {
        return activityService.auditActivity(request);
    }

    @Override
    public Map terminateActivity(Map request) {
        return activityService.terminateActivity(request);
    }

    @Override
    public Map importCardsWhitelist(MultipartFile file, Map request) {
        String activityId = BeanUtil.getPropString(request, "activity_id");
        String operatorName = BeanUtil.getPropString(request, "operator_name");
        String operatorId = BeanUtil.getPropString(request, "operator_id");
        String operatorChannel = BeanUtil.getPropString(request, "operator_channel");

        if (StringUtil.empty(activityId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "activity_id不能为空");
        }

        if (StringUtil.empty(operatorName)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "operator_name不能为空");
        }

        if (StringUtil.empty(operatorId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "operator_id不能为空");
        }

        if (StringUtil.empty(operatorChannel)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "operator_channel不能为空");
        }

        List<String> titles = Arrays.asList(ConstantUtil.KEY_MERCHANT_SN);

        List<List<Map>> sheets = ExcelUtil.getListFromExcelFile(file, titles);

        if (sheets == null || sheets.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }
        List<Map> firstSheet = sheets.get(0);
        if (firstSheet == null || firstSheet.size() < 2) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }
        //去除标题
        firstSheet.remove(0);
        if (firstSheet.size() > 1000) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过1000条");
        }

        List<String> sns = new ArrayList<>();
        for (Map map : firstSheet) {
            String sn = BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_SN);

            if (sn != null) {
                sns.add(sn);
            }
        }

        if (sns.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }

        ListResult merchantInfos = withdrawConfigService.findMerchantWithdrawrealtimeConfigs(
                new PageInfo(1, 1000),
                CollectionUtil.hashMap(
                        "merchant_sns", sns,
                        MerchantWithdrawrealtimeConfig.STATUS, MerchantWithdrawrealtimeConfig.STATUS_ENABLE
                )
        );

        if (merchantInfos.getTotal() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据无可用商户号");
        }

        List<Map> ids = new ArrayList<>();
        for (Map map : merchantInfos.getRecords()) {
            ids.add(Collections.singletonMap(ConstantUtil.KEY_MERCHANT_ID, BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_ID)));
        }

        Map importResult = whitelistService.importWhiteList(CollectionUtil.hashMap(
            "activity_id", activityId,
            "bizIdList", ids,
            "operator_name", operatorName,
            "operator_id", operatorId,
            "operator_channel", operatorChannel
        ));

        List<Map<String, String>> errorIds = (List<Map<String, String>>)BeanUtil.getProperty(importResult, "errorBiz");
        importResult.remove("errorBiz");

        if (errorIds.size() == 0) {
            return importResult;
        }

        Map<String, Map> idMap = new HashMap();
        for (Map map : merchantInfos.getRecords()) {
            idMap.put(BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_ID), map);
        }

        String uploadExcelName = null;
        try {
            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet duplicateSheet = workbook.createSheet("上传错误用户");
            SheetUtil sheetUtil = new SheetUtil(duplicateSheet);
            sheetUtil.appendRow(Arrays.asList("商户号", "失败原因"));
            for (Map<String, String> idAndReason : errorIds) {
                String id = BeanUtil.getPropString(idAndReason, "merchant_id");
                String reason = BeanUtil.getPropString(idAndReason, "errorMsg");

                sheetUtil.appendRow(
                        Arrays.asList(
                                BeanUtil.getProperty(BeanUtil.getProperty(idMap, id), ConstantUtil.KEY_MERCHANT_SN),
                                reason
                        )
                );
            }
            uploadExcelName = uploadExcelToOSS("cards-program-import-whitelist", workbook);
        } catch (Exception e) {}

        importResult.put("importErrorFileName", uploadExcelName);

        return importResult;
    }

    @Override
    public Map batchImportCardsWhitelist(MultipartFile file, Map request) {
        final String activityId = BeanUtil.getPropString(request, "activity_id");
        final String activityName = BeanUtil.getPropString(request, "activity_name");
        final String operatorName = BeanUtil.getPropString(request, "operator_name");
        final String operatorId = BeanUtil.getPropString(request, "operator_id");
        final String operatorChannel = BeanUtil.getPropString(request, "operator_channel");

        if (StringUtil.empty(activityId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "activity_id不能为空");
        }

        if (StringUtil.empty(operatorName)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "operator_name不能为空");
        }

        if (StringUtil.empty(operatorId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "operator_id不能为空");
        }

        if (StringUtil.empty(operatorChannel)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "operator_channel不能为空");
        }

        List<String> titles = Arrays.asList(ConstantUtil.KEY_MERCHANT_SN);

        List<List<Map>> sheets = ExcelUtil.getListFromExcelFile(file, titles);

        if (sheets == null || sheets.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }
        List<Map> firstSheet = sheets.get(0);
        if (firstSheet == null || firstSheet.size() < 2) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }
        //去除标题
        firstSheet.remove(0);

        Iterator<Map> it = firstSheet.iterator();
        while (it.hasNext()) {
            Map merchant = it.next();

            if (BeanUtil.getPropString(merchant, ConstantUtil.KEY_MERCHANT_SN) == null) {
                it.remove();
            }
        }

        if (firstSheet.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }

        Map taskApplyLog = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_IMPORT_D0_CARDS
        ));

        final String taskId = BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID);
        final List merchants = firstSheet;

        //异步执行导入操作
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                try {
                    batchImportCardsWhitelistTask(merchants, taskId, activityName, activityId, operatorName, operatorId, operatorChannel);
                } catch (Exception e) {
                    logger.error("batchImportCardsWhitelist() error", e);
                    ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                            TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                            TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "导入过程中发生错误")));
                }
            }
        };
        try {
            cardsExecutor.submit(runnable);
        } catch (Exception e) {
            logger.error("submit batchImportCardsWhitelist() to threadPool error", e);
            ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                    TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "提交导入异步任务发生错误")));
        }

//        HttpRequestUtil.getSession().setAttribute(SESSION_IMPORT_MERCHANT_DRAW_REALTIME, taskId);
        return CollectionUtil.hashMap("taskId", taskId, "task_apply_log_id", taskId);
    }

    private void batchImportCardsWhitelistTask(List<Map> merchants, String taskId, String activityName, String activityId, String operatorName, String operatorId, String operatorChannel) {
        int total = merchants.size();
        //没有通过校验
        List<Map> merchants_invalid = new ArrayList<>();
        List<String> success_sns = new ArrayList<>();

        //校验
        for (int i = 0; i < merchants.size(); i++) {
            //excel中重复
            if (success_sns.contains(BeanUtil.getPropString(merchants.get(i), ConstantUtil.KEY_MERCHANT_SN))){
                merchants.get(i).put("errorMsg", "在excel中重复");
                merchants_invalid.add(merchants.get(i));
                merchants.remove(i);
                i--;
                continue;
            }

            //通过校验
            success_sns.add(BeanUtil.getPropString(merchants.get(i), ConstantUtil.KEY_MERCHANT_SN));
        }

        int successSize = success_sns.size();
        int batchSize = successSize / 1000;
        if (successSize % 1000 > 0) {
            batchSize += 1;
        }

        for (int i = 0; i < batchSize; i++) {
            int batchIndex = (i + 1) * 1000 < successSize ? 1000 : successSize % 1000;
            List<String> batchSns = success_sns.subList(i * 1000, (i * 1000) + batchIndex);

            ListResult merchantInfos = withdrawConfigService.findMerchantWithdrawrealtimeConfigs(
                    new PageInfo(1, 1000),
                    CollectionUtil.hashMap(
                            "merchant_sns", batchSns,
                            MerchantWithdrawrealtimeConfig.STATUS, MerchantWithdrawrealtimeConfig.STATUS_ENABLE
                    )
            );

            if (merchantInfos.getTotal() < batchSns.size()) {
                Map batchMerchants = new HashMap();
                for (Map map : merchantInfos.getRecords()) {
                    batchMerchants.put(BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_SN), map);
                }

                for (String sn : batchSns) {
                    if (batchMerchants.get(sn) == null) {
                        merchants_invalid.add(CollectionUtil.hashMap(ConstantUtil.KEY_MERCHANT_SN, sn, "errorMsg", "没有D0提现权限"));
                    }
                }
            }

            if (merchantInfos.getTotal() == 0) {
                continue;
            }

            List<Map> ids = new ArrayList<>();
            for (Map map : merchantInfos.getRecords()) {
                ids.add(CollectionUtil.hashMap(
                        ConstantUtil.KEY_MERCHANT_ID, BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_ID)
                ));
            }

            Map batchImportResult = new HashMap();
            List<Map<String, String>> errorIds = new ArrayList<>();
            try {
                batchImportResult = whitelistService.importWhiteList(CollectionUtil.hashMap(
                        "activity_id", activityId,
                        "bizIdList", ids,
                        "operator_name", operatorName,
                        "operator_id", operatorId,
                        "operator_channel", operatorChannel
                ));
            } catch (Exception e) {
                String errorMsg = e.getMessage();

                for (Map<String, String> idMap : ids) {
                    idMap.put("errorMsg", errorMsg);
                    errorIds.add(idMap);
                }
            }

            List<Map<String, String>> errorBiz = (List<Map<String, String>>)BeanUtil.getProperty(batchImportResult, "errorBiz");
            batchImportResult.remove("errorBiz");

            if (errorBiz != null) {
                errorIds.addAll(errorBiz);
            }

            if (errorIds != null && errorIds.size() > 0) {
                Map<String, Map> idMap = new HashMap();
                for (Map map : merchantInfos.getRecords()) {
                    idMap.put(BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_ID), map);
                }

                for (Map<String, String> idAndReason : errorIds) {
                    String id = BeanUtil.getPropString(idAndReason, ConstantUtil.KEY_MERCHANT_ID);
                    String reason = BeanUtil.getPropString(idAndReason, "errorMsg");

                    merchants_invalid.add(CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_SN,
                            BeanUtil.getProperty(BeanUtil.getProperty(idMap, id), ConstantUtil.KEY_MERCHANT_SN),
                            "errorMsg",
                            reason
                    ));
                }
            }
        }

        //保存导入结果到任务
        String uploadExcelName = null;
        if (merchants_invalid.size() > 0) {
            try {
                HSSFWorkbook workbook = new HSSFWorkbook();
                HSSFSheet invalidSheet = workbook.createSheet("导入错误商户");
                SheetUtil sheetUtil = new SheetUtil(invalidSheet);
                sheetUtil.appendRow(Arrays.asList("商户号", "错误原因"));
                for (Map merchant : merchants_invalid) {
                    sheetUtil.appendRow(
                            Arrays.asList(
                                    BeanUtil.getPropString(merchant, ConstantUtil.KEY_MERCHANT_SN),
                                    BeanUtil.getPropString(merchant, "errorMsg")
                            )
                    );
                }
                uploadExcelName = uploadExcelToOSS("cards-program-batch-import-whitelist", workbook);
            } catch (Exception e) {}
        }

        Map<String, Object> result = new HashMap<>();

        result.put("D0卡券活动名称", activityName);
        result.put("downloadResultUrl", uploadExcelName);
        result.put("导入总数", total);
        result.put("导入失败总数", merchants_invalid.size());
        result.put("导入成功总数", total - merchants_invalid.size());

        ospTaskService.updateTask(CollectionUtil.hashMap(DaoConstants.ID, taskId,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                TaskApplyLog.APPLY_RESULT, result));
    }

    @Override
    public Map checkCardsWhitelist(MultipartFile file) {
        List<String> titles = Collections.singletonList(ConstantUtil.KEY_MERCHANT_SN);

        List<List<Map>> sheets = ExcelUtil.getListFromExcelFile(file, titles);

        if (sheets == null || sheets.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }
        List<Map> firstSheet = sheets.get(0);
        if (firstSheet == null || firstSheet.size() < 2) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }
        //去除标题
        firstSheet.remove(0);
        if (firstSheet.size() > 1000) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过1000条");
        }

        List<String> sns = new ArrayList<>();
        for (Map map : firstSheet) {
            String sn = BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_SN);

            if (sn != null) {
                sns.add(sn);
            }
        }

        if (sns.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }

        ListResult merchantInfos = withdrawConfigService.findMerchantWithdrawrealtimeConfigs(
            new PageInfo(1, 1000),
            CollectionUtil.hashMap(
                "merchant_sns", sns,
                MerchantWithdrawrealtimeConfig.STATUS, MerchantWithdrawrealtimeConfig.STATUS_ENABLE
            )
        );

        if (merchantInfos.getTotal() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据无可用商户号");
        }

        Map<String, Map> idMap = new HashMap();
        for (Map map : merchantInfos.getRecords()) {
            idMap.put(BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_ID), map);
        }

        Map checkResult = whitelistService.checkWhiteList(CollectionUtil.listOf(idMap.keySet()));
        List<String> duplicateIds = (List<String>)BeanUtil.getProperty(checkResult, "existIdList");
        checkResult.remove("existIdList");

        String uploadExcelName = null;
        if (duplicateIds.size() > 0) {
            try {
                HSSFWorkbook workbook = new HSSFWorkbook();
                HSSFSheet duplicateSheet = workbook.createSheet("重复商户");
                SheetUtil sheetUtil = new SheetUtil(duplicateSheet);
                sheetUtil.appendRow(Collections.singletonList("商户号"));
                for (String id : duplicateIds) {
                    sheetUtil.appendRow(
                        Collections.singletonList(
                            BeanUtil.getProperty(BeanUtil.getProperty(idMap, id), ConstantUtil.KEY_MERCHANT_SN)
                        )
                    );
                }
                uploadExcelName = uploadExcelToOSS("cards-program-duplicate-whitelist", workbook);
            } catch (Exception e) {}
        }

        checkResult.put("duplicateFileName", uploadExcelName);

        return checkResult;
    }

    @Override
    public ListResult findActivityWhitelist(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        String activityId = BeanUtil.getPropString(request, "activity_id");

        if (pageInfo.getPageSize() > 1000) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "page_size太大");
        }

        if (StringUtil.empty(activityId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "activity_id不能为空");
        }

        String merchantSn = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_SN);
        String merchantName = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_NAME);
        String cellphone = BeanUtil.getPropString(request, ConstantUtil.KEY_CELLPHONE);

        Map<String, String> merchantQuery = new HashMap<>();
        if (!StringUtil.empty(merchantSn)) {
            merchantQuery.put(ConstantUtil.KEY_MERCHANT_SN, merchantSn);
        }

        if (!StringUtil.empty(merchantName)) {
            merchantQuery.put(ConstantUtil.KEY_MERCHANT_NAME, merchantName);
        }

        if (!StringUtil.empty(cellphone)) {
            merchantQuery.put(ConstantUtil.KEY_CELLPHONE, cellphone);
        }

        ListResult merchants = null;
        if (!merchantQuery.isEmpty()) {
            merchants = merchantService.findMerchants(new PageInfo(1, 1000), merchantQuery);
        }

        if (merchants != null && merchants.getTotal() == 0) {
            return new ListResult(0, new ArrayList<Map>());
        }

        Map whiteParams = new HashMap<>();
        whiteParams.put(WhiteList.ACTIVITY_ID, activityId);

        if (merchants != null && merchants.getTotal() > 0) {
            List<String> merchantIds = new ArrayList<>();
            for (Map merchant : merchants.getRecords()) {
                merchantIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
            }
            whiteParams.put(WhiteList.MERCHANT_ID, merchantIds);
        }

        ListResult whitelists = whitelistService.findWhiteList(pageInfo, whiteParams);

        if (whitelists.getTotal() == 0) {
            return whitelists;
        }

        Set<String> merchantIds = new HashSet<>();
        for (Map whitelist : whitelists.getRecords()) {
            merchantIds.add(BeanUtil.getPropString(whitelist, ConstantUtil.KEY_MERCHANT_ID));
        }

        ListResult merchantsFromWhitelist = null;

        if (merchants == null) {
            merchantsFromWhitelist =
                    merchantService.findMerchants(
                            new PageInfo(1, whitelists.getRecords().size()),
                            CollectionUtil.hashMap("merchant_ids", CollectionUtil.listOf(merchantIds)));
        } else {
            merchantsFromWhitelist = merchants;
        }

        Map<String, Map> merchantMapFromWhitelist = new HashMap<>();
        for (Map merchant : merchantsFromWhitelist.getRecords()) {
            merchantMapFromWhitelist.put(BeanUtil.getPropString(merchant, DaoConstants.ID), merchant);
        }

        for (Map whitelist : whitelists.getRecords()) {
            String merchantId = BeanUtil.getPropString(whitelist, ConstantUtil.KEY_MERCHANT_ID);
            Map merchant = (Map)BeanUtil.getProperty(merchantMapFromWhitelist, merchantId);
            whitelist.put(ConstantUtil.KEY_MERCHANT_NAME, BeanUtil.getPropString(merchant, Merchant.NAME));
            whitelist.put(ConstantUtil.KEY_MERCHANT_SN, BeanUtil.getPropString(merchant, Merchant.SN));
            whitelist.put(ConstantUtil.KEY_CELLPHONE, BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE));
        }

        return whitelists;
    }

    private String uploadExcelToOSS(String prefix, HSSFWorkbook workbook) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        byte[] content = bos.toByteArray();
        bos.close();
        String fullName = "portal/statement/sp_import_result/" + prefix + "-" + new Date().getTime() + ".xls";
        ByteArrayInputStream bais = new ByteArrayInputStream(content);

        ossFileUploader.uploadIfNotExists(OssFileUploader.IMAGE_BUCKET_NAME, fullName, bais, content.length);
        return fullName;
    }

    @Override
    public Map batchDeleteActivityWhitelist(Map request) {
        String activityId = BeanUtil.getPropString(request, "activity_id");
        String operatorName = BeanUtil.getPropString(request, "operator_name");
        String operatorId = BeanUtil.getPropString(request, "operator_id");
        String operatorChannel = BeanUtil.getPropString(request, "operator_channel");

        if (StringUtil.empty(activityId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "activity_id不能为空");
        }

        if (StringUtil.empty(operatorName)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "operator_name不能为空");
        }

        if (StringUtil.empty(operatorId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "operator_id不能为空");
        }

        if (StringUtil.empty(operatorChannel)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "operator_channel不能为空");
        }

        List<String> merchantIds = (List<String>)BeanUtil.getProperty(request, "merchant_ids");

        if (merchantIds == null || merchantIds.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "merchant_ids不能为空");
        }

        return whitelistService.deleteWhiteList(CollectionUtil.hashMap(
                "activity_id", activityId,
                "bizIdList", merchantIds,
                "operator_name", operatorName,
                "operator_id", operatorId,
                "operator_channel", operatorChannel
        ));
    }

    @Override
    public ListResult findCardInActivity(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        String activityId = BeanUtil.getPropString(request, "activity_id");

        if (StringUtil.empty(activityId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "activity_id不能为空");
        }

        String merchantSn = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_SN);
        String merchantName = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_NAME);
        String cellphone = BeanUtil.getPropString(request, ConstantUtil.KEY_CELLPHONE);
        int card_status = BeanUtil.getPropInt(request, "card_status", -1);

        Map<String, String> merchantQuery = new HashMap<>();
        if (!StringUtil.empty(merchantSn)) {
            merchantQuery.put(ConstantUtil.KEY_MERCHANT_SN, merchantSn);
        }

        if (!StringUtil.empty(merchantName)) {
            merchantQuery.put(ConstantUtil.KEY_MERCHANT_NAME, merchantName);
        }

        if (!StringUtil.empty(cellphone)) {
            merchantQuery.put(ConstantUtil.KEY_CELLPHONE, cellphone);
        }

        ListResult merchants = null;
        if (!merchantQuery.isEmpty()) {
            merchants = merchantService.findMerchants(new PageInfo(1, 1000), merchantQuery);
        }

        if (merchants != null && merchants.getTotal() == 0) {
            return new ListResult(0, new ArrayList<Map>());
        }

        Map cardParams = new HashMap<>();
        cardParams.put(Card.ACTIVITY_ID, Collections.singletonList(activityId));

        if (card_status != -1) {
            cardParams.put(Card.STATUS, Collections.singletonList(card_status));
        }

        if (merchants != null && merchants.getTotal() > 0) {
            List<String> merchantIds = new ArrayList<>();
            for (Map merchant : merchants.getRecords()) {
                merchantIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
            }
            cardParams.put(Card.MERCHANT_ID, merchantIds);
        }

        ListResult cardList = cardService.findCards(pageInfo, cardParams);

        if (cardList.getTotal() == 0) {
            return cardList;
        }

        Set<String> merchantIds = new HashSet<>();
        for (Map card : cardList.getRecords()) {
            merchantIds.add(BeanUtil.getPropString(card, ConstantUtil.KEY_MERCHANT_ID));
        }

        ListResult whitelists = whitelistService.findWhiteList(
                new PageInfo(1, merchantIds.size()),
                CollectionUtil.hashMap(WhiteList.ACTIVITY_ID, activityId, WhiteList.MERCHANT_ID, merchantIds));
        Map<String, Map> whitelistMap = new HashMap<>();
        for (Map whitelist : whitelists.getRecords()) {
            whitelistMap.put(BeanUtil.getPropString(whitelist, ConstantUtil.KEY_MERCHANT_ID), whitelist);
        }

        ListResult merchantsFromWhitelist = null;

        if (merchants == null) {
            merchantsFromWhitelist = merchantService.findMerchants(
                    new PageInfo(1, merchantIds.size()),
                    CollectionUtil.hashMap("merchant_ids", CollectionUtil.listOf(merchantIds)));
        } else {
            merchantsFromWhitelist = merchants;
        }

        Map<String, Map> merchantMapFromWhitelist = new HashMap<>();
        for (Map merchant : merchantsFromWhitelist.getRecords()) {
            merchantMapFromWhitelist.put(BeanUtil.getPropString(merchant, DaoConstants.ID), merchant);
        }

        for (Map card : cardList.getRecords()) {
            String merchantId = BeanUtil.getPropString(card, ConstantUtil.KEY_MERCHANT_ID);
            Map merchant = (Map)BeanUtil.getProperty(merchantMapFromWhitelist, merchantId);
            Map whitelist = (Map)BeanUtil.getProperty(whitelistMap, merchantId);

            card.put(ConstantUtil.KEY_MERCHANT_NAME, BeanUtil.getPropString(merchant, Merchant.NAME));
            card.put(ConstantUtil.KEY_MERCHANT_SN, BeanUtil.getPropString(merchant, Merchant.SN));
            card.put(ConstantUtil.KEY_CELLPHONE, BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE));
            card.put("whitelist_ctime", BeanUtil.getPropString(whitelist, ConstantUtil.KEY_CTIME));
        }

        return cardList;
    }

    @Override
    public ListResult findCardInMerchant(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);

        if (pageInfo.getPageSize() > 1000) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "page_size太大");
        }

        String merchantSn = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_SN);
        String merchantName = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_NAME);
        String cellphone = BeanUtil.getPropString(request, ConstantUtil.KEY_CELLPHONE);
        String merchantIdParam = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        String activityName = BeanUtil.getPropString(request, "activity_name");
        List<Integer> cardStatus = (List<Integer>) BeanUtil.getProperty(request, "card_status");
        List<Integer> cardType = (List<Integer>) BeanUtil.getProperty(request, "card_type");

        Map merchantQuery = new HashMap<>();
        if (!StringUtil.empty(merchantSn)) {
            merchantQuery.put(ConstantUtil.KEY_MERCHANT_SN, merchantSn);
        }

        if (!StringUtil.empty(merchantName)) {
            merchantQuery.put(ConstantUtil.KEY_MERCHANT_NAME, merchantName);
        }

        if (!StringUtil.empty(cellphone)) {
            merchantQuery.put(ConstantUtil.KEY_CELLPHONE, cellphone);
        }

        if (!StringUtil.empty(merchantIdParam)) {
            merchantQuery.put("merchant_ids", Collections.singletonList(merchantIdParam));
        }

        ListResult merchants = null;
        if (!merchantQuery.isEmpty()) {
            merchants = merchantService.findMerchants(new PageInfo(1, 1000), merchantQuery);
        }

        if (merchants != null && merchants.getTotal() == 0) {
            return new ListResult(0, new ArrayList<Map>());
        }

        Map cardParams = new HashMap<>();

        ListResult activities = null;
        if (!StringUtil.empty(activityName)) {
            cardParams.put("activity_name", activityName);

            activities = activityService.findActivity(
                    new PageInfo(1, 1000),
                    CollectionUtil.hashMap(Activity.NAME, activityName));
            if (activities.getTotal() > 0) {
                List<String> activityIds = new ArrayList<>();
                for (Map activity : activities.getRecords()) {
                    activityIds.add(BeanUtil.getPropString(activity, DaoConstants.ID));
                }
                cardParams.put(Card.ACTIVITY_ID, activityIds);
            }
        }

        if (CollectionUtils.isNotEmpty(cardStatus)) {
            cardParams.put(Card.STATUS, cardStatus);
        }

        if (CollectionUtils.isNotEmpty(cardType)) {
            cardParams.put(Card.TYPE, cardType);
        }

        if (merchants != null && merchants.getTotal() > 0) {
            List<String> merchantIds = new ArrayList<>();
            for (Map merchant : merchants.getRecords()) {
                merchantIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
            }
            cardParams.put(Card.MERCHANT_ID, merchantIds);
        }

        ListResult cardList = cardService.filterCard4Osp(pageInfo, cardParams);

        if (cardList.getTotal() == 0) {
            return cardList;
        }

        Set<String> merchantIds = new HashSet<>();
        for (Map card : cardList.getRecords()) {
            merchantIds.add(BeanUtil.getPropString(card, ConstantUtil.KEY_MERCHANT_ID));
        }

        ListResult merchantsFromCards = null;
        if (merchants == null) {
            merchantsFromCards = merchantService.findMerchants(
                    new PageInfo(1, merchantIds.size()),
                    CollectionUtil.hashMap("merchant_ids", CollectionUtil.listOf(merchantIds)));
        } else {
            merchantsFromCards = merchants;
        }

        Set<String> activityIds = new HashSet<>();
        for (Map card : cardList.getRecords()) {
            activityIds.add(BeanUtil.getPropString(card, Card.ACTIVITY_ID));
        }
        ListResult activitiesFromCards = null;
        if (activities == null) {
            activitiesFromCards = activityService.findActivity(
                    new PageInfo(1, activityIds.size()),
                    CollectionUtil.hashMap(DaoConstants.ID, CollectionUtil.listOf(activityIds)));
        } else {
            activitiesFromCards = activities;
        }

        Map<String, Map> merchantMapFromCard = new HashMap<>();
        for (Map merchant : merchantsFromCards.getRecords()) {
            merchantMapFromCard.put(BeanUtil.getPropString(merchant, DaoConstants.ID), merchant);
        }

        Map<String, Map> activityMapFromCard = new HashMap<>();
        for (Map activity : activitiesFromCards.getRecords()) {
            activityMapFromCard.put(BeanUtil.getPropString(activity, DaoConstants.ID), activity);
        }
        for (Map card : cardList.getRecords()) {
            String merchantId = BeanUtil.getPropString(card, Card.MERCHANT_ID);
            String activityId = BeanUtil.getPropString(card, Card.ACTIVITY_ID);
            Map merchant = (Map)BeanUtil.getProperty(merchantMapFromCard, merchantId);
            Map activity = (Map)BeanUtil.getProperty(activityMapFromCard, activityId);

            card.put(ConstantUtil.KEY_MERCHANT_NAME, BeanUtil.getPropString(merchant, Merchant.NAME));
            card.put(ConstantUtil.KEY_MERCHANT_SN, BeanUtil.getPropString(merchant, Merchant.SN));
            card.put(ConstantUtil.KEY_CELLPHONE, BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE));
            card.put("activity_name", BeanUtil.getPropString(activity, Activity.NAME));
            card.put("activity_sn", BeanUtil.getPropString(activity, Activity.SN));
        }

        return cardList;
    }

    @Override
    public Map findCardBySn(Map request) {
        String sn = BeanUtil.getPropString(request, Card.SN);
        return cardService.getCardDetailBySn(sn);
    }

    @Override
    public void exportCardInActivity(Map params, HttpServletResponse response) throws IOException {
        params.put("page_size", 60000);
        ListResult result = findCardInActivity(params);
        List<Map> records = result.getRecords();
        if (result.getTotal() > 60000) {
            throw new UpayException(UpayException.CODE_ACCESS_DENIED, "一次导出的记录太多,请重新选择");
        }
        Workbook workbook = buildCardInActivityExcel(records);
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + DateTimeUtil.getTimeString(new Date().getTime()) + "-cardInActivity.xls");
        workbook.write(response.getOutputStream());
    }

    private HSSFWorkbook buildCardInActivityExcel(List<Map> cards) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("卡券数据");
        SheetUtil sheetUtil = new SheetUtil(sheet);
        List<String> headers = Arrays.asList(
                "导入时间",
                "商户号",
                "商户名称",
                "商户手机号",
                "卡券编码",
                "领取渠道",
                "领取时间",
                "启用时间",
                "卡券状态",
                "发放者"
        );
        sheetUtil.appendRow(headers);

        List<String> columnArr = Arrays.asList(
                "whitelist_ctime",
                ConstantUtil.KEY_MERCHANT_SN,
                ConstantUtil.KEY_MERCHANT_NAME,
                ConstantUtil.KEY_CELLPHONE,
                Card.SN,
                Card.PROVIDER_CHANNEL,
                DaoConstants.CTIME,
                Card.EFFECTIVE_TIME,
                Card.STATUS,
                Card.PROVIDER_NAME
        );

        for(Map card: cards){
            List values = new ArrayList();
            for(String column: columnArr){
                if (column.equals("whitelist_ctime") ||
                        column.equals(DaoConstants.CTIME) ||
                        column.equals(Card.EFFECTIVE_TIME)) {
                    if (StringUtil.empty(BeanUtil.getPropString(card, column))) {
                        values.add("");
                    } else {
                        values.add(DateTimeUtil.getDigitalTimeString(Long.valueOf(BeanUtil.getPropString(card, column))));
                    }
                    continue;
                }


                if (column.equals(Card.PROVIDER_CHANNEL)) {
                    values.add(CardUtil.getChannelDesc(BeanUtil.getPropString(card, column)));
                    continue;
                }

                if (column.equals(Card.STATUS)) {
                    values.add(CardUtil.getStatusDesc(BeanUtil.getPropString(card, column)));
                    continue;
                }

                values.add(BeanUtil.getPropString(card, column));
            }
            sheetUtil.appendRow(values);
        }

        return workbook;
    }

    @Override
    public Map updateActivityPriority(Map request) {
        String id = BeanUtil.getPropString(request, "id");
        int priority = BeanUtil.getPropInt(request, "priority", 0);

        if (StringUtil.empty(id)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "id不能为空");
        }

        return activityService.updateActivityPriority(
                CollectionUtil.hashMap(DaoConstants.ID, id, "priority", priority));
    }
}
