package com.wosai.upay.service.remote;

import com.wosai.profit.sharing.model.Application;
import com.wosai.web.api.ListResult;

import javax.validation.Valid;
import java.util.Map;

public interface RemoteApplicationService {

    /**
     * 新建应用
     * @param application
     *  name
     *  desc
     * @return 分账应用信息
     */
    Map createApplication(@Valid Application application);

    /**
     * 修改应用
     * @param application
     *  id
     *  desc
     * @return 更新后的结果信息
     */
    Map updateApplication(Application application);


    /**
     * 获取应用信息
     * @param id 应用id
     * @return 应用id信息
     */
    Map getApplication(String id);

    /**
     * 关闭
     * @param id 应用id
     */
    void closeApplication(String id);

    /**
     * 启用
     * @param id 应用id
     */
    void enableApplication(String id);

    /**
     * 禁用
     * @param id 应用id
     */
    void disableApplication(String id);

    /**
     * 查询应用
     * @param conditions 查询条件
     * @param pagination 分页条件
     * @return 应用列表
     */
    ListResult<Map> queryApplications(Map<String, Object> conditions, Map<String, Object> pagination);

}
