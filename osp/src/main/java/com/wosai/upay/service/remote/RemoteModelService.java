package com.wosai.upay.service.remote;

import com.wosai.profit.sharing.model.Model;
import com.wosai.web.api.ListResult;

import java.util.Map;

public interface RemoteModelService {

    /**
     * 新建分账模型
     * @param model
     *  name
     *  receiver_ids
     * @return 获取分账模型
     */
    Map createModel(Model model);

    /**
     * 修改分账模型
     * @param model
     *  id
     *  name
     * @return 更新后的结果
     */
    Map updateModel(Model model);

    /**
     * 获取分账模型信息
     * @param id 模型id
     * @return 返回结果
     */
    Map<String,Object> getModel(String id);

    /**
     * 禁用
     * @param id 模型id
     */
    void disableModel(String id);

    /**
     * 启用
     * @param id
     */
    void enableModel(String id);

    /**
     * 查询模型信息
     * @param conditions
     *  name 模糊查询
     *  status
     */
    ListResult<Map<String,Object>> queryModels(Map<String, Object> conditions, Map<String, Object> map);
}

