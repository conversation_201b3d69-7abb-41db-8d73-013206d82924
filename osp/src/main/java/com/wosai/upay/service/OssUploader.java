package com.wosai.upay.service;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSErrorCode;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.ObjectMetadata;
import com.wosai.upay.helper.FileUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

@Component
public class OssUploader {
    private static final String endpointURL = "http://oss.aliyuncs.com";

    private static final String accessId = "LTAIgdvAVQ7FnGIO";

    private static final String accessKey = "Da99zhuALMOrvIQquyGe7q36gEmuJR";

    private static final String STATICS_BUCKET_NAME = "wosai-statics";

    private static final String IMAGE_BUCKET_NAME = "wosai-images";

    @Value("${oss.base-url}")
    private String baseUrl;

    private OSSClient client;

    public OssUploader() {
        client = new OSSClient(endpointURL, accessId, accessKey);
    }

    //上传图片
    public void uploadImage(String key, InputStream input, long length) {
        upload(IMAGE_BUCKET_NAME, key, input, length);
    }

    //上传文件
    public void uploadStaticsFile(String key, InputStream input, long length) {
        upload(STATICS_BUCKET_NAME, key, input, length);
    }

    public void uploadIfNotExists(String bucketName, String key, InputStream input, long length, String contentType) {
        if (!exists(bucketName, key)) {
            upload(bucketName, key, input, length, contentType);
        }
    }

    public boolean exists(String bucketName, String key) {
        try {
            getObjectMetadata(bucketName, key);
            return true;
        } catch (OSSException e) {
            if (OSSErrorCode.NO_SUCH_KEY.equals(e.getErrorCode()) || OSSErrorCode.NO_SUCH_BUCKET.equals(e.getErrorCode())) {
                return false;
            } else {
                throw e;
            }
        }
    }

    private void upload(String bucketName, String key, InputStream input, long length, String contentType) {
        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(length);
        if (contentType != null) {
            objectMeta.setContentType(contentType);
        }
        client.putObject(bucketName, key, input, objectMeta);
    }

    private ObjectMetadata getObjectMetadata(String bucketName, String key) {
        return client.getObjectMetadata(bucketName, key);
    }

    // 上传文件
    private void upload(String bucketName, String key, InputStream input, long length) {
        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(length);
        client.putObject(bucketName, key, input, objectMeta);
    }

    public String createExcelUpload(XSSFWorkbook workbook,String fileName,String baseDir) throws IOException{
        fileName = fileName + System.currentTimeMillis();
        String fileExt = ".xlsx";
        String fullName = fileName + fileExt;
        String ossKey = baseDir + fullName;
        String ossUrl = baseUrl + ossKey;
        File file = File.createTempFile(fileName, fileExt);
        FileUtil.excelToFile(workbook, file.getAbsolutePath());

        FileInputStream inputStream = null;
        try {
            inputStream = new FileInputStream(file);
            uploadStaticsFile(ossKey, inputStream, file.length());
            return ossUrl;
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

}
