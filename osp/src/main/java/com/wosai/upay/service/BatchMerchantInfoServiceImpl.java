package com.wosai.upay.service;

import com.wosai.data.util.CollectionUtil;
import com.wosai.sales.core.service.IKeeperService;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.core.service.UserService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.helper.SheetUtil;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import com.wosai.upay.util.ExcelUtil;
import com.wosai.upay.wallet.service.WalletService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2019/08/12
 */
@Service
public class BatchMerchantInfoServiceImpl implements BatchMerchantInfoService {

    final private static Integer LAKALA_SN = 0; // 拉卡拉商户号
    final private static Integer BANK_SN = 1; //银行商户号
    final private static Integer ALIPAY_NOT_DIRECT_TYPE_SN = 2; //支付宝间联商户号
    final private static Integer WEIXIN_NOT_DIRECT_TYPE_SN = 3; //微信间联商户号
    final private static Integer WEIXIN_DIRECT_TYPE_SN = 4; //微信直联商户号
    final private static Integer ALIPAY_DIRECT_TYPE_SN= 5; //支付宝直联商户号
    final private static Integer MERCHANT_SN = 6; //商户号
    private static final int MAX_LOG_THREAD_COUNT = 10; //最多同时有多少个线程执行
    private static final int MERCHANT_STATUS_CLOSED = 0; //商户关闭
    private static final int MERCHANT_STATUS_NORMAL = 1; //商户正常
    private static final int MERCHANT_STATUS_FORBIDDEN = 2; //商户禁止
    private static final int ALIPAY_PAYWAY = 2; //支付宝
    private static final int WEIXIN_PAYWAY = 3; //微信
    private static final Logger logger = LoggerFactory.getLogger(BatchMerchantInfoServiceImpl.class);
    ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(MAX_LOG_THREAD_COUNT);


    @Autowired
    private IMerchantService salesMerchantService;
    @Autowired
    private ProviderTradeParamsService providerTradeParamsService;
    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;
    @Autowired
    private UserService userService;
    @Autowired
    private IKeeperService keeperService;
    @Autowired
    private OssUploader ossUploader;
    @Autowired
    @Qualifier(value = "tradeConfigServiceBatch")
    private TradeConfigService tradeConfigServiceBatch;
    @Autowired
    private WalletService walletService;

    @Override
    public String findMerchantInfo(MultipartFile file, Integer typeCode) throws InterruptedException {
        Workbook workbook = ExcelUtil.getExcelFromFile(file);
        List<String> typeSnList = ExcelUtil.getFirstRowFromSheet(workbook.getSheetAt(0));
        //去除表头
        if (typeSnList != null) {
            typeSnList.remove(0);
        }

        if (CollectionUtils.isEmpty(typeSnList)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "空excel没有商户数据");
        }

        if (typeSnList.size() > 200) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过200条");
        }

        List<Map> merchantInfo = getMerchantInfo(typeSnList, typeCode);

        return generateMerchantInfoExcel(merchantInfo);
    }

    @Override
    public List<Map> getMerchantInfo(List<String> typeSnList, Integer typeCode) throws InterruptedException {
        List<Map> merchantInfos = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch latch = new CountDownLatch(typeSnList.size());
        for (String typeSn : typeSnList) {
            threadPoolExecutor.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        if (typeCode.equals(MERCHANT_SN)) {
                            Map merchantInfo = getMerchantInfoByMerchantSn(typeSn);
                            merchantInfo.put("查询条件", "商户号" + typeSn);
                            merchantInfo.put("备注", null);
                            merchantInfos.add(merchantInfo);
                        } else if (typeCode.equals(LAKALA_SN)) {
                            Map merchantInfo = getMerchantInfoByMerchantContractSn(typeSn, "pay_merchant_id");
                            merchantInfo.put("查询条件", "拉卡拉商户号" + typeSn);
                            merchantInfo.put("备注", null);
                            merchantInfos.add(merchantInfo);
                        } else if (typeCode.equals(ALIPAY_NOT_DIRECT_TYPE_SN)) {
                            Map merchantInfo = getMerchantInfoByMerchantContractSn(typeSn, "pay_merchant_id");
                            merchantInfo.put("查询条件", "支付宝间联商户号" + typeSn);
                            merchantInfo.put("备注", null);
                            merchantInfos.add(merchantInfo);
                        } else if (typeCode.equals(WEIXIN_NOT_DIRECT_TYPE_SN)) {
                            Map merchantInfo = getMerchantInfoByMerchantContractSn(typeSn, "pay_merchant_id");
                            merchantInfo.put("查询条件", "微信间联商户号" + typeSn);
                            merchantInfo.put("备注", null);
                            merchantInfos.add(merchantInfo);
                        } else if (typeCode.equals(BANK_SN)) {
                            Map merchantInfo = getMerchantInfoByMerchantContractSn(typeSn, "provider_merchant_id");
                            merchantInfo.put("查询条件", "银行商户号" + typeSn);
                            merchantInfo.put("备注", null);
                            merchantInfos.add(merchantInfo);
                        } else if (typeCode.equals(ALIPAY_DIRECT_TYPE_SN)) {
                            List<Map> merchants = getMerchantInfoByAlipayDirectSn(typeSn);
                            merchantInfos.addAll(merchants);
                        } else if (typeCode.equals(WEIXIN_DIRECT_TYPE_SN)){
                            List<Map> merchants = getMerchantInfoByWeixinDirectSn(typeSn);
                            merchantInfos.addAll(merchants);
                        }
                    } catch (Exception e) {
                        logger.error("商户信息查询失败:{}", e.getMessage());
                        Map merchantInfo = CollectionUtil.hashMap(
                                "备注", "查询失败",
                                "查询条件", typeSn);
                        merchantInfos.add(merchantInfo);
                    } finally {
                        latch.countDown();
                    }
                }
            });
        }
        latch.await();
        return merchantInfos;
    }

    private Map getMerchantInfoByMerchantContractSn(String typeSn, String typeId) {
        PageInfo pageInfo = new PageInfo(1,1);
        Map params = CollectionUtil.hashMap(typeId, typeSn);
        ListResult data = providerTradeParamsService.listMerchantProviderParams(pageInfo, params);
        if (data.getTotal() != 0) {
            String merchantSn = MapUtils.getString(data.getRecords().get(0), "merchant_sn", null);
            if (merchantSn != null) {
                return getMerchantInfoByMerchantSn(merchantSn);
            }
        }
        return null;
    }

    /**
     * 直连商户号存在一对多情形
     *
     * @param sn
     * @return
     */
    private List<Map> getMerchantInfoByAlipayDirectSn(String sn) {
        return getMerchantInfoByDriectSn(sn, 2);
    }

    /**
     * 直连商户号存在一对多情形
     *
     * @param sn
     * @return
     */
    private List<Map> getMerchantInfoByWeixinDirectSn(String sn) {
        return getMerchantInfoByDriectSn(sn, 3);
    }

    private List<Map> getMerchantInfoByDriectSn(String sn, int payway){
        int page = 1;
        int pageSize = 100;
        long total = 0L;
        List<Map> merchantList = new ArrayList<>();
        MerchantProviderParamsCustomDto dto = new MerchantProviderParamsCustomDto();
        dto.setPayway(payway);
        dto.setPay_merchant_id(sn);
        //读取数据
        while (true) {
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPage(page);
            pageInfo.setPageSize(pageSize);
            com.wosai.web.api.ListResult<MerchantProviderParamsCustomDto> data = merchantProviderParamsService.findMerchantProviderParamsList(pageInfo, dto);
            total = data.getTotal();
            List<MerchantProviderParamsCustomDto> paramsDtos = data.getRecords();
            if (CollectionUtils.isEmpty(paramsDtos)) {
                break;
            }
            for(MerchantProviderParamsCustomDto d : paramsDtos){
                String merchantSn = d.getMerchant_sn();
                Map merchant = getMerchantInfoByMerchantSn(merchantSn);
                if(payway == 2){
                    merchant.put("支付宝直联商户号",sn);
                    merchant.put("查询条件", "支付宝直连商户号" + sn);
                }else if(payway == 3) {
                    merchant.put("微信直联商户号",sn);
                    merchant.put("查询条件", "微信直连商户号" + sn);
                }
                merchant.put("备注", null);
                merchantList.add(merchant);
            }
            page += 1;
            if (page > (total / pageSize + 1)) {
                break;
            }
        }
        return merchantList;
    }

    @Override
    public Map getMerchantInfoByMerchantSn(String merchantSn) {
        Map merchant = salesMerchantService.getMerchantBySn(merchantSn);
        Map result = CollectionUtil.hashMap("商户名称", MapUtils.getString(merchant, "name", null),
                                                    "商户号", MapUtils.getString(merchant, "sn", null),
                                                    "商户状态", MapUtils.getString(merchant, "status", null)
                                            );
        Integer merchantStatus = MapUtils.getInteger(merchant, "status", null);
        if (merchantStatus == null) {
            result.put("商户状态", null);
        } else if (merchantStatus == MERCHANT_STATUS_CLOSED) {
            result.put("商户状态", "关闭");
        } else if (merchantStatus == MERCHANT_STATUS_NORMAL) {
            result.put("商户状态", "正常");
        } else if (merchantStatus == MERCHANT_STATUS_FORBIDDEN) {
            result.put("商户状态", "禁止");
        }
        //获取推广人（Promoter）的信息
        String promoterId = MapUtils.getString(merchant, "user_id", null);
        Map promoterInfo = getPromoterInfo(promoterId);

        //获取维护人（Keeper）的信息，商户的多个维护人，取最新的维护人
        String merchantId = MapUtils.getString(merchant, "id", null);
        Map keeperInfo = getKeeperInfo(merchantId);

        //调core-b的接口去查拉卡拉商户号、支付宝间联商户号、微信间联商户号、银行商户号

        Map lakala = getOtherSn(merchantId, null);
        Map weixinNotDirect = getOtherSn(merchantId, WEIXIN_PAYWAY);
        Map alipayNotDirect = getOtherSn(merchantId, ALIPAY_PAYWAY);
        long balance = walletService.getBalance(merchantId);

        //根据merchantId来了获取微信和支付宝的直联商户号
//        String weixin = getDirectSn(merchantId, WEIXIN_PAYWAY);
//        String alipay = getDirectSn(merchantId, ALIPAY_PAYWAY);
//        result.put("微信直联商户号", weixin);
//        result.put("支付宝直联商户号", alipay);

        //合并结果
        result.putAll(promoterInfo);
        result.putAll(keeperInfo);
        result.putAll(lakala);
        result.putAll(weixinNotDirect);
        result.putAll(alipayNotDirect);
        result.put("收款余额", balance/100.0);

        return result;
    }

    private Map getPromoterInfo(String promoterId) {
        Map promoter = userService.getUserWithOrganization(promoterId);
        return CollectionUtil.hashMap(
                "推广人", MapUtils.getString(promoter, "linkman", null),
                "推广人组织", MapUtils.getString(promoter, "name_path", null)
                                    );
    }

    @Override
    public Map getKeeperInfo(String merchantId) {
        List<Map<String, Object>> keepers = keeperService.findMerchantKeepers(merchantId);
        Map keeper = getLatestKeeper(keepers);
        return CollectionUtil.hashMap(
                "维护人", MapUtils.getString(keeper, "linkman", null),
                "维护人组织", MapUtils.getString(keeper, "organization_names", null)
        );
    }

    private Map getLatestKeeper(List<Map<String, Object>> keepers) {
        if (!CollectionUtils.isEmpty(keepers)) {
            Map<String, Object> latestKeeper = keepers.get(0);
            for (Map<String, Object> keeper : keepers) {
                LocalDateTime mtime = LocalDateTime.ofInstant(
                        Instant.ofEpochMilli(MapUtils.getLongValue(keeper,"mtime")), ZoneId.systemDefault());
                LocalDateTime newMtime = LocalDateTime.ofInstant(
                        Instant.ofEpochMilli(MapUtils.getLongValue(latestKeeper,"mtime")), ZoneId.systemDefault());
                if (mtime.compareTo(newMtime) > 0) {
                    latestKeeper = keeper;
                }
            }
            return latestKeeper;
        }
        return null;
    }


    @Override
    public Map getOtherSn(String merchantId, Integer payway) {
        Map resultMap = new HashMap();
        Map result = tradeConfigServiceBatch.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        Map<String, Object> params = MapUtils.getMap(result, "params");
        if (params != null && payway != null) {
            for (String key : params.keySet()) {
                if (key.contains("_trade_params")) {
                    Object tradeParams = params.get(key);
                    if (tradeParams instanceof Map) {
                        if (payway == ALIPAY_PAYWAY && ((Map) tradeParams).containsKey("alipay_sub_mch_id")) {
                            resultMap.put("支付宝间联商户号" ,MapUtils.getString((Map)tradeParams, "alipay_sub_mch_id"));
                            resultMap.put("银行商户号" ,MapUtils.getString((Map)tradeParams, "provider_mch_id"));
                            return resultMap;
                        } else if (payway == WEIXIN_PAYWAY && ((Map) tradeParams).containsKey("weixin_sub_mch_id") ) {
                            resultMap.put("微信间联商户号" ,MapUtils.getString((Map)tradeParams, "weixin_sub_mch_id"));
                            resultMap.put("银行商户号" ,MapUtils.getString((Map)tradeParams, "provider_mch_id"));
                            return resultMap;
                        }
                    }
                }
            }
        } else if (params != null && params.containsKey("lakala_trade_params")) {
            Object lakalaTradeParams = params.get("lakala_trade_params");
            if (lakalaTradeParams instanceof Map) {
                resultMap.put("拉卡拉商户号", MapUtils.getString((Map)lakalaTradeParams, "lakala_merc_id"));
                return resultMap;
            }
        }
        return resultMap;
    }

    @Override
    public String getDirectSn(String merchantId, Integer payway) {
        //payway 支付宝 2；微信 3
        Map result = tradeConfigServiceBatch.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        Map<String, Map> params = MapUtils.getMap(result, "params");
        Integer b2cFormal = MapUtils.getInteger(result, "b2c_formal");
        if (params != null && b2cFormal != null) {
            if (b2cFormal.equals(1)) {
                return getAlipayOrWeixinDirectSn(params, payway);
            }
        }
        return null;
    }

    private String getAlipayOrWeixinDirectSn(Map<String, Map> params, Integer payway) {
        String resultString = "";
        if (payway == ALIPAY_PAYWAY && (params.containsKey("alipay_v2_trade_params") || params.containsKey("alipay_wap_v2_trade_params") ||
                params.containsKey("alipay_h5_v2_trade_params") || params.containsKey("alipay_app_v2_trade_params"))) {
            for (Object value : params.values()) {
                if (value instanceof Map) {
                    if (MapUtils.getString((Map)value, "pid") != null) {
                        resultString = MapUtils.getString((Map)value, "pid");
                        break;
                    }
                }
            }
        } else if (payway == WEIXIN_PAYWAY&& (params.containsKey("weixin_trade_params") || params.containsKey("weixin_wap_trade_params") ||
                params.containsKey("weixin_mini_trade_params") || params.containsKey("weixin_h5_trade_params") || params.containsKey("weixin_app_trade_params"))
        ) {
            for (Object value : params.values()) {
                if (value instanceof Map) {
                    if (MapUtils.getString((Map)value, "weixin_sub_mch_id") != null) {
                        resultString = MapUtils.getString((Map)value, "weixin_sub_mch_id");
                        break;
                    }
                }
            }
        }
        return resultString;
    }

    @Override
    public String generateMerchantInfoExcel(List<Map> merchantInfo) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("商户相关信息");
        SheetUtil sheetUtil = new SheetUtil(sheet);
        List<String> title = Arrays.asList("商户名称", "商户号", "商户状态", "推广人","推广人组织", "维护人","维护人组织", "拉卡拉商户号",
                "支付宝间联商户号","微信间联商户号", "银行商户号", "微信直联商户号", "支付宝直联商户号", "查询条件", "备注","收款余额");
        sheetUtil.appendRow(title);
        sheetUtil.appendRows(title, merchantInfo);
        String baseDir = "osp/merchantInfo/";
        try {
            return ossUploader.createExcelUpload(workbook, "查询商户相关信息", baseDir);
        } catch (Exception e) {
            logger.error("文件上传OSS失败", e);
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件上传OSS失败，无法下载，请重试");
        }
    }
}
