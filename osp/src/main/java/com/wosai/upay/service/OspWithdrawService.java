package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created by kay on 16/11/2.
 */
public interface OspWithdrawService {
    /**
     * 获取提现列表
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                merchant_id
     * @return
     */
    ListResult getWithDrawList(Map<String, Object> request);
    /**
     * 提现申请通过，驳回
     *
     * @param request
     * @return
     */
    String changeWithDrawStatus(Map request);

    /**
     * 批量打款申请，用于d1打款, 异步处理
     * @param data
     */
    void asyncBatchTransferForD1(List<Map> data);

    /**
     * 修改提现记录备注
     *
     * @param request
     * @return
     */
    Map updateWithdrawRemark(Map request);

    /**
     * 商户提现记录导出
     *
     *
     * @param params
     * @param response
     */
    Map exportWithdrawList(Map params, HttpServletResponse response) throws Exception;

    /**
     * 批量提现状态修改
     *
     * @param request
     */
    List batchChangeWithDrawStatus(Map request);

    /**
     * 查询和同步拉卡拉打款状态
     *
     * @param
     * @return
     */
    String withdrawVerifyOfLakala(Map request);

    /**
     * 批量查询和同步拉卡拉打款状态
     *
     * @param
     * @return
     */
    List<Map> batchWithdrawVerifyOfLakala(Map request);

    /**
     * 获取提现操作记录
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                merchant_id
     * @return
     */
    List<Map<String, Object>> getWithdrawOperationLog(Map<String, Object> request);

    /**
     * 修改赔付状态
     *
     * @param request : {
     *                records: [withdraw_id...], // withdraw id 数组
     *                clearing_delayed: 0, // 清算是否超时 0:没超时； 1：超时
     *                need_to_compensate: 0, // 是否需要赔付 0：不需要； 1： 需要
     *                compensation_status: 0, // 赔付状态 0：赔付未开始； 1：赔付进行中；2：赔付完成
     *                remark: "", // 选填
     *                compensation_amount: "" // 必填，单位：分
     *                }
     * @return failure {total: 2, "withdraw_id": "error message", ...} key=total, value=失败记录数，其余：key=withdraw_id，value=异常信息
     */
    Map batchChangeCompensationStatus(Map<String, Object> request);

    /**
     * 获取赔付详情
     * @param request
     * @return
     */
    Map getCompensationAmount(Map<String,Object> request);
    /**
     * 发起赔付流程
     * @param request 请求
     * @return 结果
     */
    Map d1CompensationForMoney(Map<String,Object> request);


    /**
     * 获取D0白名单列表
     *
     * @param request page
     *                page_size
     *                merchant_name
     *                merchant_sn
     *                merchant_contact_phone
     *                order_by: [{field: ,order: }, {field: ,order: }]
     * @return
     */
    ListResult getWithDrawRealTimeList(Map<String, Object> request);

    /**
     * 批量导入D0白名单
     *
     * @param file
     * @param noticeCode
     * @param noticeSubCode
     * @return
     */
    Map batchImportDrawRealTime(MultipartFile file, String noticeCode, String noticeSubCode);

    /**
     * 批量修改D0白名单
     *
     * @param file
     * @return
     */
    Map importDrawRealTimeConfig(MultipartFile file);

    /**
     * 查询保存导入D0白名单详情
     *
     * @param request
     * @return
     */
    public void getImportDrawRealTimeLog(Map<String, Object> request, HttpServletResponse response) throws IOException;

    /**
     * 批量删除D0白名单
     * merchant_sn
     *
     * @param request merchant_ids:[]
     *                notice_code
     *                notice_sub_code
     *                remark
     * @return
     */
    Map batchDeleteDrawRealTime(Map<String, Object> request);

    /**
     * 修改免费次数
     * merchant_id
     *
     * @param request merchant_id
     *                inOrDecreaseNum
     * @return
     */
    Map updateDrawRealTimeFreeCount(Map<String, Object> request);

    /**
     * 修改商户层级提现规则配置
     *
     * @param request
     * @return
     */
    Map batchUpdateDrawRealTimeFreeCount(Map<String, Object> request);

    /**
     * 修改通用提现规则配置
     *
     * @param request
     * @return
     */
    Map updateWithDrawRulerConfig(Map<String, Object> request);

    /**
     * 新增全局禁用提现规则配置
     *
     * @param request
     * @return
     */
    Map addWithDrawSystemRuleConfig(Map<String, Object> request);

    Map deleteWithDrawSystemRuleConfig(Map<String, Object> request);

    Map updateWithDrawSystemRuleConfig(Map<String,Object> request);

    /**
     * 新增银行禁用提现规则配置
     *
     * @param request
     * @return
     */
    Map addWithDrawBankSystemRuleConfig(Map<String, Object> request);

    Map deleteWithDrawBankSystemRuleConfig(Map<String,Object> request);

    Map updateWithDrawBankSystemRuleConfig(Map<String,Object> request);


    /**
     * 修改商户层级提现规则配置
     *
     * @param request
     * @return
     */
    Map batchUpdateMerchantWithDrawRulerConfig(Map<String, Object> request);

    /**
     * 获取通用提现规则配置
     *
     * @param request
     * @return
     */
    Map getWithDrawRulerConfig(Map<String, Object> request);
    /**
     * 新增推广者组织提现规则配置（如果推广者组织已有规则则报错）
     *
     * @param request
     * @return
     */
    Map createOrganizationWithDrawRulerConfig(Map<String, Object> request);
    /**
     * 修改推广者组织提现规则配置
     *
     * @param request
     * @return
     */
    Map updateOrganizationWithDrawRulerConfig(Map<String, Object> request);
    /**
     * 批量创建推广者组织提现规则配置
     *
     * @param request
     * @return
     */
    List<Map> batchCreateOrganizationWithDrawRulerConfig(Map<String, Object> request);
    /**
     * 获取推广者组织提现规则配置
     *
     * @param request
     * @return
     */
    Map getOrganizationWithDrawRulerConfig(Map<String, Object> request);
    /**
     * 查询推广者组织D0体现规则配置
     *
     * @param request
     *          parentId
     *          top    true or false or null
     * @return
     */
    ListResult findOrganizationWithDrawRuleConfigs(Map<String, Object> request);
    /**
     * 删除推广者组织提现规则配置
     *
     * @param request
     * @return
     */
    void deleteOrganizationWithDrawRulerConfig(Map<String, Object> request);

    /**
     * 商户是否有D0的权限
     *
     * @param request
     * @return {"allow":true,"remainder_number":10}
     */
    Map merchantAvailableWithdrawRealtime(Map<String, Object> request);
    /**
     * 商户是否有提现权限
     *
     * @param request
     * @return {"allow":true}
     */
    Map merchantAvailableWithdraw(Map<String, Object> request);


    /***
     * 批量修改短信通知
     * @param request
     * @return
     */
    Map batchModifySmsNotify(Map request);

    /**
     * 商户是否关闭短信提现通知
     *
     * @param request
     * @return {"allow":true,"remainder_number":10}
     */
    Map merchantSmsCloseStatus(Map request);

    /**
     *获取通知规则
     * @param request
     * @return
     */
    Map getNoticeRule(Map request);
    /**
     *获取通知规则集合
     * @param request
     * @return
     */
    List<Map> getNoticeRules(Map request);
    /**
     *删除通知规则
     * @param request
     * @return
     */
    void deleteNoticeRule(Map request);
    /**
     *修改通知规则
     * @param request
     * @return
     */
    Map updateNoticeRule(Map request);
    /**
     *创建通知规则
     * @param request
     * @return
     */
    Map createNoticeRule(Map request);
    /**
     *获取通知规则列表
     * @param request
     * @return
     */
    ListResult findNoticeRules(Map request);
    /**
     *获取通知模板编号集合
     * @param request
     * @return
     */
    List<Map> getNoticeRuleCodes(Map request);

    /**
     * 批量禁用商户D0
     * @return
     */
    Map batchDisableMerchantwithdrawrealtime(Map request);
    /**
     * 批量启用商户D0
     * @return
     */
    Map batchEnableMerchantwithdrawrealtime(Map request);


    /**
     * 批量导入强制结算名单
     *
     * @param file
     * @return
     */
    Map batchImportForceClearMerchants(MultipartFile file);

    /**
     * 查询导入强制结算名单结果
     *
     * @param request
     * @return
     */
    void getImportForceClearMerchantsDetail(Map<String, Object> request, HttpServletResponse response) throws IOException;

    /**
     * 查询d1优先打款批次详情
     *
     * @param request
     */
    Map getD1WithdrawBatch(Map request);
    /**
     * 查询商户所属d1优先打款批次
     *
     * @param request
     */
    Map getMerchantAnalyzedD1WithdrawBatch(Map request);

    /**
     * 查询d1优先打款批次
     *
     * @param request
     * page
     * page_size
     *
     */
    ListResult findD1WithdrawBatchs(Map request);

    /**
     * 修改d1优先打款批次
     *
     * @param request
     */
    Map updateD1WithdrawBatch(Map request);

    /**
     * 删除d1优先打款批次
     *
     * @param request
     * batch_id
     */
    void deleteD1WithdrawBatch(Map request);

    /**
     * 批量导入优先批次白名单
     *
	 * @param multipartFile
	 */
	Map importPriorMerchantWhiteList(MultipartFile multipartFile);

    /**
     * 查询商户禁用原因
     * @param request
     * @return
     */
    List<Map<String, Object>> queryMerchantDisableReason(Map request);

    /**
     * 查询商户d0入口是否可见
     * @param request
     * @return
     */
    boolean queryMerchantD0EntranceVisible(Map request);

    /**
     * 获取商户节假日大额提现拆分白名单
     * @param request
     * @return
     */
	Map getLargeAmountWithdrawSpConfig(Map request);

    /**
     * 商户关闭节假日大额提现拆分白名单设置
     * @param request
     */
    void closeLargeAmountWithdrawSpConfig(Map request);

    /**
     * 商户激活假日大额提现拆分白名单设置
     * @param request
     */
    void enableLargeAmountWithdrawSpConfig(Map request);

    /**
     * 查询禁用原因
     * @param request
     */
    Map queryD0DisableReason(Map request);

    /**
     * 获取用户的d1提现配置
     *
     */
    Map queryMerchantD1Config(Map<String,Object> request);

    /**
     * 新增商户d1提现配置
     * @param request
     * @return
     */
    void addMerchantD1Config(Map<String,Object> request);

    /**
     * 修改商户d1提现配置
     * @param request
     * @return
     */
    void updateMerchantD1Config(Map<String,Object> request);

    /**
     * 获取d1批次列表
     */
    List<Map<String,Object>> getAllD1BatchList();


    /**
     * 设置提现为等待拉卡拉划款
     */
    void withdrawOperatorTransferWaitLakalaRepairPay(Map<String,Object> request);

    /**
     * 批量删除商户禁用原因
     */
    Map batchDeleteMerchantD0DisableReason(MultipartFile file);

    /**
     * 余额转出权限控制
     */
    void forbidWalletTurnOut(Map<String,Object> map);

    void allowWalletTurnOut(Map<String,Object> map);

    Map getAllowWalletTurnOutStatus(Map<String,Object> map);
}

