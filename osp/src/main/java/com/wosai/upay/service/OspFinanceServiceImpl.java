package com.wosai.upay.service;

import com.google.common.collect.Lists;
import com.wosai.assistant.response.OrganizationBean;
import com.wosai.assistant.service.OrganizationRpcService;
import com.wosai.bsm.financebackend.constant.FinanceConstant;
import com.wosai.bsm.financebackend.constant.RequestConstant;
import com.wosai.bsm.financebackend.constant.Rule;
import com.wosai.bsm.financebackend.exception.FinanceBackendBizErrorException;
import com.wosai.bsm.financebackend.model.*;
import com.wosai.bsm.financebackend.service.*;
import com.wosai.bsm.loanbackend.model.MerchantInfo;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.merchant.Bean.MerchantConstant;
import com.wosai.shouqianba.withdrawservice.model.NoticeRule;
import com.wosai.shouqianba.withdrawservice.service.NoticeRuleService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.common.util.MapUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.*;
import org.apache.commons.collections.map.HashedMap;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

@Service
@SuppressWarnings("unchecked")
public class OspFinanceServiceImpl implements OspFinanceService {

    private static Logger logger = LoggerFactory.getLogger(OspFinanceServiceImpl.class);

    private static final String[] IGNORE_FIELDS = new String[]{"merchant_id", "merchant_sn"};
    private static final String[] IGNORE_DATE_PARAMS = new String[]{"page", "page_size", "order_by", "date_start", "date_end"};

    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private UserAmountService userAmountService;
    @Autowired
    private WithdrawRecordService withdrawRecordService;
    @Autowired
    private IncomeRecordService incomeRecordService;
    @Autowired
    private BankService bankService;
    @Autowired
    private RuleService ruleService;
    @Autowired
    private DailyRecordService dailyRecordService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private PurchaseRecordService purchaseRecordService;
    @Autowired
    private OspTaskService ospTaskService;
    @Autowired
    private LogService logService;
    @Autowired
    private BusinessLogService businessLogService;
    @Autowired
    private OssFileUploader ossFileUploader;
    @Autowired
    private PushService pushService;
    @Autowired
    private NoticeRuleService noticeRuleService;
    @Autowired
    private OrganizationRpcService organizationRpcService;
    @Autowired
    private BankAccountRecordService bankAccountRecordService;
    @Autowired
    private StatementTaskService statementTaskService;


    public static final int MAX_LOG_THREAD_COUNT = 2; //最多同时有多少个线程执行
    private static final String SESSION_IMPORT_FINANCE_WHITELIST = "importWhitelist_task";

    public static final String SMS_STATUS = "sms_status";
    ThreadPoolExecutor financeExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(MAX_LOG_THREAD_COUNT);
    ThreadPoolExecutor noticeExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(2);

    @Override
    public Map<String, Object> getUserInfoByIdOrSn(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        Map<String, Object>
                merchant,
                mer;
        if (!StringUtil.empty(merchantId)) {
            merchant = userInfoService.getUserInfoByIdSlave(merchantId);
            mer = merchantService.getMerchant(merchantId);
        } else {
            String merchantSn = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_SN);
            merchant = userInfoService.getUserInfoBySnSlave(merchantSn);
            mer = merchantService.getMerchantBySn(merchantSn);
        }
        String merchantName = BeanUtil.getPropString(mer, Merchant.NAME);
        if (!CollectionUtils.isEmpty(merchant)) {
            merchant.put(ConstantUtil.KEY_MERCHANT_NAME, merchantName);
        }
        return merchant;
    }

    @Override
    public ListResult findUserInfoList(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        ListResult listResult = null;

        String merchantSn = BeanUtil.getPropString(request, UserInfo.SN);
        String cellphone = BeanUtil.getPropString(request, "cellphone");
        String merchantName = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_NAME);

        if (!StringUtil.empty(merchantSn)) {
            listResult = getUserInfoList(pageInfo, ConstantUtil.KEY_MERCHANT_SN, merchantSn);
        } else if (!StringUtil.empty(cellphone)) {
            listResult = getUserInfoList(pageInfo, "cellphone", cellphone);
        } else if (!StringUtil.empty(merchantName)) {
            listResult = getUserInfoList(pageInfo, ConstantUtil.KEY_MERCHANT_NAME, merchantName);
        } else {
            listResult = userInfoService.findUserInfosSlave(pageInfo, request);
        }
        if (listResult == null || listResult.getRecords() == null || listResult.getRecords().size() < 1) {
            return new ListResult(0, new ArrayList<Map>());
        }

        //补充商户名称
        List<Map> userInfoList = listResult.getRecords();
        addUserName(userInfoList);
        return new ListResult(listResult.getTotal(), userInfoList);
    }

    private ListResult getUserInfoList(PageInfo pageInfo, String queryField, String fieldValue) {
        PageInfo tmpPageInfo = new PageInfo(pageInfo.getPage(), 999);
        ListResult merchantResult = merchantService.findMerchants(tmpPageInfo, CollectionUtil.hashMap(queryField, fieldValue));
        if (merchantResult == null || merchantResult.getRecords() == null || merchantResult.getRecords().size() < 1) {
            return merchantResult;
        }
        List<Map> merchantRecords = merchantResult.getRecords();
        List<String> userIds = new ArrayList<>();
        for (Map merchantRecord : merchantRecords) {
            userIds.add(BeanUtil.getPropString(merchantRecord, DaoConstants.ID));
        }
        return userInfoService.findUserInfosSlave(pageInfo, CollectionUtil.hashMap(
                RequestConstant.USER_IDS, userIds
        ));
    }

    private void addUserName(List<Map> userInfoList) {
        if (userInfoList == null || userInfoList.size() < 1) {
            return;
        }
        //拿到merchantIds一次性去访问 加快速度
        List<String> merchantIds = new ArrayList<>();
        for (Map userInfo : userInfoList) {
            merchantIds.add(BeanUtil.getPropString(userInfo, DaoConstants.ID));
        }
        //查询商户集合
        ListResult merchants = merchantService.findMerchants(new PageInfo(1, userInfoList.size()), CollectionUtil.hashMap(
                "merchant_ids", merchantIds
        ));
        //添加民治逻辑
        if (merchants != null && merchants.getRecords() != null && merchants.getRecords().size() > 0) {
            //id,name键值对
            Map merchantNameMap = new HashMap();
            for (Map merchant : merchants.getRecords()) {
                merchantNameMap.put(BeanUtil.getPropString(merchant, DaoConstants.ID), BeanUtil.getPropString(merchant, Merchant.NAME));
            }
            //添加name
            for (Map user : userInfoList) {
                user.put("merchant_name", BeanUtil.getPropString(merchantNameMap, BeanUtil.getPropString(user, DaoConstants.ID)));
            }
        }
    }

    @Override
    public Map<String, Object> isOpenFinance(Map<String, Object> request) {
        Map<String, Object> merchant = getUserInfoByIdOrSn(request);
        int financeStatus = BeanUtil.getPropInt(merchant, "status");
        if (financeStatus != 2) {
            return CollectionUtil.hashMap(
                    "status", false,
                    "message", "用户未开通理财或理财账户禁用/关闭, 当前状态为: " + financeStatus);
        }
        return CollectionUtil.hashMap(
                "status", true,
                "message", "用户已开通理财");
    }

    @Override
    public Map<String, Object> getUserAmountByUserId(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, FinanceConstant.KEY_USER_ID);
        return userAmountService.getUserAmountByUseId(merchantId);
    }

    @Override
    public Map<String, Object> getUserAmountRecord(Map<String, Object> request) {
        String id = BeanUtil.getPropString(request, DaoConstants.ID);
        return userAmountService.getUserAmountRecordSlave(id);
    }

    @Override
    public ListResult findUserAmountRecordList(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        normalizeDateParam(request);
        return userAmountService.findUserAmountRecordsSlave(pageInfo, request);
    }

    @Override
    public Map exportUserAmountRecordList(Map<String, Object> request) {
        //参数检查
        long date_start = BeanUtil.getPropLong(request, "date_start");
        long date_end = BeanUtil.getPropLong(request, "date_end");
        if (date_start <= 0 || date_end <= 0 || date_start > date_end) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "流水导出时间有误");
        }
        String merchantId = BeanUtil.getPropString(request, FinanceConstant.KEY_USER_ID);
        if (StringUtil.empty(merchantId)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "请先选择理财用户再进行导出操作");
        }
        ListResult listResult = statementTaskService.findStatementTasksSlave(new PageInfo(1, 1), CollectionUtil.hashMap(
                StatementTask.USER_ID, merchantId,
                RequestConstant.APPLY_STATUSES, Arrays.asList(StatementTask.APPLY_STATUS_CREATED, StatementTask.APPLY_STATUS_OPERATING)
        ));
        if (listResult != null && listResult.getTotal() > 0){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "该理财用户有正在执行中的对账任务,请等待执行完成后再来操作!");
        }


        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        String sn = BeanUtil.getPropString(merchant, MerchantInfo.SN);
        String name = BeanUtil.getPropString(merchant, MerchantInfo.NAME);
        String dateName = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String fileName = sn + name + dateName;
        String operatorId = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERID);
        if (StringUtil.empty(operatorId)) {
            operatorId = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        }


        return statementTaskService.createStatementTask(CollectionUtil.hashMap(
                StatementTask.TYPE, StatementTask.TYPE_FINANCE_WALLET_TRANSACTION,
                StatementTask.APPLY_SYSTEM, StatementTask.APPLY_SYSTEM_SP,
                StatementTask.TITLE, fileName,
                StatementTask.USER_ID, merchantId,
                StatementTask.OPERATOR_ID, operatorId,
                StatementTask.APPLY_STATUS, StatementTask.APPLY_STATUS_CREATED,
                StatementTask.APPLY_DETAIL, request
        ));
    }

    @Override
    public ListResult findStatementTaskList(Map<String, Object> request) {
        String type = BeanUtil.getPropString(request, StatementTask.TYPE);
        if (StringUtil.empty(type)) {
            request.put(StatementTask.TYPE, StatementTask.TYPE_FINANCE_WALLET_TRANSACTION);
        }
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        normalizeDateParam(request);
        return statementTaskService.findStatementTasksSlave(pageInfo, request);
    }

    @Override
    public void deleteStatementTask(Map<String, Object> request) {
        String statementTaskId = BeanUtil.getPropString(request, DaoConstants.ID);
        if (StringUtil.empty(statementTaskId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出任务id不可为空!");
        }
        statementTaskService.deleteStatementTask(statementTaskId);
    }

    @Override
    public Map<String, Object> getIncomeRecord(Map<String, Object> request) {
        String id = BeanUtil.getPropString(request, DaoConstants.ID);
        return incomeRecordService.getIncomeRecord(id);
    }

    @Override
    public ListResult findIncomeRecordList(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        normalizeDateParam(request);
        return incomeRecordService.findIncomeRecordsSlave(pageInfo, request);
    }

    @Override
    public Map<String, Object> getLastTwoDayIncomeAmount(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, FinanceConstant.KEY_USER_ID);
        return incomeRecordService.getLastTwoDayIncomeAmount(merchantId);
    }

    @Override
    public Map<String, Object> getIncomeAmountYesterday(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, FinanceConstant.KEY_USER_ID);
        long amount = incomeRecordService.getIncomeAmountYesterday(merchantId);
        return CollectionUtil.hashMap("amount", amount);
    }

    @Override
    public Map<String, Object> getIncomeAmountDayBeforeYesterday(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, FinanceConstant.KEY_USER_ID);
        long amount = incomeRecordService.getIncomeAmountDayBeforeYesterday(merchantId);
        return CollectionUtil.hashMap("amount", amount);
    }

    @Override
    public Map<String, Object> getDailyRecordByDate(Map<String, Object> request) {
        String date = BeanUtil.getPropString(request, "date");
        return dailyRecordService.getDailyRecordByDate(date);
    }

    @Override
    public Map<String, Object> getLastTwoDayDailyRecord(Map<String, Object> request) {
        return dailyRecordService.getLastTwoDayDailyRecord();
    }

    @Override
    public ListResult findDailyRecordList(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        normalizeDateParam(request);
        return dailyRecordService.findDailyRecordsSlave(pageInfo, request);
    }

    @Override
    public Map<String, Map> getAllWithdrawRealTimeBanks(Map<String, Object> request) {
        return bankService.findAllWithdrawRealTimeBanks();
    }

    @Override
    public Map<String, Object> getBalanceAutoSwitchRule(Map<String, Object> request) {
        return ruleService.getBalanceAutoSwitchRule();
    }

    @Override
    public Map<String, Object> getWithdrawRecordDetail(Map<String, Object> request) {
        String id = BeanUtil.getPropString(request, DaoConstants.ID);
        Map<String, Object> withdrawRecord = withdrawRecordService.getWithdrawRecord(id);
        if (!CollectionUtils.isEmpty(withdrawRecord)) {
            String userId = BeanUtil.getPropString(withdrawRecord, FinanceConstant.KEY_USER_ID);
            Map<String, Object> merchant = merchantService.getMerchant(userId);
            withdrawRecord.put("merchant_name", BeanUtil.getPropString(merchant, "name"));
        }
        return withdrawRecord;
    }

    @Override
    public ListResult findWithdrawRecordList(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        ListResult withdrawRecordListResult;
        List<Map> withdrawRecordList = new LinkedList<>();
        String merchantSn = BeanUtil.getPropString(request, "sn");
        String merchantName = BeanUtil.getPropString(request, "merchant_name");
        String cellphone = BeanUtil.getPropString(request, "cellphone");

        if (!StringUtil.empty(merchantSn) || !StringUtil.empty(cellphone) || !StringUtil.empty(merchantName)) {
            Map params = CollectionUtil.hashMap();
            if (!StringUtil.empty(merchantSn)){
                params.put(ConstantUtil.KEY_MERCHANT_SN, merchantSn);
            }
            if (!StringUtil.empty(cellphone)){
                params.put("cellphone", cellphone);
            }
            if (!StringUtil.empty(merchantName)){
                params.put("merchant_name", merchantName);
            }

            ListResult merchants = merchantService.findMerchants(new PageInfo(pageInfo.getPage(), 999), params);
            if(merchants == null || merchants.getRecords() == null || merchants.getRecords().size() < 1){
                return new ListResult(0, new ArrayList<>());
            }

            List<String> merchantIds = new ArrayList<>();
            for(Map merchant : merchants.getRecords()){
                merchantIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
            }

            request.put(RequestConstant.USER_IDS, merchantIds);
        }


        withdrawRecordListResult = withdrawRecordService.findWithdrawRecordsSlave(pageInfo, request);
        withdrawRecordList = withdrawRecordListResult.getRecords();
        addUserNamesByUserIds(withdrawRecordList);

        return new ListResult(withdrawRecordListResult.getTotal(), withdrawRecordList);
    }

    private void addUserNamesByUserIds(List<Map> withdrawRecordList) {
        if (!CollectionUtils.isEmpty(withdrawRecordList)) {
            for (Map withdrawRecord : withdrawRecordList) {
                String userId = BeanUtil.getPropString(withdrawRecord, FinanceConstant.KEY_USER_ID);
                Map<String, Object> merchant = merchantService.getMerchant(userId);
                withdrawRecord.put("merchant_name", BeanUtil.getPropString(merchant, "name"));
            }
        }
    }


    @Override
    public Map<String, Object> getWithdrawStatus(Map<String, Object> request) {
        String withdrawId = BeanUtil.getPropString(request, DaoConstants.ID);
        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        String remark = "运营人员手动发起查证";
        return withdrawRecordService.manualVerifyWithdrawStatus(withdrawId, operator, remark);
    }

    @Override
    public Map<String, Object> updateWithdrawRecordRemark(Map<String, Object> request) {
        String withdrawId = BeanUtil.getPropString(request, DaoConstants.ID);
        String remark = BeanUtil.getPropString(request, "remark");
        return withdrawRecordService.updateWithdrawRecord(CollectionUtil.hashMap(
                "id", withdrawId,
                "remark", remark
        ));
    }

    @Override
    public List<Map> findWithdrawStatusList(Map<String, Object> request) {

        ArrayList<String> ids = (ArrayList<String>) BeanUtil.getProperty(request, "ids");
        ArrayList result = new ArrayList();
        if (ids != null && ids.size() > 0) {
            for (String id : ids) {
                Map<String, Object> transferResult;
                try {
                    transferResult = getWithdrawStatus(CollectionUtil.hashMap(DaoConstants.ID, id));
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_SUCCESS,
                            "msg", "succ",
                            "data", transferResult
                    ));
                } catch (Exception e) {
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_UNKNOWN_ERROR,
                            "msg", e.getMessage(),
                            "data", ""
                    ));
                }
            }
        }
        return result;
    }

    @Override
    public ListResult getUserAmountTurnover(Map<String, Object> request) {
        String withdrawRecordId = BeanUtil.getPropString(request, DaoConstants.ID);
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return withdrawRecordService.findWithdrawRecordLogsSlave(
                pageInfo,
                CollectionUtil.hashMap("withdraw_id", withdrawRecordId)
        );
    }

    @Override
    public ListResult findPurchaseRecordsList(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        ListResult purchaseRecordListResult;
        List<Map> purchaseRecord = new LinkedList<>();
        String merchantSn = BeanUtil.getPropString(request, "sn");
        String merchantName = BeanUtil.getPropString(request, "merchant_name");
        String cellphone = BeanUtil.getPropString(request, "cellphone");

        if (!StringUtil.empty(merchantSn)) {
            return getPurchaseRecordList(purchaseRecord, pageInfo, "merchant_sn", merchantSn);
        } else if (!StringUtil.empty(cellphone)) {
            return getPurchaseRecordList(purchaseRecord, pageInfo, "cellphone", cellphone);
        } else if (!StringUtil.empty(merchantName)) {
            return getPurchaseRecordList(purchaseRecord, pageInfo, "merchant_name", merchantName);
        }

        purchaseRecordListResult = purchaseRecordService.findPurchaseRecordsSlave(pageInfo, request);
        purchaseRecord = purchaseRecordListResult.getRecords();
        addUserNamesByUserIds(purchaseRecord);

        return new ListResult(purchaseRecordListResult.getTotal(), purchaseRecord);
    }

    private ListResult getPurchaseRecordList(List<Map> purchaseRecordList, PageInfo pageInfo, String queryField, String fieldValue) {
        PageInfo tmpPageInfo = new PageInfo(pageInfo.getPage(), 999);
        ListResult merchantList = merchantService.findMerchants(tmpPageInfo, CollectionUtil.hashMap(queryField, fieldValue));
        List<Map> merchantRecords = merchantList.getRecords();
        int resultTotal = 0;
        for (Map merchantRecord : merchantRecords) {
            String merchantId = BeanUtil.getPropString(merchantRecord, DaoConstants.ID);
            ListResult purchaseRecordResult =
                    purchaseRecordService.findPurchaseRecordsSlave(pageInfo, CollectionUtil.hashMap(FinanceConstant.KEY_USER_ID, merchantId));
            resultTotal += purchaseRecordResult.getTotal();
            List<Map> purchaseRecords = purchaseRecordResult.getRecords();
            purchaseRecordList.addAll(purchaseRecords);
        }
        addUserNamesByUserIds(purchaseRecordList);
        return new ListResult(resultTotal, purchaseRecordList);
    }

    @Override
    public Map<String, Object> getPurchaseRecord(Map<String, Object> request) {
        String purchaseId = BeanUtil.getPropString(request, DaoConstants.ID);
        if (!StringUtil.empty(purchaseId)) {
            return purchaseRecordService.getPurchaseRecord(purchaseId);
        }
        String purchaseSn = BeanUtil.getPropString(request, "purchase_sn");
        return purchaseRecordService.getPurchaseRecordByPurchaseSn(purchaseSn);
    }

    @Override
    public Map<String, Object> getRuleByType(Map<String, Object> request) {
        String type = BeanUtil.getPropString(request, "type");
        return ruleService.getGeneralRuleByType(type);
    }

    @Override
    public Map<String, Object> updateRealtimeWithDrawRule(Map<String, Object> request) {
        request.put("valid_day", "every_day");
        Map<String, Object> rule = CollectionUtil.hashMap("type", Rule.TYPE_FB_WITHDRAW_REAL_TIME_RULE);
        rule.put("rule", request);
        return ruleService.updateGeneralRuleByType(rule);
    }

    @Override
    public Map<String, Object> updateStandardWithDrawRule(Map<String, Object> request) {
        request.put("valid_day", "every_day");
        Map<String, Object> rule = CollectionUtil.hashMap("type", Rule.TYPE_FB_WITHDRAW_STANDARD_RULE);
        rule.put("rule", request);
        return ruleService.updateGeneralRuleByType(rule);
    }

    @Override
    public Map<String, Object> updatePurchaseRule(Map<String, Object> request) {
        request.put("valid_day", "every_day");
        Map<String, Object> rule = CollectionUtil.hashMap("type", Rule.TYPE_FB_PURCHASE_RULE);
        rule.put("rule", request);
        return ruleService.updateGeneralRuleByType(rule);
    }

    @Override
    public Map<String, Object> updateWithdrawSystemDisableRule(Map<String, Object> request) {
        Map<String, Object> rule = CollectionUtil.hashMap("type", Rule.TYPE_FB_WITHDRAW_SYSTEM_DISABLE_RULE);
        rule.put("rule", request);
        return ruleService.updateGeneralRuleByType(rule);
    }

    @Override
    public Map<String, Object> updateWithdrawBankDisableRule(Map<String, Object> request) {
        Map<String, Object> rule = CollectionUtil.hashMap("type", Rule.TYPE_FB_WITHDRAW_SYSTEM_DISABLE_BANK_RULE);
        rule.put("rule", request);
        return ruleService.updateGeneralRuleByType(rule);
    }

    @Override
    public Map<String, Object> addNotice(Map<String, Object> request) {
        Map noticeRule = ruleService.getGeneralRuleByType(Rule.TYPE_FB_NOTICE_RULE);
        List<Map<String, Object>> ruleList = noticeRule.get("rule") == null ? Lists.<Map<String, Object>>newArrayList() : (List<Map<String, Object>>) noticeRule.get("rule");
        String addCode = request.get("code").toString().trim();
        request.put("code", addCode);
        for (Map<String, Object> ruleItem : ruleList) {
            String code = ruleItem.get("code").toString().trim();
            if (code.equals(addCode)) {
                ruleList.remove(ruleItem);
                break;
            }
        }
        ruleList.add(request);
        Map<String, Object> rule = CollectionUtil.hashMap("type", Rule.TYPE_FB_NOTICE_RULE);
        rule.put("rule", ruleList);
        return ruleService.updateGeneralRuleByType(rule);
    }

    @Override
    public Map<String, Object> updateWalletSwitchRule(Map<String, Object> request) {
        Map<String, Object> rule = CollectionUtil.hashMap("type", Rule.TYPE_WALLET_SWITCH_RULE);
        Map<String, Object> map = (Map<String, Object>) ruleService.getGeneralRuleByType(Rule.TYPE_WALLET_SWITCH_RULE).get("rule");
        Set<Map.Entry<String, Object>> entries = request.entrySet();
        for (Map.Entry<String, Object> entiry : entries) {
            map.put(entiry.getKey(), entiry.getValue());
        }
        rule.put("rule", map);
        return ruleService.updateGeneralRuleByType(rule);
    }

    @Override
    public Map batchImportWhitelist(MultipartFile file) {
        if (file == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "必须上传文件");
        }
        String fileName = file.getOriginalFilename();
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex == -1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        String type = fileName.substring(lastIndex + 1, fileName.length()).toLowerCase();
        if (!"xls".equals(type) && !"xlsx".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }

        InputStream is = null;
        Workbook workbook = null;
        try {
            is = file.getInputStream();
            if ("xls".equals(type)) {
                workbook = new HSSFWorkbook(is);
            } else if ("xlsx".equals(type)) {
                workbook = new XSSFWorkbook(is);
            }
        } catch (Exception e) {
            logger.error("excel格式解析不支持", e);
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel格式解析不支持");
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                }
            }
        }
        if (workbook == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel格式解析异常");
        }

        /**
         * 解析成list
         */
        final List<Map> merchantes_request = extraDataFromExcel(workbook);
        if (merchantes_request == null || merchantes_request.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "空excel没有理财数据");
        }
        if (merchantes_request.size() > 5000) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过5000条");
        }

        //创建任务
        Map taskApplyLog = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_IMPORT_FINANCE_WHITELIST
        ));

        final String taskId = BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        //异步执行导入操作
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                try {
                    batchImportWhitelistTask(merchantes_request, taskId, businessRequest, operator);
                } catch (Exception e) {
                    logger.error("importDrawRealTime() error", e);
                    ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                            TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                            TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "导入过程中发生错误")));
                }
            }
        };
        try {
            financeExecutor.submit(runnable);
        } catch (Exception e) {
            logger.error("submit importDrawRealTime() to threadPool error", e);
            ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                    TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "提交导入异步任务发生错误")));
        }

        HttpRequestUtil.getSession().setAttribute(SESSION_IMPORT_FINANCE_WHITELIST, taskId);
        return CollectionUtil.hashMap("taskId", taskId, "task_apply_log_id", taskId);
    }

    /**
     * 1.openOrClose 0关  1开
     * 2.解析csv为List<Map>
     * 3.商户号去重、去空、去空格
     * 4.备注没有传入默认给一个"运营关闭余额提现理财入口" / "运营开启余额提现理财入口"
     * 5.异步处理 创建异步任务 并返回
     *
     * ----
     * 调理财接口开关 openPurchaseInWalletWithdraw   closePurchaseInWalletWithdraw
     * 处理完成  更新异步处理任务：内容 以及状态
     * ----
     *
     * @param file
     * @param openOrClose
     * @return
     */
    @Override
    public Map batchOpenOrCloseFinancePurchaseInWalletWithdraw(MultipartFile file, String openOrClose) {
        if (file == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "必须上传文件");
        }
        String fileName = file.getOriginalFilename();
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex == -1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        String type = fileName.substring(lastIndex + 1, fileName.length()).toLowerCase();
        if (!"csv".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }

        if(openOrClose == null || !Arrays.asList("0", "1").contains(openOrClose)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "开关操作类型不存在!");
        }
        String purchaseType = "运营关闭余额提现理财入口";
        if("1".equals(openOrClose)) {
            purchaseType = "运营开启余额提现理财入口";
        }

        List<Map> list = new ArrayList<>();
        InputStreamReader inputStreamReader = null;
        BufferedReader bufferedReader = null;
        try {
            inputStreamReader = new InputStreamReader(file.getInputStream(), "UTF-8");
            bufferedReader = new BufferedReader(inputStreamReader);
            bufferedReader.readLine();
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                String[] item = line.split(",");
                if(!StringUtil.empty(item[0]) && !StringUtil.empty(item[0].trim())) {
                    String value = item[1].trim() == null ? purchaseType : item[1].trim();
                    list.add(CollectionUtil.hashMap("sn", item[0].trim(), "remark", value));
                }
            }
        } catch (IOException e) {
            logger.error("解析csv流异常", e);
        }finally {
            try {
                if (bufferedReader != null){
                    bufferedReader.close();
                }
                if (inputStreamReader != null){
                    inputStreamReader.close();
                }
            } catch (IOException e) {
                logger.error("关闭文件流异常", e);
            }
        }

        if (list == null || list.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "空csv没有理财数据");
        }
        if (list.size() > 100000) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过10w条");
        }

        //创建任务
        Map taskApplyLog = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_FINANCE_PURCHASE_IN_WALLET_WHITELIST
        ));

        final String taskId = BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID);
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        //异步执行导入操作
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                try {
                    batchOpenOrCloseFinancePurchaseInWalletWithdrawTask(list, taskId, operator, openOrClose);
                } catch (Exception e) {
                    logger.error("batchOpenOrCloseFinancePurchaseInWalletWithdrawTask() error", e);
                    ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                            TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                            TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "导入过程中发生错误")));
                }
            }
        };
        try {
            financeExecutor.submit(runnable);
        } catch (Exception e) {
            logger.error("submit batchImportInWalletWhitelistTask() to threadPool error", e);
            ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                    TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "提交导入异步任务发生错误")));
        }

        return CollectionUtil.hashMap("taskId", taskId, "task_apply_log_id", taskId);
    }

    private Map batchOpenOrCloseFinancePurchaseInWalletWithdrawTask(List<Map> merchantes, String taskId, String operator, String openOrClose) {
        int total = merchantes.size();
        //重复的商户
        List<Map> merchants_repeat = new ArrayList<>();
        //失败的商户
        List<Map> merchants_error = new ArrayList<>();
        //成功的商户
        List<Map> merchants_success = new ArrayList<>();

        //去重，并将重复的商户信息添加进merchants_repeat
        for (int i = 0; i < merchantes.size(); i++) {
            for (int j = i + 1; j < merchantes.size(); j++) {
                if (BeanUtil.getPropString(merchantes.get(i), UserInfo.SN).equals(BeanUtil.getPropString(merchantes.get(j), UserInfo.SN))) {
                    merchantes.get(j).put("errmsg", "csv中重复商户");
                    merchants_repeat.add(merchantes.get(j));
                    merchantes.remove(j);
                    j--;
                }
            }
        }

        //调理财接口开关 openPurchaseInWalletWithdraw   closePurchaseInWalletWithdraw
        for (int i = 0; i < merchantes.size(); i++) {
            Map merchant = merchantes.get(i);
            merchant.put(UserInfo.OPERATOR, operator);
            try {
                if("0".equals(openOrClose)) {
                    userInfoService.closePurchaseInWalletWithdraw(merchant);
                } else {
                    userInfoService.openPurchaseInWalletWithdraw(merchant);
                }
                merchants_success.add(merchant);
            } catch (Exception e) {
                merchants_error.add(CollectionUtil.hashMap(UserInfo.SN, merchant.get(UserInfo.SN), "errmsg", e.getMessage()));
                logger.info("导入余额提现页面理财白名单失败：[sn:{}],[原因:{}]", merchant.get(UserInfo.SN), e.getMessage());
            }
        }

        //保存导入结果到任务
        logger.info("导入余额提现页面理财白名单结果 总数[{}],导入失败详情[{}],导入重复详情[{}],导入成功详情[{}]", total, merchants_error, merchants_repeat, merchants_success);
        String csvDownloadUrl = uploadFinanceWhitelistImportResult(merchants_error, merchants_repeat, merchants_success);

        Map<String, Object> result = new HashedMap();
        //设置下载地址
        result.put("downloadResultUrl", csvDownloadUrl);
        result.put("导入总数", total);
        result.put("导入失败总数", merchants_error.size());
        if (merchants_error.size() > 20) {
            merchants_error = merchants_error.subList(0, 20);
            merchants_error.add(CollectionUtil.hashMap(
                    "msg", "过多不再显示.........."
            ));
        }
        result.put("导入失败详情", merchants_error);
        result.put("重复总数", merchants_repeat.size());
        if (merchants_repeat.size() > 20) {
            merchants_repeat = merchants_repeat.subList(0, 20);
            merchants_repeat.add(CollectionUtil.hashMap(
                    "msg", "过多不再显示.........."
            ));
        }
        result.put("重复详情", merchants_repeat);
        result.put("导入成功总数", merchants_success.size());

        ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                TaskApplyLog.APPLY_STATUS, merchantes.size() > 0 ? TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS : TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                TaskApplyLog.APPLY_RESULT, result));

        return result;
    }

    @Override
    public List<Map> submitRemit(Map<String, Object> request) {
        ArrayList<String> ids = (ArrayList<String>) BeanUtil.getProperty(request, "ids");
        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        String remark = "运营人员手动发起提交打款";
        ArrayList result = new ArrayList();
        if (ids != null && ids.size() > 0) {
            for (String id : ids) {
                Map<String, Object> transferResult;
                try {
                    transferResult = withdrawRecordService.submitTransfer(id, operator, remark);
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_SUCCESS,
                            "msg", "succ",
                            "data", transferResult
                    ));
                } catch (Exception e) {
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_UNKNOWN_ERROR,
                            "msg", e.getMessage(),
                            "data", ""
                    ));
                }
            }
        }
        return result;
    }

    @Override
    public List<Map> remitSuccess(Map<String, Object> request) {
        ArrayList<String> ids = (ArrayList<String>) BeanUtil.getProperty(request, "ids");
        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        String remark = "运营人员手动更新打款成功";
        ArrayList result = new ArrayList();
        if (ids != null && ids.size() > 0) {
            for (String id : ids) {
                Map<String, Object> transferResult;
                try {
                    transferResult = withdrawRecordService.transferSuccess(id, operator, remark);
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_SUCCESS,
                            "msg", "succ",
                            "data", transferResult
                    ));
                } catch (Exception e) {
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_UNKNOWN_ERROR,
                            "msg", e.getMessage(),
                            "data", ""
                    ));
                }
            }
        }
        return result;
    }

    @Override
    public List<Map> remitFailToManual(Map<String, Object> request) {
        ArrayList<String> ids = (ArrayList<String>) BeanUtil.getProperty(request, "ids");
        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        String remark = "运营人员手动更新打款失败需人工";
        ArrayList result = new ArrayList();
        if (ids != null && ids.size() > 0) {
            for (String id : ids) {
                Map<String, Object> transferResult;
                try {
                    transferResult = withdrawRecordService.transferFailManualIng(id, operator, remark);
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_SUCCESS,
                            "msg", "succ",
                            "data", transferResult
                    ));
                } catch (Exception e) {
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_UNKNOWN_ERROR,
                            "msg", e.getMessage(),
                            "data", ""
                    ));
                }
            }
        }
        return result;
    }

    @Override
    public List<Map> waitForRemitResult(Map<String, Object> request) {
        ArrayList<String> ids = (ArrayList<String>) BeanUtil.getProperty(request, "ids");
        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        String remark = "运营人员手动更新等待打款结果";
        ArrayList result = new ArrayList();
        if (ids != null && ids.size() > 0) {
            for (String id : ids) {
                Map<String, Object> transferResult;
                try {
                    transferResult = withdrawRecordService.transferWait(id, operator, remark);
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_SUCCESS,
                            "msg", "succ",
                            "data", transferResult
                    ));
                } catch (Exception e) {
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_UNKNOWN_ERROR,
                            "msg", e.getMessage(),
                            "data", ""
                    ));
                }
            }
        }
        return result;
    }

    @Override
    public ListResult queryRemitResult(Map<String, Object> request) {
        //TODO 查打款结果
//        ArrayList<String> ids = (ArrayList<String>)BeanUtil.getProperty(request, "ids");
//        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
//        String remark = "运营人员手动发起查证";
//        ArrayList result = new ArrayList();
//        if (ids != null && ids.size() > 0) {
//            for (String id : ids) {
//                result.add(withdrawRecordService.submitTransfer(id, operator, remark));
//            }
//        }
//        return new ListResult(result.size(),result);
        return null;
    }

    @Override
    public List<Map> transferFail(Map<String, Object> request) {
        ArrayList<String> ids = (ArrayList<String>) BeanUtil.getProperty(request, "ids");
        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        String remark = "运营人员手动更新打款失败已返还";
        ArrayList result = new ArrayList();
        if (ids != null && ids.size() > 0) {
            for (String id : ids) {
                Map<String, Object> transferResult;
                try {
                    transferResult = withdrawRecordService.transferFail(id, operator, remark);
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_SUCCESS,
                            "msg", "succ",
                            "data", transferResult
                    ));
                } catch (Exception e) {
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_UNKNOWN_ERROR,
                            "msg", e.getMessage(),
                            "data", ""
                    ));
                }
            }
        }
        return result;
    }

    @Override
    public Map batchUpdateFinanceDrawRealTimeFreeCount(Map<String, Object> request) {
        final int inOrDecreaseNum = BeanUtil.getPropInt(request, "in_or_decrease_num");
        if (inOrDecreaseNum == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "增减免费次数不可为空或0!");
        }
        final String remark = BeanUtil.getPropString(request, "remark");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "修改理财账户免费次数数目为0");
        }
        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_UPDATE_FINANCE_FREECOUNT,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final List<String> merchant_ids_copy = merchant_ids;
        final String task_apply_log_id = BeanUtil.getPropString(task, com.wosai.data.dao.DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        financeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchUpdateFinanceDrawRealTimeFreeCountWithIds(merchant_ids_copy, inOrDecreaseNum, remark, task_apply_log_id, businessRequest, operator);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }

    @Override
    public Map batchCloseUser(Map<String, Object> request) {
        if (!(request.containsKey(NoticeRule.CODE) && request.containsKey(NoticeRule.SUB_CODE))) {
            request.put(NoticeRule.CODE, "3003");
            request.put(NoticeRule.SUB_CODE, "07");
        }
        final String remark = BeanUtil.getPropString(request, "remark");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        final Map notice = getNoticeRule(request);
        if (notice == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则不存在!");
        }
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "禁用理财账户数目为0");
        }
        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_CLOSE_FINANCE_STATUS,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final List<String> merchant_ids_copy = merchant_ids;
        final String task_apply_log_id = BeanUtil.getPropString(task, com.wosai.data.dao.DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        financeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchCloseFinanceWithIds(merchant_ids_copy, remark, notice, task_apply_log_id, businessRequest, operator);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }

    @Override
    public Map batchEnableUser(Map<String, Object> request) {
        if (!(request.containsKey(NoticeRule.CODE) && request.containsKey(NoticeRule.SUB_CODE))) {
            request.put(NoticeRule.CODE, "3003");
            request.put(NoticeRule.SUB_CODE, "06");
        }
        final String remark = BeanUtil.getPropString(request, "remark");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        final Map notice = getNoticeRule(request);
        if (notice == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则不存在!");
        }
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "启用理财账户数目为0");
        }
        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_ENABLE_FINANCE_STATUS,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final List<String> merchant_ids_copy = merchant_ids;
        final String task_apply_log_id = BeanUtil.getPropString(task, com.wosai.data.dao.DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        financeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchEnableFinanceWithIds(merchant_ids_copy, remark, notice, task_apply_log_id, businessRequest, operator);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }

    @Override
    public Map batchDisableUser(Map<String, Object> request) {
        if (!(request.containsKey(NoticeRule.CODE) && request.containsKey(NoticeRule.SUB_CODE))) {
            request.put(NoticeRule.CODE, "3003");
            request.put(NoticeRule.SUB_CODE, "05");
        }
        final String remark = BeanUtil.getPropString(request, "remark");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        final Map notice = getNoticeRule(request);
        if (notice == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则不存在!");
        }
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "禁用理财账户数目为0");
        }
        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_DISABLE_FINANCE_STATUS,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final List<String> merchant_ids_copy = merchant_ids;
        final String task_apply_log_id = BeanUtil.getPropString(task, DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        financeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchDisableFinanceWithIds(merchant_ids_copy, remark, notice, task_apply_log_id, businessRequest, operator);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }


    private void normalizeDateParam(Map<String, Object> request) {
        MapUtil.removeKeys(request, IGNORE_DATE_PARAMS);
        String dateMax = BeanUtil.getPropString(request, "date_max");
        String dateMin = BeanUtil.getPropString(request, "date_min");
        request.put("date_begin", dateMin);
        request.put("date_end", dateMax);
        MapUtil.removeKeys(request, new String[]{"date_max", "date_min"});
    }

    private int toInt(Object obj) {
        if (obj == null) {
            return 0;
        }
        try {
            String s = obj.toString();
            if (s.indexOf(".") > 0) {
                s = s.substring(0, s.indexOf(".")); // 防止解析为小数，去掉0
            }
            int value = Integer.valueOf(s);
            return value < 0 ? 0 : value;
        } catch (Exception e) {
            return 0;
        }
    }

    private String toString(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    private List<Map> extraDataFromExcel(Workbook hssfWorkbook) {
        List<Map> merchantInfos = new ArrayList<Map>();
        try {
            // 循环Sheet
            Sheet hssfSheet = hssfWorkbook.getSheetAt(0);
            if (hssfSheet == null) {
                return null;
            }
            // 循环行Row
            for (int rowNum = 1; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                Row hssfRow = hssfSheet.getRow(rowNum);
                if (hssfRow == null) {
                    continue;
                }
                String merchantSn = toString(hssfRow.getCell(0)).replaceAll(" ", "");
                if (merchantSn.indexOf(".") != -1) {
                    merchantSn = merchantSn.substring(0, merchantSn.indexOf(".")); // 防止解析为小数，去掉0
                }
                int remainderNumber = toInt(hssfRow.getCell(1));
                if (!StringUtil.empty(merchantSn)) {
                    merchantInfos.add(CollectionUtil.hashMap(
                            UserInfo.SN, merchantSn,
                            UserInfo.REMAINDER_NUMBER, remainderNumber,
                            UserInfo.ADD_REMARK, toString(hssfRow.getCell(2)).replaceAll(" ", "")));
                }
            }
            return merchantInfos;
        } catch (Exception e) {
            logger.error("解析理财白名单excel失败", e);
            return null;
        }
    }

    private Map batchImportWhitelistTask(List<Map> merchantes, String taskId, Map businessRequest, String operator) {

        int total = merchantes.size();
        //重复的商户
        List<Map> merchants_repeat = new ArrayList<>();
        //失败商户号
        List<Map> errorMerchants = new ArrayList<>();
        //成功的商户
        List<Map> successMerchants = new ArrayList<>();
        //去重
        for (int i = 0; i < merchantes.size(); i++) {
            for (int j = i + 1; j < merchantes.size(); j++) {
                if (BeanUtil.getPropString(merchantes.get(i), UserInfo.SN).equals(BeanUtil.getPropString(merchantes.get(j), UserInfo.SN))) {
                    merchantes.get(j).put("errmsg", "excel中重复商户");
                    merchants_repeat.add(merchantes.get(j));
                    merchantes.remove(j);
                    j--;
                }
            }
        }

        for (int i = 0; i < merchantes.size(); i++) {
            Map merchant = merchantes.get(i);
            merchant.put(UserInfo.TYPE, UserInfo.TYPE_MERCHANT_SQB);
            merchant.put(UserInfo.ADD_BY, operator);
            merchant.put(UserInfo.ADD_CHANNEL, UserInfo.ADD_CHANNEL_SP);
            try {
                userInfoService.createUserInfo(merchant);
                successMerchants.add(merchant);
            } catch (Exception e) {
                errorMerchants.add(CollectionUtil.hashMap(UserInfo.SN, merchant.get(UserInfo.SN), "errmsg", e.getMessage()));
                logger.info("导入理财白名单失败：[sn:{}],[原因:{}]", merchant.get(UserInfo.SN), e.getMessage());
            }
        }
        //保存导入结果到任务
        logger.info("导入理财白名单结果 总数[{}],非法详情[{}],重复详情[{}],导入成功详情[{}]", total, errorMerchants, merchants_repeat, successMerchants);
        String excelDownloadUrl = uploadFinanceWhitelistImportResult(errorMerchants, merchants_repeat, successMerchants);

        Map<String, Object> result = new HashedMap();
        //设置下载地址
        result.put("downloadResultUrl", excelDownloadUrl);
        result.put("导入总数", total);
        result.put("导入失败总数", errorMerchants.size());
        if (errorMerchants.size() > 20) {
            errorMerchants = errorMerchants.subList(0, 20);
            errorMerchants.add(CollectionUtil.hashMap(
                    "msg", "过多不再显示.........."
            ));
        }
        result.put("导入失败详情", errorMerchants);
        result.put("重复总数", merchants_repeat.size());
        if (merchants_repeat.size() > 20) {
            merchants_repeat = merchants_repeat.subList(0, 20);
            merchants_repeat.add(CollectionUtil.hashMap(
                    "msg", "过多不再显示.........."
            ));
        }
        result.put("重复详情", merchants_repeat);
        result.put("导入成功总数", successMerchants.size());

        ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                TaskApplyLog.APPLY_STATUS, merchantes.size() > 0 ? TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS : TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                TaskApplyLog.APPLY_RESULT, result));
        return result;
    }

    private String uploadFinanceWhitelistImportResult(List<Map> merchants_unValid, List<Map> merchants_repeat, List<Map> merchants_success) {
        try {
            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet failureSheet = workbook.createSheet("失败详情");
            List<String> failureHeaders = new ArrayList<String>() {{
                add("商户sn");
                add("失败原因");
            }};
            List<String> failureField = new ArrayList<String>() {{
                add(UserInfo.SN);
                add("errmsg");
            }};
            buildExcelDetail(failureSheet, failureHeaders, merchants_repeat, failureField);
            buildExcelDetail(failureSheet, null, merchants_unValid, failureField);

            HSSFSheet successSheet = workbook.createSheet("成功详情");
            buildExcelDetail(successSheet, null, merchants_success, null);
            return uploadStatementToOSS("xls", workbook);
        } catch (Exception e) {

        }
        return "";
    }

    private void buildExcelDetail(HSSFSheet sheet, List<String> headers, List<Map> list, List<String> keys) {
        SheetUtil sheetUtil = new SheetUtil(sheet);
        if (headers != null) {
            sheetUtil.appendRow(headers);
        }
        if (list != null) {
            for (Map map : list) {
                List values = new ArrayList();
                if (keys != null) {
                    for (String key : keys) {
                        values.add(BeanUtil.getProperty(map, key));
                    }
                } else {
                    map = map == null ? new HashMap() : map;
                    Set<String> mKeys = map.keySet();
                    for (String key : mKeys) {
                        values.add(BeanUtil.getProperty(map, key));
                    }
                }
                sheetUtil.appendRow(values);
            }
        }
    }

    private String uploadStatementToOSS(String ext, HSSFWorkbook workbook) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        byte[] content = bos.toByteArray();
        bos.close();
        String fullName = "portal/statement/sp_import_result/" + CrudUtil.randomUuid() + "." + ext;
        ByteArrayInputStream bais = new ByteArrayInputStream(content);
        //todo 开个保存报表的bucket
        ossFileUploader.uploadIfNotExists(OssFileUploader.IMAGE_BUCKET_NAME, fullName, bais, content.length);
        return fullName;
    }


    /**
     * 批量修改理财快赎免费次数
     *
     * @param merchant_ids
     * @param inOrDecreaseNum
     * @param remark
     * @return
     */
    private List<Map> batchUpdateFinanceDrawRealTimeFreeCountWithIds(List<String> merchant_ids, int inOrDecreaseNum, String remark, final String task_apply_log_id, Map businessRequest, String operator) {
        boolean flag = false;
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> result = new ArrayList<>();
        List<Map> falureDetail = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        for (String merchant_id : merchant_ids) {
            try {
                updateFinanceDrawRealTimeFreeCount(merchant_id, remark, inOrDecreaseNum, operator);
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", ""
                ));
                successIds.add(merchant_id);
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                result.add(falure);
                falureDetail.add(falure);
            }
        }
        String excelDownloadUrl = "";
        if (falureDetail.size() > 20) {
            excelDownloadUrl = uploadExcelResult(falureDetail,
                    Arrays.asList("商户sn", "错误原因"),
                    Arrays.asList(ConstantUtil.KEY_MERCHANT_ID, "msg")
            );
            falureDetail = falureDetail.subList(0, 20);
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"

            ));
            flag = true;
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                TaskApplyLog.APPLY_RESULT, flag ? CollectionUtil.hashMap(
                        "批量修改理财快赎免费次数总数", result.size(),
                        "批量修改理财快赎免费次数成功数", successIds.size(),
                        "批量修改理财快赎免费次数失败数", result.size() - successIds.size(),
                        "批量修改理财快赎免费次数失败详情", falureDetail,
                        "downloadResultUrl", excelDownloadUrl
                ) : CollectionUtil.hashMap(
                        "批量修改理财快赎免费次数总数", result.size(),
                        "批量修改理财快赎免费次数成功数", successIds.size(),
                        "批量修改理财快赎免费次数失败数", result.size() - successIds.size(),
                        "批量修改理财快赎免费次数失败详情", falureDetail
                )
        ));
        return result;
    }

    private void updateFinanceDrawRealTimeFreeCount(String merchant_id, String remark, int inOrDecreaseNum, String operator) {
        if (inOrDecreaseNum == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "增加减少免费次数为0，无需变动");
        }
        Map map = CollectionUtil.hashMap(DaoConstants.ID, merchant_id, UserInfo.REMARK, remark, UserInfo.OPERATOR, operator, "op_number", Math.abs(inOrDecreaseNum));
        //减少免费次数
        if (inOrDecreaseNum < 0) {
            userInfoService.decreaseFreeNumber(map);
        }
        //增加免费次数
        else {
            userInfoService.increaseFreeNumber(map);
        }
    }

    /**
     * 批量禁用理财账户状态
     *
     * @param merchant_ids
     * @param remark
     * @return
     */
    private List<Map> batchDisableFinanceWithIds(List<String> merchant_ids, String remark, final Map notice, final String task_apply_log_id, Map businessRequest, String operator) {
        boolean flag = false;
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> result = new ArrayList<>();
        List<Map> falureDetail = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        for (String merchant_id : merchant_ids) {
            try {
                disableFinanceUser(merchant_id, BeanUtil.getPropString(notice, NoticeRule.APP_TIP), operator);
                userInfoService.updateUserInfo(CollectionUtil.hashMap(
                        DaoConstants.ID, merchant_id,
                        UserInfo.REMARK, remark
                ));
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", null
                ));
                successIds.add(merchant_id);
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                result.add(falure);
                falureDetail.add(falure);
            }
        }
        String excelDownloadUrl = "";
        if (falureDetail.size() > 20) {
            flag = true;
            excelDownloadUrl = uploadExcelResult(falureDetail,
                    Arrays.asList("商户sn", "错误原因"),
                    Arrays.asList(ConstantUtil.KEY_MERCHANT_ID, "msg")
            );
            falureDetail = falureDetail.subList(0, 20);
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"
            ));

        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE,
                TaskApplyLog.APPLY_RESULT, flag ? CollectionUtil.hashMap(
                        "批量禁用理财账户状态总数", result.size(),
                        "批量禁用理财账户状态成功数", successIds.size(),
                        "批量禁用理财账户状态失败数", result.size() - successIds.size(),
                        "批量禁用理财账户状态失败详情", falureDetail,
                        "downloadResultUrl", excelDownloadUrl
                ) : CollectionUtil.hashMap(
                        "批量禁用理财账户状态总数", result.size(),
                        "批量禁用理财账户状态成功数", successIds.size(),
                        "批量禁用理财账户状态失败数", result.size() - successIds.size(),
                        "批量禁用理财账户状态失败详情", falureDetail
                )
        ));
        //发送通知
        final List<String> successIds_copy = successIds;
        if (CollectionUtils.isEmpty(successIds_copy)) {
            ospTaskService.updateTask(CollectionUtil.hashMap(
                    DaoConstants.ID, task_apply_log_id,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS
            ));
            return result;
        }
        noticeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                pushService.pushTask(CollectionUtil.hashMap(
                        PushTask.NOTICE_RULE, notice,
                        PushTask.TASK_APPLY_LOG_ID, task_apply_log_id,
                        PushTask.MERCHANT_IDS, successIds_copy
                ));
            }
        });
        return result;
    }

    /**
     * 批量关闭理财账户状态
     *
     * @param merchant_ids
     * @param remark
     * @return
     */
    private List<Map> batchCloseFinanceWithIds(List<String> merchant_ids, String remark, final Map notice, final String task_apply_log_id, Map businessRequest, String operator) {
        boolean flag = false;
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> result = new ArrayList<>();
        List<Map> falureDetail = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        for (String merchant_id : merchant_ids) {
            try {
                closeFinanceUser(merchant_id, remark, operator);
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", null
                ));
                successIds.add(merchant_id);
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                result.add(falure);
                falureDetail.add(falure);
            }
        }
        String excelDownloadUrl = "";
        if (falureDetail.size() > 20) {
            flag = true;
            excelDownloadUrl = uploadExcelResult(falureDetail,
                    Arrays.asList("商户sn", "错误原因"),
                    Arrays.asList(ConstantUtil.KEY_MERCHANT_ID, "msg")
            );
            falureDetail = falureDetail.subList(0, 20);
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"
            ));
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE,
                TaskApplyLog.APPLY_RESULT, flag ? CollectionUtil.hashMap(
                        "批量关闭理财账户状态总数", result.size(),
                        "批量关闭理财账户状态成功数", successIds.size(),
                        "批量关闭理财账户状态失败数", result.size() - successIds.size(),
                        "批量关闭理财账户状态失败详情", falureDetail,
                        "downloadResultUrl", excelDownloadUrl
                ) : CollectionUtil.hashMap(
                        "批量关闭理财账户状态总数", result.size(),
                        "批量关闭理财账户状态成功数", successIds.size(),
                        "批量关闭理财账户状态失败数", result.size() - successIds.size(),
                        "批量关闭理财账户状态失败详情", falureDetail
                )
        ));
        //发送通知
        final List<String> successIds_copy = successIds;
        if (CollectionUtils.isEmpty(successIds_copy)) {
            ospTaskService.updateTask(CollectionUtil.hashMap(
                    com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS
            ));
            return result;
        }
        noticeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                pushService.pushTask(CollectionUtil.hashMap(
                        PushTask.NOTICE_RULE, notice,
                        PushTask.TASK_APPLY_LOG_ID, task_apply_log_id,
                        PushTask.MERCHANT_IDS, successIds_copy
                ));
            }
        });
        return result;
    }

    /**
     * 批量启用理财账户状态
     *
     * @param merchant_ids
     * @param remark
     * @return
     */
    private List<Map> batchEnableFinanceWithIds(List<String> merchant_ids, String remark, final Map notice, final String task_apply_log_id, Map businessRequest, String operator) {
        boolean flag = false;
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> result = new ArrayList<>();
        List<Map> falureDetail = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        for (String merchant_id : merchant_ids) {
            try {
                enableFinanceUser(merchant_id, remark, operator);
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", null
                ));
                successIds.add(merchant_id);
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                result.add(falure);
                falureDetail.add(falure);
            }
        }
        String excelDownloadUrl = "";
        if (falureDetail.size() > 20) {
            excelDownloadUrl = uploadExcelResult(falureDetail,
                    Arrays.asList("商户sn", "错误原因"),
                    Arrays.asList(ConstantUtil.KEY_MERCHANT_ID, "msg")
            );
            falureDetail = falureDetail.subList(0, 20);
            flag = true;
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"
            ));
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE,
                TaskApplyLog.APPLY_RESULT, flag ? CollectionUtil.hashMap(
                        "批量启用理财账户状态总数", result.size(),
                        "批量启用理财账户状态成功数", successIds.size(),
                        "批量启用理财账户状态失败数", result.size() - successIds.size(),
                        "批量启用理财账户状态失败详情", falureDetail,
                        "downloadResultUrl", excelDownloadUrl
                ) : CollectionUtil.hashMap(
                        "批量启用理财账户状态总数", result.size(),
                        "批量启用理财账户状态成功数", successIds.size(),
                        "批量启用理财账户状态失败数", result.size() - successIds.size(),
                        "批量启用理财账户状态失败详情", falureDetail
                )
        ));

        //发送通知
        final List<String> successIds_copy = successIds;
        if (CollectionUtils.isEmpty(successIds_copy)) {
            ospTaskService.updateTask(CollectionUtil.hashMap(
                    com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS
            ));
            return result;
        }
        noticeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                pushService.pushTask(CollectionUtil.hashMap(
                        PushTask.NOTICE_RULE, notice,
                        PushTask.TASK_APPLY_LOG_ID, task_apply_log_id,
                        PushTask.MERCHANT_IDS, successIds_copy
                ));
            }
        });
        return result;
    }

    /**
     * 批量重开理财账户状态
     *
     * @param merchant_ids
     * @param remark
     * @return
     */
    private List<Map> batchReOpenFinanceWithIds(List<String> merchant_ids, String remark, final Map notice, final String task_apply_log_id, Map businessRequest, String operator) {
        boolean flag = false;
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> result = new ArrayList<>();
        List<Map> falureDetail = new ArrayList<>();
        List<String> successIds = new ArrayList<>();

        for (String merchant_id : merchant_ids) {
            try {
                reOpenFinanceUser(merchant_id, remark, operator);
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", null
                ));
                successIds.add(merchant_id);
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                result.add(falure);
                falureDetail.add(falure);
            }
        }
        String excelDownloadUrl = "";
        if (falureDetail.size() > 20) {
            excelDownloadUrl = uploadExcelResult(falureDetail,
                    Arrays.asList("商户sn", "错误原因"),
                    Arrays.asList(ConstantUtil.KEY_MERCHANT_ID, "msg")
            );
            falureDetail = falureDetail.subList(0, 20);
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"
            ));
            flag = true;
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE,
                TaskApplyLog.APPLY_RESULT, flag ? CollectionUtil.hashMap(
                        "批量重开理财账户状态总数", result.size(),
                        "批量重开理财账户状态成功数", successIds.size(),
                        "批量重开理财账户状态失败数", result.size() - successIds.size(),
                        "批量重开理财账户状态失败详情", falureDetail,
                        "downloadResultUrl", excelDownloadUrl
                ) : CollectionUtil.hashMap(
                        "批量重开理财账户状态总数", result.size(),
                        "批量重开理财账户状态成功数", successIds.size(),
                        "批量重开理财账户状态失败数", result.size() - successIds.size(),
                        "批量重开理财账户状态失败详情", falureDetail
                )
        ));

        //发送通知
        final List<String> successIds_copy = successIds;
        if (CollectionUtils.isEmpty(successIds_copy)) {
            ospTaskService.updateTask(CollectionUtil.hashMap(
                    com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS
            ));
            return result;
        }
        noticeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                pushService.pushTask(CollectionUtil.hashMap(
                        PushTask.NOTICE_RULE, notice,
                        PushTask.TASK_APPLY_LOG_ID, task_apply_log_id,
                        PushTask.MERCHANT_IDS, successIds_copy
                ));
            }
        });
        return result;
    }

    /**
     * 批量开启理财账户在线服务
     *
     * @param merchant_ids
     * @param remark
     * @return
     */
    private List<Map> batchOpenOnlineServiceWithIds(List<String> merchant_ids, String remark, final String task_apply_log_id, Map businessRequest, String operator, Integer onlineService) {
        boolean flag = false;
        //修改任务状态
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE
        ));
        List<Map> result = new ArrayList<>();
        List<Map> falureDetail = new ArrayList<>();
        List<String> successIds = new ArrayList<>();

        for (String merchant_id : merchant_ids) {
            try {
                userInfoService.updateUserInfo(CollectionUtil.hashMap(
                        UserInfo.OPERATOR, operator,
                        UserInfo.REMARK, remark,
                        "online_service", onlineService,
                        DaoConstants.ID, merchant_id
                ));
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_SUCCESS,
                        "msg", "succ",
                        "data", null
                ));
                successIds.add(merchant_id);
            } catch (Exception e) {
                Map falure = CollectionUtil.hashMap(
                        "merchant_id", merchant_id,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", ""
                );
                result.add(falure);
                falureDetail.add(falure);
            }
        }
        String excelDownloadUrl = "";
        if (falureDetail.size() > 20) {
            excelDownloadUrl = uploadExcelResult(falureDetail,
                    Arrays.asList("商户sn", "错误原因"),
                    Arrays.asList(ConstantUtil.KEY_MERCHANT_ID, "msg")
            );
            falureDetail = falureDetail.subList(0, 20);
            falureDetail.add(CollectionUtil.hashMap(
                    "merchant_id", "...",
                    "code", "...",
                    "msg", "...",
                    "data", "错误数过多不全部显示"
            ));
            flag = true;
        }
        ospTaskService.updateTask(CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                TaskApplyLog.APPLY_RESULT, flag ? CollectionUtil.hashMap(
                        "批量操作理财账户在线服务状态总数", result.size(),
                        "批量操作理财账户在线服务状态成功数", successIds.size(),
                        "批量操作理财账户在线服务状态失败数", result.size() - successIds.size(),
                        "批量操作理财账户在线服务状态失败详情", falureDetail,
                        "downloadResultUrl", excelDownloadUrl
                ) : CollectionUtil.hashMap(
                        "批量操作理财账户在线服务状态总数", result.size(),
                        "批量操作理财账户在线服务状态成功数", successIds.size(),
                        "批量操作理财账户在线服务状态失败数", result.size() - successIds.size(),
                        "批量操作理财账户在线服务状态失败详情", falureDetail
                )
        ));

        final List<String> successIds_copy = successIds;
        if (CollectionUtils.isEmpty(successIds_copy)) {
            ospTaskService.updateTask(CollectionUtil.hashMap(
                    com.wosai.data.dao.DaoConstants.ID, task_apply_log_id,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS
            ));
            return result;
        }
        return result;
    }

    private void disableFinanceUser(String merchant_id, String remark, String operator) {
        userInfoService.disableUser(CollectionUtil.hashMap(
                DaoConstants.ID, merchant_id,
                UserInfo.DISABLE_BY, operator,
                UserInfo.DISABLE_REMARK, remark,
                UserInfo.DISABLE_TYPE, UserInfo.DISABLE_TYPE_MANUAL
        ));
    }

    private void closeFinanceUser(String merchant_id, String remark, String operator) {
        userInfoService.closeUser(CollectionUtil.hashMap(DaoConstants.ID, merchant_id, UserInfo.CLOSE_BY, operator, UserInfo.CLOSE_REMARK, remark));
    }

    private void enableFinanceUser(String merchant_id, String remark, String operator) {
        userInfoService.enableUser(CollectionUtil.hashMap(DaoConstants.ID, merchant_id, UserInfo.OPERATOR, operator, UserInfo.REMARK, remark));
    }

    private void reOpenFinanceUser(String merchant_id, String remark, String operator) {
        userInfoService.reEnableUser(CollectionUtil.hashMap(DaoConstants.ID, merchant_id, UserInfo.OPERATOR, operator, UserInfo.REMARK, remark));
    }

    @Override
    public Map getNoticeRule(Map request) {
        String code = BeanUtil.getPropString(request, NoticeRule.CODE);
        if (StringUtil.empty(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码不能为空");
        }
        if (!WithdrawUtil.NOTICE_RULE_CODES.contains(code)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则编码非系统定义code");
        }
        return noticeRuleService.getNoticeRuleByCodeAndSubCode(code, BeanUtil.getPropString(request, NoticeRule.SUB_CODE));
    }

    @Override
    public Map batchReEnableUser(Map request) {
        if (!(request.containsKey(NoticeRule.CODE) && request.containsKey(NoticeRule.SUB_CODE))) {
            request.put(NoticeRule.CODE, "3003");
            request.put(NoticeRule.SUB_CODE, "08");
        }
        final String remark = BeanUtil.getPropString(request, "remark");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        final Map notice = getNoticeRule(request);
        if (notice == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "通知规则不存在!");
        }
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "重开理财账户数目为0");
        }
        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_REOPEN_FINANCE_STATUS,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final List<String> merchant_ids_copy = merchant_ids;
        final String task_apply_log_id = BeanUtil.getPropString(task, com.wosai.data.dao.DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        financeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchReOpenFinanceWithIds(merchant_ids_copy, remark, notice, task_apply_log_id, businessRequest, operator);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }

    @Override
    public Map batchOnlineService(Map request) {
        final String remark = BeanUtil.getPropString(request, "remark");
        final Integer onlineService = BeanUtil.getPropInt(request, "online_service");
        if (StringUtil.empty(remark)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "备注不可为空!");
        }
        if (onlineService == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "online_service参数不可为空!");
        } else if ((onlineService != 0) && (onlineService != 1)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "online_service参数只能为0或1!");
        }
        List<String> merchant_ids = (List) BeanUtil.getProperty(request, "merchant_ids");
        if (merchant_ids == null || merchant_ids.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "开通理财在线客服账户数目为0");
        }
        //创建任务
        Map task = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_OPEN_ONLINE_SERVICE_STATUS,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_APPLY
        ));
        final List<String> merchant_ids_copy = merchant_ids;
        final String task_apply_log_id = BeanUtil.getPropString(task, com.wosai.data.dao.DaoConstants.ID);
        final Map businessRequest = HttpRequestUtil.getBusinessogRequest();
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        financeExecutor.submit(new Runnable() {
            @Override
            public void run() {
                batchOpenOnlineServiceWithIds(merchant_ids_copy, remark, task_apply_log_id, businessRequest, operator, onlineService);
            }
        });
        return CollectionUtil.hashMap(
                "task_apply_log_id", task_apply_log_id
        );
    }

    @Override
    public List batchReWithdraw(Map request) {
        ArrayList<String> ids = (ArrayList<String>) BeanUtil.getProperty(request, "ids");
        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        String remark = "运营人员手动重新发起赎回";
        ArrayList result = new ArrayList();
        if (ids != null && ids.size() > 0) {
            for (String id : ids) {
                Map<String, Object> transferResult;
                try {
                    transferResult = withdrawRecordService.submitApply(id, operator, remark);
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_SUCCESS,
                            "msg", "succ",
                            "data", transferResult
                    ));
                } catch (Exception e) {
                    result.add(CollectionUtil.hashMap(
                            "remitId", id,
                            "code", UpayException.CODE_UNKNOWN_ERROR,
                            "msg", e.getMessage(),
                            "data", ""
                    ));
                }
            }
        }
        return result;
    }

    @Override
    public Map operateWithdrawRealTime(Map request) {
        String merchantId = null;
        String remark = BeanUtil.getPropString(request, "remark");
        String merchantSn = BeanUtil.getPropString(request, ConstantUtil.KEY_SN);
        if (StringUtil.empty(merchantSn)) {
            merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_ID);
        }
        boolean isId = !StringUtil.empty(merchantId);
        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        String functionCode = BeanUtil.getPropString(request, BizOpLog.BUSINESS_FUNCTION_CODE);
        if (!StringUtil.empty(functionCode)) {
            if ("1000068".equals(functionCode) || "1000069".equals(functionCode)) {
                Map userInfoOld = isId ? userInfoService.getUserInfoById(merchantId) : userInfoService.getUserInfoBySn(merchantSn);
                Map userInfoNew = null;
                Map req = CollectionUtil.hashMap(
                        isId ? ConstantUtil.KEY_ID : ConstantUtil.KEY_SN, isId ? merchantId : merchantSn,
                        "operator", operator,
                        "remark", remark
                );
                if ("1000068".equals(functionCode)) {
                    userInfoNew = userInfoService.openWithdrawRealTime(req);
                } else {
                    userInfoNew = userInfoService.closeWithdrawRealTime(req);
                }
                businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                        BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, userInfoOld,
                        BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, userInfoNew,
                        BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "user_info",
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id",
                                BizOpLog.BUSINESS_FUNCTION_CODE, functionCode,
                                BizOpLog.REMARK, remark
                        )
                ));
                return userInfoNew;
            }
        }
        return CollectionUtil.hashMap(
                "code", UpayException.CODE_UNKNOWN_ERROR,
                "msg", "business_function_code 必须为1000068 或 1000069"
        );
    }

    private String uploadExcelResult(List<Map> merchants_unValid, List<String> headers, List<String> fields) {
        try {
            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet failureSheet = workbook.createSheet("失败详情");
            buildExcelDetail(failureSheet, headers, merchants_unValid, fields);
            return uploadStatementToOSS("xls", workbook);
        } catch (Exception e) {

        }
        return "";
    }

    @Override
    public List<Map> getAutoAddRules(Map request) {
        List<Map> result = ruleService.getAutoAddRules();
        for (Map map : result) {
            List<String> organizations = (List) map.getOrDefault("organizations", Lists.newArrayList());
            List<OrganizationBean> organizationList = Lists.newArrayList();
            for (String organizationId : organizations) {
                OrganizationBean organization = organizationRpcService.getOrganizationById(organizationId);
                if (organization != null) {
                    organizationList.add(organization);
                }
            }
            map.put("organization_details", organizationList);
        }
        return result;
    }

    @Override
    public Map getAutoAddRuleByCode(Map request) {
        String code= BeanUtil.getPropString(request, "code");
        if (StringUtil.empty(code)) {
            throw new FinanceBackendBizErrorException("code 不能为空!");
        }
        Map map = ruleService.getAutoAddRuleByCode(code);
        List<String> organizations = (List) map.getOrDefault("organizations", Lists.newArrayList());
        List<OrganizationBean> organizationList = Lists.newArrayList();
        for (String organizationId : organizations) {
            OrganizationBean organization = organizationRpcService.getOrganizationById(organizationId);
            if (organization != null) {
                organizationList.add(organization);
            }
        }
        map.put("organization_details", organizationList);
        return map;
    }

    @Override
    public Map updateAutoAddRule(Map request) {
        return ruleService.updateAutoAddRule(request);
    }

    @Override
    public Map addAutoAddRule(Map request) {
        return ruleService.addAutoAddRule(request);
    }

    @Override
    public void deleteAutoAddRuleByCode(Map request) {
        String code= BeanUtil.getPropString(request, "code");
        if (StringUtil.empty(code)) {
            throw new FinanceBackendBizErrorException("code 不能为空!");
        }
        ruleService.deleteAutoAddRuleByCode(code);
    }

    @Override
    public List<Map> batchWithdrawSlow(Map request) {
        List<String> merchantIds = (List) BeanUtil.getProperty(request, "merchant_ids");
        if (merchantIds == null || merchantIds.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "请先选择要处理的商户");
        }

        //操作人
        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        if (StringUtil.empty(operator)){
            throw  new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
        }
        String operatorId = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERID);
        if (StringUtil.empty(operatorId)) {
            operatorId = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        }

        //处理发起慢赎
        List<Map> result = new ArrayList<>();
        for(String merchantId : merchantIds){
            try {
                long userBeforeAmount = BeanUtil.getPropLong(userAmountService.getUserAmountByUseId(merchantId), UserAmount.BALANCE);
                Map amountInfo = withdrawRecordService.getUserMaxWithdrawMoney(CollectionUtil.hashMap(
                        FinanceConstant.KEY_USER_ID, merchantId,
                        WithdrawRecord.WITHDRAW_MODE, WithdrawRecord.WITHDRAW_MODE_STAND
                ));
                long maxAmount = BeanUtil.getPropLong(amountInfo, FinanceConstant.KEY_MAX_WITHDRAW_AVA_MONEY);
                if (maxAmount > 0){
                    Map withdraw = withdrawRecordService.submitWithdraw(CollectionUtil.hashMap(
                            FinanceConstant.KEY_USER_ID, merchantId,
                            WithdrawRecord.WITHDRAW_MODE, WithdrawRecord.WITHDRAW_MODE_STAND,
                            WithdrawRecord.AMOUNT, maxAmount,
                            "allow_withdraw", false
                    ));
                    result.add(CollectionUtil.hashMap(
                            "merchant_id", merchantId,
                            "code", UpayException.CODE_SUCCESS,
                            "msg", "succ",
                            "data", withdraw
                    ));



                    //记录商户日志
                    long userAfterAmount = BeanUtil.getPropLong(userAmountService.getUserAmountByUseId(merchantId), UserAmount.BALANCE);
                    List<String> fields = new ArrayList<>();
                    List<Object> befores = new ArrayList<>();
                    List<Object> afters = new ArrayList<>();
                    for(String key : Arrays.asList(UserAmount.BALANCE)){
                        fields.add("user_amount#" + key);
                        befores.add(getYuan(userBeforeAmount) + "元");
                        afters.add(getYuan(userAfterAmount) + "元");
                    }
                    businessLogService.createBizOpLog(CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_SYSTEM_CODE, BusinessLogUtil.BUSINESS_SYSTEM_CODE_SP,
                            BizOpLog.BUSINESS_SYSTEM_VERSION, BusinessLogUtil.BUSINESS_SYSTEM_VERSION_SP,
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                            BizOpLog.BUSINESS_FUNCTION_CODE, "1006003",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BizOpLog.OP_ID, UUID.randomUUID().toString(),
                            BizOpLog.OP_USER_ID, operatorId,
                            BizOpLog.OP_USER_NAME, operator,
                            BizOpLog.OP_OBJECT_ID, merchantId,
                            BizOpLog.OP_TIME, new Date().getTime(),
                            BizOpLog.REMARK, "强制结算",
                            BizOpLog.BUSINESS_OBJECT_COLUMN_CODES, fields,
                            BizOpLog.OP_COLUMN_VALUE_BEFORES, befores,
                            BizOpLog.OP_COLUMN_VALUE_AFTERS, afters
                    ));


                }else {
                    result.add(CollectionUtil.hashMap(
                            "merchant_id", merchantId,
                            "code", UpayException.CODE_UNKNOWN_ERROR,
                            "msg", "余额为0",
                            "data", null
                    ));
                }
            }catch (Exception e){
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchantId,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", null
                ));
            }
        }

        return result;
    }

    @Override
    public List<Map> batchWithdrawFast(Map request) {
        List<String> merchantIds = (List) BeanUtil.getProperty(request, "merchant_ids");
        if (merchantIds == null || merchantIds.size() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "请先选择要处理的商户");
        }

        //操作人
        String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        if (StringUtil.empty(operator)){
            throw  new UpayException(UpayException.CODE_NEED_LOGIN, "请重新登录");
        }
        String operatorId = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERID);
        if (StringUtil.empty(operatorId)) {
            operatorId = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        }

        //处理发起慢赎
        List<Map> result = new ArrayList<>();
        for(String merchantId : merchantIds){
            try {
                long userBeforeAmount = BeanUtil.getPropLong(userAmountService.getUserAmountByUseId(merchantId), UserAmount.BALANCE);
                Map amountInfo = withdrawRecordService.getUserMaxWithdrawMoney(CollectionUtil.hashMap(
                        FinanceConstant.KEY_USER_ID, merchantId,
                        WithdrawRecord.WITHDRAW_MODE, WithdrawRecord.WITHDRAW_MODE_REAL_TIME
                ));
                long maxAmount = BeanUtil.getPropLong(amountInfo, FinanceConstant.KEY_MAX_WITHDRAW_AVA_MONEY);
                maxAmount = maxAmount > 1000000? 1000000:maxAmount;
                if (maxAmount > 0){
                    Map withdraw = withdrawRecordService.submitWithdraw(CollectionUtil.hashMap(
                            FinanceConstant.KEY_USER_ID, merchantId,
                            WithdrawRecord.WITHDRAW_MODE, WithdrawRecord.WITHDRAW_MODE_REAL_TIME,
                            WithdrawRecord.AMOUNT, maxAmount,
                            "allow_withdraw", false
                    ));
                    result.add(CollectionUtil.hashMap(
                            "merchant_id", merchantId,
                            "code", UpayException.CODE_SUCCESS,
                            "msg", "succ",
                            "data", withdraw
                    ));


                    //记录商户日志
                    long userAfterAmount = BeanUtil.getPropLong(userAmountService.getUserAmountByUseId(merchantId), UserAmount.BALANCE);
                    List<String> fields = new ArrayList<>();
                    List<Object> befores = new ArrayList<>();
                    List<Object> afters = new ArrayList<>();
                    for(String key : Arrays.asList(UserAmount.BALANCE)){
                        fields.add("user_amount#" + key);
                        befores.add(getYuan(userBeforeAmount) + "元");
                        afters.add(getYuan(userAfterAmount) + "元");
                    }
                    businessLogService.createBizOpLog(CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_SYSTEM_CODE, BusinessLogUtil.BUSINESS_SYSTEM_CODE_SP,
                            BizOpLog.BUSINESS_SYSTEM_VERSION, BusinessLogUtil.BUSINESS_SYSTEM_VERSION_SP,
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                            BizOpLog.BUSINESS_FUNCTION_CODE, "1006003",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BizOpLog.OP_ID, UUID.randomUUID().toString(),
                            BizOpLog.OP_USER_ID, operatorId,
                            BizOpLog.OP_USER_NAME, operator,
                            BizOpLog.OP_OBJECT_ID, merchantId,
                            BizOpLog.OP_TIME, new Date().getTime(),
                            BizOpLog.REMARK, "强制结算",
                            BizOpLog.BUSINESS_OBJECT_COLUMN_CODES, fields,
                            BizOpLog.OP_COLUMN_VALUE_BEFORES, befores,
                            BizOpLog.OP_COLUMN_VALUE_AFTERS, afters
                    ));
                }else {
                    result.add(CollectionUtil.hashMap(
                            "merchant_id", merchantId,
                            "code", UpayException.CODE_UNKNOWN_ERROR,
                            "msg", "余额为0",
                            "data", null
                    ));
                }
            }catch (Exception e){
                result.add(CollectionUtil.hashMap(
                        "merchant_id", merchantId,
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "data", null
                ));
            }
        }

        return result;
    }


    @Override
    public Map getUserCurrentBankAccountRecord(Map request) {
        String merchantId = BeanUtil.getPropString(request, FinanceConstant.KEY_USER_ID);
        return bankAccountRecordService.getUserCurrentBankAccountRecordSlave(merchantId);
    }

    @Override
    public Map queryRedeemAmount(Map request) {
        Map result = withdrawRecordService.queryRedeemAmount(request);
        if(result.containsKey(FinanceConstant.KEY_LKL_EXCEPTION)){
            result.remove(FinanceConstant.KEY_LKL_EXCEPTION);
        }
        return result;
    }

    @Override
    public void reChangeCard(Map request) {
        bankAccountRecordService.reChangeCard(request);
    }

    /**
     * 分转员表示
     * 如果小数点后没有数字直接取整，负责保留小数点后数字
     * @param fen
     * @return
     */
    public static String getYuan(long fen){
        BigDecimal bd = new BigDecimal(fen);
        bd = bd.divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP);
        if (bd.doubleValue() - bd.intValue() > 0) {
            return bd.doubleValue() + "";
        }
        return bd.intValue() + "";
    }
}
