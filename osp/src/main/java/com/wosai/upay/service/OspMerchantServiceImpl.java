package com.wosai.upay.service;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.wosai.assistant.response.Page;
import com.wosai.assistant.response.UserBean;
import com.wosai.biz.merchantlevel.model.MerchantActiveLevel;
import com.wosai.biz.merchantlevel.service.MerchantActiveLevelService;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.iot.api.common.domain.IotVendor;
import com.wosai.iot.api.rpc.SmartCloudSoundService;
import com.wosai.iot.api.rpc.vo.QuerySnVO;
import com.wosai.merchant.Bean.MerchantConstant;
import com.wosai.merchant.service.IMerchantBankAccountService;
import com.wosai.merchant.service.IMerchantService;
import com.wosai.merchant.service.IStockMerchantAuditService;
import com.wosai.merchant.service.IStockMerchantService;
import com.wosai.risk.bean.RiskSpecialOperationBean;
import com.wosai.risk.constants.RiskSubjectTypeEnum;
import com.wosai.risk.service.IRiskMerchantLimitService;
import com.wosai.risk.service.IRiskSpecialOperationService;
import com.wosai.risk.service.IRiskTransLimitService;
import com.wosai.sales.core.model.Merchants;
import com.wosai.sales.core.service.IKeeperService;
import com.wosai.sales.core.service.IStoreService;
import com.wosai.shouqianba.withdrawservice.model.MerchantD1WithdrawConfig;
import com.wosai.shouqianba.withdrawservice.service.PushService;
import com.wosai.shouqianba.withdrawservice.service.WithdrawConfigService;
import com.wosai.sp.merchant.management.model.MerchantDisable;
import com.wosai.sp.merchant.management.service.MerchantDisableService;
import com.wosai.trade.service.SwitchService;
import com.wosai.upay.bank.info.api.model.CardbinList;
import com.wosai.upay.bank.info.api.service.BankBizService;
import com.wosai.upay.bank.model.MerchantBusinessLicense;
import com.wosai.upay.bank.model.MerchantBusinessLicenseUpdateVo;
import com.wosai.upay.bank.model.Request;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.bean.Order;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.common.util.MapUtil;
import com.wosai.upay.core.constant.CoreCommonConstants;
import com.wosai.upay.core.exception.CoreException;
import com.wosai.upay.core.exception.CoreMerchantBankAccountNoKeyInfoChangedException;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.SpecialAuthWhitelist;
import com.wosai.upay.core.service.*;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.helper.CommonUtil;
import com.wosai.upay.helper.RecordLogUtil;
import com.wosai.upay.job.model.CustomAppidApply;
import com.wosai.upay.job.service.ContractWeixinService;
import com.wosai.upay.merchant.audit.api.service.ApplicationService;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import com.wosai.upay.merchant.contract.service.LakalaWanmaService;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import com.wosai.upay.user.api.service.SpecialAuthWhitelistService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.*;
import com.wosai.upay.wallet.service.WalletService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.InvalidParameterException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

import static com.wosai.upay.common.dao.PageInfoUtil.extractPageInfo;
import static com.wosai.upay.common.util.ConstantUtil.*;
import static com.wosai.upay.constant.MerchantContactConstant.*;
import static com.wosai.upay.exception.UpayException.CODE_INVALID_PARAMETER;


public class OspMerchantServiceImpl implements OspMerchantService {
    private static final Logger logger = LoggerFactory.getLogger(OspMerchantServiceImpl.class);
    private String[] mailNotificationOrderSendto;
    @Autowired
    private UserService userService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private IMerchantService iMerchantService;
    @Autowired
    private SupportService supportService;
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private ProviderTradeParamsService providerTradeParamsService;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private JavaMailSenderImpl javaMailSender;
    @Autowired
    private WalletService walletService;
    @Autowired
    OspOrganizationService ospOrganizationService;
    @Autowired
    private BusinessLogService businessLogService;
    @Autowired
    private SpecialAuthWhitelistService specialAuthWhitelistService;
    @Autowired
    private MerchantActiveLevelService merchantActiveLevelService;
    //新增的精确查询服务
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private OspTaskService ospTaskService;
    @Autowired
    private IStockMerchantService stockMerchantService;
    @Autowired
    private IRiskSpecialOperationService riskSpecialOperationService;
    @Autowired
    private IStockMerchantAuditService stockMerchantAuditService;
    @Autowired
    private IMerchantBankAccountService merchantBankAccountService;
    @Autowired
    MerchantAuditService merchantAuditService;
    @Autowired
    private PushService pushService;
    @Autowired
    private IKeeperService keeperService;
    @Autowired
    private com.wosai.upay.merchant.contract.service.LakalaService lakalaService;
    @Autowired
    private com.wosai.sales.core.service.IMerchantService salesMerchantService;
    @Autowired
    private IStoreService istoreService;
    @Autowired
    private CurrencyFeerateService currencyFeerateService;
    @Autowired
    private LakalaWanmaService lakalaWanmaService;
    @Autowired
    private WithdrawConfigService withdrawConfigService;
    @Autowired
    private IRiskMerchantLimitService riskMerchantLimitService;
    @Autowired
    private SmartCloudSoundService smartCloudSoundService;
    @Autowired
    private ContractWeixinService contractWeixinService;

    @Autowired
    private com.wosai.upay.merchant.contract.service.SupportService supportServiceMerchant;

    @Autowired
    private BankService bankService;


    @Autowired
    BankBusinessLicenseService bankBusinessLicenseService;

    @Autowired
    private BankBizService bankBizService;

    @Autowired
    private IRiskTransLimitService riskTransLimitService;

    @Value("${oss.base-url}")
    private String ossBaseUrl;
    @Autowired
    private OssUploader ossUploader;

    @Autowired
    SwitchService switchService;
    @Autowired
    RecordLogUtil recordLogUtil;
    @Autowired
    private MerchantDisableService merchantDisableService;

    static Config config = ConfigService.getAppConfig();

    ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(2);//用于执行变更操作的线程池

    private static final String[] BANK_ACCOUNT_FIELDS = new String[]{
            "merchant_id", "type",
            "holder", "id_type",
            "identity", "holder_id_front_photo",
            "holder_id_back_photo", "holder_id_front_ocr_status",
            "holder_id_back_ocr_status", "holder_id_status",
            "tax_payer_id", "number",
            "verify_status", "bank_name",
            "branch_name", "city",
            "cellphone", "extra",
            "change_time", "bank_card_image",
            "transfer_voucher", "id_validity",
            "letter_of_authorization", "bank_card_status"
    };

    public OspMerchantServiceImpl(String mailNotificationOrderSendto) {
        if (!StringUtil.empty(mailNotificationOrderSendto)) {
            this.mailNotificationOrderSendto = mailNotificationOrderSendto.split(",");
        }
    }

    @Override
    public void enableMerchant(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, DaoConstants.ID);
        Map merchantOld = merchantService.getMerchantByMerchantId(merchantId);
        merchantService.enableMerchant(merchantId);
        Map merchantNew = merchantService.getMerchantByMerchantId(merchantId);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID
                )
        ));

        supportService.removeCachedParams(BeanUtil.getPropString(merchantNew, KEY_SN));
    }

    @Override
    public void disableMerchant(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, DaoConstants.ID);
        Map merchantOld = merchantService.getMerchantByMerchantId(merchantId);
        merchantService.disableMerchant(merchantId);
        Map merchantNew = merchantService.getMerchantByMerchantId(merchantId);
        //logger.info("HttpRequest before[{}]",HttpRequestUtil.getRequest().getHeader("businessLog"));

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID
                )
        ));
        supportService.removeCachedParams(BeanUtil.getPropString(merchantNew, KEY_SN));
    }

    @Override
    public void closeMerchant(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, DaoConstants.ID);
        Map merchantOld = merchantService.getMerchant(merchantId);
        merchantService.closeMerchant(merchantId);
        Map merchantNew = merchantService.getMerchantByMerchantId(merchantId);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID
                )
        ));
        supportService.removeCachedParams(BeanUtil.getPropString(merchantNew, KEY_SN));
    }

    @Override
    public ListResult findMerchants(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        String organizationId = BeanUtil.getPropString(request, "organizationId");
        String userId = BeanUtil.getPropString(request, "userId");
        long total = 0;
        if (!StringUtil.empty(organizationId) || !StringUtil.empty(userId)) {
            Page<String> merchants = ospOrganizationService.findByUserAndOrganization(request);
            //取第一页数据、存在推广人组织、推广人就忽略其他查询条件
            pageInfo.setPage(1);
            if (merchants != null && merchants.getData() != null && merchants.getData().size() > 0) {
                total = merchants.getTotalNumber();
                request = CollectionUtil.hashMap(
                        "merchant_ids", merchants.getData()
                );
            } else {
                return new ListResult();
            }
        }

        String iotSn = MapUtils.getString(request, "iot_sn");
        if (StringUtils.isNotBlank(iotSn)) {
            QuerySnVO querySnVO = new QuerySnVO();
            querySnVO.setDevice_sn(iotSn);
            try {
                IotVendor iotVendor = smartCloudSoundService.isBindingBySN(querySnVO);
                if (iotVendor != null) {
                    request.put("merchant_id", iotVendor.getMerchant_id());
                } else {
                    return ListResult.emptyListResult();
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
                return ListResult.emptyListResult();
            }
        }

        //<!-- 新增查询 --!>

        //client_sn 新增 商户终端号
        String client_sn = BeanUtil.getPropString(request, "client_sn");
        if (!StringUtil.empty(client_sn)) {
            ListResult tmp = terminalService.findTerminals(pageInfo, CollectionUtil.hashMap("client_sn", client_sn));
            if (tmp != null && tmp.getRecords() != null && tmp.getRecords().size() > 0) {
                String merchant_id = (String) tmp.getRecords().get(0).get("merchant_id");
                request.put("merchant_id", merchant_id);
            } else {
                return new ListResult();
            }
            request.remove("client_sn");
        }

        //store_sn 新增 商户门店号
        String store_sn = BeanUtil.getPropString(request, "store_sn");
        if (!StringUtil.empty(store_sn)) {
            ListResult tmp = storeService.findStores(pageInfo, CollectionUtil.hashMap("sn", store_sn));
            if (tmp != null && tmp.getRecords() != null && tmp.getRecords().size() > 0) {
                String merchant_id = (String) tmp.getRecords().get(0).get("merchant_id");
                request.put("merchant_id", merchant_id);
            } else {
                return new ListResult();
            }
            request.remove("store_sn");
        }

        //老板账号查询 username
        String username = BeanUtil.getPropString(request, "username");
        if (!StringUtil.empty(username)) {
            Map map = userService.getAccountByUsername(username);
            String account_id = BeanUtil.getPropString(map, "id");
            if (!StringUtil.empty(account_id)) {
                map = userService.getMerchantUserByAccountId(account_id);
                String merchant_id = BeanUtil.getPropString(map, "merchant_id");
                if (StringUtil.empty(merchant_id)) {
                    return new ListResult(0, new ArrayList<Map>());
                }
                request.put(TransactionParam.MERCHANT_ID, merchant_id);
            } else {
                return new ListResult();
            }
            request.remove("username");
        }

        //<!-- 新增查询结束--!>
        //拉卡拉商户号查询
        if (StringUtils.isNotBlank(MapUtils.getString(request, TransactionParam.LAKALA_MERC_ID))) {
            String lakalaMerchantId = MapUtils.getString(request, TransactionParam.LAKALA_MERC_ID);
            Map<String, Object> params = new HashMap<>();
            params.put("provider_merchant_id", lakalaMerchantId);
            ListResult listResult = providerTradeParamsService.listMerchantProviderParams(new PageInfo(1, 1), params);
            if (listResult.getRecords().size() == 0) {
                return new ListResult();
            }
            Map map = listResult.getRecords().get(0);
            if (CollectionUtils.isEmpty(map)) {
                return new ListResult();
            }
            request.put(TransactionParam.MERCHANT_SN, MapUtils.getString(map, TransactionParam.MERCHANT_SN));
        }

        //<!-- 业务系统优化新增查询 --!>
        //商户维护人 merchant_keeper_id
        if (StringUtils.isNotBlank(MapUtils.getString(request, "merchant_keeper_id"))) {
            String merchantKeeperId = MapUtils.getString(request, "merchant_keeper_id");
            List<String> merchantIds = salesMerchantService.getMerchantIdsByKeeperId(merchantKeeperId);
            if (merchantIds.size() < 1) {
                return new ListResult();
            }
            request.put("merchant_ids", merchantIds);
        }

        //门店维护人查询  store_keeper_id
        if (StringUtils.isNotBlank(MapUtils.getString(request, "store_keeper_id"))) {
            String storeKeeperId = MapUtils.getString(request, "store_keeper_id");
            List<String> merchantIds = istoreService.getMerchantIdsByStoreKeeperId(storeKeeperId);
            if (merchantIds.size() < 1) {
                return new ListResult();
            }
            request.put("merchant_ids", merchantIds);
        }

        String number = MapUtils.getString(request, MerchantBankAccount.NUMBER);
        String identity = MapUtils.getString(request, MerchantBankAccount.IDENTITY);
        String holder = MapUtils.getString(request, MerchantBankAccount.HOLDER);

        if (StringUtils.isNotBlank(number) || StringUtils.isNotBlank(identity) || StringUtils.isNotBlank(holder)) {
            ListResult result = merchantService.findMerchantBankAccounts(new PageInfo(1, 100), CollectionUtil.hashMap(
                    MerchantBankAccount.NUMBER, number,
                    MerchantBankAccount.IDENTITY, identity,
                    MerchantBankAccount.HOLDER, holder
            ));
            if (CollectionUtils.isEmpty(result.getRecords())) {
                return ListResult.emptyListResult();
            }
            request.put("merchant_ids", CommonUtil.getValues(result.getRecords(), MerchantBankAccount.MERCHANT_ID));
        }

        ListResult result = merchantService.findMerchants(pageInfo, request);
        if (result != null && !CollectionUtils.isEmpty(result.getRecords())) {
            List<String> merchantIds = new ArrayList<>();
            Set<String> merchantSns = new HashSet<>();

            for (Map map : result.getRecords()) {
                merchantIds.add(BeanUtil.getPropString(map, DaoConstants.ID));
                merchantSns.add(BeanUtil.getPropString(map, Merchant.SN));
            }
            try {
                Map<String, UserBean> organinfos = ospOrganizationService.getUserByMerchantIds(CollectionUtil.hashMap("merchantIds", merchantIds));

                if (organinfos != null && organinfos.size() > 0) {
                    for (Map map : result.getRecords()) {
                        map.put("organization", organinfos.get(BeanUtil.getPropString(map, DaoConstants.ID)));
                    }
                }
            } catch (Throwable e) {
                logger.error(e.getMessage());
            }
        }
        if (result != null && result.getTotal() < total) {
            result.setTotal(total);
        }
        return result;
    }

    public List<Map> getMerchantDisableReasons(Map request){
        String merchantSn = BeanUtil.getPropString(request, "merchant_sn");
        return merchantDisableService.getMerchantDisableReasons(merchantSn);
    }

    /**
     * 导出商户信息
     *
     * @param request
     * @return
     */
    public String exportMerchants(Map request) {
        Map terminal = (Map) BeanUtil.getProperty(request, "terminal");
        String terminalSn = BeanUtil.getPropString(terminal, "sn");
        String deviceFingerprint = BeanUtil.getPropString(terminal, "device_fingerprint");
        if (!StringUtil.empty(terminalSn) || !StringUtil.empty(deviceFingerprint)) {
            ListResult data = terminalService.findTerminals(PageInfoUtil.extractPageInfo(terminal), terminal);
            if (data != null) {
                if (data.getTotal() == 0) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出数据为空");
                }
                request.put("merchant_sn", BeanUtil.getPropString(data.getRecords().get(0), "merchant_sn"));
            }
        }

        request.put("page", 1);
        request.put("page_size", 501);
        //findMerchants接口total固定为10000
        ListResult listResult = findMerchants(request);
        List<Map> merchants = listResult.getRecords();
        if (CollectionUtils.isEmpty(merchants)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出数据为空");
        }
        if (merchants.size() > 500) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "数据超过500条, 不支持导出");
        }
        List<Map> merchantList = new ArrayList<>(merchants.size());
        for (Map m : merchants) {
            merchantList.add(CollectionUtil.hashMap(
                    "商户号", BeanUtil.getPropString(m, "sn"),
                    "商户名称", BeanUtil.getPropString(m, "name"),
                    "商户状态", (BeanUtil.getPropInt(m, "status") == 1 ? "正常" : "禁用"),
                    "推广人", BeanUtil.getNestedProperty(m, "organization.name"),
                    "推广人组织", BeanUtil.getNestedProperty(m, "organization.organizationNames")
            ));
        }

        try {
            File tmpFile = File.createTempFile("商户信息导出", ".xlsx");
            List<String> headers = Arrays.asList("商户号", "商户名称", "商户状态", "推广人", "推广人组织");
            Workbook wbook = ExcelUtil.buildExcelDetail(merchantList, "商户信息导出", headers, headers);
            FileOutputStream fos = new FileOutputStream(tmpFile);
            wbook.write(fos);
            fos.close();
            //上传到OSS
            FileInputStream fios = new FileInputStream(tmpFile);
            // calculate path
            String ossFilePath = "osp/merchantInfo/export/" + System.currentTimeMillis() + ".xlsx";
            ossUploader.uploadStaticsFile(ossFilePath, fios, tmpFile.length());
            return ossBaseUrl + ossFilePath;
        } catch (Exception e) {
            logger.error("商户信息导出失败", e);
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "商户信息导出失败，请重试");
        }
    }

    @Override
    public Map getMerchantBySn(Map request) {
        String merchantSn = BeanUtil.getPropString(request, "merchant_sn");
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (merchant != null) {
            String mercahntId = BeanUtil.getPropString(merchant, DaoConstants.ID);
            Map<String, UserBean> organinfo = ospOrganizationService.getUserByMerchantIds(CollectionUtil.hashMap("merchantIds", Collections.singletonList(mercahntId)));
            if (organinfo != null && organinfo.containsKey(mercahntId)) {
                merchant.put("organization", organinfo.get(mercahntId));
            }
            List<Map<String, Object>> keepers = keeperService.findKeepers(mercahntId, Merchants.class, true);
            merchant.put("keepers", keepers);
        }
        Map<String, Object> d1WithdrawConfig = withdrawConfigService.getMerchantD1WithdrawConfig(BeanUtil.getPropString(merchant, DaoConstants.ID));
        merchant.put("d1WithdrawConfig", d1WithdrawConfig);
        Map<String, Object> customerPhoneInfo = contractWeixinService.getWeixinUpdateTime(CollectionUtil.hashMap("merchant_sn", merchantSn));
        merchant.put("customer_phone_mtime", BeanUtil.getPropString(customerPhoneInfo, DaoConstants.MTIME));
        return merchant;
    }

    @Override
    public Map getMerchant(Map request) {
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        return merchantService.getMerchant(merchantId);
    }

    /**
     * 查询商户基本信息及照片相关信息
     *
     * @param request
     * @return
     */
    @Override
    public Map queryApplicationBaseByCondition(Map request) {
        String merchantId = BeanUtil.getPropString(request, KEY_MERCHANT_ID);

        String merchantSn = BeanUtil.getPropString(request, KEY_MERCHANT_SN);

        if (StringUtil.empty(merchantId) && StringUtil.empty(merchantSn)) {
            throw new UpayException(CODE_INVALID_PARAMETER, "参数 merchant_id与merchant_sn不能同时为空值");
        }

        ListResult listResult = applicationService.findApplicationBases(extractPageInfo(request), request);

        if (listResult != null && listResult.getTotal() > 0) {

            return (Map) listResult.getRecords().get(0);
        }

        return null;

    }

    /**
     * 创建商户银行账户
     *
     * @param request
     */
    @Override
    public Map createMerchantBankAccount(Map request) {

        Map merchantOld = null;
        Map merchantNew = merchantService.bindMerchantBankAccount(request);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_ADD,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_bank_account",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));
        return merchantNew;
    }


    /**
     * 编辑商户基本信息及照片相关信息
     *
     * @param request
     */
    //log
    @Override
    public Map updateApplicationBase(Map request) {
        return applicationService.updateApplicationBase(request);
    }

    /**
     * 编辑商户接口信息
     *
     * @param request
     */
    @Override
    public Map updateMerchant(Map request) {
        String[] notUpdateColumns = new String[]{
                Merchant.SN, Merchant.STATUS
        };
        for (String key : notUpdateColumns) {
            request.remove(key);
        }

        String merchantId = BeanUtil.getPropString(request, DaoConstants.ID, "");
        Map merchantOld = merchantService.getMerchant(merchantId);
        Map merchantNew = merchantService.updateMerchant(request);

        //记录业务日志
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000015",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID
                )
        ));
        return merchantNew;
    }

    /**
     * 查询商户银行账号
     *
     * @param request
     * @return
     */
    @Override
    public Map queryMerchantBankAccount(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        Map result = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        if (!StringUtils.isEmpty(BeanUtil.getPropString(result, "id_validity"))) {
            String date = BeanUtil.getPropString(result, "id_validity");
            if (date.contains("-") || date.contains("~")) {
                String[] dates = date.split("-");
                if (dates.length < 2) dates = date.split("~");
                if (dates.length == 2) {
                    result.put("id_card_start_date", dates[0]);
                    result.put("id_card_end_date", dates[1]);
                }
            }
        }
        // 判断是否需要分支行
        int type = MapUtils.getIntValue(result, MerchantBankAccount.TYPE);
        boolean needBankinfo = bankBizService
                .checkNeedBankinfo(type, MapUtils.getString(result, 1 == type ? MerchantBankAccount.NUMBER : MerchantBankAccount.BANK_NAME));
        result.put(CardbinList.NEED_BANKINFO, needBankinfo ? 1 : 0);
        return result;
    }

    @Override
    public Map getMerchantDeveloper(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        return merchantService.getMerchantDeveloperByMerchantId(merchantId);
    }

    @Override
    public Map getMerchantConfigFormalParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        int payway = BeanUtil.getPropInt(request, MerchantConfig.PAYWAY);
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        Map result = new HashMap();
        result.put(MerchantConfig.B2C_FORMAL, BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.B2C_FORMAL, false));
        result.put(MerchantConfig.C2B_FORMAL, BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.C2B_FORMAL, false));
        result.put(MerchantConfig.WAP_FORMAL, BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.WAP_FORMAL, false));
        result.put(MerchantConfig.PARAMS, BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS));
        return result;
    }

    @Override
    public void updateAlipayV1TradeParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        request.remove(ConstantUtil.KEY_MERCHANT_ID);
        Integer payWay = MerchantConfig.PAYWAY_ALIPAY_V1;
        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        tradeConfigService.updateAlipayV1TradeParams(merchantId, request);
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));

        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
    }

    @Override
    public void updateAlipayV2TradeParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        request.remove(ConstantUtil.KEY_MERCHANT_ID);
        Integer payWay = MerchantConfig.PAYWAY_ALIPAY_V2;
        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        tradeConfigService.updateAlipayV2TradeParams(merchantId, request);
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));

        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
    }

    @Override
    public void updateWeixinTradeParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        request.remove(ConstantUtil.KEY_MERCHANT_ID);
        Integer payWay = MerchantConfig.PAYWAY_WEIXIN;
        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        tradeConfigService.updateWeixinTradeParams(merchantId, request);
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));


        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
    }

    @Override
    public void updateWeixinHKTradeParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        request.remove(ConstantUtil.KEY_MERCHANT_ID);
        Integer payWay = 19;

        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        tradeConfigService.updateWeixinHKTradeParams(merchantId, request);
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));


        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
    }


    @Override
    public void updateWeixinWapTradeParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        request.remove(ConstantUtil.KEY_MERCHANT_ID);
        Integer payWay = MerchantConfig.PAYWAY_WEIXIN;
        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        tradeConfigService.updateWeixinWapTradeParams(merchantId, request);
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);


        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));

        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
    }

    @Override
    public List getAnalyzedMerchantConfigs(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        Map creditCardConfigs = tradeConfigService.getMerchantIsLimitCreditCard(merchantId);
        List<Map> merchantConfigs = tradeConfigService.getAnalyzedMerchantConfigs(merchantId);
        if (CollectionUtils.isEmpty(merchantConfigs)) {
            return merchantConfigs;
        }
        for (Map map : merchantConfigs) {
            map.put(TransactionParam.ALLOW_CREDIT_PAY, MapUtils.getString(creditCardConfigs, MapUtils.getString(map, MerchantConfig.PAYWAY), TransactionParam.CREDIT_PAY_ENABLE));
        }
        ListResult currencyFees = currencyFeerateService.getCurrencyFeerate(merchantId, new PageInfo(1, 1000));
        merchantConfigs.addAll(buildForeignCurrencyAndCreditCard(creditCardConfigs, currencyFees.getRecords()));
        CommonUtil.sort(merchantConfigs, MerchantConfig.PAYWAY);
        return merchantConfigs;
    }

    private List<Map> buildForeignCurrencyAndCreditCard(Map creditCardConfigs, List<Map> foreignCurrencys) {
        if (CollectionUtils.isEmpty(foreignCurrencys)) {
            return Collections.EMPTY_LIST;
        }
        Map<String, Map> result = new HashMap<>();
        for (Map map : foreignCurrencys) {
            String payway = MapUtils.getString(map, MerchantConfig.PAYWAY);
            String subway = MapUtils.getString(map, "sub_payway");

            if (!result.containsKey(payway)) {
                result.put(payway, CollectionUtil.hashMap(
                        MerchantConfig.PAYWAY, payway,
                        MerchantConfig.B2C_FEE_RATE, "0.6",
                        MerchantConfig.B2C_STATUS, 0,
                        MerchantConfig.C2B_FEE_RATE, "0.6",
                        MerchantConfig.C2B_STATUS, 0,
                        MerchantConfig.WAP_FEE_RATE, "0.6",
                        MerchantConfig.WAP_STATUS, 0
                ));
            }
            //'1': 'B扫C'  '2': 'C扫B', '3': 'WAP支付',
            Map<String, Object> config = result.get(payway);
            String id = "";
            String feeRateKey = "";
            String statusKey = "";
            switch (subway) {
                case "1":
                    id = "b2c_id";
                    feeRateKey = MerchantConfig.B2C_FEE_RATE;
                    statusKey = MerchantConfig.B2C_STATUS;
                    break;
                case "2":
                    id = "c2b_id";
                    feeRateKey = MerchantConfig.C2B_FEE_RATE;
                    statusKey = MerchantConfig.C2B_STATUS;
                    break;
                case "3":
                    id = "wap_id";
                    feeRateKey = MerchantConfig.WAP_FEE_RATE;
                    statusKey = MerchantConfig.WAP_STATUS;
                    break;
            }
            config.put(TransactionParam.ALLOW_CREDIT_PAY, MapUtils.getString(creditCardConfigs, TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE));
            config.put(id, MapUtils.getString(map, ConstantUtil.KEY_ID));
            config.put(feeRateKey, MapUtils.getString(map, CurrencyFeerate.FEE_RATE));
            config.put(CurrencyFeerate.CURRENCY_CODES_PAIRS, MapUtils.getString(map, CurrencyFeerate.CURRENCY_CODES_PAIRS));
            config.put(CurrencyFeerate.REMARK, MapUtils.getString(map, CurrencyFeerate.REMARK));
            config.put(statusKey, MapUtils.getIntValue(map, CurrencyFeerate.STATUS, 0));
        }

        return CommonUtil.convertToListMap(result);
    }

    @Override
    public Map getMerchantConfigByMerchantIdAndPayway(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        String paywayStr = BeanUtil.getPropString(request, "payway");
        if (StringUtil.empty(paywayStr)) {
            return tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        } else {
            int payway = BeanUtil.getPropInt(request, "payway");
            return tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        }
    }


    @Override
    public void updateMerchantConfigStatusAndFeeRate(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        request.remove(ConstantUtil.KEY_MERCHANT_ID);
        Integer payWay = BeanUtil.getPropInt(request, MerchantConfig.PAYWAY, -100);
        payWay = payWay == -100 ? null : payWay;

        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        tradeConfigService.updateMerchantConfigStatusAndFeeRate(merchantId, request);
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));

        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
    }

    @Override
    public Map createCurrencyFeeRateMappingRules(Map request) {
        String merchantId = MapUtils.getString(request, ConstantUtil.KEY_MERCHANT_ID);
        String payway = MapUtils.getString(request, CurrencyFeerate.PAYWAY);
        String subPayway = MapUtils.getString(request, CurrencyFeerate.SUB_PAYWAY);

        ListResult list = currencyFeerateService.getCurrencyFeerate(merchantId, new PageInfo(1, 1000));
        if (list.getTotal() > 0) {
            List<Map> result = list.getRecords();
            for (Map temp : result) {
                if (StringUtils.equals(payway, MapUtils.getString(temp, CurrencyFeerate.PAYWAY))
                        && StringUtils.equals(subPayway, MapUtils.getString(temp, CurrencyFeerate.SUB_PAYWAY))) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "C端钱包配置已存在!");
                }
            }
        }

        Map result = currencyFeerateService.createCurrencyFeerateMappingRules(BeanUtil.getPart(request, Lists.newArrayList(
                CurrencyFeerate.MERCHANT_ID,
                CurrencyFeerate.PAYWAY,
                CurrencyFeerate.SUB_PAYWAY,
                CurrencyFeerate.CURRENCY_CODES_PAIRS,
                CurrencyFeerate.FEE_RATE,
                CurrencyFeerate.STATUS,
                CurrencyFeerate.REMARK
        )));
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, null,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, result,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_ADD,
                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000014",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "currency_feerate_mapping",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, CurrencyFeerate.MERCHANT_ID
                )
        ));

        return result;
    }


    @Override
    public Map updateCurrencyFeerate(Map request) {
        String id = MapUtils.getString(request, ConstantUtil.KEY_ID);
        Map before = currencyFeerateService.getCurrencyFeeRateById(id);
        Map after = currencyFeerateService.updateCurrencyFeerate(request);
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, before,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, after,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000014",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "currency_feerate_mapping",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, CurrencyFeerate.MERCHANT_ID
                )
        ));
        return after;
    }

    @Override
    public Map getMerchantTradeValidateParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        Map merchantTradeValidateParams = tradeConfigService.getMerchantTradeValidateParams(merchantId);
        //对结果中的渠道单笔限额做一个转换返回
        if (merchantTradeValidateParams.containsKey(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN) && null != merchantTradeValidateParams.get(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN)) {
            getMerchantSingleMax(merchantTradeValidateParams);
        }
        return merchantTradeValidateParams;
    }

    /**
     * 整理渠道单笔限额的格式
     *
     * @param merchantTradeValidateParams
     * @return:
     * @author: huangwenbin
     * @date: 2018/7/5 15:00
     */
    private void getMerchantSingleMax(Map merchantTradeValidateParams) {
        Object merchantSingleMap = merchantTradeValidateParams.get(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN);
        if (merchantSingleMap != null && merchantSingleMap instanceof Map) {
            int aliLimit = getPayWaySingleMax((Map) merchantSingleMap, ParamConstantUtil.PAY_WAY_ALI);
            if (aliLimit > Integer.MIN_VALUE) {
                merchantTradeValidateParams.put(ParamConstantUtil.ALIPAY_LIMIT, aliLimit);
            }
            int wechantLimit = getPayWaySingleMax((Map) merchantSingleMap, ParamConstantUtil.PAY_WAY_WECHAT);
            if (wechantLimit > Integer.MIN_VALUE) {
                merchantTradeValidateParams.put(ParamConstantUtil.WECHAT_PAY_LIMIT, wechantLimit);
            }
        }

    }

    /**
     * 获取某种支付渠道下的限额不同subpayway的限额的最大值
     *
     * @param merchantSingleMap
     * @param payWay
     * @return:
     * @author: huangwenbin
     * @date: 2018/7/5 17:56
     */
    private int getPayWaySingleMax(Map merchantSingleMap, String payWay) {
        if (merchantSingleMap.containsKey(payWay)) {
            Object limitMap = merchantSingleMap.get(payWay);
            if (limitMap != null && limitMap instanceof Map && ((Map) limitMap).size() > 0) {
                int limit = Integer.MIN_VALUE;
                for (Object subPayWayLimit : ((Map) limitMap).values()) {
                    if (subPayWayLimit != null && Integer.parseInt(subPayWayLimit.toString()) > limit) {
                        limit = Integer.parseInt(subPayWayLimit.toString());
                    }
                }
                return limit;
            }
        }
        return Integer.MIN_VALUE;
    }


    @Override
    public void updateMerchantTradeValidateParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        request.remove(ConstantUtil.KEY_MERCHANT_ID);
        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (request.containsKey(ParamConstantUtil.MERCHANT_SN) && (request.containsKey(ParamConstantUtil.ALIPAY_LIMIT) || request.containsKey(ParamConstantUtil.WECHAT_PAY_LIMIT))) {
            riskMerchantLimitService.setMerchantSingleMax(request);
        }
        tradeConfigService.updateMerchantTradeValidateParams(merchantId, request);
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000002"
                )
        ));
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
        riskTransLimitService.rollbackTempTransLimitByMerchantId(merchantId);//回滚临时额度
    }

    @Override
    public void updateMerchantDailyPaywayMaxSumOfTrans(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        int payway = BeanUtil.getPropInt(request, Order.PAYWAY);
        Map<String, String> subPaywayConfig = (Map<String, String>) request.get("sub_payway_config");
        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        tradeConfigService.updateMerchantDailyPaywayMaxSumOfTrans(merchantId, payway, subPaywayConfig);
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id",
                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000002"
                )
        ));
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
    }

    @Override
    public long merchantAvailableMspRefund(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        if (StringUtil.empty(merchantId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户id不可为空");
        }
        Map permission = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, merchantId
        );
        if (merchantService.getMerchant(merchantId) == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户id不存在!");
        }
        if (permission == null || permission.isEmpty()) {
            return 0;
        } else {
            return 1;
        }
    }

    @Override
    public void updateMerchantMspRefundPermission(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        Map merchant = merchantService.getMerchant(merchantId);
        if (merchant == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "商户不存在");
        }
        String objectName = BeanUtil.getPropString(merchant, Merchant.NAME);

        //判断是否需要权限变更
        long permissionOld = merchantAvailableMspRefund(request);
        int permission = BeanUtil.getPropInt(request, "permission_msp_refund");
        if (!SpecialAuthWhitelist.MSP_REFUND_PERMISSIONS.contains(permission)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "permission_msp_refund参数不支持!");
        }
        if (permission == permissionOld) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "无效或重复操作!");
        }
        Map mspRefundPermissionOld = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, merchantId
        );
        if (mspRefundPermissionOld != null) {
            mspRefundPermissionOld.put("object_name", objectName);
        }
        String mspRefundPermissionId = BeanUtil.getPropString(mspRefundPermissionOld, DaoConstants.ID);

        if (SpecialAuthWhitelist.MSP_REFUND_PERMISSION_CLOSE == permission && !StringUtil.empty(mspRefundPermissionId)) {
            specialAuthWhitelistService.deleteSpecialAuthWhitelist(mspRefundPermissionId);
        } else if (SpecialAuthWhitelist.MSP_REFUND_PERMISSION_OPEN == permission) {
            specialAuthWhitelistService.createSpecialAuthWhitelist(
                    CollectionUtil.hashMap(
                            SpecialAuthWhitelist.AUTH_TYPE, SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND,
                            SpecialAuthWhitelist.OBJECT_ID, merchantId,
                            SpecialAuthWhitelist.OBJECT_TYPE, SpecialAuthWhitelist.OBJECT_TYPE_MERCHANT
                    )
            );
            try {
                //发送短信通知
                pushService.sendSms(merchantId, CollectionUtil.hashMap(
                        "template", "k5gEi",
                        "vars", CollectionUtil.hashMap(),
                        "throwException", true,
                        "exceptionMsg", "商户开通退款权限发送短信提醒"
                ));
            } catch (Exception e) {
                logger.error("商户开通退款权限发送短信提醒异常merchantId[{}]", merchantId, e);
            }
        }

        Map mspRefundPermissionNew = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, merchantId
        );
        if (mspRefundPermissionNew != null) {
            mspRefundPermissionNew.put("object_name", objectName);
        }

        //保存业务日志
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, mspRefundPermissionOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, mspRefundPermissionNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "special_auth_whitelist",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "special_auth_whitelist",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "object_id",
                        BizOpLog.REMARK, "",
                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000058"
                )
        ));
    }


    //Will be logged
    @Override
    public Map batchUpdateMerchantConfig(MultipartFile file) {
        List<String> titles = new ArrayList(Arrays.asList(
                ConstantUtil.KEY_MERCHANT_SN,
                "merchant_daily_max_sum_of_trans",
                Merchant.WITHDRAW_MODE,
                "remark"
        ));
        List<String> titlesFromExecl = ExcelUtil.getFirstRowFromExcelFile(file);
        List<String> checkPayWays = titlesFromExecl.subList(4, titlesFromExecl.size());
        titles.addAll(checkPayWays);
        List<List<Map>> datas = ExcelUtil.getListFromExcelFile(file, titles);
        if (datas == null || datas.size() < 1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }
        List<Map> data = datas.get(0);
        if (data == null || data.size() < 2) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件为空");
        }
        //去除标题
        data.remove(0);
        if (data.size() > 200) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导入数据超过200条");
        }
        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        String userId = BeanUtil.getPropString(user, DaoConstants.ID);
        Map params = CollectionUtil.hashMap(
                "userId", userId,
                "userName", BeanUtil.getPropString(user, Account.USERNAME)
        );
        //记录日志
        logger.info("批量修改商户结算配置日志记录request[{}],params[{}]", data, params);
        Map result = null;
        try {
            result = batchUpdateMerchantConfigDetails(data, params, checkPayWays);
        } catch (Exception e) {
            logger.error("批量修改商户结算配置日志记录异常", e);
        }
        logger.info("批量修改商户结算配置日志记录结果request[{}],params[{}],result[{}]", data, params, result);

        Map taskApplyLog = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_CHANGE_MERCHANT_CONFIG,
                TaskApplyLog.APPLY_SYSTEM, TaskApplyLogUtil.APPLY_SYSTEM_SP,
                TaskApplyLog.PAYLOAD, result,
                TaskApplyLog.USER_ID, userId,
                TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS,
                TaskApplyLog.APPLY_RESULT, result
        ));
        logger.info("批量修改商户结算配置taskApplyLog[{}]", BeanUtil.getPropString(taskApplyLog, DaoConstants.ID));
        HttpRequestUtil.getSession().setAttribute("SESSION_BATCH_UPDATE_MERCHANT_CONFIG", BeanUtil.getPropString(taskApplyLog, DaoConstants.ID));
        sendMailAsynchronous(mailNotificationOrderSendto, "批量修改商户费率和限额", JacksonUtil.toJsonString(params) + "," + JacksonUtil.toJsonString(result));
        return result;
    }

    @Override
    public ListResult getMerchantAllActiveLevelByMerchantId(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        pageInfo.setOrderBy(Arrays.asList(new OrderBy(MerchantActiveLevel.MONTH, OrderBy.OrderType.DESC)));
        return merchantActiveLevelService.getMerchantAllActiveLevelByMerchantId(merchantId, pageInfo, request);
    }

    /**
     * 导入逻辑
     * 1.去除重复的商户和商户号为空的
     * 2.去除无备注信息商户
     * 3.额度，提现方式，费率全为空
     * 4.去除商户不存在信息
     * 5.导入
     *
     * @param merchantes
     */
    private Map batchUpdateMerchantConfigDetails(List<Map> merchantes, Map params, List<String> checkPayWays) {
        String username = BeanUtil.getPropString(params, "userName");
        int total = merchantes.size();
        //没有通过校验（条件1、2、3、4）
        List<Map> merchants_unValid = new ArrayList<>();

        //去重  商户号为空 备注为空    额度，提现方式，费率全为空
        List<String> checkParams = new ArrayList(Arrays.asList(Merchant.WITHDRAW_MODE, "merchant_daily_max_sum_of_trans"));
        //允许的提现方式
        List<String> checkWithdrawModes = Arrays.asList("1", "2");
        //成功merchat_sns
        List<String> merchant_has_sns = new ArrayList<>();
        //还原检查参数列表
        checkParams.addAll(checkPayWays);
        for (int i = 0; i < merchantes.size(); i++) {
            //商户号为空
            if (merchant_has_sns.contains(BeanUtil.getPropString(merchantes.get(i), ConstantUtil.KEY_MERCHANT_SN, ""))) {
                merchantes.get(i).put("errmsg", "excel中重复商户");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //商户号为空
            if (StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), ConstantUtil.KEY_MERCHANT_SN))) {
                merchantes.get(i).put("errmsg", "商户号为空");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //备注为空
            if (StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), "remark"))) {
                merchantes.get(i).put("errmsg", "备注为空");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //额度，提现方式，费率全为空
            int checkNum = 0;
            for (String checkParam : checkParams) {
                if (StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), checkParam))) {
                    checkNum++;
                }
            }
            if (StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), "merchant_daily_max_sum_of_trans")) &&
                    StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), Merchant.WITHDRAW_MODE)) &&
                    checkNum == checkParams.size()) {
                merchantes.get(i).put("errmsg", "额度，提现方式，费率全为空");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }

            //费率为0.00到2.00
            boolean isBreak = false;
            for (String checkParam : checkPayWays) {
                if (!StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), checkParam))) {
                    String fee_rate = BeanUtil.getPropString(merchantes.get(i), checkParam);
                    if (!CommonUtil.isUpToStandard(fee_rate)) {
                        merchantes.get(i).put("errmsg", "费率为0.00到2.00");
                        merchants_unValid.add(merchantes.get(i));
                        merchantes.remove(i);
                        i--;
                        isBreak = true;
                        break;
                    }
                }
            }
            if (isBreak) {
                continue;
            }

            //提现方式是否允许
            if (!StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), Merchant.WITHDRAW_MODE)) && !checkWithdrawModes.contains(BeanUtil.getPropString(merchantes.get(i), Merchant.WITHDRAW_MODE))) {
                merchantes.get(i).put("errmsg", "提现方式:" + BeanUtil.getPropString(merchantes.get(i), Merchant.WITHDRAW_MODE) + " 不存在 ");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
            //额度 < 0
            if (!StringUtil.empty(BeanUtil.getPropString(merchantes.get(i), "merchant_daily_max_sum_of_trans"))) {
                try {
                    if (BeanUtil.getPropLong(merchantes.get(i), "merchant_daily_max_sum_of_trans") < 0) {
                        merchantes.get(i).put("errmsg", "额度:" + BeanUtil.getPropString(merchantes.get(i), "merchant_daily_max_sum_of_trans") + " 不可小于0 ");
                        merchants_unValid.add(merchantes.get(i));
                        merchantes.remove(i);
                        i--;
                        continue;
                    }
                } catch (Exception e) {
                    merchantes.get(i).put("errmsg", "额度:" + BeanUtil.getPropString(merchantes.get(i), "merchant_daily_max_sum_of_trans") + " 非法数字 ");
                    merchants_unValid.add(merchantes.get(i));
                    merchantes.remove(i);
                    i--;
                    continue;
                }
            }

            //成功merchat_sns
            merchant_has_sns.add(BeanUtil.getPropString(merchantes.get(i), ConstantUtil.KEY_MERCHANT_SN));
        }

        //商户信息补全
        final ListResult merchantInfos = merchant_has_sns.size() == 0 ? new ListResult(0, new ArrayList<Map>()) : merchantService.findMerchants(new PageInfo(1, merchant_has_sns.size()), CollectionUtil.hashMap("merchant_sns", merchant_has_sns));
        Map merchantInfosMap = new HashMap() {{
            for (Map map : merchantInfos.getRecords()) {
                put(BeanUtil.getPropString(map, Merchant.SN), map);
            }
        }};
        for (int i = 0; i < merchantes.size(); i++) {
            if (!merchantInfosMap.containsKey(BeanUtil.getPropString(merchantes.get(i), ConstantUtil.KEY_MERCHANT_SN))) {
                merchantes.get(i).put("errmsg", "商户sn不存在");
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
        }

        Map httpReques = HttpRequestUtil.getBusinessogRequest();

        //执行状态和提现操作
        for (int i = 0; i < merchantes.size(); i++) {
            Map merchant = (Map) BeanUtil.getProperty(merchantInfosMap, BeanUtil.getPropString(merchantes.get(i), ConstantUtil.KEY_MERCHANT_SN));
            String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
            List<Map> merchantConfigs = tradeConfigService.getMerchantConfigsByMerchantId(merchantId);
            String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
            String daily_max = BeanUtil.getPropString(merchantes.get(i), "merchant_daily_max_sum_of_trans");
            String withdraw_mode = BeanUtil.getPropString(merchantes.get(i), Merchant.WITHDRAW_MODE);
            String remark = BeanUtil.getPropString(merchantes.get(i), "remark");
            boolean ok = true;
            String errorMsg = "";
            //修改额度
            if (!StringUtil.empty(daily_max)) {
                try {
                    tradeConfigService.updateMerchantTradeValidateParams(merchantId, CollectionUtil.hashMap(
                            "merchant_daily_max_sum_of_trans", daily_max
                    ));
                    errorMsg = errorMsg + "修改额度成功 ";
                } catch (Exception e) {
                    logger.error("修改额度异常params[{}]", merchantes.get(i), e);
                    errorMsg = errorMsg + "修改额度失败 ";
                    ok = false;
                }
            }
            //修改提现方式
            if (!StringUtil.empty(withdraw_mode)) {
                try {
                    merchantService.updateMerchant(CollectionUtil.hashMap(
                            DaoConstants.ID, merchantId,
                            Merchant.WITHDRAW_MODE, withdraw_mode
                    ));
                    errorMsg = errorMsg + "修改提现方式成功 ";
                } catch (Exception e) {
                    logger.error("修改提现方式异常params[{}]", merchantes.get(i), e);
                    errorMsg = errorMsg + "修改提现方式失败 ";
                    ok = false;
                }
            }


            //修改费率
            for (String payWay : checkPayWays) {
                String payWayFee = BeanUtil.getPropString(merchantes.get(i), payWay);
                if (!StringUtil.empty(payWayFee)) {
                    try {
                        Map config = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, Integer.valueOf(payWay));
                        if (null != config
                                && null != config.get(MerchantConfig.PARAMS)
                                && MerchantConfig.STATUS_OPENED == BeanUtil.getPropInt(config, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_STATUS), MerchantConfig.STATUS_CLOSED)) {
                            logger.info("商户已配置特优费率，修改费率失败 payWay[{}],params[{}]", payWay, merchantes.get(i));
                            errorMsg = errorMsg + "修改费率:" + payWay + " 失败，设置了分级费率不支持批量更新  ";
                            ok = false;
                            continue;
                        }

                        tradeConfigService.updateMerchantConfigStatusAndFeeRate(merchantId, CollectionUtil.hashMap(
                                MerchantConfig.PAYWAY, Integer.parseInt(payWay),
                                MerchantConfig.C2B_FEE_RATE, payWayFee,
                                MerchantConfig.B2C_FEE_RATE, payWayFee,
                                MerchantConfig.WAP_FEE_RATE, payWayFee,
                                MerchantConfig.MINI_FEE_RATE, payWayFee
                        ));
                        errorMsg = errorMsg + "修改费率:" + payWay + " 成功 ";
                    } catch (Exception e) {
                        logger.error("修改费率异常payWay[{}],params[{}]", payWay, merchantes.get(i), e);
                        errorMsg = errorMsg + "修改费率:" + payWay + " 失败 ";
                        ok = false;
                    }
                }
            }
            //清除缓存
            supportService.removeCachedParams(merchantSn);

            //记录商户日志
            Map merchant_new = merchantService.getMerchant(merchantId);
            String op_id = BusinessLogUtil.uuid();
            long op_time = new Date().getTime();
            //记录商户日志
            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchant,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchant_new,
                    BusinessLogUtil.LOG_PARAM_REQUEST, httpReques,
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_BATCH_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID,
                            BizOpLog.REMARK, remark,
                            BizOpLog.OP_TIME, op_time,
                            BizOpLog.BUSINESS_FUNCTION_CODE, "1000003"
                    )
            ));

            List<Map> merchantConfigs_new = tradeConfigService.getMerchantConfigsByMerchantId(merchantId);
            for (Map merchantConifg : merchantConfigs) {
                String payWay_config = BeanUtil.getPropString(merchantConifg, MerchantConfig.PAYWAY, "");
                for (Map merchantConfigNew : merchantConfigs_new) {
                    if (BeanUtil.getPropString(merchantConfigNew, MerchantConfig.PAYWAY, "").equals(payWay_config)) {
                        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConifg,
                                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                                BusinessLogUtil.LOG_PARAM_REQUEST, httpReques,
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_BATCH_MODIFY,
                                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, ConstantUtil.KEY_MERCHANT_ID,
                                        BizOpLog.REMARK, remark,
                                        BizOpLog.OP_TIME, op_time,
                                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000002"
                                )
                        ));
                    }
                }
            }

            if (!ok) {
                merchantes.get(i).put("errmsg", errorMsg);
                merchants_unValid.add(merchantes.get(i));
                merchantes.remove(i);
                i--;
                continue;
            }
        }

        //保存导入结果到任务
        Map<String, Object> result = new HashedMap();
        result.put("total", total);
        result.put("success", total - merchants_unValid.size());
        result.put("failure", merchants_unValid.size());
        result.put("failureDetail", merchants_unValid);
        return result;
    }

    @Override
    public void getBatchUpdateMerchantConfigDetail(Map<String, Object> request, HttpServletResponse response) throws IOException {
        String logId = (String) HttpRequestUtil.getSession().getAttribute("SESSION_BATCH_UPDATE_MERCHANT_CONFIG");
        Map payload = (Map) BeanUtil.getProperty(ospTaskService.getTask(CollectionUtil.hashMap(DaoConstants.ID, logId)), TaskApplyLog.PAYLOAD);
        List<Map> errors = new ArrayList<>();
        if (payload == null || !payload.containsKey("failureDetail")) {
            errors.add(CollectionUtil.hashMap(ConstantUtil.KEY_MERCHANT_SN, "", "errmsg", "导入商户错误过多，无法保存，已记录在日志中，请查看相关日志"));
        } else {
            errors = (List<Map>) BeanUtil.getProperty(payload, "failureDetail");
        }
        HSSFWorkbook workbook = ExcelUtil.buildExcelDetail(errors, "批量修改商户配置失败商户", Arrays.asList("商户号", "失败原因"), Arrays.asList(ConstantUtil.KEY_MERCHANT_SN, "errmsg"));
        String fileName = new Date().getTime() + "-importFail.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }


    @Override
    public ListResult findMerchantUsers(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return userService.findMerchantUsers(pageInfo, request);
    }

    @Override
    public void deleteMerchantUser(Map request) {
        if (request != null) {
            String merchant_user_id = BeanUtil.getPropString(request, "merchant_user_id");
            String account_id = BeanUtil.getPropString(request, "account_id");
            Map merchantUserOld = userService.getAccount(account_id);
            if (!StringUtil.empty(merchant_user_id) && !StringUtil.empty(account_id)) {
                userService.deleteMerchantUser(merchant_user_id);
                userService.deleteAccountTruly(account_id);
            }
            Map merchantUserNew = userService.getAccount(account_id);

            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantUserOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantUserNew,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant_user",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_DEL,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "account",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID
                    )
            ));
        }
    }

    @Override
    public List<Map> getMerchantUserStoreAuths(Map request) {
        return userService.getMerchantUserStoreAuths(request);
    }

    @Override
    public List<Map> getMerchantUserDepartmentAuths(Map request) {
        return userService.getMerchantUserDepartmentAuths(request);
    }

    @Override
    public ListResult findMerchantUserStoreAuths(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return userService.findMerchantUserStoreAuths(pageInfo, request);
    }

    @Override
    public long getBalance(Map request) {
        return walletService.getBalance(BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID));
    }


    @Override
    public List<Map> batchUpdateWithdrawMode(Map request) {
        if (StringUtil.empty(BeanUtil.getPropString(request, "withdraw_mode"))) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "提现方式不能为空");
        }
        int withdraw_mode = BeanUtil.getPropInt(request, "withdraw_mode");
        List<String> merchantSnList = (List<String>) request.get("snList");
        List<Map> result = new LinkedList<>();
        if (merchantSnList == null) {
            return result;
        }
        for (String sn : merchantSnList) {
            try {
                Map merchant = merchantService.getMerchantBySn(sn);
                if (merchant == null) throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "门店sn不正确");
                String id = BeanUtil.getPropString(merchant, ConstantUtil.KEY_ID);
                updateMerchant(CollectionUtil.hashMap(
                        ConstantUtil.KEY_ID, id,
                        Merchant.WITHDRAW_MODE, withdraw_mode
                ));
                result.add(CollectionUtil.hashMap(
                        "code", UpayException.CODE_SUCCESS,
                        "id", id
                ));
            } catch (Exception e) {
                result.add(CollectionUtil.hashMap(
                        "code", UpayException.CODE_UNKNOWN_ERROR,
                        "msg", e.getMessage(),
                        "id", sn
                ));
            }
        }
        return result;
    }

    String getCellValue(int colIndex, Row row) {
        Cell cell = row.getCell(colIndex);
        if (cell == null) {
            return null;
        }
        if (cell.getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
            if (colIndex == 1 || colIndex == 0)
                return String.format("%.0f", cell.getNumericCellValue());
            return String.format("%.2f", cell.getNumericCellValue());
        }
        return cell.toString().trim();

    }

    public void sendMailAsynchronous(final String[] tos, final String subject, final String content) {
        threadPoolExecutor.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    sendMail(tos, subject, content);
                } catch (Exception e) {
                    logger.error("发送邮件异常 tos[{}] subject[{}] content[{}]", tos, subject, content, e);
                }
            }
        });
    }

    public void sendMail(String[] tos, String subject, String content) {
        SimpleMailMessage mailMessage = new SimpleMailMessage();
        mailMessage.setFrom(javaMailSender.getUsername());
        mailMessage.setTo(tos);
        mailMessage.setText(content);
        mailMessage.setSubject(subject);
        javaMailSender.send(mailMessage);
    }

    @Override
    public Map getLakalaTradeParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        return tradeConfigService.getLakalaTradeParams(merchantId);
    }


    @Override
    public void updateLakalaTradeParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        Map lakalaTradeParams = (Map) BeanUtil.getProperty(request, "lakala_trade_params");
        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        tradeConfigService.updateLakalaTradeParams(merchantId, lakalaTradeParams);
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));
        Map merchant = getMerchant(CollectionUtil.hashMap(
                TransactionParam.MERCHANT_ID, merchantId
        ));
        String merchantSn = MapUtils.getString(merchant, Merchant.SN);
        lakalaService.createWanmaTask(merchantSn);

        Map lakalaTradeParam = tradeConfigService.getLakalaTradeParams(merchantId);
        providerTradeParamsService.saveProviderTradeParams(CollectionUtil.hashMap(
                "merchant_sn", merchantSn,
                "channel_no", "WSJG",
                "tenant_sn", "1",
                "contract_status", 1,
                "provider_merchant_id", MapUtils.getString(lakalaTradeParam, "lakala_merc_id"),
                "pay_conf_add_status", 1,
                "alipay_merchant_id", "1",
                "weixin_merchant_id", "1",
                "weixin_subdev_config_status", 1
                )
        );
    }

    @Override
    public void updateBankAccountVerifyStatus(Map request) {
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        int verify_status = BeanUtil.getPropInt(request, "verify_status", -1);
        if (StringUtil.empty(merchantId) || verify_status < 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "merchant_id,verify_status不可为空!");
        }
        Map merchantBankAccountInfoOld = merchantService.getMerchantBankAccountByMerchantId(merchantId);

        merchantService.stateMerchantBankAccount(merchantId, verify_status);
        //通知风控系统
        noticeRiskBankAccountVerifyStatus(merchantId, verify_status, (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME));
        Map merchantBankAccountInfoNew = merchantService.getMerchantBankAccountByMerchantId(merchantId);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantBankAccountInfoOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantBankAccountInfoNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_bank_account",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));

    }

    /**
     * 人工通过异步通知风控系统三要素审核通过
     *
     * @param merchantId
     * @param status
     */
    private void noticeRiskBankAccountVerifyStatus(final String merchantId, final int status, final String operator) {
        threadPoolExecutor.submit(new Runnable() {
            @Override
            public void run() {
                String sn = "";
                try {
                    Map merchant = merchantService.getMerchant(merchantId);
                    sn = BeanUtil.getPropString(merchant, Merchant.SN);
                    if (status == MerchantBankAccount.VERIFY_STATUS_SUCC) {
                        logger.info("人工通过异步通知风控系统三要素审核通过info merchant_id[{}], merchant_sn[{}], status[{}], operator[{}]", merchantId, sn, status, operator);
                        stockMerchantService.receiveBankAccountVerifyStatus(CollectionUtil.hashMap(
                                "merchantSn", sn,
                                "verifyStatus", status,
                                "source", "osp"
                        ));
                    }
                } catch (Exception e) {
                    logger.error("人工通过异步通知风控系统三要素审核通过error merchant_id[{}], merchant_sn[{}], status[{}]", merchantId, sn, status, e);
                }
            }
        });
    }

    @Override
    public Map getWftTradeParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        if (StringUtil.empty(merchantId)) {
            return null;
        }
        Map params = tradeConfigService.getWftTradeParams(merchantId);
        if (params != null) {
            String pMchId = BeanUtil.getPropString(params, TransactionParam.CITICBANK_GROUP_NO);
            if ("**********".equals(pMchId)) {
                params.put("citicbank_group_name", "上海喔噻");
            } else if ("**********".equals(pMchId)) {
                params.put("citicbank_group_name", "湖南欧耶");
            }
        }
        return params;
    }


    @Override
    public void rebindMerchantBankAccount(final Map request) {
        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        String username = BeanUtil.getPropString(user, Account.USERNAME);
        try {
            //增加修改渠道
            if (request != null) {
                request.put("change_way", "SP");
            }

            final String merchant_id = BeanUtil.getPropString(request, "merchant_id");
            final String remark = BusinessLogUtil.getHeaderParamsRemark(HttpRequestUtil.getBusinessogRequest());
            final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);

            final Map merchantBankAccountInfoOld = merchantService.getMerchantBankAccountByMerchantId(merchant_id);

            merchantService.rebindMerchantBankAccount(request);

            //证件状态默认通过
            try {
                int holderIdStatus = BeanUtil.getPropInt(merchantBankAccountInfoOld, MerchantBankAccount.HOLDER_ID_STATUS, -1);
                int holderIdFrontStatus = BeanUtil.getPropInt(merchantBankAccountInfoOld, MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, -1);
                int holderIdBackStatus = BeanUtil.getPropInt(merchantBankAccountInfoOld, MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, -1);
                if (holderIdStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS || holderIdFrontStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS || holderIdBackStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS) {
                    merchantService.updateBankAccountEdgeInfo(CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchant_id,
                            MerchantBankAccount.HOLDER_ID_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                            MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                            MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                            "change_way", "sp",
                            "operator", operator,
                            "remark", "运营修改结算信息 证件状态自动通过"
                    ));
                }
                int type = BeanUtil.getPropInt(merchantBankAccountInfoOld, MerchantBankAccount.TYPE, -1);
                int changeType = BeanUtil.getPropInt(request, MerchantBankAccount.TYPE, -1);
                if (changeType != -1) {
                    type = changeType;
                }
                //对公营业执照自动通过
                if (type == 2) {
                    merchantAuditService.noticeStatusChange(CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchant_id,
                            MerchantAudit.BUSINESS_LICENSE_STATUS, MerchantAudit.STATUS_PASSED,
                            MerchantAudit.AUDITOR, operator,
                            MerchantAudit.AUDIT_PLATFORM, "sp",
                            MerchantAudit.REMARK, "运营修改结算信息 证件状态自动通过"
                    ));
                }
            } catch (Exception e) {
                logger.error("修改商户结算信息，证件照默认通过 request[{}]", request, e);
            }

            final Map merchantBankAccountInfoNew = merchantService.getMerchantBankAccountByMerchantId(merchant_id);

            businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                    BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantBankAccountInfoOld,
                    BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantBankAccountInfoNew,
                    BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                    BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                            BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                            BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_bank_account",
                            BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                    )
            ));


            //通知风控银行类型变更
            if (BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.TYPE, -2) != BeanUtil.getPropInt(merchantBankAccountInfoOld, MerchantBankAccount.TYPE, -2)) {
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        notifyChangeAccountType(merchant_id, BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.TYPE), remark, operator, merchantBankAccountInfoNew);
                    }
                });
            } else {
                //通知风控异名换卡
                //持卡人姓名或身份证号
                if (!BeanUtil.getPropString(merchantBankAccountInfoNew, MerchantBankAccount.HOLDER, "").equals(BeanUtil.getPropString(merchantBankAccountInfoOld, MerchantBankAccount.HOLDER, ""))
                        || !BeanUtil.getPropString(merchantBankAccountInfoNew, MerchantBankAccount.IDENTITY, "").equals(BeanUtil.getPropString(merchantBankAccountInfoOld, MerchantBankAccount.IDENTITY, ""))
                ) {
                    threadPoolExecutor.submit(new Runnable() {
                        @Override
                        public void run() {
                            notifyChangeBankNoWithDifferName(merchant_id, remark, operator, merchantBankAccountInfoNew);
                        }
                    });
                }
            }

            //通知商户服务
            threadPoolExecutor.submit(new Runnable() {
                @Override
                public void run() {
                    notifyMerchantServiceSyncBankInfo(merchant_id);
                }
            });

            logger.info("sp rebindMercahntAccount success operator[{}],param[{}]", username, request);
        } catch (Exception e) {
            if (e instanceof CoreMerchantBankAccountNoKeyInfoChangedException && ((CoreMerchantBankAccountNoKeyInfoChangedException) e).getCode() == CoreException.CODE_MERCHNAT_BANKACCOUNT_NO_KEY_INFO_CHANGED) {
                logger.info("sp rebindMercahntAccount noneedUpdate operator[{}],param[{}]", username, request);
                return;
            }
            logger.info("sp rebindMercahntAccount exception operator[{}],param[{}],exception", username, request, e);
            throw e;
        }
    }


    @Override
    public void updateBankAccountEdgeInfo(Map request) {

        String merchant_id = BeanUtil.getPropString(request, "merchant_id");
        Map merchantBankAccountInfoOld = merchantService.getMerchantBankAccountByMerchantId(merchant_id);
        merchantService.updateBankAccountEdgeInfo(request);
        Map merchantBankAccountInfoNew = merchantService.getMerchantBankAccountByMerchantId(merchant_id);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantBankAccountInfoOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantBankAccountInfoNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_bank_account",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));
    }


    @Override
    public void updateMerchantBankAccountInfo(final Map request) {
        Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
        String username = BeanUtil.getPropString(user, Account.USERNAME);

        final String merchant_id = BeanUtil.getPropString(request, "merchant_id");
        final String remark = BusinessLogUtil.getHeaderParamsRemark(HttpRequestUtil.getBusinessogRequest());
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        Map merchantBankAccountInfoOld = merchantService.getMerchantBankAccountByMerchantId(merchant_id);

        merchantService.updateMerchantBankAccountInfo(request);
        //证件状态默认通过
        autoPassCertiStatus(request, merchantBankAccountInfoOld, merchant_id, operator);

        final Map merchantBankAccountInfoNew = merchantService.getMerchantBankAccountByMerchantId(merchant_id);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantBankAccountInfoOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantBankAccountInfoNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_bank_account",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));

        //通知风控银行类型变更
        if (BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.TYPE, -2) != BeanUtil.getPropInt(merchantBankAccountInfoOld, MerchantBankAccount.TYPE, -2)) {
            threadPoolExecutor.submit(new Runnable() {
                @Override
                public void run() {
                    notifyChangeAccountType(merchant_id, BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.TYPE), remark, operator, merchantBankAccountInfoNew);
                }
            });
        } else {
            //通知风控异名换卡
            //持卡人姓名或身份证号
            if (!BeanUtil.getPropString(merchantBankAccountInfoNew, MerchantBankAccount.HOLDER, "").equals(BeanUtil.getPropString(merchantBankAccountInfoOld, MerchantBankAccount.HOLDER, ""))
                    || !BeanUtil.getPropString(merchantBankAccountInfoNew, MerchantBankAccount.IDENTITY, "").equals(BeanUtil.getPropString(merchantBankAccountInfoOld, MerchantBankAccount.IDENTITY, ""))
            ) {
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        notifyChangeBankNoWithDifferName(merchant_id, remark, operator, merchantBankAccountInfoNew);
                    }
                });
            }
        }

        //通知商户服务
        threadPoolExecutor.submit(new Runnable() {
            @Override
            public void run() {
                notifyMerchantServiceSyncBankInfo(merchant_id);
            }
        });

        //证件状态默认通过
        threadPoolExecutor.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    int holderIdStatus = BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.HOLDER_ID_STATUS, -1);
                    int holderIdFrontStatus = BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, -1);
                    int holderIdBackStatus = BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, -1);
                    if (holderIdStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS || holderIdFrontStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS || holderIdBackStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS) {
                        updateBankAccountEdgeInfo(CollectionUtil.hashMap(
                                ConstantUtil.KEY_MERCHANT_ID, merchant_id,
                                MerchantBankAccount.HOLDER_ID_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                                MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                                MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                                "change_way", "sp",
                                "operator", operator,
                                "remark", "运营修改结算信息 证件状态自动通过"
                        ));
                    }
                    //对公营业执照自动通过
                    int type = BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.TYPE, -1);
                    if (type == 2) {
                        merchantAuditService.noticeStatusChange(CollectionUtil.hashMap(
                                ConstantUtil.KEY_MERCHANT_ID, merchant_id,
                                MerchantAudit.BUSINESS_LICENSE_STATUS, MerchantAudit.STATUS_PASSED,
                                MerchantAudit.AUDITOR, operator,
                                MerchantAudit.AUDIT_PLATFORM, "sp",
                                MerchantAudit.REMARK, "运营修改结算信息 证件状态自动通过"
                        ));
                    }
                } catch (Exception e) {
                    logger.error("修改商户结算信息，证件照默认通过 request[{}]", request, e);
                }
            }
        });

        logger.info("sp updateMerchantBankAccountInfo success operator[{}],param[{}]", username, request);
    }


    @Override
    public Map allowChangeMerchantBankAccount(Map request) {
        if (StringUtil.empty(BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID))) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "商户ID不能为空");
        }
        Map bankAccountInfo = extractBankAccountInfo(request);
        if (bankAccountInfo.size() == 1) {
            return CollectionUtil.hashMap(
                    "allow", "true",
                    "code", "11000",
                    "message", "ok"
            );
        }
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        request.put("platform", "SP");
        request.put("operator", operator);
        Map allowResult = merchantBankAccountService.allowChangeMerchantBankAccount(bankAccountInfo, request);
        boolean allow = BeanUtil.getPropBoolean(allowResult, "allow", true);
        String code = BeanUtil.getPropString(allowResult, "code", "");
        long financeAmount = BeanUtil.getPropLong(allowResult, "amount");
        List<Integer> changeTypes = (List<Integer>) BeanUtil.getProperty(allowResult, "changeTypes");
        changeTypes = changeTypes == null ? new ArrayList<Integer>() : changeTypes;
        //理财有余额的情况
        if (code.equals("11005") || code.equals("11006") || code.equals("11012") || financeAmount > 0) {
            allowResult.put("message", "理财账户余额不为0，请联系商户在收钱吧APP将理财余额转出成功后再变更银行卡");
        }
        //理财异名换卡需要人脸识别 文案转换
        else if (code.equals("11017") && (request.containsKey("identity") || request.containsKey("type") || request.containsKey("holder"))) {
            allowResult.put("message", "请先确保把收款理财余额提完并到账，以及没有待到账收益，再发起换卡。");
        }
        //理财无余额且状态正常  并且存在异名或对公私转
        else if (allowResult.containsKey("balance") && (
                changeTypes.contains(MerchantConstant.BANK_CHANGE_TYPE_DIFF_NAME) || changeTypes.contains(MerchantConstant.BANK_CHANGE_TYPE_DIFF_TYPE)
        )) {
            allowResult.put("message", "商户开通了理财账户，是否确认异名换卡，变更后理财账户将自动关闭且不可开启");
            allowResult.put("code", "11002");
        }
        return allowResult;
    }

    @Override
    public void changeMerchantBankAccount(final Map request) {
        final String merchantId = BeanUtil.getPropString(request, "merchant_id");
        updateLegalPersonAndLicense(request);
        Map bankAccountInfo = extractBankAccountInfo(request);
        Map merchantBankAccountInfoOld = merchantService.getMerchantBankAccountByMerchantId(merchantId);

        if (bankAccountInfo.size() == 1) {
            return;
        }

        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        request.put("platform", "SP");
        request.put("operator", operator);


        //证件状态默认通过
        bankAccountInfo.put(MerchantBankAccount.HOLDER_ID_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS);
        bankAccountInfo.put(MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS);
        bankAccountInfo.put(MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS);
        bankAccountInfo.put("bank_card_status", 1);

        merchantBankAccountService.changeMerchantBankAccount(bankAccountInfo, request);

        /**
         * 关键敏感信息必须记录商户日期！！！  modify by yinzihe 2018-07-23
         */
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantBankAccountInfoOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, bankAccountInfo,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_bank_account",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));

        //通知商户服务
        threadPoolExecutor.submit(new Runnable() {
            @Override
            public void run() {
                notifyMerchantServiceSyncBankInfo(merchantId);
            }
        });

        //证件状态默认通过
        threadPoolExecutor.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    Map<String, Object> merchantBankAccountInfoNew = merchantService.getMerchantBankAccountByMerchantId(merchantId);
                    int holderIdStatus = BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.HOLDER_ID_STATUS, -1);
                    int holderIdFrontStatus = BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, -1);
                    int holderIdBackStatus = BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, -1);
                    if (holderIdStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS || holderIdFrontStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS || holderIdBackStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS) {
                        updateBankAccountEdgeInfo(CollectionUtil.hashMap(
                                ConstantUtil.KEY_MERCHANT_ID, merchantId,
                                MerchantBankAccount.HOLDER_ID_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                                MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                                MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                                "change_way", "sp",
                                "operator", operator,
                                "remark", "运营修改结算信息 证件状态自动通过"
                        ));
                    }
                    //对公营业执照自动通过
                    int type = BeanUtil.getPropInt(merchantBankAccountInfoNew, MerchantBankAccount.TYPE, -1);
                    if (type == 2) {
                        merchantAuditService.noticeStatusChange(CollectionUtil.hashMap(
                                ConstantUtil.KEY_MERCHANT_ID, merchantId,
                                MerchantAudit.BUSINESS_LICENSE_STATUS, MerchantAudit.STATUS_PASSED,
                                MerchantAudit.AUDITOR, operator,
                                MerchantAudit.AUDIT_PLATFORM, "sp",
                                MerchantAudit.REMARK, "运营修改结算信息 证件状态自动通过"
                        ));
                    }
                } catch (Exception e) {
                    logger.error("修改商户结算信息，证件照默认通过 request[{}]", request, e);
                }
            }
        });

        logger.info("sp updateMerchantBankAccountInfo success operator[{}],param[{}]", operator, request);
    }

    @Override
    public boolean updateBusinessLicenseInfo(Map businessLicense) {
        MerchantBusinessLicenseUpdateVo license = new MerchantBusinessLicenseUpdateVo()
                .setMerchant_id(BeanUtil.getPropString(businessLicense, "merchant_id"))
                .setPhoto(BeanUtil.getPropString(businessLicense, "photo"))
                .setAddress(BeanUtil.getPropString(businessLicense, "address"))
                .setName(BeanUtil.getPropString(businessLicense, "name"))
                .setValidity(BeanUtil.getPropString(businessLicense, "validity"))
                .setLetter_of_authorization(BeanUtil.getPropString(businessLicense, "letter_of_authorization"))
                .setLegal_person_id_card_back_photo(BeanUtil.getPropString(businessLicense, "legal_person_id_card_back_photo"))
                .setLegal_person_id_card_front_photo(BeanUtil.getPropString(businessLicense, "legal_person_id_card_front_photo"))
                .setId_validity(BeanUtil.getPropString(businessLicense, "id_validity"));
        if (businessLicense.get("type") != null) {
            license.setType((int) (businessLicense.get("type")));
        }
        bankBusinessLicenseService.updateBusinessLicenseByMerchantId(license);
        return true;
    }


    @Override
    public MerchantBusinessLicense getMerchantBusinessLicense(Map request) {

        return bankBusinessLicenseService.getBusinessLicenseByMerchantId(BeanUtil.getPropString(request, "merchant_id"));
    }


    @Override
    public boolean updateBankOtherMessage(Map merchantBankAccount) {
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        merchantBankAccount.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_SP);
        merchantBankAccount.put(Request.KEY_OPERATOR, operator);
        return bankService.updateBankOtherMessage(merchantBankAccount);
    }


    @Override
    public Map checkoutAllowChangeCard(Map params) {
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);

        params.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_SP);
        params.put(Request.KEY_OPERATOR, operator);
        return bankService.checkoutAllowChangeCard(params);
    }

    @Override
    public Map bindBusinessHolderMerchantBankAccount(Map merchantBankAccount){
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        merchantBankAccount.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_SP);
        merchantBankAccount.put(Request.KEY_OPERATOR, operator);
        return bankService.bindBusinessHolderMerchantBankAccount(merchantBankAccount);
    }

    @Override
    public Map bindPrivateHolderMerchantBankAccount(Map merchantBankAccount){
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        merchantBankAccount.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_SP);
        merchantBankAccount.put(Request.KEY_OPERATOR, operator);
        return bankService.bindPrivateHolderMerchantBankAccount(merchantBankAccount);
    }

    @Override
    public Map bindMerchantBankAccount(Map merchantBankAccount){
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        merchantBankAccount.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_SP);
        merchantBankAccount.put(Request.KEY_OPERATOR, operator);
        return bankService.bindMerchantBankAccount(merchantBankAccount);
    }

    @Override
    public Map replaceMerchantBankAccount(Map merchantBankAccountPre){
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        merchantBankAccountPre.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_SP);
        merchantBankAccountPre.put(Request.KEY_OPERATOR, operator);
        return bankService.replaceMerchantBankAccount(merchantBankAccountPre);
    }

    @Override
    public Map updateMerchantBankAccount(Map merchantBankAccountPre){
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        merchantBankAccountPre.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_SP);
        merchantBankAccountPre.put(Request.KEY_OPERATOR, operator);
        return bankService.updateMerchantBankAccount(merchantBankAccountPre);
    }

    @Override
    public Map getMerchantBankAccountPre(Map<String, Object> request) {
        String id = BeanUtil.getPropString(request, DaoConstants.ID);
        Map requestMap = CollectionUtil.hashMap(Request.MERCHANT_BANK_ACCOUNT_PRE_ID, id, Request.KEY_PLATFORM, Request.KEY_PLATFORM_SP);
        return bankService.getMerchantBankAccountPre(requestMap);
    }

    @Override
    public ListResult findMerchantBankAccounts(Map<String, Object> request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        String merchantId = BeanUtil.getPropString(request, MerchantBankAccount.MERCHANT_ID);
        if (StringUtil.empty(merchantId)) {
            throw new InvalidParameterException("merchant_id 不能为空");
        }
        request.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_SP);
        return bankService.findMerchantBankAccounts(pageInfo, request);
    }


    @Override
    public void deletedMerchantBankAccountPre(Map<String, Object> request) {
        final String operator = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        request.put(Request.KEY_PLATFORM, Request.KEY_PLATFORM_SP);
        request.put(Request.KEY_OPERATOR, operator);
        bankService.deletedMerchantBankAccountPre(request);
    }

    @Override
    public Map getMerchantBankAccountByMerchantId(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        return bankService.getMerchantBankAccountByMerchantId(merchantId);
    }

    private void autoPassCertiStatus(Map<String, Object> request, Map<String, Object> merchantBankAccountInfo, String merchantId, String operator) {
        try {
            int holderIdStatus = BeanUtil.getPropInt(merchantBankAccountInfo, MerchantBankAccount.HOLDER_ID_STATUS, -1);
            int holderIdFrontStatus = BeanUtil.getPropInt(merchantBankAccountInfo, MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, -1);
            int holderIdBackStatus = BeanUtil.getPropInt(merchantBankAccountInfo, MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, -1);
            if (holderIdStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS || holderIdFrontStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS || holderIdBackStatus != MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS) {
                merchantService.updateBankAccountEdgeInfo(CollectionUtil.hashMap(
                        ConstantUtil.KEY_MERCHANT_ID, merchantId,
                        MerchantBankAccount.HOLDER_ID_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                        MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                        MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS,
                        "change_way", "sp",
                        "operator", operator,
                        "remark", "运营修改结算信息 证件状态自动通过"
                ));
            }
            int type = BeanUtil.getPropInt(merchantBankAccountInfo, MerchantBankAccount.TYPE, -1);
            int changeType = BeanUtil.getPropInt(request, MerchantBankAccount.TYPE, -1);
            if (changeType != -1) {
                type = changeType;
            }
            //对公营业执照自动通过
            if (type == 2) {
                merchantAuditService.noticeStatusChange(CollectionUtil.hashMap(
                        ConstantUtil.KEY_MERCHANT_ID, merchantId,
                        MerchantAudit.BUSINESS_LICENSE_STATUS, MerchantAudit.STATUS_PASSED,
                        MerchantAudit.AUDITOR, operator,
                        MerchantAudit.AUDIT_PLATFORM, "sp",
                        MerchantAudit.REMARK, "运营修改结算信息 证件状态自动通过"
                ));
            }
        } catch (Exception e) {
            logger.error("修改商户结算信息，证件照默认通过 request[{}]", request, e);

        }
    }

    /**
     * 更新法人姓名和营业执照照片
     *
     * @param request
     * @return
     */
    private void updateLegalPersonAndLicense(Map request) {
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        Map merchant = merchantService.getMerchant(merchantId);
        if (MapUtils.isEmpty(merchant)) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "商户ID为" + merchantId + " 不存在");
        }
        Map updatePart = BeanUtil.getPart(request, Arrays.asList(Merchant.LEGAL_PERSON_NAME, Merchant.BUSINESS_LICENSE_PHOTO));
        if (updatePart.size() > 0) {
            updatePart.put(DaoConstants.ID, merchantId);
            updateMerchant(updatePart);
        }
    }

    @Override
    public Map<String, Object> allowChangeMerchantWithdrawMode(Map<String, Object> request) {
        Map<String, Object> baseRequest = CollectionUtil.hashMap(
                "merchant_id", BeanUtil.getPropString(request, "merchant_id"),
                "withdraw_mode", BeanUtil.getPropInt(request, "withdraw_mode")
        );

        Map<String, Object> extraRequest = CollectionUtil.hashMap(
                "platform", BeanUtil.getPropString(request, "platform", "SP"),
                "operator", (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME),
                "remark", BeanUtil.getPropString(request, "remark", "运营人员发起")
        );

        return iMerchantService.allowChangeMerchantWithdrawMode(baseRequest, extraRequest);
    }

    @Override
    public void updateMerchantWithdrawMode(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, "merchant_id");
        Map<String, Object> d1Config = new HashMap<>();
        if (request.containsKey(MerchantD1WithdrawConfig.INTELLIGENCE_SINGLE_MIN_LIMIT_WITHDRAW)) {
            Long intelligenceSingleMinLimit = BeanUtil.getPropLong(request, MerchantD1WithdrawConfig.INTELLIGENCE_SINGLE_MIN_LIMIT_WITHDRAW);
            if (intelligenceSingleMinLimit < 1 || intelligenceSingleMinLimit > 100000) {
                throw new InvalidParameterException("智能提现最小金额为0.01-1000");
            }
            d1Config.put(MerchantD1WithdrawConfig.INTELLIGENCE_SINGLE_MIN_LIMIT_WITHDRAW, intelligenceSingleMinLimit);
        }
        if (request.containsKey(MerchantD1WithdrawConfig.STANDARD_SINGLE_MIN_LIMIT_WITHDRAW)) {
            Long standardSingleMinLimit = BeanUtil.getPropLong(request, MerchantD1WithdrawConfig.STANDARD_SINGLE_MIN_LIMIT_WITHDRAW);
            if (standardSingleMinLimit < 10 || standardSingleMinLimit > 100000) {
                throw new InvalidParameterException("普通提现最小金额为0.1-1000");
            }
            d1Config.put(MerchantD1WithdrawConfig.STANDARD_SINGLE_MIN_LIMIT_WITHDRAW, standardSingleMinLimit);
        }
        if (!d1Config.isEmpty()) {
            d1Config.put("merchant_id", merchantId);
            withdrawConfigService.updateMerchantD1WithdrawConfig(d1Config);
        }
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        if (!BeanUtil.getPropString(merchant, "withdraw_mode").equals(BeanUtil.getPropString(request, "withdraw_mode"))) {
            Map<String, Object> baseRequest = CollectionUtil.hashMap(
                    "merchant_id", BeanUtil.getPropString(request, "merchant_id"),
                    "withdraw_mode", BeanUtil.getPropInt(request, "withdraw_mode")
            );
            Map<String, Object> extraRequest = CollectionUtil.hashMap(
                    "platform", BeanUtil.getPropString(request, "platform", "SP"),
                    "operator", (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME),
                    "remark", BeanUtil.getPropString(request, "remark", "运营人员发起")
            );
            iMerchantService.changeMerchantWithdrawMode(baseRequest, extraRequest);
        }
    }

    @Override
    public void updateMerchantIsLimitCreditCard(Map<String, Object> request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        Integer payWay = MapUtils.getIntValue(request, MerchantConfig.PAYWAY);
        String creditPay = MapUtils.getString(request, TransactionParam.ALLOW_CREDIT_PAY);

        Map oldConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        tradeConfigService.updateMerchantIsLimitCreditCard(merchantId, payWay, creditPay);
        Map newConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, oldConfig,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, newConfig,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));

    }

    @Override
    public boolean getCreateStoreLimit(Map<String, Object> request) {
        String merchantId = MapUtils.getString(request, KEY_MERCHANT_ID);
        Map whiteList = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(4, merchantId);
        return !CollectionUtils.isEmpty(whiteList);
    }

    @Override
    public void updateCreateStoreLimit(Map<String, Object> request) {
        String merchantId = MapUtils.getString(request, KEY_MERCHANT_ID);
        boolean isOpen = MapUtils.getBoolean(request, "is_open");
        Map whiteList = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(4, merchantId);
        if (isOpen && CollectionUtils.isEmpty(whiteList)) {
            specialAuthWhitelistService.createSpecialAuthWhitelist(CollectionUtil.hashMap(
                    SpecialAuthWhitelist.AUTH_TYPE, 4,
                    SpecialAuthWhitelist.OBJECT_ID, merchantId,
                    SpecialAuthWhitelist.OBJECT_TYPE, SpecialAuthWhitelist.OBJECT_TYPE_MERCHANT
            ));
        } else if (!isOpen && !CollectionUtils.isEmpty(whiteList)) {
            specialAuthWhitelistService.deleteSpecialAuthWhitelistByAuthTypeAndObjectId(4, merchantId);
        }
    }

    @Override
    public Map getMerchantWeixinChannelMessage(Map request) {
        String merchantId = BeanUtil.getPropString(request, KEY_MERCHANT_ID);
        return contractWeixinService.getMerchantChannelMessage(CollectionUtil.hashMap(MerchantConfig.MERCHANT_ID, merchantId));
    }

    @Override
    public Map getCustomAppidApplyByParams(Map request) {
        String merchantId = BeanUtil.getPropString(request, KEY_MERCHANT_ID);
        return contractWeixinService.getCustomAppidApplyByParams(
                CollectionUtil.hashMap(
                        MerchantConfig.MERCHANT_ID, merchantId,
                        CustomAppidApply.TYPE, 1
                ));
    }

    @Override
    public Map updateMerchantNoWeixinAppid(Map request) {
        String merchantId = BeanUtil.getPropString(request, KEY_MERCHANT_ID);
        String username = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        return contractWeixinService.contractWechantAddDevConfig(
                CollectionUtil.hashMap(
                        MerchantConfig.MERCHANT_ID, merchantId,
                        CustomAppidApply.TYPE, 1,
                        CustomAppidApply.USERNAME, username,
                        CustomAppidApply.REQUEST_REASON, BeanUtil.getPropString(request, CustomAppidApply.REQUEST_REASON)
                ));
    }

    @Override
    public Map rollbackMerchantWeixinConfig(Map request) {
        String merchantId = BeanUtil.getPropString(request, KEY_MERCHANT_ID);
        return contractWeixinService.rollbackMerchantWeixinConfig(
                CollectionUtil.hashMap(
                        MerchantConfig.MERCHANT_ID, merchantId
                )
        );
    }

    @Override
    public Map validMerchantName(Map request) {
        boolean validFlag = config.getBooleanProperty(VALID_MERCHANTNAME, true);
        //默认通过校验
        Map result = CollectionUtil.hashMap(RESULT_CODE, 0, IS_NAME, 0, IS_BUSINESS_NAME, 0, MESSAGE, "");
        //调用结果
        if (validFlag) {
            try {
                String name = MapUtils.getString(request, Merchant.NAME);
                String businessName = MapUtils.getString(request, Merchant.BUSINESS_NAME);
                Map nameResp = supportServiceMerchant.getSensitiveMerchantNameFromLkl(CollectionUtil.hashMap(TYPE, SP, Merchant.NAME, name, Merchant.BUSINESS_NAME, name));
                Map businessResp = supportServiceMerchant.getSensitiveMerchantNameFromLkl(CollectionUtil.hashMap(TYPE, SP, Merchant.NAME, businessName, Merchant.BUSINESS_NAME, businessName));
                if (BLACKLIST.equals(MapUtils.getString(nameResp, RESULT_CODE)) || BLACKLIST.equals(MapUtils.getString(businessResp, RESULT_CODE))) {
                    result.put(RESULT_CODE, 1);
                    result.put(BLACKLIST.equals(MapUtils.getString(nameResp, RESULT_CODE)) ? IS_NAME : IS_BUSINESS_NAME, 1);
                    result.put(MESSAGE,
                            BLACKLIST.equals(MapUtils.getString(nameResp, RESULT_CODE)) ? MapUtils.getString(nameResp, MESSAGE) : MapUtils
                                    .getString(businessResp, MESSAGE));
                } else if (GRAYLIST.equals(MapUtils.getString(nameResp, RESULT_CODE)) || GRAYLIST.equals(MapUtils.getString(businessResp, RESULT_CODE))) {
                    result.put(RESULT_CODE, 2);
                    result.put(GRAYLIST.equals(MapUtils.getString(nameResp, RESULT_CODE)) ? IS_NAME : IS_BUSINESS_NAME, 1);
                    result.put(MESSAGE,
                            GRAYLIST.equals(MapUtils.getString(nameResp, RESULT_CODE)) ? MapUtils.getString(nameResp, MESSAGE) : MapUtils
                                    .getString(businessResp, MESSAGE));
                }
            } catch (Exception e) {
                logger.error("getSensitiveMerchantNameFromLkl is error:{}", e);
            }
        }
        return result;
    }

    @Override
    public Map validMerchantBlacklist(Map request) {
        boolean validFlag = config.getBooleanProperty(VALID_MERCHANTBLACKLIST, true);
        //默认通过校验
        Map result = CollectionUtil.hashMap(RESULT_CODE, 1, MESSAGE, "");
        if (validFlag) {
            try {
                String id = StringUtils.isBlank(MapUtils.getString(request, "identity")) ? MapUtils
                        .getString(request, "legal_person_id_number") : MapUtils.getString(request, "identity");
                request.put("legal_person_id_number", id);
                request.put("identity", id);
                request.put(TYPE, SP);
                request.put(LEGAL_PERSON_REGISTER_NO, MapUtils.getString(request, TAX_PAYER_ID));
                Map response = supportServiceMerchant.getBlackMerchant(request);
                // 在黑名单中
                if ("0".equals(MapUtils.getString(response, RESULT_CODE))) {
                    result.put(RESULT_CODE, 0);
                    Map lklResp = MapUtils.getMap(response, LKL_MESSAGE);
                    Map alipayResp = MapUtils.getMap(response, ALIPAY_MESSAGE);
                    result.put(MESSAGE, MapUtils.getString("0".equals(MapUtils.getString(lklResp, RESULT_CODE)) ? lklResp : alipayResp, MESSAGE));
                }
            } catch (Exception e) {
                logger.error("getBlackMerchant is error:{}", e);
            }
        }
        return result;
    }


    /**
     * @param request
     * @return
     */
    private Map extractBankAccountInfo(Map request) {
        Map bankAccountInfo = new HashMap();
        for (String field : BANK_ACCOUNT_FIELDS) {
            String fieldValue = BeanUtil.getPropString(request, field);
            if (!StringUtil.empty(fieldValue)) {
                bankAccountInfo.put(field, fieldValue);
            }
        }
        MapUtil.removeKeys(request, BANK_ACCOUNT_FIELDS);
        return bankAccountInfo;
    }


    /**
     * 通知风控银行类型变更
     *
     * @param merchantId
     * @param accountType
     * @param remark
     * @param operator
     */
    private void notifyChangeAccountType(String merchantId, int accountType, String remark, String operator, Map merchantBankAccountInfoNew) {
        int type = accountType == 1 ? 0 : 1;
        try {
//            riskActivityService.notifyChangeAccountType(merchantId, RiskSubjectTypeEnum.MERCHANT.getCode(), type, CollectionUtil.hashMap(
//                    Extra.PLATFORM, "sp",
//                    Extra.REMARK, remark,
//                    Extra.OPEATROR, operator
//            ));
            // 改为先保存，后续通过读core-business银行卡变更队列消息比对保存的数据进行notify
            merchantBankAccountInfoNew.put("change_type", type);
            saveRiskSpecialOperation(merchantId, remark, operator, RiskSpecialOperationBean.OPERATION_TYPE_CHANGE_ACCOUNT_TYPE, merchantBankAccountInfoNew);
        } catch (Exception e) {
            logger.error("通知风控系统银行卡账户类型异常merchantId[{}], accountType[{}], remark[{}], operator[{}], type[{}]", merchantId, accountType, remark, operator, type, e);
        }
    }

    /**
     * 通知风控异名换卡
     *
     * @param merchantId
     * @param remark
     * @param operator
     */
    private void notifyChangeBankNoWithDifferName(String merchantId, String remark, String operator, Map merchantBankAccountInfoNew) {
        try {
//            riskActivityService.notifyChangeBankNoWithDifferName(merchantId, RiskSubjectTypeEnum.MERCHANT.getCode(), CollectionUtil.hashMap(
//                    Extra.PLATFORM, "sp",
//                    Extra.REMARK, remark,
//                    Extra.OPEATROR, operator
//            ));
            // 改为先保存，后续通过读core-business银行卡变更队列消息比对保存的数据进行notify
            saveRiskSpecialOperation(merchantId, remark, operator, RiskSpecialOperationBean.OPERATION_TYPE_CHANGE_BANK_NO_WITH_DIFFER_NAME, merchantBankAccountInfoNew);
        } catch (Exception e) {
            logger.error("通知风控系统银行卡异名换卡异常merchantId[{}], remark[{}], operator[{}]", merchantId, remark, operator, e);
        }
    }

    private void saveRiskSpecialOperation(String merchantId, String remark, String operator, int specialOperationType, Map content) {
        riskSpecialOperationService.saveSpecialOperation(CollectionUtil.hashMap(
                RiskSpecialOperationBean.OPERATION_TYPE, specialOperationType,
                RiskSpecialOperationBean.SUBJECT_ID, null,
                RiskSpecialOperationBean.SUBJECT_BIZ_ID, merchantId,
                RiskSpecialOperationBean.SUBJECT_BIZ_TYPE, RiskSubjectTypeEnum.MERCHANT.getCode(),
                RiskSpecialOperationBean.OPERATION_RULE, null,
                RiskSpecialOperationBean.OPERATION_CONTENT, JacksonUtil.toJsonString(content),
                RiskSpecialOperationBean.REMARK, remark,
                RiskSpecialOperationBean.STATUS, RiskSpecialOperationBean.STATUS_TO_DEAL,
                RiskSpecialOperationBean.APPLY_PLATFORM, RiskSpecialOperationBean.APPLY_PLATFORM_SP,
                RiskSpecialOperationBean.APPLY_USER, operator
        ));
    }

    /**
     * 通知商户服务银行卡变动
     *
     * @param merchantId
     */
    private void notifyMerchantServiceSyncBankInfo(String merchantId) {
        try {
            stockMerchantAuditService.syncBankInfoFromDataSourceNotExistAndCreate(merchantId);
        } catch (Exception e) {
            logger.error("通知商户服务银行卡变动 merchantId[{}]", merchantId, e);
        }
    }

    @Override
    public Map updateMerchantHistoryTradeRefundFlag(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);

        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        tradeConfigService.updateMerchantHistoryTradeRefundFlag(merchantId, BeanUtil.getPropInt(request, Merchant.STATUS));
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);

        // 设置修改状态
        if (null != merchantConfigOld) {
            int historyFlag = BeanUtil.getPropInt(merchantConfigOld, String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG));
            BeanUtil.setNestedProperty(merchantConfigOld,
                    String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG),
                    historyFlag == TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_OPEN ? "同支付源" : "默认");
        }

        BeanUtil.setNestedProperty(merchantConfigNew,
                String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG),
                BeanUtil.getPropInt(request, Merchant.STATUS) == TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_OPEN ? "同支付源" : "默认");

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000014",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));


        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
        return null;
    }

    @Override
    public Map updateMerchantDeposit(Map request) {
        String merchantId = BeanUtil.getPropString(request, ConstantUtil.KEY_MERCHANT_ID);
        Map deposit = (Map) request.get(TransactionParam.DEPOSIT);
        Map merchantConfigOld = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (null != merchantConfigOld && null != merchantConfigOld.get(MerchantConfig.PARAMS)) {
            Map<String, Object> oldDeposit = (Map<String, Object>) BeanUtil.getProperty(merchantConfigOld.get(MerchantConfig.PARAMS), TransactionParam.DEPOSIT);
            if (null != oldDeposit && !oldDeposit.isEmpty()) {
                for (String key : oldDeposit.keySet()) {
                    if (null != oldDeposit.get(key)) {
                        oldDeposit.put(key, BeanUtil.getPropInt(oldDeposit, key) == TransactionParam.DEPOSIT_OPEN ? "开启" : "关闭");
                    }
                }
            }
        }
        tradeConfigService.updateMerchantDeposit(merchantId, CollectionUtil.hashMap("weixin", BeanUtil.getPropInt(deposit, "weixin"), "alipay", BeanUtil.getPropInt(deposit, "alipay")));
        Map merchantConfigNew = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (null != merchantConfigNew && null != merchantConfigNew.get(MerchantConfig.PARAMS)) {
            Map<String, Object> newDeposit = (Map<String, Object>) BeanUtil.getProperty(merchantConfigNew.get(MerchantConfig.PARAMS), TransactionParam.DEPOSIT);
            if (null != newDeposit && !newDeposit.isEmpty()) {
                for (String key : newDeposit.keySet()) {
                    if (null != newDeposit.get(key)) {
                        newDeposit.put(key, BeanUtil.getPropInt(newDeposit, key) == TransactionParam.DEPOSIT_OPEN ? "开启" : "关闭");
                    }
                }
            }
        }

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, merchantConfigOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, merchantConfigNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "merchant",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000014",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "merchant_config",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "merchant_id"
                )
        ));

        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, KEY_SN));
        return null;
    }

    @Override
    public Map getGiftCardStatus(Map request) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", tradeConfigService.getGiftCardStatus(BeanUtil.getPropString(request, TransactionParam.MERCHANT_ID)));
        return result;
    }

    @Override
    public void updateGiftCardStatus(Map request) {
        String merchantId = BeanUtil.getPropString(request, TransactionParam.MERCHANT_ID);
        boolean status = BeanUtil.getPropBoolean(request, "status");
        tradeConfigService.updateGiftCardParams(merchantId, status);
    }

    @Override
    public Map getAlipaySellerId(Map request) {
        String merchantId = BeanUtil.getPropString(request, MerchantConfigCustom.MERCHANT_ID);
        if (StringUtils.isBlank(merchantId)) {
            throw new UpayException(CODE_INVALID_PARAMETER, "商户id不能为空");
        }

        Map result = tradeConfigService.getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, null
                , MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID);
        return CollectionUtil.hashMap(
                "seller_id", BeanUtil.getPropString(result, MerchantConfigCustom.B2C_VALUE)
        );
    }

    @Override
    public Map updateAlipaySellerId(Map request) {
        String merchantId = BeanUtil.getPropString(request, MerchantConfigCustom.MERCHANT_ID);
        String sellerId = BeanUtil.getPropString(request, "seller_id");
        if (StringUtils.isBlank(merchantId)) {
            throw new UpayException(CODE_INVALID_PARAMETER, "商户id不能为空");
        }

        Map<String, Object> params = CollectionUtil.hashMap(
                MerchantConfigCustom.MERCHANT_ID, merchantId,
                CoreCommonConstants.MERCHANT_CONFIG_CUSTOM_VALUE, sellerId
        );
        Map result = tradeConfigService.updateAlipaySellerId(params);
        return CollectionUtil.hashMap(
                "seller_id", BeanUtil.getPropString(result, MerchantConfigCustom.B2C_VALUE)
        );
    }

    @Override
    public void openMerchantPay(Map request) {
        String merchantId = BeanUtil.getPropString(request, TransactionParam.MERCHANT_ID);
        switchService.openMerchantPay(merchantId);
        Map before = CollectionUtil.hashMap("merchant_trade_status", "关闭", "merchant_id", merchantId);
        Map after = CollectionUtil.hashMap("merchant_trade_status", "开启", "merchant_id", merchantId);
        recordLogUtil.handleLogAfterChange(merchantId, before, after);

    }

    @Override
    public void closeMerchantPay(Map request) {
        String merchantId = BeanUtil.getPropString(request, TransactionParam.MERCHANT_ID);
        switchService.closeMerchantPay(merchantId);
        Map before = CollectionUtil.hashMap("merchant_trade_status", "开启", "merchant_id", merchantId);
        Map after = CollectionUtil.hashMap("merchant_trade_status", "关闭", "merchant_id", merchantId);
        recordLogUtil.handleLogAfterChange(merchantId, before, after);


    }

    @Override
    public Integer queryMerchantPayStatus(Map request) {
        String merchantId = BeanUtil.getPropString(request, TransactionParam.MERCHANT_ID);
        return switchService.queryMerchantPayStatus(merchantId);
    }

}
