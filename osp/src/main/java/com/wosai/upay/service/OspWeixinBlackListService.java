package com.wosai.upay.service;

import com.wosai.upay.helper.CorePlatformsValidated;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@CorePlatformsValidated
public interface OspWeixinBlackListService {

    /**
     * 校验appid
     */
    Map isAppidInWeixinBlackList(Map request);

    /**
     * 校验license_name
     */
    Map isLicenseNameInWeixinBlackList(Map request);


    /**
     * 上传黑名单
     */
    Map<String, Object> uploadBlackListExcel(MultipartFile multipartFile);

    /**
     * 删除黑名单
     */
    Map deleteBlackList(Map request);

    /**
     * 分页查询黑名单
     */
    Map getBlackList(Map request);

}
