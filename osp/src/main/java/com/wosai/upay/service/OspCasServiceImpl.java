package com.wosai.upay.service;

import com.alibaba.fastjson.JSONObject;
import com.wosai.janus.api.cas.model.ValidateResponse;
import com.wosai.sdk.support.CommonRedisOps;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.restTemplateUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URI;

@Service
public class OspCasServiceImpl implements OspCasService {

    private final Logger logger = LoggerFactory.getLogger(OspCasServiceImpl.class);

    @Value("${osp.server.url}")
    private String ospServerUrl;
    @Value("${osp.client.url}")
    private String ospClientUrl;
    @Value("${cas.server.url}")
    private String casServerUrl;

    @Autowired
    private CommonRedisOps redisOps;

    @Override
    public void login(HttpServletResponse response) {
        try {
            response.sendRedirect(buildLoginUrl());
        } catch (IOException e) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "跳转登录失败");
        }
    }

    @Override
    public void logout(HttpServletRequest request, HttpServletResponse response) {
        String sessionId = HttpRequestUtil.getCookieValueByName(SESSIONID);
        logger.info("local logout: {}", sessionId);
        if (StringUtils.isNotBlank(sessionId)) {
            // 清掉session
            this.invalidate(sessionId);
            // 清掉cookie
            Cookie cookie = new Cookie(SESSIONID, sessionId);
            cookie.setMaxAge(0);
            cookie.setPath("/");
            response.addCookie(cookie);
        }
        // 通知cas退出登录
        try {
            response.sendRedirect(buildLogoutUrl());
        } catch (IOException e) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "退出登录失败");
        }
    }

    @Override
    public void validate(HttpServletRequest request, HttpServletResponse response, String ticket) {
        boolean needLogin = true;

        if (StringUtils.isNotBlank(ticket)) {
            RestTemplate restTemplate = restTemplateUtil.getRestTemplate();
            ResponseEntity<ValidateResponse> responseEntity = restTemplate
                    .getForEntity(UriComponentsBuilder
                            .fromUri(URI.create(casServerUrl))
                            .path("validate")
                            .queryParam("service", ospServerUrl)
                            .queryParam("ticket", ticket)
                            .build().toUri(), ValidateResponse.class);

            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                ValidateResponse.AuthenticationSuccess success = responseEntity.getBody()
                        .getServiceResponse()
                        .getAuthenticationSuccess();
                if (success != null) {
                    HttpSession session = request.getSession();
                    String sessionId = success.getAttributes().getUid();
                    String loginName = success.getAttributes().getLoginName();
                    String cellPhone = success.getUser();
                    logger.info("validate success: {}", sessionId);
                    // 账号信息保存到session中
                    session.setAttribute(CommonLoginService.SESSION_ACCOUNT_ID, sessionId);
                    session.setAttribute(CommonLoginService.SESSION_USERNAME, loginName);
                    session.setAttribute(CommonLoginService.SESSION_CELLPHONE, cellPhone);

                    // 登录信息保存到redis中
                    JSONObject userInfo = new JSONObject();
                    userInfo.put(CommonLoginService.SESSION_ACCOUNT_ID, sessionId);
                    userInfo.put(CommonLoginService.SESSION_USERNAME, loginName);
                    redisOps.set(sessionId, userInfo.toJSONString());

                    // session id写到前端cookie
                    Cookie cookie = new Cookie(SESSIONID, sessionId);
                    cookie.setMaxAge(1800);
                    cookie.setPath("/");
                    response.addCookie(cookie);

                    needLogin = false;
                }
            }
        }
        try {
            String url = ospClientUrl;
            if (StringUtils.isNotEmpty(HttpRequestUtil.getCookieValueByName(CommonLoginService.SUCCESS_URL))) {
                url = HttpRequestUtil.getCookieValueByName(CommonLoginService.SUCCESS_URL);
                //清掉cookie中保存的地址
                Cookie cookie = new Cookie(CommonLoginService.SUCCESS_URL, url);
                cookie.setMaxAge(0);
                cookie.setPath("/");
                response.addCookie(cookie);
            }
            response.sendRedirect(needLogin ? buildLoginUrl() : url);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void invalidate(String sessionId) {
        logger.info("remote logout: {}", sessionId);
        HttpRequestUtil.getSession().invalidate();
        redisOps.delete(sessionId);
    }

    @Override
    public void redirect(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String path = request.getParameter(CommonLoginService.SUCCESS_PATH);
        String url = request.getParameter(CommonLoginService.SUCCESS_URL);
        if (StringUtils.isEmpty(url)) {
            //sp采用的是虚拟路由，加上/#
            url = ospClientUrl + (StringUtils.isEmpty(path) ? "" : "/#" + path);
        }
        //已登录的直接跳转，未登录的记录下需要跳转的地址并跳转至登录页面
        String sessionId = HttpRequestUtil.getCookieValueByName(SESSIONID);
        if (StringUtils.isNotEmpty(sessionId) && redisOps.hasKey(sessionId)) {
            response.sendRedirect(url);
        } else {
            Cookie cookie = new Cookie(CommonLoginService.SUCCESS_URL, url);
            cookie.setMaxAge(1800);
            cookie.setPath("/");
            response.addCookie(cookie);
            response.sendRedirect(buildLoginUrl());
        }
    }

    private String buildLoginUrl() {
        return UriComponentsBuilder
                .fromUri(URI.create(casServerUrl))
                .path("login")
                .queryParam("service", ospServerUrl)
                // .queryParam("renew", true)
                .build().toUriString();
    }

    private String buildLogoutUrl() {
        return UriComponentsBuilder
                .fromUri(URI.create(casServerUrl))
                .path("logout")
                .queryParam("service", ospServerUrl)
                .build()
                .toUriString();
    }
}
