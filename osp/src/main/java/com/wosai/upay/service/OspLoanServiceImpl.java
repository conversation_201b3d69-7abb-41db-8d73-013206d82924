package com.wosai.upay.service;

import com.wosai.bsm.loanbackend.common.CommonConstants;
import com.wosai.bsm.loanbackend.model.ApplyRecord;
import com.wosai.bsm.loanbackend.model.MerchantInfo;
import com.wosai.bsm.loanbackend.model.UserInfo;
import com.wosai.bsm.loanbackend.service.*;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.exception.CommonInvalidParameterException;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.DateUtil;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.SheetUtil;
import com.wosai.upay.util.TaskApplyLogUtil;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpSession;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @author: wangchao
 * 2018/5/17 0017
 */
@Service
public class OspLoanServiceImpl implements OspLoanService {
    public static final int MAX_LOG_THREAD_COUNT = 2; //最多同时有多少个线程执行
    private static final String SESSION_SET_BATCH_FOR_LOAN_MERCHANT = "setBatchForLoanMerchant_task";
    private static Logger logger = LoggerFactory.getLogger(OspLoanServiceImpl.class);

    private static final String OSP_LOAN_BASE_DIR = "osp/loan/remain15day";

    private static final String ENDPOINT_URL = "https://statics.wosaimg.com";

    @Autowired
    private OspTaskService ospTaskService;
    @Autowired
    private OssFileUploader ossFileUploader;
    @Autowired
    private LoanService loanService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private MerchantInfoService merchantInfoService;
    @Autowired
    private ApplyRecordService applyRecordService;
    @Autowired
    private RefundRecordService refundRecordService;
    @Autowired
    private ProviderWhitelistService providerWhitelistService;

    ThreadPoolExecutor executor = (ThreadPoolExecutor) Executors.newFixedThreadPool(MAX_LOG_THREAD_COUNT);

    @Override
    public Map getUserLoanInfo(Map request) {
        String merchantId = (String) request.getOrDefault("merchantId", "");
        return loanService.getUserLoanInfo(merchantId);
    }

    @Override
    public Map isInProviderBlacklist(Map request) {
        String merchantId = (String) request.getOrDefault("merchantId", "");
        return loanService.isInProviderBlacklist(merchantId);
    }


    @Override
    @Deprecated
    public Map updateUserInfo(Map request) {
        String merchantId = BeanUtil.getPropString(request, "merchantId");
        //外面的注册参数检查没有用？
        if (merchantId == null || merchantId.isEmpty()) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "merchantId不能为空");
        }
        String userId = BeanUtil.getPropString(request, "userId");
        if (userId == null || userId.isEmpty()) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "userId不能为空");
        }

        boolean areAllEmpty = true;
        String cellphone = BeanUtil.getPropString(request, UserInfo.CELLPHONE);
        String id = BeanUtil.getPropString(request, UserInfo.IDENTITY);
        Map<String, Object> merchantInfoToChange = CollectionUtil.hashMap(
                DaoConstants.ID, merchantId
        );
        Map<String, Object> userInfoToChange = CollectionUtil.hashMap(
                DaoConstants.ID, userId
        );
        if (cellphone != null && !cellphone.isEmpty()) {
            areAllEmpty = false;
            merchantInfoToChange.put(MerchantInfo.CONTACT_CELLPHONE, BeanUtil.getPropString(request, UserInfo.CELLPHONE));
            userInfoToChange.put(UserInfo.CELLPHONE, BeanUtil.getPropString(request, UserInfo.CELLPHONE));
        }
        if (id != null && !id.isEmpty()) {
            areAllEmpty = false;
            merchantInfoToChange.put(MerchantInfo.IDENTITY, BeanUtil.getPropString(request, UserInfo.IDENTITY));
            userInfoToChange.put(UserInfo.IDENTITY, BeanUtil.getPropString(request, UserInfo.IDENTITY));
        }
        if (areAllEmpty) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "请至少提交一个更新项");
        }

        Map<String, Object> merchantInfo = merchantInfoService.updateMerchantInfo(merchantInfoToChange);
        Map<String, Object> userInfo = userInfoService.updateUserInfo(userInfoToChange);
        return CollectionUtil.hashMap(
                "merchantInfo", merchantInfo,
                "userInfo", userInfo
        );

    }

    @Override
    @Deprecated
    public Map<String, Object> changeOpen(Map request) {
        String merchatnId = (String) request.getOrDefault("merchantId", "");
        String open = (String) request.getOrDefault("open", "");

        HttpSession session = HttpRequestUtil.getSession();
        String operatorId = (String) session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        String remark = String.format("%s-sp", (String) session.getAttribute(CommonLoginService.SESSION_USERNAME));

        Map<String, Object> merchantInfo = merchantInfoService.getMerchantInfo(merchatnId);
        String provider = BeanUtil.getPropString(merchantInfo, MerchantInfo.PROVIDER);

        return openOrChange(merchatnId, provider, open, operatorId, remark);
    }

    @Override
    @Deprecated
    public Map<String, Object> changeProvider(Map request) {
        String merchatnId = (String) request.getOrDefault("merchantId", "");
        String providerChange = (String) request.getOrDefault("provider", "");
        HttpSession session = HttpRequestUtil.getSession();
        String operatorId = (String) session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        String remark = String.format("%s-sp", (String) session.getAttribute(CommonLoginService.SESSION_USERNAME));

        Map<String, Object> merchantInfo = merchantInfoService.getMerchantInfo(merchatnId);
        String provider = BeanUtil.getPropString(merchantInfo, MerchantInfo.PROVIDER);

        if (provider.equals(providerChange)) {
            return CollectionUtil.hashMap(
                    "success", false,
                    "message", "切换资金方与当前资金方一致"
            );
        }

        return openOrChange(merchatnId, providerChange, "change", operatorId, remark);
    }

    @Override
    public Map<String, Object> changeOpenOrProvider(Map request) {
        String merchantId = BeanUtil.getPropString(request, "merchantId", "");
        String operate =  BeanUtil.getPropString(request, "operate", "");
        String newProvider =  BeanUtil.getPropString(request, "provider", "");
        HttpSession session = HttpRequestUtil.getSession();
        String operatorId = (String) session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        String remark = String.format("%s-sp", (String) session.getAttribute(CommonLoginService.SESSION_USERNAME));

        if ("open".equals(operate) && StringUtil.empty(newProvider)) {
            throw new CommonInvalidParameterException("open时provider必传");
        }

        Map<String, Object> merchantInfo = merchantInfoService.getMerchantInfo(merchantId);
        String provider = BeanUtil.getPropString(merchantInfo, MerchantInfo.PROVIDER);
        // 如果是开放且新旧对接方不一致则是change而非open
        if ("open".equals(operate) && !StringUtil.empty(provider) && !StringUtil.empty(newProvider) && !provider.equals(newProvider)) {
            operate = "change";
        }

        return openOrChange(merchantId, newProvider, operate, operatorId, remark);
    }

    @Override
    public List<Map> findUserMultiMerchantInfos(Map request) {
        String merchantId = (String) request.getOrDefault("merchantId", "");
        Map<String, Object> merchantInfo = merchantInfoService.getMerchantInfo(merchantId);
        String loanUserId = BeanUtil.getPropString(merchantInfo, MerchantInfo.LOAN_USER_ID);
        if (StringUtil.empty(loanUserId)) {
            return new ArrayList<Map>();
        } else {
            return merchantInfoService.findMerchantInfos(new PageInfo(1, 100), CollectionUtil.hashMap(MerchantInfo.LOAN_USER_ID, loanUserId)).getRecords();
        }
    }

    private Map openOrChange(String merchantId, String provider, String operate, String operatorId, String remark) {
        Map operateMap = CollectionUtil.hashMap(
                "checkTransaction", true,
                "operator_channel", 1,
                "operator_id", operatorId,
                "remark", remark
        );
        // 当change或者open不为空才传new_provider
        if ("change".equals(operate) || "open".equals(operate)) {
            operateMap.put("new_provider", provider);
        }
        return providerWhitelistService.commonChangeProvider(merchantId, null, operate, operateMap);
    }


    @Override
    public Map getBatches(Map request) {
        HashMap<String, Object> result = new HashMap<>();
        result.put("batches", loanService.getBatches());
        return result;
    }

    @Override
    public Map setBatch(MultipartFile file, String batch) {
        if (batch == null || batch.isEmpty()) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "分组名不能为空");
        }
        if (file == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "必须上传文件");
        }
        List<String> merchants = new ArrayList<>();
        Scanner scanner = null;
        try {
            scanner = new Scanner(file.getInputStream());
            while (scanner.hasNext()) {
                String line = scanner.nextLine();
                if (line == null || line.isEmpty()) {
                    continue;
                }
                merchants.add(line);
                if (merchants.size() > 10000) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "数据超过1W条");
                }
            }
        } catch (IOException e) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件读取失败");
        } finally {
            if (scanner != null) {
                scanner.close();
            }
        }
        if (merchants.isEmpty()) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件解析失败");
        }

        //创建任务异步执行
        Map taskApplyLog = ospTaskService.createTask(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogUtil.TYPE_BATCH_SET_LOAN_BATCH
        ));

        final String taskId = BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID);
        final String operator = (String) HttpRequestUtil.getSession()
                .getAttribute(CommonLoginService.SESSION_USERNAME);
        //异步执行导入操作
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                try {
                    doSetBatch(batch, merchants, operator, taskId);
                } catch (Exception e) {
                    logger.error("setBatchMultiMerchant() error", e);
                    ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                            TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                            TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "导入过程中发生错误")));
                }
            }
        };
        try {
            executor.submit(runnable);
        } catch (Exception e) {
            logger.error("submit setBatchMultiMerchant() to threadPool error", e);
            ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                    TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                    TaskApplyLog.APPLY_RESULT, CollectionUtil.hashMap("msg", "提交导入异步任务发生错误")));
        }

        HttpRequestUtil.getSession().setAttribute(SESSION_SET_BATCH_FOR_LOAN_MERCHANT, taskId);
        return CollectionUtil.hashMap("taskId", taskId, "task_apply_log_id", taskId);
    }

    /**
     * 分页查询贷款申请记录.
     * <p>
     * pageInfo
     * queryFilter provider            贷款对接方code
     * loan_company_code   贷款公司/资金方code
     * user_id             贷款借款人id
     * apply_id            贷款申请ID
     * merchant_id         商户id，冗余
     * current_status      当前状态（收钱吧）
     * current_status_provider当前状态（对接方）
     * apply_balance       申请金额
     * apply_period        申请贷款周期
     * approve_balance     审批金额
     * approve_period      审批贷款周期
     * pay_rate            还款利率
     * rate_type           计息方式 1:先息后本 2:等额本息
     * refuse_reason       拒绝描述
     * apply_date          申请日期
     * approve_date        审批日期
     * lend_date           第一次放款日期
     * deleted             已删除：0否1是
     *
     * @return
     */
    @Override
    public Map findApplyRecords(Map request) {
        Map<String, Object> pageInfo = (Map<String, Object>) request.get("pageInfo");
        Map<String, Object> queryFilter = (Map<String, Object>) request.get("queryFilter");
        String userId = BeanUtil.getPropString(queryFilter, ApplyRecord.USER_ID);
        if (!StringUtil.empty(userId) && BeanUtil.getPropBoolean(queryFilter, "syncUnfinishedLoan")) {
            loanService.syncUnfinishedLoan(CollectionUtil.hashMap(ApplyRecord.USER_ID, userId));
        }
        ListResult listResult = applyRecordService.findApplyRecords(
                new PageInfo(BeanUtil.getPropInt(pageInfo, "page", 1),
                        BeanUtil.getPropInt(pageInfo, "pageSize", 10)),
                queryFilter);
        return CollectionUtil.hashMap(
                "total", listResult.getTotal(),
                "records", listResult.getRecords()
        );
    }

    /**
     * 分页查询贷款还款计划.
     * <p>
     * param pageInfo
     * param queryFilter provider            贷款对接方code
     * loan_company_code   贷款公司/资金方code
     * user_id             贷款借款人id
     * merchant_id         商户id，冗余
     * apply_id            贷款申请ID
     * lend_id             贷款放款ID
     * refund_id           贷款还款ID
     * refund_period       还款期数序号
     * pre_refund_date     预计还款日期
     * refund_date         真实还款日期
     * status              还款状态
     * overdue             当前是否逾期 0:否 1:是
     * once_overdue        本次还款是否曾经有过逾期 0:否 1:是
     * rate_type           计息方式 1:先息后本 2:等额本息
     * repay_type          还款方式 1:银行代扣 2:主动还款
     * refund_bank_name    还款银行卡
     * refund_bank_card    还款银行卡号
     * deleted             已删除：0否1是
     *
     * @return
     */
    @Override
    public Map findRefundRecords(Map request) {
        Map<String, Object> pageInfo = (Map<String, Object>) request.get("pageInfo");
        Map<String, Object> queryFilter = (Map<String, Object>) request.get("queryFilter");
        ListResult listResult = refundRecordService.findRefundRecordsAndSumOther(
                new PageInfo(BeanUtil.getPropInt(pageInfo, "page", 1),
                        BeanUtil.getPropInt(pageInfo, "pageSize", 10)),
                queryFilter);
        return CollectionUtil.hashMap(
                "total", listResult.getTotal(),
                "records", listResult.getRecords()
        );
    }

    /**
     * 贷款业务常量.
     *
     * @return
     */
    @Override
    public Map getBusinessConstants() {
        return loanService.getBusinessConstants();
    }

    @Override
    public Map batchChangeProvider(MultipartFile file, Map request) throws IOException {
        //清除15天前历史文件
        executor.submit(new Runnable() {
            @Override
            public void run() {
                ossFileUploader.deleteFilesDaysBefore(OSP_LOAN_BASE_DIR, 15);
            }
        });

        String remark = BeanUtil.getPropString(request, "remark");

        if (StringUtils.length(remark) > 128 || StringUtils.length(remark) == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "remark长度在0-128之间");
        }

        if (!file.getOriginalFilename().endsWith("txt")
                && !file.getOriginalFilename().endsWith("csv")) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件类型必须是.txt或.csv");
        }

        if (file.getSize() > 1024 * 1024 || file.getSize() == 0) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件大小必须在(0-1024k]");
        }

        HttpSession session = HttpRequestUtil.getSession();
        String operatorId = (String) session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        if (StringUtil.empty(operatorId)) {
            operatorId = "********-0000-0000-0000-************";
        }

        String userName = (String) session.getAttribute(CommonLoginService.SESSION_USERNAME);
        if (StringUtil.empty(userName)) {
            userName = "anonymous";
        }

        String ossKey = String.format("%s/cp_%s_%s", OSP_LOAN_BASE_DIR,
                DateUtil.formatDate(new Date(),DateUtil.FORMATTER_DATE_INT), file.getOriginalFilename());
        InputStream inputStream = file.getInputStream();
        ossFileUploader.uploadStaticsFile(ossKey, inputStream, file.getSize());

        String fileUrl = String.format("%s/%s", ENDPOINT_URL, ossKey);

        request.put(CommonConstants.BATCH_PROVIDER_CHNAGE_OPERATOR_ID, operatorId);
        request.put(CommonConstants.BATCH_PROVIDER_CHNAGE_OPERATOR, userName);
        request.put(CommonConstants.BATCH_PROVIDER_CHNAGE_FILE_URL, fileUrl);

        return providerWhitelistService.createBatchChangeProviderTask(request);
    }

    private void doSetBatch(String batch, List<String> merchants, String operator, String taskId) {
        int total = merchants.size();

        logger.debug("addBatch:" + batch);

        List<String> fail = new ArrayList<>();
        List<String> succeed = new ArrayList<>();
        for (int i = 0; i < merchants.size(); i++) {
            Map<String, Object> setResult = null;
            try {
                setResult = loanService.setMerchantBatch(merchants.get(i), batch);
            } catch (Exception e) {
                //后面取code为-1
                logger.warn("setMerchantBatch", e);
            }
            int code = BeanUtil.getPropInt(setResult, "code", -1);
            if (code == 0) {
                succeed.add(merchants.get(i));
            } else {
                fail.add(merchants.get(i));
            }
        }
        logger.info("设置贷款用户分组结果 总数[{}],非法详情[{}],成功详情[{}]", total, fail, succeed);
        String excelUrl = uploadSetBatchResult(fail, succeed);

        Map<String, Object> result = new HashedMap();
        //设置下载地址
        result.put("batch", batch);
        result.put("downloadResultUrl", excelUrl);
        result.put("导入总数", total);
        result.put("导入失败总数", fail.size());
        if (fail.size() > 20) {
            fail = fail.subList(0, 20);
            fail.add("过多不再显示..........");
        }
        result.put("导入失败详情", fail);
        result.put("导入成功总数", succeed.size());

        ospTaskService.updateTask(CollectionUtil.hashMap(com.wosai.data.dao.DaoConstants.ID, taskId,
                TaskApplyLog.APPLY_STATUS, merchants.size() > 0 ? TaskApplyLogUtil.APPLY_STATUS_EXCUTE_SUCCESS : TaskApplyLogUtil.APPLY_STATUS_EXCUTE_FAILURE,
                TaskApplyLog.APPLY_RESULT, result));
    }

    private String uploadSetBatchResult(List<String> fail, List<String> succeed) {
        try {
            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet failureSheet = workbook.createSheet("失败详情");
            List<String> headers = new ArrayList<String>() {{
                add("商户id");
            }};
            SheetUtil sheetUtil = new SheetUtil(failureSheet);
            sheetUtil.appendRow(headers);
            for (int i = 0; i < fail.size(); i++) {
                final String merchantId = fail.get(i);
                sheetUtil.appendRow(new ArrayList() {{
                    add(merchantId);
                }});
            }

            HSSFSheet successSheet = workbook.createSheet("成功详情");
            sheetUtil = new SheetUtil(successSheet);
            sheetUtil.appendRow(headers);
            for (int i = 0; i < succeed.size(); i++) {
                final String merchantId = succeed.get(i);
                sheetUtil.appendRow(new ArrayList() {{
                    add(merchantId);
                }});
            }
            return uploadStatementToOSS("xls", workbook);
        } catch (Exception e) {
            logger.warn("uploadSetBatchResult", e);
        }
        return "";
    }

    private String uploadStatementToOSS(String ext, HSSFWorkbook workbook) throws IOException {
        String filename = CrudUtil.randomUuid();
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        byte[] content = bos.toByteArray();
        bos.close();
        String fullName = "portal/statement/sp_import_result/" + filename + "." + ext;
        ByteArrayInputStream bais = new ByteArrayInputStream(content);
        ossFileUploader.uploadIfNotExists(OssFileUploader.IMAGE_BUCKET_NAME, fullName, bais, content.length);
        return fullName;
    }
}
