package com.wosai.upay.service;


import java.util.Map;

/**
 * Created by jian<PERSON> on 30/3/16.
 */
public interface OspUserLoginService {

    /**
     * 超级管理员/老板.
     */
    String ROLE_SUPER_ADMIN = "10";

    String ROLE_WITHDRAW_VERIFY = "3";

    String ROLE_WR = "2";

    //运营中心-客服部-客服管理
    String ROLE_CUSTOMER_MANAGE = "7";

    //收钱吧-通用角色-审批创建
    String ROLE_COMMON_APPROVAL_CREATE="71";

    //收钱吧-通用角色-审批处理 70
    String ROLE_COMMON_APPROVAL_HANDLE="70";

    //运营中心-运营部-系统管理员  31
    String ROLE_OPERATE_SYSTEM_ADMIN ="31";

    //运营中心-运营部-清结算专员  3
    String ROLE_OPERATE_LIQUIDATION_COMMISSIONER ="3";

    //运营中心-运营部-业务支持专员 32
    String ROLE_OPERATE_BUSINESS_SUPPORT_SPECIALIST ="32";

    //运营中心-风控部-风控专员  6
    String ROLE_RISK_MANAGEMEN_COMMISSIONER ="6";

    //运营中心-风控部-风控管理  35
    String ROLE_RISK_MANAGEMEN_MANAGEMENT ="35";

    //产研中心-产品部-支付业务 48
    String ROLE_PRODUCT_PAYMENT_BUSINESS ="48";

    /**
     * 返回用户基本信息
     * @return
     */
    Map getUserInfo(Map<String, Object> request);





}
