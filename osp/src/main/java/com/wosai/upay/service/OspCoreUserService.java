package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;

import java.util.List;
import java.util.Map;

/**
 * core-business 用户接口类.
 */
public interface OspCoreUserService {
    /**
     * 创建Account.
     *
     * @param request
     */
    Map createAccount(Map request);

    /**
     * 根据accountId删除Account.
     *
     * @param request account_id
     */
    void deleteAccount(Map request);

    /**
     * 修改Account. 不更新密码,不修改用户名
     *
     * @param request
     */
    Map updateAccount(Map request);

    /**
     * 修改商户超级管理员登录用户名.
     * 逻辑：
     * 1，根据新new_username查account，查不到的话新建；
     * 2，创建merchant_user，如果account_id关联过用户的话则会提示错误；
     * 3，删除原merchant_user；
     *
     * @param request merchant_user_id
     *                new_username
     *                new_password
     * @return
     */
    Map modifyMerchantSuperAdminUsername(Map request);

    /**
     * 修改密码
     *
     * @param request username
     *                password
     *                md5 密码是否已md5加密过 布尔型
     * @return
     */
    Map updateAccountPassword(Map request);

    /**
     * 根据accountId获取Account.
     *
     * @param request account_id
     * @return
     */
    Map getAccount(Map request);

    /**
     * 通过用户名获取账号信息
     *
     * @param request username
     * @return
     */
    Map getAccountByUsername(Map request);

    /**
     * 通过手机号码获取账号信息
     *
     * @param request cellphone
     * @return
     */
    Map getAccountByCellphone(Map request);

    /**
     * 创建VendorUser.
     *
     * @param request
     */
    Map createVendorUser(Map request);

    /**
     * 根据vendorUserId删除VendorUser.
     *
     * @param request vendor_user_id
     */
    void deleteVendorUser(Map request);

    /**
     * 修改VendorUser.
     *
     * @param request
     */
    Map updateVendorUser(Map request);

    /**
     * 根据vendorUserId获取VendorUser.
     *
     * @param request vendor_user_id
     * @return
     */
    Map getVendorUser(Map request);

    /**
     * 根据accountId获取VendorUser.
     *
     * @param request accountId
     * @return
     */
    Map getVendorUserByAccountId(Map request);


    /**
     * 根据vendorUserId禁用VendorUser.
     *
     * @param request vendor_user_id
     * @return
     */
    void disableVendorUser(Map request);

    /**
     * 分页查询VendorUser.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                vendor_id           商户id
     *                account_id          user表里面的id
     *                deleted
     * @return
     */
    ListResult findVendorUsers(Map request);


    /**
     * 创建MerchantUser.
     *
     * @param request
     */
    Map createMerchantUser(Map request);

    /**
     * 根据merchantUserId删除MerchantUser.
     *
     * @param request merchant_user_id
     */
    void deleteMerchantUser(Map request);

    /**
     * 修改MerchantUser.
     *
     * @param request
     */
    Map updateMerchantUser(Map request);

    /**
     * 根据merchantUserId获取MerchantUser.
     *
     * @param request merchant_user_id
     * @return
     */
    Map getMerchantUser(Map request);

    /**
     * 根据时间查商户的第一个注册用户.
     * @param request merchant_id
     * @return
     */
    Map getFirstMerchantUser(Map request);

    /**
     * 根据accountId获取MerchantUser.
     *
     * @param request account_id
     * @return
     */
    Map getMerchantUserByAccountId(Map request);

    /**
     * 获取用户的所有角色
     *
     * @param request merchant_user_id
     * @return
     */
    List getMerchantUserRoles(Map request);

    /**
     * 获取用户所有的权限
     *
     * @param request merchant_user_id
     * @return
     */
    List getMerchantUserPermissions(Map request);

    /**
     * 根据merchantUserId禁用MerchantUser.
     *
     * @param request merchant_user_id
     * @return
     */
    void disableMerchantUser(Map request);

    /**
     * 分页查询MerchantUser.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                merchant_id         商户id
     *                store_id            门店id
     *                account_id          user表里面的id
     *                deleted
     * @return
     */
    ListResult findMerchantUsers(Map request);

    /**
     * 创建SolicitorUser.
     *
     * @param request
     */
    Map createSolicitorUser(Map request);

    /**
     * 根据solicitorUserId删除SolicitorUser.
     *
     * @param request solicitor_user_id
     */
    void deleteSolicitorUser(Map request);

    /**
     * 修改SolicitorUser.
     *
     * @param request
     */
    Map updateSolicitorUser(Map request);

    /**
     * 根据solicitorUserId获取SolicitorUser.
     *
     * @param request solicitor_user_id
     * @return
     */
    Map getSolicitorUser(Map request);

    /**
     * 根据accountId获取SolicitorUser.
     *
     * @param request accountId
     * @return
     */
    Map getSolicitorUserByAccountId(Map request);

    /**
     * 根据solicitorUserId禁用SolicitorUser.
     *
     * @param request solicitor_user_id
     * @return
     */
    void disableSolicitorUser(Map request);

    /**
     * 分页查询SolicitorUser.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                solicitor_id        推广者id
     *                account_id          user表里面的id
     *                deleted
     * @return
     */
    ListResult findSolicitorUsers(Map request);

    /**
     * 删除老板账号
     * @param  request merchant_user_id
     *                 account_id
     *
     *
     */
    void deleteBoss(Map request);
}
