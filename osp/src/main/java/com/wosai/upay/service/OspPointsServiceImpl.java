package com.wosai.upay.service;

import com.wosai.app.backend.api.service.IAccountService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.opr.point.service.PointService;
import com.wosai.reward.entity.*;
import com.wosai.reward.entity.enums.Event;
import com.wosai.reward.entity.enums.TagGroup;
import com.wosai.reward.exception.RewardException;
import com.wosai.reward.service.*;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.SheetUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Created by xuyuanxiang on 2017/8/7.
 */
@Service
public class OspPointsServiceImpl implements OspPointsService {

    Logger logger = LoggerFactory.getLogger(OspPointsServiceImpl.class);

    @Autowired
    public OspPointsServiceImpl(
            AccountService accountService
            , PointsService pointsService
            , RuleService ruleService
            , HistoryService historyService
            , TagService tagService
            , IAccountService iAccountService
            , PointService pointService
    ) {
        this.accountService = accountService;
        this.pointsService = pointsService;
        this.ruleService = ruleService;
        this.historyService = historyService;
        this.tagService = tagService;
        this.iAccountService = iAccountService;
        this.pointService = pointService;
    }

    private final AccountService accountService;
    private final PointsService pointsService;
    private final RuleService ruleService;
    private final HistoryService historyService;
    private final TagService tagService;
    private final IAccountService iAccountService;
    private final PointService pointService;

    private final Pattern pattern = Pattern.compile("\\d{11}");
    public static final String OSP_ACCOUNT = "osp_account";
    public static final String CELLPHONES = "cellphones";
    public static final String MERCHANT_ID = "merchant_id";
    public static final String ID = "id";
    public static final String CELLPHONE = "cellphone";
    public static final String CODE = "code";
    public static final String MSG = "msg";
    public static final String DATA = "data";
    public static final String NAME = "name";
    public static final String CITY = "city";
    public static final String INDUSTRY = "industry";
    public static final String PAYWAY = "payway";
    public static final String ROLE = "role";
    public static final String DESCRIPTION = "description";
    public static final String POINTS = "points";
    public static final String POINTS_UPPER_LIMIT = "pointsUpperLimit";
    public static final String DELETED = "deleted";
    public static final String ENABLED = "enabled";
    public static final String EFFECTIVE_TIME = "effectiveTime";
    public static final String EXPIRED_TIME = "expiredTime";
    public static final String ACCEPT_EVENTS = "acceptEvents";
    public static final String ACCEPT_REFERENCE_TAGS = "acceptReferenceTags";
    public static final String REWARD_THRESHOLDS = "rewardThresholds";
    public static final String CONSTRAINTS = "constraints";
    public static final String REJECT_REFERENCES = "rejectReferences";
    public static final String ACCEPT_REFERENCES = "acceptReferences";
    public static final String START_REGISTER_TIME = "startRegisterTime";
    public static final String END_REGISTER_TIME = "endRegisterTime";
    public static final String VALUE = "value";
    public static final String REFERENCE_FIELD = "referenceField";
    public static final String START_TIMESTAMP = "startTimestamp";
    public static final String END_TIMESTAMP = "endTimestamp";
    public static final String KEYWORDS = "keywords";
    public static final String PAGE = "page";
    public static final String SIZE = "size";
    public static final String GROUP = "group";
    public static final String FAILED_RESULTS = "batchImportPointsAccountsFailedResults";
    public static final String FAILED_ADD_POINTS_RESULTS = "batchAddPointsAccountsFailedResults";
    public static final String ORIGIN_FILENAME = "originFilename";
    public static final String FAILURES = "failures";
    public static final String TOTAL = "total";


    @Override
    public Map batchImportAccounts(MultipartFile file, HttpServletRequest httpServletRequest) {
        if (file == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "必须上传文件");
        }
        String fileName = file.getOriginalFilename();
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex == -1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        String type = fileName.substring(lastIndex + 1, fileName.length()).toLowerCase();
        if ("xlsx".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "不支持xlsx格式，请使用xls");
        }
        if (!"xls".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        InputStream is = null;
        try {
            is = file.getInputStream();
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(is);
            List<String> cellphones = new ArrayList<>();
            // 循环Sheet
            for (int numSheet = 0; numSheet < hssfWorkbook.getNumberOfSheets(); numSheet++) {
                HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(numSheet);
                if (hssfSheet == null) {
                    continue;
                }
                // 循环行Row
                for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                    HSSFRow hssfRow = hssfSheet.getRow(rowNum);
                    if (hssfRow == null) {
                        continue;
                    }
                    HSSFCell hssfCell = hssfRow.getCell(0);
                    hssfCell.setCellType(Cell.CELL_TYPE_STRING);
                    String cellphone = hssfCell.toString();
                    if (StringUtils.isNotEmpty(cellphone)) {
                        cellphones.add(cellphone);
                    }
                }
            }
            if (!cellphones.isEmpty()) {
                Map<String, Object> request = new HashMap<>();
                request.put(CELLPHONES, cellphones);
                return this.batchCreateAccounts(request, httpServletRequest);
            }
        } catch (IOException e) {
            logger.error("", e);
            throw new UpayException(UpayException.CODE_IO_EXCEPTION, UpayException.getCodeDesc(UpayException.CODE_IO_EXCEPTION));
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    logger.error("", e);
                }
            }
        }
        return null;
    }

    private Map batchCreateAccounts(Map request, HttpServletRequest httpServletRequest) {
        List cellphones = get(request, CELLPHONES, List.class);
        if (cellphones == null || cellphones.isEmpty()) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "手机号不可为空");
        }
        final Map<String, Object> result = new HashMap<>();
        result.put(TOTAL, cellphones.size());
        final List<Map<String, Object>> failures = new ArrayList<>();
        final List<AccountRequest> accountRequests = new ArrayList<>();
        final Map<String, String> createdCellphoneMap = new HashMap<>();

        for (Object temp : cellphones) {
            if (temp == null || !String.class.isAssignableFrom(temp.getClass())) {
                continue;
            }
            String cellphone = (String) temp;
            if (StringUtils.isEmpty(cellphone)) {
                continue;
            }
            Map<String, Object> details = new HashMap<>();
            details.put(CELLPHONE, cellphone);
            if (!pattern.matcher(cellphone).matches()) {
                details.put(CODE, UpayException.CODE_INVALID_PARAMETER);
                details.put(MSG, "无效的手机号格式！");
                failures.add(details);
                continue;
            }
            Map user = this.getAppAccountByCellphone(cellphone);
            if (user == null || user.isEmpty() || !user.containsKey(ID)) {
                details.put(CODE, UpayException.CODE_INVALID_PARAMETER);
                details.put(MSG, "手机号对应账号不存在！");
                failures.add(details);
                continue;
            }
            String referenceId = get(user, ID, String.class);
            String remark = get(user, MERCHANT_ID, String.class);
            createdCellphoneMap.put(referenceId, cellphone);
            accountRequests.add(AccountRequest.builder()
                    .referenceId(referenceId)
                    .remark(remark)
                    .build());
        }

        if (accountRequests.isEmpty()) {
            if (!failures.isEmpty()) {
                setSession(httpServletRequest, FAILED_RESULTS, failures);
            }
            result.put(FAILURES, failures);
            return result;
        }

        try {
            List<AccountResponse> responses = accountService.batchCreate(accountRequests);
            for (AccountResponse response : responses) {
                int code = response.getCode();
                if (code == 200) continue;
                String cellphone = createdCellphoneMap.get(response.getRequest().getReferenceId());
                Map<String, Object> details = new HashMap<>();
                details.put(CELLPHONE, cellphone);
                details.put(CODE, UpayException.CODE_INVALID_PARAMETER);
                String msg;
                switch (code) {
                    case 304:
                        msg = "该手机号对应积分账户已开通，系统未对该账户做任何修改！";
                        break;
                    default:
                        msg = response.getMsg();
                }
                details.put(MSG, msg);
                failures.add(details);
            }
            if (!failures.isEmpty()) {
                setSession(httpServletRequest, FAILED_RESULTS, failures);
            }
            result.put(FAILURES, failures);
            return result;
        } catch (RewardException e) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, e.getMessage());
        }
    }

    @Override
    public Rule findRule(Map request) {
        long id = getId(request);
        if (id == 0) return null;
        try {
            return ruleService.findOne(id);
        } catch (RewardException e) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, e.getMessage());
        }
    }

    @Override
    public Page<Rule> findRules(Map request) {
        long id = getId(request);
        Integer page = get(request, PAGE, Integer.class);
        Integer size = get(request, SIZE, Integer.class);
        RuleQueryCriteria.RuleQueryCriteriaBuilder builder = RuleQueryCriteria.builder();
        if (page == null || page < 0) page = 0;
        if (size == null || size < 0) size = 20;
        if (id > 0L) {
            builder.id(id);
        }
        if (request.containsKey(CITY)) {
            builder.tag(TagGroup.city, transform(get(request, CITY, List.class)));
        }
        if (request.containsKey(INDUSTRY)) {
            builder.tag(TagGroup.industry, transform(get(request, INDUSTRY, List.class)));
        }
        if (request.containsKey(PAYWAY)) {
            builder.tag(TagGroup.payway, transform(get(request, PAYWAY, List.class)));
        }
        builder.deleted(false);
        try {
            Page<Rule> rulePage = ruleService.findAll(builder.build(), page, size);
            if (rulePage != null) {
                return rulePage;
            }
            return new Page<>(Collections.EMPTY_LIST, 0, 0, page, size);
        } catch (RewardException e) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, e.getMessage());
        }
    }

    private Set<String> transform(List list) {
        Set<String> results = null;
        if (list == null || list.isEmpty()) return null;
        for (Object temp : list) {
            if (temp != null && String.class.isAssignableFrom(temp.getClass())) {
                String value = (String) temp;
                if (StringUtils.isNotEmpty(value)) {
                    if (results == null) {
                        results = new HashSet<>();
                    }
                    results.add(value);
                }
            }
        }
        return results;
    }

    @Override
    public Rule saveRule(Map request) {
        Map user = (Map) HttpRequestUtil.getSession().getAttribute(OSP_ACCOUNT);
        String ospUsername = get(user, Account.USERNAME, String.class);

        long id = getId(request);
        String name = get(request, NAME, String.class);
        String description = get(request, DESCRIPTION, String.class);
        Long points = getLong(request, POINTS);
        Long pointsUpperLimit = getLong(request, POINTS_UPPER_LIMIT);
        Boolean deleted = get(request, DELETED, Boolean.class);
        Boolean enabled = get(request, ENABLED, Boolean.class);
        Long effectiveTime = getLong(request, EFFECTIVE_TIME);
        Long expiredTime = getLong(request, EXPIRED_TIME);

        Rule rule;
        Set<Event> events;
        Set<ReferenceTag> referenceTags;
        Set<RuleRewardThreshold> rewardThresholds;
        if (id > 0) {
            rule = ruleService.findOne(id);
            if (rule == null) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "id对应规则不存在");
            }
            events = rule.getAcceptEvents();
            referenceTags = rule.getAcceptReferenceTags();
            rewardThresholds = rule.getRewardThresholds();
            events.clear();
            referenceTags.clear();
            rewardThresholds.clear();
        } else {
            events = new HashSet<>();
            referenceTags = new HashSet<>();
            rewardThresholds = new HashSet<>();
            rule = new Rule();
            rule.setCreatedBy(ospUsername);
            rule.setAcceptEvents(events);
            rule.setAcceptReferenceTags(referenceTags);
            rule.setRewardThresholds(rewardThresholds);
        }
        rule.setName(name);
        rule.setDescription(description);
        rule.setLastModifiedBy(ospUsername);
        rule.setPoints(points == null ? 0 : points);
        rule.setPointsUpperLimit(pointsUpperLimit == null ? 0 : pointsUpperLimit);
        rule.setDeleted(deleted == null ? false : deleted);
        rule.setEnabled(enabled == null ? false : enabled);
        rule.setEffectiveTime(effectiveTime == null ? 0 : effectiveTime);
        rule.setExpiredTime(expiredTime == null ? 0 : expiredTime);

        List acceptEvents = get(request, ACCEPT_EVENTS, List.class);
        if (acceptEvents != null && !acceptEvents.isEmpty()) {
            for (Object eventObj : acceptEvents) {
                if (eventObj != null && String.class.isAssignableFrom(eventObj.getClass())) {
                    for (Event event : Event.values()) {
                        if (event.name().equals(eventObj)) {
                            events.add(event);
                            break;
                        }
                    }
                }
            }
            if (events.isEmpty()) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "非法事件类型：" + acceptEvents);
            }
        }

        Map constraints = get(request, CONSTRAINTS, Map.class);
        if (constraints != null) {
            List rejectReferences = get(constraints, REJECT_REFERENCES, List.class);
            List acceptReferences = get(constraints, ACCEPT_REFERENCES, List.class);
            Long startRegisterTime = getLong(constraints, START_REGISTER_TIME);
            Long endRegisterTime = getLong(constraints, END_REGISTER_TIME);
            ReferenceConstraints.ReferenceConstraintsBuilder constraintsBuilder = ReferenceConstraints.builder()
                    .startRegisterTime(startRegisterTime == null ? 0 : startRegisterTime)
                    .endRegisterTime(endRegisterTime == null ? 0 : endRegisterTime);
            if (rejectReferences != null && !rejectReferences.isEmpty()) {
                for (Object temp : rejectReferences) {
                    if (temp != null && String.class.isAssignableFrom(temp.getClass())) {
                        constraintsBuilder.rejectReference((String) temp);
                    }
                }
            }
            if (acceptReferences != null && !acceptReferences.isEmpty()) {
                for (Object temp : acceptReferences) {
                    if (temp != null && String.class.isAssignableFrom(temp.getClass())) {
                        constraintsBuilder.acceptReference((String) temp);
                    }
                }
            }
            rule.setConstraints(constraintsBuilder.build());
        } else {
            rule.setConstraints(null);
        }

        Map acceptReferenceTagMap = get(request, ACCEPT_REFERENCE_TAGS, Map.class);
        if (acceptReferenceTagMap != null && !acceptReferenceTagMap.isEmpty()) {
            Set tagGroups = acceptReferenceTagMap.keySet();
            for (Object groupObj : tagGroups) {
                if (groupObj != null && String.class.isAssignableFrom(groupObj.getClass())) {
                    TagGroup tagGroup = null;
                    for (TagGroup tagGroupTemp : TagGroup.values()) {
                        if (tagGroupTemp.name().equals(groupObj)) {
                            tagGroup = tagGroupTemp;
                            break;
                        }
                    }
                    if (tagGroup == null) {
                        throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "无效的标签组名: " + groupObj);
                    }
                    List tags = get(acceptReferenceTagMap, groupObj, List.class);
                    if (tags == null || tags.isEmpty()) {
//                        throw new UpayException(UpayException.CODE_INVALID_PARAMETER, groupObj + " 不能为空");
                        continue;
                    }
                    for (Object tagObj : tags) {
                        if (tagObj != null || String.class.isAssignableFrom(tagObj.getClass())) {
                            String tagName = (String) tagObj;
                            if (StringUtils.isNotEmpty(tagName)) {
                                referenceTags.add(
                                        ReferenceTag.builder()
                                                .groupName(tagGroup)
                                                .name(tagName)
                                                .build()
                                );
                            }
                        }
                    }
                }
            }
        }

        List rewardThresholdList = get(request, REWARD_THRESHOLDS, List.class);
        if (rewardThresholdList != null && !rewardThresholdList.isEmpty()) {
            for (Object rewardThresholdObj : rewardThresholdList) {
                if (rewardThresholdObj != null && Map.class.isAssignableFrom(rewardThresholdObj.getClass())) {
                    Map rewardThreshold = (Map) rewardThresholdObj;
                    if (rewardThreshold.isEmpty()) continue;
                    long rewardPoints = getLong(rewardThreshold, POINTS);
                    long value = getLong(rewardThreshold, VALUE);
                    String field = get(rewardThreshold, REFERENCE_FIELD, String.class);
                    if (rewardPoints == 0L
                            || value == 0L
                            || StringUtils.isEmpty(field)) {
                        throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "达成条件参数错误！");
                    }
                    RuleRewardThreshold.ReferenceField referenceField = null;
                    for (RuleRewardThreshold.ReferenceField filedTemp : RuleRewardThreshold.ReferenceField.values()) {
                        if (filedTemp.name().equals(field)) {
                            referenceField = filedTemp;
                            break;
                        }
                    }
                    if (referenceField == null) {
                        throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "无效的达成条件，参考字段名错误：" + field);
                    }
                    rewardThresholds.add(
                            RuleRewardThreshold.builder()
                                    .points(rewardPoints)
                                    .value(value)
                                    .referenceField(referenceField)
                                    .build()
                    );
                }
            }
        }
        try {
            logger.info("save rule: {}", rule);
            return ruleService.save(rule);
        } catch (RewardException e) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, e.getMessage());
        }
    }

    @Override
    public PointsResponse findPointsByCellphone(Map request) {
        String cellphone = get(request, CELLPHONE, String.class);
        if (StringUtils.isEmpty(cellphone)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "手机号不能为空！");
        }
        Map user = this.getAppAccountByCellphone(cellphone);
        if (user == null || user.isEmpty()) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "手机号对应账号不存在！");
        }
        String referenceId = get(user, ID, String.class);
        try {
            return pointsService.findByReferenceId(referenceId);
        } catch (RewardException e) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, e.getMessage());
        }
    }

    @Override
    public Page<HistoryResponse> findHistories(Map request) {
        String cellphone = get(request, CELLPHONE, String.class);
        if (StringUtils.isEmpty(cellphone)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "账号不能为空");
        }
        Map user = this.getAppAccountByCellphone(cellphone);
        if (user == null || user.isEmpty()) {
            return null;
        }
        String referenceId = get(user, ID, String.class);
        HistoryQueryCriteria.HistoryQueryCriteriaBuilder builder =
                HistoryQueryCriteria.builder()
                        .referenceId(referenceId);
        long startTimestamp = getLong(request, START_TIMESTAMP);
        long endTimestamp = getLong(request, END_TIMESTAMP);
        if (startTimestamp > 0) {
            builder.startTimestamp(startTimestamp);
        }
        if (endTimestamp > 0) {
            builder.endTimestamp(endTimestamp);
        }
        List keywords = get(request, KEYWORDS, List.class);
        if (keywords != null && !keywords.isEmpty()) {
            for (Object wordObj : keywords) {
                if (wordObj != null && String.class.isAssignableFrom(wordObj.getClass())) {
                    String word = (String) wordObj;
                    if (StringUtils.isNotEmpty(word)) {
                        builder.keyword(word);
                    }
                }
            }
        }
        Integer page = get(request, PAGE, Integer.class);
        Integer size = get(request, SIZE, Integer.class);
        if (page == null) page = 0;
        if (size == null || size == 0) size = 20;
        try {
            return historyService.findAll(builder.build(), page, size);
        } catch (RewardException e) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, e.getMessage());
        }
    }

    @Override
    public Set<String> findTags(Map request) {
        String group = get(request, GROUP, String.class);
        TagGroup tagGroup = null;
        for (TagGroup temp : TagGroup.values()) {
            if (temp.name().equals(group)) {
                tagGroup = temp;
                break;
            }
        }
        if (tagGroup == null) {
            return null;
        }
        try {
            return tagService.findByGroup(tagGroup);
        } catch (RewardException e) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, e.getMessage());
        }
    }

    @Override
    public void exportFailedAccounts(Map params, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (params == null || params.isEmpty() || response == null) return;
        logger.info("exportFailedAccount: params={}", params);
        String originFileName = get(params, ORIGIN_FILENAME, String.class);
        List results = getSession(request, FAILED_RESULTS, List.class);
        if (results.isEmpty()) {
            throw new UpayException(UpayException.CODE_SESSION_ERROR, "失败名单为空！");
        }
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet();
        SheetUtil sheetUtil = new SheetUtil(sheet);
        for (Object temp : results) {
            if (temp == null || !Map.class.isAssignableFrom(temp.getClass())) {
                continue;
            }
            Map result = (Map) temp;
            if (result.isEmpty()) continue;
            String cellphone = get(result, CELLPHONE, String.class);
            String msg = get(result, MSG, String.class);
            sheetUtil.appendRow(Arrays.asList(cellphone, msg));
            logger.info("exportFailedAccount: appendRow={}, {}", cellphone, msg);
        }
        String filename = "上传灰度失败名单_" + originFileName;
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        workbook.write(response.getOutputStream());
    }

    @Override
    public Boolean enableRule(Map request) {
        long id = getId(request);
        if (id > 0) {
            return ruleService.enable(id);
        }
        return false;
    }

    @Override
    public Boolean disableRule(Map request) {
        long id = getId(request);
        if (id > 0) {
            return ruleService.disable(id);
        }
        return false;
    }

    @Override
    public Boolean deleteRule(Map request) {
        long id = getId(request);
        if (id > 0) {
            return ruleService.delete(id);
        }
        return false;
    }

    public long getId(Map request) {
        return getLong(request, ID);
    }

    public long getLong(Map source, String key) {
        String strVal = get(source, key, String.class);
        if (StringUtils.isNotEmpty(strVal)) {
            return Long.parseLong(strVal);
        }
        Integer intVal = get(source, key, Integer.class);
        if (intVal != null) {
            return intVal.longValue();
        }
        Long longVal = get(source, key, Long.class);
        if (longVal != null) {
            return longVal;
        }
        return 0L;
    }

    @Override
    public void exportFailedBatchAddPointsAccounts(Map params, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (params == null || params.isEmpty() || response == null) return;
        logger.info("exportFailedBatchAddPointsAccounts: params={}", params);
        String originFileName = get(params, ORIGIN_FILENAME, String.class);
        List results = getSession(request, FAILED_ADD_POINTS_RESULTS, List.class);
        if (results.isEmpty()) {
            throw new UpayException(UpayException.CODE_SESSION_ERROR, "失败名单为空！");
        }
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet();
        SheetUtil sheetUtil = new SheetUtil(sheet);
        for (Object temp : results) {
            if (temp == null || !Map.class.isAssignableFrom(temp.getClass())) {
                continue;
            }
            Map result = (Map) temp;
            if (result.isEmpty()) continue;
            String cellphone = get(result, CELLPHONE, String.class);
            String points = get(result, POINTS, String.class);
            String msg = get(result, MSG, String.class);
            sheetUtil.appendRow(Arrays.asList(cellphone, points, msg));
            logger.info("exportFailedBatchAddPointsAccounts: appendRow={}, {}", cellphone, msg);
        }
        String filename = "上传批量加积分失败名单_" + originFileName;
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        workbook.write(response.getOutputStream());
    }

    @Override
    public Map batchImportAccountsToAddPoints(MultipartFile file, AccountChangePointsRequest.Expired expired, HttpServletRequest request) {
        if (file == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "必须上传文件");
        }
        String fileName = file.getOriginalFilename();
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex == -1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        String type = fileName.substring(lastIndex + 1, fileName.length()).toLowerCase();
        if ("xlsx".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "不支持xlsx格式，请使用xls");
        }
        if (!"xls".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        InputStream is = null;
        try {
            is = file.getInputStream();
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(is);
            List<Map<String, String>> accounts = new ArrayList<>();
            // 循环Sheet
            for (int numSheet = 0; numSheet < hssfWorkbook.getNumberOfSheets(); numSheet++) {
                HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(numSheet);
                if (hssfSheet == null) {
                    continue;
                }
                // 循环行Row
                for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                    HSSFRow hssfRow = hssfSheet.getRow(rowNum);
                    if (hssfRow == null) {
                        continue;
                    }
                    Map<String, String> item = new HashMap<>();
                    HSSFCell firstCell = hssfRow.getCell(0);
                    if (firstCell != null) {
                        firstCell.setCellType(Cell.CELL_TYPE_STRING);
                        String cellphone = firstCell.toString();
                        item.put(CELLPHONE, cellphone);
                    }
                    HSSFCell secondCell = hssfRow.getCell(1);
                    if (secondCell != null) {
                        secondCell.setCellType(Cell.CELL_TYPE_STRING);
                        String points = secondCell.toString();
                        item.put(POINTS, points);
                    }
                    if (item.isEmpty()) continue;
                    accounts.add(item);
                }
            }
            if (!accounts.isEmpty()) {
                return this.batchAddPointsToAccounts(accounts, expired, request);
            }
        } catch (IOException e) {
            logger.error("", e);
            throw new UpayException(UpayException.CODE_IO_EXCEPTION, UpayException.getCodeDesc(UpayException.CODE_IO_EXCEPTION));
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    logger.error("", e);
                }
            }
        }
        return null;
    }

    private Map batchAddPointsToAccounts(
            final List<Map<String, String>> accounts,
            AccountChangePointsRequest.Expired expired,
            HttpServletRequest request) {
        Map operator = getSession(request, OSP_ACCOUNT, Map.class);
        String ospUsername = get(operator, Account.USERNAME, String.class);

        if (accounts == null || accounts.isEmpty()) return null;
        final List<Map<String, Object>> failures = new ArrayList<>();
        final Map<String, String> createdCellphoneMap = new HashMap<>();
        final Set<AccountChangePointsRequest> changePointsRequests = new HashSet<>();
        final Map<String, Object> result = new HashMap<>();
        result.put(TOTAL, accounts.size());

        for (Map<String, String> account : accounts) {
            Map<String, Object> details = new HashMap<>();
            String cellphone = get(account, CELLPHONE, String.class);
            String pointsString = get(account, POINTS, String.class);
            details.put(CELLPHONE, cellphone);
            details.put(POINTS, pointsString);
            if (StringUtils.isEmpty(cellphone) || !pattern.matcher(cellphone).matches()) {
                details.put(CODE, UpayException.CODE_INVALID_PARAMETER);
                details.put(MSG, "无效的手机号格式！");
                failures.add(details);
                continue;
            }
            if (StringUtils.isEmpty(pointsString)) {
                details.put(CODE, UpayException.CODE_INVALID_PARAMETER);
                details.put(MSG, "第二列所加积分数值不能为空！");
                failures.add(details);
                continue;
            }
            Long points;
            try {
                points = Long.parseLong(pointsString);
            } catch (NumberFormatException ignored) {
                details.put(CODE, UpayException.CODE_INVALID_PARAMETER);
                details.put(MSG, "第二列所加积分数值必须为数字！");
                failures.add(details);
                continue;
            }
            if (points < 1) {
                details.put(CODE, UpayException.CODE_INVALID_PARAMETER);
                details.put(MSG, "第二列所加积分数值不能小于1！");
                failures.add(details);
                continue;
            }
            Map user = this.getAppAccountByCellphone(cellphone);
            if (user == null || user.isEmpty() || !user.containsKey(ID)) {
                details.put(CODE, UpayException.CODE_INVALID_PARAMETER);
                details.put(MSG, "手机号对应账号不存在！");
                failures.add(details);
                continue;
            }
            String referenceId = get(user, ID, String.class);
            String remark = get(user, MERCHANT_ID, String.class);
            createdCellphoneMap.put(referenceId, cellphone);
            changePointsRequests.add(
                    AccountChangePointsRequest.builder()
                            .referenceId(referenceId)
                            .remark(remark)
                            .expired(expired)
                            .points(points)
                            .build()
            );
        }

        if (changePointsRequests.isEmpty()) {
            if (!failures.isEmpty()) {
                setSession(request, FAILED_ADD_POINTS_RESULTS, failures);
            }
            result.put(FAILURES, failures);
            return result;
        }

        List<AccountChangePointsResponse> responses =
                accountService.batchAddPoints(
                        changePointsRequests,
                        System.currentTimeMillis(),
                        ospUsername
                );
        for (AccountChangePointsResponse response : responses) {
            int code = response.getCode();
            if (code == 200) continue;
            String cellphone = createdCellphoneMap.get(response.getRequest().getReferenceId());
            long points = response.getRequest().getPoints();
            Map<String, Object> details = new HashMap<>();
            details.put(CELLPHONE, cellphone);
            details.put(POINTS, points + "");
            details.put(CODE, UpayException.CODE_INVALID_PARAMETER);
            details.put(MSG, response.getMsg());
            failures.add(details);
        }

        if (!failures.isEmpty()) {
            setSession(request, FAILED_ADD_POINTS_RESULTS, failures);
        }
        result.put(FAILURES, failures);
        return result;
    }

    private <T> T get(Map source, Object key, Class<T> valueType) {
        if (source == null || key == null || valueType == null || source.isEmpty()) return null;
        Object value = source.get(key);
        if (value == null) {
            return null;
        } else if (!valueType.isAssignableFrom(value.getClass())) {
            return null;
        } else {
            return (T) value;
        }
    }

    private Map getAppAccountByCellphone(String cellphone) {
        Map<String, Object> params = new HashMap<>();
        params.put(CELLPHONE, cellphone);
        Map response = iAccountService.getAccountByCriteria(params);
        String code = get(response, CODE, String.class);
        String msg = get(response, MSG, String.class);
        if (com.wosai.app.backend.api.consts.ExceptionConstants.SUCC.equals(code)) {
            List data = get(response, DATA, List.class);
            if (data != null && !data.isEmpty()) {
                Object userObj = data.get(0);
                if (userObj != null && Map.class.isAssignableFrom(userObj.getClass())) {
                    return (Map) userObj;
                }
            }
            return null;
        } else {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, msg == null ? "查询账号失败！" : msg);
        }
    }

    private void setSession(HttpServletRequest request, String key, Object data) {
        if (request != null) {
            HttpSession session = request.getSession(true);
            if (session != null) {
                session.setAttribute(key, data);
            }
        }
    }

    private <T> T getSession(HttpServletRequest request, String key, Class<T> clazz) {
        if (request != null) {
            HttpSession session = request.getSession(true);
            if (session != null) {
                Object data = session.getAttribute(key);
                if (data != null && clazz.isAssignableFrom(data.getClass())) {
                    return (T) data;
                }
            }
        }
        return null;
    }


    @Override
    public Map getUserInfo(Map<String, Object> requestMap) {
        return pointService.getUserInfo(requestMap);
    }

    @Override
    public ListResult filterTransaction(Map<String, Object> requestMap) {
        return pointService.filterTransaction(requestMap);
    }

    @Override
    public Map getAllTransactionEvent() {
        return pointService.getAllTransactionEvent();
    }

}
