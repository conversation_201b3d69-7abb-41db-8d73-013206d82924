package com.wosai.upay.service;

import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.VendorConfig;
import com.wosai.upay.core.service.VendorService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

@Service
public class OspVendorServiceImpl implements OspVendorService {
    private static final Logger logger = LoggerFactory.getLogger(OspVendorServiceImpl.class);

    @Autowired
    private VendorService vendorService;

    @Autowired
    private BusinessLogService businessLogService;

    @Autowired
    private UserService userService;


    @Override
    public Map createVendor(Map<String, Object> request) {
        String sp_username = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        Map vendor = vendorService.createVendorComplete(request);
        logger.info("当前操作：新建开发者，时间：{},操作人 {} ,操作内容：新增开发者 sn = {}", new SimpleDateFormat("yyyy-MM-dd HHmmss").format(new Date()), sp_username,BeanUtil.getPropString(request, ConstantUtil.KEY_SN));
        return vendor;
    }

    @Override
    public void setVendorAuth(Map<String, Object> request) {
        vendorService.setVendorAuth(request);
    }

    @Override
    public Map getVendorAuth(Map<String, Object> request) {
        return vendorService.getVendorAuth(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public void deleteVendor(Map<String, Object> request) {
        vendorService.deleteVendor(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public void deleteVendorBySn(Map<String, Object> request) {
        vendorService.deleteVendorBySn(BeanUtil.getPropString(request, ConstantUtil.KEY_SN));
    }

    @Override
    public Map updateVendor(Map<String, Object> request) {
        return vendorService.updateVendor(request);
    }

    @Override
    public Map getVendor(Map<String, Object> request) {
        return vendorService.getVendor(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public Map getVendorBySn(Map<String, Object> request) {
        return vendorService.getVendorBySn(BeanUtil.getPropString(request, ConstantUtil.KEY_SN));
    }

    @Override
    public void disableVendor(Map<String, Object> request) {
        String vendorId = BeanUtil.getPropString(request, ConstantUtil.KEY_ID);
        Map vendorOld = vendorService.getVendor(vendorId);
        vendorService.disableVendor(vendorId);
        Map vendorNew = vendorService.getVendor(vendorId);
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, vendorOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, vendorNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "vendor",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "vendor",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID
                )
        ));
    }

    @Override
    public void enableVendor(Map<String, Object> request) {
        String vendorId = BeanUtil.getPropString(request, ConstantUtil.KEY_ID);
        Map vendorOld = vendorService.getVendor(vendorId);
        vendorService.enableVendor(vendorId);
        Map vendorNew = vendorService.getVendor(vendorId);
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, vendorOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, vendorNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "vendor",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "vendor",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID
                )
        ));
    }

    @Override
    public void closeVendor(Map<String, Object> request) {
        vendorService.closeVendor(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public ListResult findVendors(Map<String, Object> request) {
        return vendorService.findVendors(PageInfoUtil.extractPageInfo(request), request);
    }

    @Override
    public String resetAppKey(Map<String, Object> request) {
        return vendorService.resetAppKey(BeanUtil.getPropString(request, ConstantUtil.KEY_SN));
    }

    @Override
    public String getAppKey(Map<String, Object> request) {
        return vendorService.getAppKey(BeanUtil.getPropString(request, ConstantUtil.KEY_SN));
    }

    @Override
    public Map createVendorApp(Map<String, Object> request) {
        return vendorService.createVendorApp(request);
    }

    @Override
    public void deleteVendorApp(Map<String, Object> request) {
        vendorService.deleteVendorApp(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public Map updateVendorApp(Map<String, Object> request) {
        return vendorService.updateVendorApp(request);
    }

    @Override
    public Map getVendorApp(Map<String, Object> request) {
        return vendorService.getVendorApp(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public ListResult findVendorApps(Map<String, Object> request) {
        return vendorService.findVendorApps(PageInfoUtil.extractPageInfo(request), request);
    }

    @Override
    public Map createVendorConfig(Map<String, Object> request) {
        return vendorService.createVendorConfig(request);
    }

    @Override
    public Map updateVendorConfig(Map<String, Object> request) {
        return vendorService.updateVendorConfig(request);
    }

    @Override
    public Map getVendorConfigByVendorId(Map<String, Object> request) {
        return vendorService.getVendorConfigByVendorId(BeanUtil.getPropString(request, VendorConfig.VENDOR_ID));
    }

    @Override
    public ListResult findVendorConfigs(Map<String, Object> request) {
        return vendorService.findVendorConfigs(PageInfoUtil.extractPageInfo(request), request);
    }

    @Override
    public Map createVendorDeveloper(Map<String, Object> request) {
        return vendorService.createVendorDeveloper(request);
    }

    @Override
    public Map updateVendorDeveloper(Map<String, Object> request) {
        return vendorService.updateVendorDeveloper(request);
    }

    @Override
    public Map getVendorDeveloperByVendorId(Map<String, Object> request) {
        return vendorService.getVendorDeveloperByVendorId(BeanUtil.getPropString(request, VendorConfig.VENDOR_ID));
    }

    @Override
    public ListResult findVendorDevelopers(Map<String, Object> request) {
        return vendorService.findVendorDevelopers(PageInfoUtil.extractPageInfo(request), request);
    }

    @Override
    public Map resetPassword(Map<String, Object> request) {
        String sp_username = (String) HttpRequestUtil.getSession().getAttribute(CommonLoginService.SESSION_USERNAME);
        logger.info("当前操作：重置密码，时间：{},操作人 {}", new SimpleDateFormat("yyyy-MM-dd HHmmss").format(new Date()), sp_username);
        String username = BeanUtil.getPropString(request, "username");
        String password = BeanUtil.getPropString(request, "password");
        if(StringUtil.empty(username)||StringUtil.empty(password)){
            throw new RuntimeException("username 和 password 都不能为null");
        }
        return userService.updateAccountPassword(username, password, false);
    }


}
