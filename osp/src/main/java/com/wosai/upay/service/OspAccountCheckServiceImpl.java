package com.wosai.upay.service;

import com.google.common.collect.Maps;
import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.util.CollectionUtil;
import com.wosai.service.IMerchantGrayService;
import com.wosai.service.enumeration.AccessTokenType;
import com.wosai.upay.constant.CommonConstant;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.service.kafka.KafkaService;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.HttpRequestUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class OspAccountCheckServiceImpl implements OspAccountCheckService {

    @Autowired
    private IMerchantGrayService merchantGrayService;

    @Autowired
    private BusinessLogService businessLogService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private GroupService groupService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private KafkaService kafkaService;

    private final String BUSINESS_FUNCTION_CODE = "1000170";

    @Override
    public void updateTradeFeeConfig(Map map) {
        map.put(CommonConstant.TYPE, AccessTokenType.TRADE_FEE.getCode());
        map.put(CommonConstant.SOURCE, CommonConstant.SP);
        int state = MapUtils.getIntValue(map, CommonConstant.STATUS);
        boolean oldState = merchantGrayService.isExist(map);
        if (state == 1) {
            merchantGrayService.insert(map);
        } else {
            merchantGrayService.delete(map);
        }
        boolean newState = merchantGrayService.isExist(map);
//        //发消息给神策
//        kafkaService.writeAppTradeFeeShowStateChange(CollectionUtil.hashMap(
//                CommonConstant.MERCHANT_ID, map.get(CommonConstant.MERCHANT_ID),
//                CommonConstant.SOURCE, CommonConstant.SP,
//                CommonConstant.EVENT_TYPE, newState ? CommonConstant.TURN_ON : CommonConstant.TURN_OFF
//        ));
//
        //保存业务日志
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, CollectionUtil.hashMap(
                        CommonConstant.MERCHANT_ID, map.get(CommonConstant.MERCHANT_ID),
                        CommonConstant.STATUS, oldState ? CommonConstant.OPEN : CommonConstant.CLOSE
                ),
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, CollectionUtil.hashMap(
                        CommonConstant.MERCHANT_ID, map.get(CommonConstant.MERCHANT_ID),
                        CommonConstant.STATUS, newState ? CommonConstant.OPEN : CommonConstant.CLOSE
                ),
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, CommonConstant.MERCHANT,
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BizOpLog.BUSINESS_FUNCTION_CODE, BUSINESS_FUNCTION_CODE,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, CommonConstant.MERCHANT_GRAY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, CommonConstant.MERCHANT_ID,
                        BizOpLog.REMARK, CommonConstant.APP_TRADE_FEE_REMARK
                )
        ));
    }


    @Override
    public Map<String, Object> getTradeFeeConfig(Map map) {
        Map result = Maps.newHashMap();
        map.put(CommonConstant.TYPE, AccessTokenType.TRADE_FEE.getCode());
        result.put(CommonConstant.STATUS, merchantGrayService.isExist(map));
        return result;
    }

    @Override
    public Map<String, Object> getSetting(Map map) {
        return merchantGrayService.query(map);
    }

    @Override
    public void updateSetting(Map map) {
        //关闭修改功能
//        merchantGrayService.update(map);
    }

    @Override
    public Map<String, Object> getOffsetHour(Map map) {
        //type 1 merchant_sn,2 group_sn,3 store_sn
        Integer type = MapUtils.getInteger(map, "type");
        String sn = MapUtils.getString(map, "sn");
        String key = "";
        if (type == 1) {
            key = MapUtils.getString(merchantService.getMerchantBySn(sn), "id");
        } else if (type == 2) {
            key = MapUtils.getString(groupService.getGroupBySn(sn), "id");
        } else if (type == 3) {
            key = MapUtils.getString(storeService.getStoreByStoreSn(sn), Store.MERCHANT_ID);
        }

        Map query = Maps.newHashMap();
        query.put("type", "3");
        query.put("merchant_id", key);

        return merchantGrayService.query(query);
    }
}
