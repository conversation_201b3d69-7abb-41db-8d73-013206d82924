package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.info.api.model.WithdrawbankList;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import com.wosai.upay.bank.info.api.service.CardBinService;
import com.wosai.upay.bank.info.api.service.DistrictsService;
import com.wosai.upay.bank.info.api.service.WithdrawBankService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import com.wosai.upay.exception.UpayException;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class OspBankServiceImpl implements OspBankService {
    private static final Logger logger = LoggerFactory.getLogger(OspBankServiceImpl.class);

    @Autowired
    private BankInfoService bankInfoService;

    @Autowired
    private CardBinService cardBinService;

    @Autowired
    private WithdrawBankService withdrawBankService;

    @Autowired
    private DistrictsService districtsService;


    /**
     * 字符串判断是否为空
     *
     * @param s
     * @return
     */
    boolean Empty(String s) {
        return s == null || s.isEmpty();
    }

    @Override
    public List getBankNames(Map<String, Object> request) {
        List<Map> result = withdrawBankService.getWithdrawBankList(PageInfoUtil.extractPageInfo(request), CollectionUtil.hashMap(
                WithdrawbankList.BANK_NAME, MapUtils.getString(request, "bankName", ""))
        );
        for (Map map : result) {
            map.put("bankName", map.get(WithdrawbankList.BANK_NAME));
        }
        return result;
    }

    @Override
    public List<String> getBranchNamesByBankNameAndGeoPosition(Map<String, Object> request) {
        String bankName = BeanUtil.getPropString(request, "bankName");
        String province = BeanUtil.getPropString(request, "province");
        String city = BeanUtil.getPropString(request, "city");
        String district = BeanUtil.getPropString(request, "district");
        if (Empty(bankName) || Empty(province)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "银行名称和省份不能为空");
        }
        return bankInfoService.getBranchNamesByBankNameAndGeoPosition(bankName, province, city, district);
    }


    @Override
    public String getBankByCardNo(Map<String, Object> request) {
        String cardNo = BeanUtil.getPropString(request, "cardNo");
        if (Empty(cardNo)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "银行卡号不能为空");
        }
        //验证银行卡卡号是否合法
        try {
            cardBinService.validBankByCardNo(cardNo);
        } catch (Exception e) {
            throw new CoreInvalidParameterException(e.getMessage());
        }
        //获取银行卡银行信息
        Map bankInfo = null;
        try {
            bankInfo = cardBinService.getBankByCardNo(cardNo);
        } catch (Exception e) {
            throw new CoreInvalidParameterException(e.getMessage());
        }
        if (bankInfo == null) {
            throw new CoreInvalidParameterException("此银行卡识别不到银行信息");
        }
        return BeanUtil.getPropString(bankInfo, "bank_name");
    }


    @Override
    public ListResult findBankInfos(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return bankInfoService.getBankInfos(pageInfo, request);
    }

    @Override
    public List<Map> getDistricts() {
        List<Map> result = districtsService.getAllprovinces();
        return result;
    }

}
