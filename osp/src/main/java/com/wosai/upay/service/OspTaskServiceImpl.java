package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.constant.WithdrawConstant;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.TaskApplyLogUtil;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class OspTaskServiceImpl implements OspTaskService {
    public static final Logger logger = LoggerFactory.getLogger(OspTaskServiceImpl.class);

    @Autowired
    private LogService logService;
    @Autowired
    private OspUserLoginService loginService;

    @Override
    public ListResult findTasks(Map<String, Object> request) {
        Map userInfo = loginService.getUserInfo(request);
        ListResult result;
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        //拥有提现导出权限的角色 添加提现导出任务
        if(WithdrawConstant.EXPORT_WITHDRAW_ROLE_LIST.contains(MapUtils.getString(userInfo, "role", ""))){
            int type = BeanUtil.getPropInt(request,TaskApplyLog.TYPE);
            List<Integer> typeList = new ArrayList<>();
            typeList.add(type);
            typeList.add(TaskApplyLogUtil.TYPE_WITHDRAW_LIST_DOWNLOAD);
            result = logService.findTaskApplyLogsByTypeList(pageInfo, request, typeList);
        }else if(WithdrawConstant.BATCH_CUT_TRANSACTION_PARAMS_ROLE_LIST.contains(MapUtils.getString(userInfo, "role", ""))){
            int type = BeanUtil.getPropInt(request,TaskApplyLog.TYPE);
            List<Integer> typeList = new ArrayList<>();
            typeList.add(type);
            typeList.add(TaskApplyLogUtil.TYPE_BATCH_CHANGE);
            result = logService.findTaskApplyLogsByTypeList(pageInfo, request, typeList);
        }else{
            result = logService.findTaskApplyLogs(pageInfo, request);
        }
        if (result != null && result.getRecords() != null && result.getRecords().size() > 0){
            for (int i = 0 ; i < result.getRecords().size() ; i++){
                result.getRecords().get(i).put(TaskApplyLogUtil.DESC_APPLY_STATUS, getMapValue(TaskApplyLogUtil.APPLY_STATUS_DESC, BeanUtil.getPropInt(result.getRecords().get(i), TaskApplyLog.APPLY_STATUS)));
                result.getRecords().get(i).put(TaskApplyLogUtil.DESC_APPLY_SYSTEM, getMapValue(TaskApplyLogUtil.APPLY_SYSTEM_DESC, BeanUtil.getPropInt(result.getRecords().get(i), TaskApplyLog.APPLY_SYSTEM)));
                result.getRecords().get(i).put(TaskApplyLogUtil.DESC_TYPE, getMapValue(TaskApplyLogUtil.TYPE_DESC, BeanUtil.getPropInt(result.getRecords().get(i), TaskApplyLog.TYPE)));
            }
        }
        return result;
    }

    @Override
    public Map getTask(Map<String, Object> request) {
        String id = BeanUtil.getPropString(request, DaoConstants.ID);
        if (StringUtil.empty(id)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "任务id不能为空！");
        }
        Map task = logService.getTaskApplyLog(id);
        if (task != null){
            task.put(TaskApplyLogUtil.DESC_APPLY_STATUS, getMapValue(TaskApplyLogUtil.APPLY_STATUS_DESC, BeanUtil.getPropInt(task, TaskApplyLog.APPLY_STATUS)));
            task.put(TaskApplyLogUtil.DESC_APPLY_SYSTEM, getMapValue(TaskApplyLogUtil.APPLY_SYSTEM_DESC, BeanUtil.getPropInt(task, TaskApplyLog.APPLY_SYSTEM)));
            task.put(TaskApplyLogUtil.DESC_TYPE, getMapValue(TaskApplyLogUtil.TYPE_DESC, BeanUtil.getPropInt(task, TaskApplyLog.TYPE)));
        }
        return task;
    }

    private String getMapValue(Map map, int key){
        if (map != null && map.containsKey(key)){
            return (String)map.get(key);
        }
        return "";
    }

    @Override
    public void deleteTask(Map<String, Object> request) {
        String id = BeanUtil.getPropString(request, DaoConstants.ID);
        if (StringUtil.empty(id)){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "任务id不能为空！");
        }
        logService.deleteTaskApplyLog(id);
    }

    @Override
    public Map updateTask(Map<String, Object> request) {
        if (request == null){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "修改任务对象不能为空！");
        }
        if (request.containsKey(TaskApplyLog.APPLY_RESULT)){
            Map task = logService.getTaskApplyLog(BeanUtil.getPropString(request, DaoConstants.ID));
            String app_result = BeanUtil.getPropString(task, TaskApplyLog.APPLY_RESULT);
            Map result = null;
            if (!StringUtil.empty(app_result)){
                try {
                    result = JacksonUtil.toBean(app_result, Map.class);
                }catch (Exception e){

                }
            }
            if (result == null){
                result = new HashMap();
            }
            Object ob = BeanUtil.getProperty(request, TaskApplyLog.APPLY_RESULT);
            if (ob instanceof Map){
                Map obMap = (Map) ob;
                Set<String> keys = obMap.keySet();
                for(String key : keys){
                    result.put(key, BeanUtil.getProperty(obMap, key));
                }
                request.put(TaskApplyLog.APPLY_RESULT, JacksonUtil.toJsonString(result));
            }
        }
        return logService.updateTaskApplyLog(request);
    }

    @Override
    public Map createTask(Map<String, Object> request) {
        if (request == null){
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "创建任务对象不能为空！");
        }
        //系统osp
        request.put(TaskApplyLog.APPLY_SYSTEM, TaskApplyLogUtil.APPLY_SYSTEM_SP);
        //时间当前时间
        request.put(TaskApplyLog.APPLY_DATE, new java.sql.Date(new Date().getTime()));
        //操作人
        String userId = BeanUtil.getPropString(request, TaskApplyLog.USER_ID);
        if (StringUtil.empty(userId)){
            userId = BeanUtil.getPropString((Map) HttpRequestUtil.getSession().getAttribute("osp_account"), Account.USERNAME);
        }
        request.put(TaskApplyLog.USER_ID, userId);
        if (request.get(TaskApplyLog.APPLY_STATUS) == null) {
            request.put(TaskApplyLog.APPLY_STATUS, TaskApplyLogUtil.APPLY_STATUS_IN_EXCUTE);
        } else {
            request.put(TaskApplyLog.APPLY_STATUS, BeanUtil.getPropInt(request, TaskApplyLog.APPLY_STATUS));
        }
        if (request.containsKey(TaskApplyLog.APPLY_RESULT)){
            Object ob = BeanUtil.getProperty(request, TaskApplyLog.APPLY_RESULT);
            if (ob instanceof Map || ob instanceof List){
                request.put(TaskApplyLog.APPLY_RESULT, JacksonUtil.toJsonString(ob));
            }
        }
        return logService.createTaskApplyLog(request);
    }
}
