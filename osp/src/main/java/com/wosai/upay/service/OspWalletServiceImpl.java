package com.wosai.upay.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.service.ExportService;
import com.wosai.upay.transaction.service.TaskLogService;
import com.wosai.upay.util.HttpRequestUtil;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * @Date: 2019-08-08 19:26
 * @Description:
 */

public class OspWalletServiceImpl implements OspWalletService {

    private final Logger logger = LoggerFactory.getLogger(OspWalletServiceImpl.class);

    @Autowired
    private ExportService exportService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    private MerchantService merchantService;

    private static final String DAY_SDF_PATTERN_YYYYMMDD = "yyyyMMdd";


    @Override
    public Map createExportWalletQueryTask(Map<String, Object> request) {

        String user_id = BeanUtil.getPropString((Map) HttpRequestUtil.getSession().getAttribute("osp_account"), DaoConstants.ID);
        //系统检查是否可以导出
        allowExport(user_id);

        //参数检查
        long date_start = BeanUtil.getPropLong(request, "date_start");
        long date_end = BeanUtil.getPropLong(request, "date_end");
        if (date_start <= 0 || date_end <= 0 || date_start > date_end) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "流水明细导出时间有误");
        }
        String timeZoneStr = BeanUtil.getPropString(request, "timeZone");

        List<String> merchant_sns = (List) BeanUtil.getProperty(request, "merchant_sns");

        if (merchant_sns != null && merchant_sns.size() > 0) {
            List<String> merchant_ids = new ArrayList<>();
            for (String merchant_sn : merchant_sns) {
                Map merchant = merchantService.getMerchantBySn(merchant_sn);
                if (merchant == null) {
                    throw new UpayException(UpayException.CODE_INVALID_PARAMETER, String.format("找不到该商户[%s]，请重新输入", merchant_sn));
                }
                merchant_ids.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
            }
            if (merchant_ids.size() > 0) {
                request.put("merchant_ids", merchant_ids);
            } else {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "导出参数为空");
            }
        }

        return exportService.createExportStatementTask(CollectionUtil.hashMap(
                StatementTaskLog.TYPE, StatementTaskLog.TYPE_BALANCE,
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_SP,
                StatementTaskLog.TITLE, getExportTitle(date_start, date_end, timeZoneStr),
                StatementTaskLog.USER_ID, user_id
        ), request);
    }


    /**
     * 检查是否允许导出
     */
    private void allowExport(String user_id) {
        //检查用户七日内正在执行的任务数是否小于50
        long now = new Date().getTime();
        long sevenDaysAgo = now - OspOrderStatementServiceImpl.EXPORT_RUNNING_LIMIT_TIME;
        ListResult taskResult = taskLogService.findTaskApplyLogs(new PageInfo(1, 10, sevenDaysAgo, now), CollectionUtil.hashMap(
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_SP,
                StatementTaskLog.USER_ID, user_id,
                StatementTaskLog.APPLY_STATUSES, StatementTaskLog.APPLY_STATUS_RUNNING
        ));
        if (taskResult != null && taskResult.getTotal() >= OspOrderStatementServiceImpl.EXPORT_RUNNING_LIMIT_COUNT) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER,
                    String.format("当前已有%s个对账单任务在生成中,请等待任务完成再来操作", OspOrderStatementServiceImpl.EXPORT_RUNNING_LIMIT_COUNT));
        }
    }


    private String getExportTitle(long time_begin, long time_end, String timeZoneStr) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DAY_SDF_PATTERN_YYYYMMDD);
        if (!StringUtils.isBlank(timeZoneStr)) {
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone(timeZoneStr));
        }

        String begin = simpleDateFormat.format(time_begin);
        String end = simpleDateFormat.format(time_end);
        return "SP" + "余额对账单" + begin + "_" + end;
    }
}
