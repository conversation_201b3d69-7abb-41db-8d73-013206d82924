package com.wosai.upay.service;

import com.wosai.upay.common.bean.ListResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * opr-merchant-activity 卡券活动接口类.
 */
public interface OspCardsService {

    /**
     * 创建卡券活动.
     *
     * @param request
     */
    Map createActivity(Map request);

    /**
     * 分页查询卡券方案.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                sn           卡券方案sn
     *                name         卡券方案名称
     *                channel      发放渠道
     *                type         卡券类型
     *                status       卡券方案状态
     *                deleted
     * @return
     */
    ListResult findActivity(Map request);

    /**
     * 通过SN获取卡券活动.
     *
     * @param request
     */
    Map getActivityBySn(Map request);

    /**
     * 更新活动
     *
     * @param request
     */
    Map updateActivity(Map request);

    /**
     * 审批活动
     *
     * @param request
     */
    Map auditActivity(Map request);

    /**
     * 中止活动
     *
     * @param request
     */
    Map terminateActivity(Map request);

    /**
     * 导入白名单
     *
     * @param file
     * @param request
     * @return
     */
    Map importCardsWhitelist(MultipartFile file, Map request);

    /**
     * 检查白名单重复
     *
     * @param file
     * @return
     */
    Map checkCardsWhitelist(MultipartFile file);

    /**
     * 批量导入白名单
     *
     * @param file
     * @param request
     * @return
     */
    Map batchImportCardsWhitelist(MultipartFile file, Map request);

    /**
     * 分页查询活动白名单.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                activity_id    活动id
     *                merchant_sn    商户号
     *                merchant_name  商户名称
     *                cellphone      商户手机号
     *                deleted
     * @return
     */
    ListResult findActivityWhitelist(Map request);

    /**
     * 删除白名单
     *
     * @param request
     */
    Map batchDeleteActivityWhitelist(Map request);

    /**
     * 分页查询在活动中的卡券.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                activity_id    活动id
     *                merchant_sn    商户号
     *                merchant_name  商户名称
     *                cellphone      商户手机号
     *                card_status    卡券状态
     *                deleted
     * @return
     */
    ListResult findCardInActivity(Map request);

    /**
     * 分页查询在活动中的卡券.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                activity_name  活动名称
     *                merchant_sn    商户号
     *                merchant_name  商户名称
     *                cellphone      商户手机号
     *                card_status    卡券状态
     *                card_type      卡券类型
     *                deleted
     * @return
     */
    ListResult findCardInMerchant(Map request);

    /**
     * 查询卡券详情
     *
     * @param request
     *          sn 卡券编号
     * @return
     */
    Map findCardBySn(Map request);

    /**
     * 批量导出卡券记录
     *
     * @param params
     * @param response
     */
    void exportCardInActivity(Map params, HttpServletResponse response) throws IOException;

    /**
     * 设置活动优先级
     *
     * @param request
     */
    Map updateActivityPriority(Map request);
}
