package com.wosai.upay.service;


import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.common.validation.*;
import com.wosai.upay.helper.CorePlatformsValidated;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 接口定义，参数校验规则定义在接口方法上，校验失败的错误提示可以支持i18n，具体做法是在src/main/resources/ValidationMessages.properties资源文件中定义错误提示的键值对，然后在这里引用错误提示键。
 * 本地化的资源文件加后缀，例如ValidationMessages_zh_CN.properties。
 *
 * <AUTHOR>
@CorePlatformsValidated
public interface OspTerminalService {

    /**
     * 绑定门店码 - 根据编号.
     *
     * @param request store_sn
     *                qr_code
     *                name
     * @return
     */
    Map bindQrcode(Map request);

    /**
     * 根据terminalId启用Terminal.
     *
     * @param request id                  UUID
     */
    void enableTerminal(Map<String, Object> request);

    /**
     * 根据terminalId禁用Terminal.
     *
     * @param request id                  UUID
     */
    void disableTerminal(Map<String, Object> request);

    /**
     * 根据storeId解绑终端.
     *
     * @param request id                  UUID
     * @return
     */
    void unbindTerminal(Map<String, Object> request);

    /**
     * 修改Terminal.
     *
     * @param request
     */
    Map updateTerminal(Map<String, Object> request);

    /**
     * 根据terminalId获取Terminal.
     *
     * @param request id                  UUID
     * @return
     */
    Map getTerminal(Map<String, Object> request);

    /**
     * 根据terminalSn获取Terminal.
     *
     * @param request sn
     * @return
     */
    Map getTerminalBySn(Map<String, Object> request);

    /**
     * 分页查询Terminal.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *      sn                  用户可见终端编号
     *      device_fingerprint  设备指纹
     *      name                终端名
     *      type                类型
     *      status              状态
     *      client_sn           商户外部终端号
     *      target_type         回调通知方式
     *      store_id            门店ID
     *      merchant_id         商户ID
     *      solicitor_id        推广渠道ID
     *      vendor_id           服务商ID
     *      vendor_app_id       服务商应用ID
     *      deleted             删除标志
     * @return
     */
    ListResult findTerminals(Map<String, Object> request);
    /**
     * 创建激活码
     *
     * @param request id                  UUID
     *
     * @return Map
     */
    Map createActivationCode(Map<String, Object> request);
    /**
     * 获取可用激活码
     *
     * @param request id                  UUID
     *
     * @return Map
     */
    ListResult getActivationCodes(Map<String, Object> request);

    /**
     * 查询开票端信息
     * @param qrCode
     * @return
     */
    Map getHemayunInvoiceTerminal(Map<String, Object> request);


    /**
     * 分页查询Terminal.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *      sn                  用户可见终端编号
     *      device_fingerprint  设备指纹
     *      name                终端名
     *      type                类型
     *      status              状态
     * @return
     */
    ListResult findSmartCloudSound(Map<String, Object> request);

    /**
     *    解绑 智能云音箱

     * @return
     */
    boolean unbindSmartCloudSound(@PropNotEmpty(value = "id", message = "{value}不可为空")
                                          Map<String, Object> request);

    Map getQrcodeByIot(Map<String, Object> param);

    Map getIotByQrcode(Map<String, Object> param);

    /**
     * 获取终端配置状态列表
     *
     * @param request
     * @return
     */
    List listTerminalConfigStatus(Map<String, Object> request);

    /**
     * 更新终端配置
     *
     * @param request
     */
    void updateTerminalConfigStatus(Map request);

}
