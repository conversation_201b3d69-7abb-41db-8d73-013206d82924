package com.wosai.upay.service;

import java.util.List;
import java.util.Map;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.helper.CorePlatformsValidated;

@CorePlatformsValidated
public interface OspTransactionService {

    /**
     * 流水查询
     * @param request
     *   merchant_id
     *   merchant_sn
     *   merchant_name
     *   store_id
     *   store_sn
     *   store_name
     *   payway
     *   sub_payway
     *   status
     *   min_total_amount
     *   max_total_amount
     *   order_sn
     *   client_sn
     *   trade_no
     *   terminal_sn
     *   type
     * @return
     */
    ListResult findTransactions(Map request);

    List getTransactionListByOrderSn(Map request);

    /**
     * 创建查询流水导出任务
     * @param request
     * @return
     */
    Map createExportTransactionQueryTask(Map<String, Object> request);
}
