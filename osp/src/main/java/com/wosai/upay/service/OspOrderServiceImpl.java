package com.wosai.upay.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.googlecode.jsonrpc4j.ProxyUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.service.remote.OrderService;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.OrderUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by jianfree on 31/3/16.
 */
public class OspOrderServiceImpl implements OspOrderService {

    private OrderService orderService ; //backend upay项目提供的接口
    private String upayGatewayUrl;
    private String [] mailNotificationOrderSendto; //订单处理接收邮件通知的邮件地址
    private ObjectMapper objectMapper = new ObjectMapper();
    private JsonRpcHttpClient preOrderRiskClient;
    @Autowired
    private JavaMailSenderImpl javaMailSender;
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private TranslateService translateService;

    public OspOrderServiceImpl(String backendUpayUrl,
                               String upayGatewayUrl,
                               String mailNotificationOrderSendto,
                               String preOrderRiskUrl){
        JsonRpcHttpClient client = null;
        try {
            client = new JsonRpcHttpClient(new URL(backendUpayUrl + "rpc/upayorder"));
            preOrderRiskClient = new JsonRpcHttpClient(new URL(preOrderRiskUrl + "rpc/risk"));
            preOrderRiskClient.setHeaders(CollectionUtil.hashMap("Content-Type", "application/json"));
        } catch (MalformedURLException e) {
            throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        this.orderService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                OrderService.class,
                client);
       this.upayGatewayUrl = upayGatewayUrl;
        if(!StringUtil.empty(mailNotificationOrderSendto)){
            this.mailNotificationOrderSendto = mailNotificationOrderSendto.split(",");
        }
    }

    @Override
    public ListResult findOrders(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        Map queryFilter = CollectionUtil.hashMap(
                "storeName", BeanUtil.getPropString(request, "store_name"),
                "storeSn", BeanUtil.getPropString(request, "store_sn"),
                "storeId", BeanUtil.getPropString(request, "store_id"),
                "merchantId", BeanUtil.getPropString(request, "merchant_id"),
                "merchantSn", BeanUtil.getPropString(request, "merchant_sn"),
                "merchantName", BeanUtil.getPropString(request, "merchant_name"),
                "payway", BeanUtil.getPropString(request, "payway"),
                "subPayway", BeanUtil.getPropString(request, "sub_payway"),
                "status", BeanUtil.getPropString(request, "status"),
                "minTotalAmount", BeanUtil.getPropString(request, "min_total_amount"),
                "maxTotalAmount", BeanUtil.getPropString(request, "max_total_amount"),
                "orderSn", BeanUtil.getPropString(request, "order_sn"),
                "clientSn", BeanUtil.getPropString(request, "client_sn"),
                "tradeNo", BeanUtil.getPropString(request, "trade_no"),
                "terminalSn", BeanUtil.getPropString(request, "terminal_sn"),
                "provider", BeanUtil.getPropString(request, "provider"),
                "deviceFingerprint", BeanUtil.getPropString(request, Terminal.DEVICE_FINGERPRINT),
                "upayQueryType", BeanUtil.getPropInt(request, "upayQueryType")
        );
        //client_sn 新增 商户终端号
        String merchant_client_sn = BeanUtil.getPropString(request,"merchant_client_sn");
        if(!StringUtil.empty(merchant_client_sn)){
            ListResult tmp = terminalService.findTerminals(pageInfo,CollectionUtil.hashMap("client_sn",merchant_client_sn));
            if(tmp != null && tmp.getRecords() != null && tmp.getRecords().size() > 0){
                String merchant_id = (String) tmp.getRecords().get(0).get("merchant_id");
                queryFilter.put("merchantId",merchant_id);
            }else{
                return new ListResult(0,new ArrayList<Map>());
            }
        }
        ListResult result = orderService.getOrderList(pageInfo, queryFilter);
        List<Map> list = result.getRecords();
        if(list != null && list.size() != 0) {
            Set<String> merchantIds = new HashSet<String>();
            for (Map map : list) {
                merchantIds.add(BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_ID, null));
            }
            translateService.translateOrderOperatorNames(list, merchantIds);
        }
        return result;
    }

    @Override
    public List getTransactionListByOrderSn(Map request) {
        String orderSn = BeanUtil.getPropString(request, "order_sn");
        List list = orderService.getTransactionListByOrderSn(orderSn);
        if (list != null && list.size() != 0) {
            translateService.translateOrderOperatorNames(list, BeanUtil.getPropString(list.get(0), ConstantUtil.KEY_MERCHANT_ID));
        }
        return list;
    }

    @Override
    public void fixOrder(Map request) {
        String orderSn = BeanUtil.getPropString(request, "order_sn");
        Map postData = new HashMap();
        postData.put("sn", orderSn);
        dealOrder(orderSn, upayGatewayUrl + "upay/v2/fix", postData, "SUCCESS", "恢复订单");
    }

    @Override
    public void cancelOrder(Map request) {
        String orderSn = BeanUtil.getPropString(request, "order_sn");
        String terminalSn = BeanUtil.getPropString(request, "terminal_sn");
        String storeSn = BeanUtil.getPropString(request, "store_sn");
        Map postData = new HashMap();
        postData.put("sn", orderSn);
        postData.put("wosai_store_id", storeSn);
        postData.put("terminal_sn", terminalSn);
        dealOrder(orderSn, upayGatewayUrl + "upay/v2/revoke", postData, "CANCEL_SUCCESS", "撤销订单");
    }

    private void dealOrder(String orderSn, String url, Map request, String successFlag , String dealFlag){
        if(mailNotificationOrderSendto != null && mailNotificationOrderSendto.length > 1){
            Map order = orderService.getOrderDetailByOrderSn(orderSn);
            if(order == null){
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "订单不存在");
            }
            Long total = BeanUtil.getPropLong(order, "original_total");
            String merchantName = BeanUtil.getPropString(order, "merchant_name");
            String operator = HttpRequestUtil.getSession().getAttribute("osp_username")+"";
            String subject = String.format("[通知] 订单处理: %s 订单号: %s 金额: %.2f 商户: %s 操作人: %s", dealFlag, orderSn, total / 100.0, merchantName, operator);
            String content = "  订单信息：\n";
            try {
                content = content + objectMapper.writeValueAsString(order);
            } catch (JsonProcessingException e) {
            }
            sendMail(mailNotificationOrderSendto, subject, content);
        }


        Map result;
        try {
            String response = postToUpay(url, request);
            result = objectMapper.readValue(response.getBytes(), Map.class);
        } catch (Exception e) {
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, e.getMessage());
        }
        String resultCode = BeanUtil.getPropString(result, "result_code");
        String errorMessage = BeanUtil.getPropString(result, "error_message");
        if("200".equals(resultCode)){
            Map bizResponse = (Map) result.get("biz_response");
            String innerResultCode = BeanUtil.getPropString(bizResponse, "result_code");
            String innerErrorMessage = BeanUtil.getPropString(bizResponse, "error_message");
            if(!successFlag.equals(innerResultCode)){
                throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, innerErrorMessage);
            }
        }else{
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, errorMessage);
        }
    }

    private String postToUpay(String url, Map request) throws Exception {
        URLConnection connection = new URL(url).openConnection();
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);
        OutputStream out = connection.getOutputStream();
        objectMapper.writeValue(out, request);
        out.close();
        InputStream result = connection.getInputStream();
        Scanner scanner = new Scanner(result);
        StringBuffer response = new StringBuffer();
        while(scanner.hasNextLine()){
            response.append(scanner.nextLine()).append("\n");
        }
        result.close();
        return response.toString();
    }

    @Override
    public void exportOrders(Map params, HttpServletResponse response) throws IOException {
        long page = BeanUtil.getPropLong(params, ConstantUtil.KEY_PAGE, 1);
        long pageSize = BeanUtil.getPropLong(params, ConstantUtil.KEY_PAGESIZE, 5000);
        params.put(ConstantUtil.KEY_PAGE, page);
        params.put(ConstantUtil.KEY_PAGESIZE, pageSize);
        ListResult listResult = findOrders(params);
        HSSFWorkbook workbook = OrderUtil.buildOrdersExcelV2(listResult.getRecords());
        String fileName =  new SimpleDateFormat("yyyy-MM-dd_HHmmss").format(new Date()) + "exportOrders.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }

    public void sendMail(String [] tos, String subject, String content){
        SimpleMailMessage mailMessage = new SimpleMailMessage();
        mailMessage.setFrom(javaMailSender.getUsername());
        mailMessage.setTo(tos);
        mailMessage.setText(content);
        mailMessage.setSubject(subject);
        javaMailSender.send(mailMessage);
    }

    @Override
    public List getPreOrderRiskDetail(Map request) {
        String clientSn = BeanUtil.getPropString(request, "client_sn");
        try {
            return preOrderRiskClient.invoke("getRiskDetailByClientSn", new Object[]{clientSn}, List.class);
        } catch (Throwable e) {
            throw new UpayException(UpayException.CODE_IO_EXCEPTION, e.getMessage());
        }
    }
}
