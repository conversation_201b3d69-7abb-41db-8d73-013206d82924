package com.wosai.upay.service;


import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.validation.PropNotEmpty;

import java.util.List;
import java.util.Map;

/**
 * 接口定义，参数校验规则定义在接口方法上，校验失败的错误提示可以支持i18n，具体做法是在src/main/resources/ValidationMessages.properties资源文件中定义错误提示的键值对，然后在这里引用错误提示键。
 * 本地化的资源文件加后缀，例如ValidationMessages_zh_CN.properties。
 *
 * <AUTHOR>
public interface OspSolicitorService {

    /**
     * 创建Solicitor.
     *
     * @param request
     */
    Map createSolicitor(Map<String, Object> request);

    /**
     * 根据storeId启用推广渠道.
     *
     * @param request id                  UUID
     * @return
     */
    void enableSolicitor(Map<String, Object> request);

    /**
     * 根据storeId禁用推广渠道.
     *
     * @param request id                  UUID
     * @return
     */
    void disableSolicitor(Map<String, Object> request);

    /**
     * 根据storeId关闭推广渠道.
     *
     * @param request id                  UUID
     * @return
     */
    void closeSolicitor(Map<String, Object> request);

    /**
     * 根据solicitorId删除Solicitor.
     *
     * @param request id                  UUID
     */
    void deleteSolicitor(Map<String, Object> request);

    /**
     * 根据solicitorSn删除Solicitor.
     *
     * @param request sn
     */
    void deleteSolicitorBySn(Map<String, Object> request);

    /**
     * 修改Solicitor.
     *
     * @param request
     */
    Map updateSolicitor(Map<String, Object> request);

    /**
     * 根据solicitorId获取Solicitor.
     *
     * @param request id                  UUID
     * @return
     */
    Map getSolicitor(Map<String, Object> request);

    /**
     * 根据solicitorSn获取Solicitor.
     *
     * @param request sn
     * @return
     */
    Map getSolicitorBySn(Map<String, Object> request);

    /**
     * 分页查询Solicitor.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *      sn                  推广者可见的编号
     *      name                推广者名称
     *      category
     *      status
     *      cellphone
     *      contact_name        联系人姓名
     *      contact_phone       联系固定电话号码
     *      contact_cellphone   联系移动电话号码
     *      contact_email       联系邮箱
     *      contact_address     联系地址
     *      deleted
     * @return
     */
    ListResult findSolicitors(Map<String, Object> request);

    /**
     * 创建SolicitorConfig.
     *
     * @param request
     */
    Map createSolicitorConfig(Map<String, Object> request);

    /**
     * 根据solicitorConfigId删除SolicitorConfig.
     *
     * @param request id                  UUID
     */
    void deleteSolicitorConfig(Map<String, Object> request);

    /**
     * 修改SolicitorConfig.
     *
     * @param request
     */
    Map updateSolicitorConfig(Map<String, Object> request);

    /**
     * 根据solicitorId获取SolicitorConfig.
     *
     * @param request solicitor_id
     * @return
     */
    Map getSolicitorConfigBySolicitorId(Map<String, Object> request);

    /**
     * 分页查询SolicitorConfig.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *      solicitor_id
     *      payway              支付方式，1：支付宝1.0；2：支付宝2.0；3：微信；4：百付宝；5：京东钱包；6：QQ钱包；7：ApplePay；8：三星支付；9：小米支付；10：华为支付；11：翼支付；12：苏宁易钱包；13：银联云闪付（AndroidPay）；14银联钱包
     *      b2c_formal          b扫c是否正式商户 0:否  1:是
     *      b2c_satus           b扫c 是否开通关闭 0:关闭 1：开通
     *      c2b_formal          c扫b是否正式商户 0:否  1:是
     *      c2b_status          c扫b是否开通关闭 0:关闭 1：开通
     *      wap_formal          wap是否正式商户 0:否  1:是
     *      wap_status          wap 是否开通关闭 0:关闭 1：开通
     *      extend1_formal      交易模式保留字段  是否正式商户 0:否  1:是
     *      extend1_status      交易模式保留字段   是否开通关闭 0:关闭 1：开通
     *      extend2_formal      交易模式保留字段   是否正式商户 0:否  1:是
     *      extend2_status      交易模式保留字段   是否开通关闭 0:关闭 1：开通
     *      deleted
     * @return
     */
    ListResult findSolicitorConfigs(Map<String, Object> request);

    /**
     * 获取商户收款通道是否开通，是否正式，费率， 正式参数等信息
     * @param request
     * merchant_id
     * @return
     */
    List getAnalyzedSolicitorConfigs(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_SOLICITOR_ID, message = "{value} 推广者id不能为空")
            })
            Map request);

    /**
     * 编辑收款通道 是否开通以及费率信息
     * @param request
     *   merchant_id
     *   payway
     *   b2c_formal
     *   b2c_satus
     *   b2c_fee_rate
     *   c2b_formal
     *   c2b_fee_rate
     *   c2b_status
     *   wap_formal
     *   wap_status
     *   wap_fee_rate
     */

    void updateSolicitorConfigStatusAndFeeRate(Map<String, Object> request);

    void updateSolicitorConfigProvider(Map<String, Object> request);

}
