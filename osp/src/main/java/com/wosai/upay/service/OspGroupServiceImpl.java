package com.wosai.upay.service;

import com.wosai.business.log.model.BizOpLog;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;

import com.wosai.merchant.Bean.MerchantInvoiceConfig;
import com.wosai.operation.service.MerchantInvoiceService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.SpringWebUtil;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import com.wosai.upay.core.model.Group;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.GroupUser;
import com.wosai.upay.core.model.user.SpecialAuthWhitelist;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.user.api.service.SpecialAuthWhitelistService;
import com.wosai.upay.user.api.service.UserService;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.BusinessLogUtil;
import com.wosai.upay.util.ExcelUtil;
import com.wosai.upay.util.HttpRequestUtil;
import com.wosai.upay.util.SheetUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

import static com.wosai.upay.common.util.ConstantUtil.KEY_ORDER_BY_FIELD;
import static com.wosai.upay.common.util.ConstantUtil.KEY_ORDER_BY_ORDER;

/**
 * .
 *
 * <AUTHOR>
 */
public class OspGroupServiceImpl implements OspGroupService {

    Logger logger = LoggerFactory.getLogger(OspGroupServiceImpl.class);

    @Autowired
    GroupService groupService;
    @Autowired
    UserService userService;
    @Autowired
    private JavaMailSenderImpl javaMailSender;
    @Autowired
    private MerchantInvoiceService merchantInvoiceService;

    @Autowired
    LogService logService;

    @Autowired
    private BusinessLogService businessLogService;

    @Autowired
    private SpecialAuthWhitelistService specialAuthWhitelistService;
    @Autowired
    private MerchantService merchantService;
    //生成固定线程池
    public static final int MAX_BUSINESS_LOG_GROUP_THREAD_COUNT = 2; //最多同时有多少个线程执行
    public static ThreadPoolExecutor businessLogGroupExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(MAX_BUSINESS_LOG_GROUP_THREAD_COUNT);


    @Override
    public Map createGroup(Map request) {
        long permission_msp_refund = BeanUtil.getPropLong(request, "permission_msp_refund");
        request.remove("permission_msp_refund");
        int invoiceWhiteList = MapUtils.getInteger(request, "invoice_white_list", 0);
        request.remove("invoice_white_list");

        Map group = groupService.createGroup(request);

        String groupId = BeanUtil.getPropString(group, DaoConstants.ID);
        //开通集团的商户后台退款权限
        if (SpecialAuthWhitelist.MSP_REFUND_PERMISSION_OPEN == permission_msp_refund) {
            specialAuthWhitelistService.createSpecialAuthWhitelist(CollectionUtil.hashMap(
                    SpecialAuthWhitelist.AUTH_TYPE, SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND,
                    SpecialAuthWhitelist.OBJECT_ID, groupId,
                    SpecialAuthWhitelist.OBJECT_TYPE, SpecialAuthWhitelist.OBJECT_TYPE_GROUP
            ));
        }
        group.put("permission_msp_refund", permission_msp_refund);
        updateMerchantInvoiceConfigStatus(group, invoiceWhiteList);
        try {
            if (group.get("group_user") != null) {
                Map groupUser = (Map) group.get("group_user");
                String username = BeanUtil.getPropString(groupUser, Account.USERNAME);
                String groupName = BeanUtil.getPropString(request, GroupUser.NAME);
                // String content = "您的“" + groupName + "”账号：" + username + "，密码：" + getGroupAccountPassFromSession(username) + "，请及时修改密码并勿向任何人透露账号密码信息。";
                String content = "“" + BeanUtil.getPropString(group, Group.NAME) + "”在收钱吧的集团账号已经创建成功，账号：" + username
                        + "，密码：" + getGroupAccountPassFromSession(username) + "，登录地址：s.shouqianba.com。请及时修改初始密码，切勿向他人透露账号信息。"
                        + "\r\n\r\n\r\n" + "上海喔噻互联网科技有限公司\r\n" + "中国上海普陀区中江路879弄天地软件园4号楼2层 邮编：200333";
                sendMail(new String[]{username}, "您的" + groupName + "创建成功", content);
            }
        } catch (Exception e) {
            logger.error("邮件发送失败", e);
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "集团创建成功但邮件通知发送失败");
        }

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, null,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, group,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "group",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_ADD,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "group",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID
                )
        ));


        return group;
    }

    private static final String GROUP_ACCOUNT_PASS_SESSION_KEY = "GROUP_ACCOUNT_PASS_SESSION_KEY:";

    private void sessionGroupAccountPass(String username, String password) {
        HttpServletRequest request = SpringWebUtil.getCurrentRequest();
        if (request != null) {
            HttpSession session = request.getSession();
            session.setAttribute(GROUP_ACCOUNT_PASS_SESSION_KEY + username, password);
        }
    }

    private String getGroupAccountPassFromSession(String username) {
        HttpServletRequest request = SpringWebUtil.getCurrentRequest();
        if (request != null) {
            HttpSession session = request.getSession();
            return (String) session.getAttribute(GROUP_ACCOUNT_PASS_SESSION_KEY + username);
        }
        return null;
    }

    @Override
    public void enableGroup(Map request) {
        groupService.enableGroup(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public void disableGroup(Map request) {
        groupService.disableGroup(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public void closeGroup(Map request) {
        groupService.closeGroup(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public void deleteGroup(Map request) {
        String groupId = BeanUtil.getPropString(request, ConstantUtil.KEY_ID);
        Map groupOld = groupService.getGroup(groupId);
        groupService.deleteGroup(groupId);
        Map groupNew = groupService.getGroup(groupId);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, groupOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, groupNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "group",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_DEL,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "group",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, DaoConstants.ID
                )
        ));
        updateMerchantInvoiceConfigStatus(groupNew,0);

    }

    @Override
    public void deleteGroupBySn(Map request) {
        groupService.deleteGroupBySn(BeanUtil.getPropString(request, ConstantUtil.KEY_SN));
    }

    @Override
    public Map updateGroup(Map request) {
        String id = BeanUtil.getPropString(request, "id");
        int invoiceWhiteList = MapUtils.getInteger(request, "invoice_white_list", 0);
        request.remove("invoice_white_list");
        Map groupOld = groupService.getGroup(id);
        Map groupNew = groupService.updateGroup(request);
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, groupOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, groupNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "group",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "group",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "id"
                )
        ));
        updateMerchantInvoiceConfigStatus(groupNew, invoiceWhiteList);
        return groupNew;
    }

    @Override
    public Map getGroup(Map request) {
        Map group = groupService.getGroup(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
        addInvoiceWhiteListToParam(group);
        return addGroupMspRefundPermission(group);
    }

    @Override
    public Map getGroupBySn(Map request) {
        Map group = groupService.getGroupBySn(BeanUtil.getPropString(request, ConstantUtil.KEY_SN));
        addInvoiceWhiteListToParam(group);
        return addGroupMspRefundPermission(group);
    }

    @Override
    public ListResult findGroups(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return groupService.findGroups(pageInfo, request);
    }

    @Override
    public Map createAccount(Map request) {
        sessionGroupAccountPass(BeanUtil.getPropString(request, Account.USERNAME), BeanUtil.getPropString(request, Account.PASSWORD));
        return groupService.createAccount(request);
    }

    @Override
    public Map updateAccountPassword(Map request) {
        String username = BeanUtil.getPropString(request, Account.USERNAME);
        String password = BeanUtil.getPropString(request, Account.PASSWORD);
        Map accountOld = userService.getAccountByUsername(username);
        Map account = userService.updateAccountPassword(username, password, false);
        Map accountNew = userService.getAccountByUsername(username);
        String accountId = BeanUtil.getPropString(accountNew, "id");
        Map groupUser = groupService.getGroupUserByAccountId(accountId);
        String groupId = BeanUtil.getPropString(groupUser, "group_id");
        accountNew.put("group_id", groupId);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, accountOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, accountNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "group",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "account",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "group_id"
                )
        ));

        try {
            // String content = "您的集团账号：" + username + "，密码修改成功，新密码：" + password + "，请勿向任何人透露账号密码信息。";
            String content = "您在收钱吧用户名为" + username + "的集团账号密码已修改为：" + password + "，请及时登录s.shouqianba.com修改密码，切勿向他人透露账号信息。"
                    + "\r\n\r\n\r\n" + "上海喔噻互联网科技有限公司\r\n" + "中国上海普陀区中江路879弄天地软件园4号楼2层 邮编：200333";
            sendMail(new String[]{username}, "您的集团账号：" + username + "密码修改成功", content);
        } catch (Exception e) {
            logger.error("邮件发送失败", e);
            throw new UpayException(UpayException.CODE_UNKNOWN_ERROR, "密码修改成功但邮件通知发送失败");
        }
        return account;
    }

    @Override
    public Map createGroupUser(Map request) {
        return groupService.createGroupUser(request);
    }

    @Override
    public void enableGroupUser(Map request) {
        groupService.enableGroupUser(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public void disableGroupUser(Map request) {
        groupService.disableGroupUser(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public void deleteGroupUser(Map request) {
        groupService.deleteGroupUser(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public Map updateGroupUser(Map request) {
        return groupService.updateGroupUser(request);
    }

    @Override
    public Map getGroupUser(Map request) {
        return groupService.getGroupUser(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public Map getGroupUserByAccountId(Map request) {
        return groupService.getGroupUserByAccountId(BeanUtil.getPropString(request, ConstantUtil.KEY_ACCOUNT_ID));
    }

    @Override
    public ListResult findGroupUsers(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return groupService.findGroupUsers(pageInfo, request);
    }

    @Override
    public List<Map> saveGroupUserMerchantAuth(Map request) {
        return groupService.saveGroupUserMerchantAuth(BeanUtil.getPropString(request, "group_user_id"), BeanUtil.getPropString(request, "merchant_ids"));
    }

    @Override
    public List<Map> importGroupUserMerchantAuth(String groupUserId, List<Map> merchantInfos) {
        final List<Map> users_befores = groupService.getGroupUserMerchantAuths(CollectionUtil.hashMap(
                "group_user_id", groupUserId
        ));

        List<Map> result = groupService.importGroupUserMerchantAuth(groupUserId, merchantInfos);
        final List<Map> users_afters = groupService.getGroupUserMerchantAuths(CollectionUtil.hashMap(
                "group_user_id", groupUserId
        ));

        try {
            businessLogGroupExecutor.submit(new Runnable() {
                @Override
                public void run() {
                    excuteImportBusinessLog(users_befores, users_afters);
                }
            });
        } catch (Exception e) {

        }
        return result;
    }

    private void excuteImportBusinessLog(List<Map> befores, List<Map> afters) {
        befores = befores == null ? new ArrayList<Map>() : befores;
        afters = afters == null ? new ArrayList<Map>() : afters;
        long op_time = new Date().getTime();
        for (Map after : afters) {
            Map _before = null;
            for (int i = 0; i < befores.size(); i++) {
                if (BeanUtil.getPropString(after, "id", "").equals(BeanUtil.getPropString(befores.get(i), "id", ""))) {
                    _before = befores.get(i);
                    befores.remove(i);
                    i--;
                    break;
                }
            }
            if (_before == null) {
                businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                        BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, _before,
                        BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, after,
                        BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                                BizOpLog.BUSINESS_OBJECT_CODE, "group",
                                BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "group_user_merchant_auth",
                                BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "group_id",
                                BizOpLog.OP_TIME, op_time
                        )
                ));
            }
        }

    }

    @Override
    public void importGroupMerchantAuth(MultipartFile file) {
        if (file == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "必须上传文件");
        }
        String fileName = file.getOriginalFilename();
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex == -1) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }
        String type = fileName.substring(lastIndex + 1, fileName.length()).toLowerCase();
        if ("xlsx".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "不支持xlsx格式，请使用xls");
        }
        if (!"xls".equals(type)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
        }

        List<Map> merchantInfos = new ArrayList<Map>();
        InputStream is = null;
        try {
            is = file.getInputStream();
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(is);
            // 循环Sheet
            for (int numSheet = 0; numSheet < hssfWorkbook.getNumberOfSheets(); numSheet++) {
                HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(numSheet);
                if (hssfSheet == null) {
                    continue;
                }
                // 循环行Row
                for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                    HSSFRow hssfRow = hssfSheet.getRow(rowNum);
                    if (hssfRow == null) {
                        continue;
                    }
                    if (rowNum != 0) {
                        String merchantSn = ExcelUtil.toValue(hssfRow.getCell(0));
                        if (!StringUtil.empty(merchantSn) && merchantSn.indexOf(".") != -1) {
                            merchantSn = merchantSn.substring(0, merchantSn.indexOf(".")); // 防止解析为小数，去掉0
                        }
                        String merchantName = toString(hssfRow.getCell(1));
                        // 防止空行造成NullPointerException
                        if (!StringUtil.empty(merchantSn) || !StringUtil.empty(merchantName)) {
                            merchantInfos.add(CollectionUtil.hashMap("merchant_sn", merchantSn, "merchant_name",
                                    merchantName));
                        }
                    }
                }
            }

            String[] groupArr = fileName.substring(0, lastIndex).split("-");
            if (groupArr.length != 2) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件命名不正确");
            }
            Map group = groupService.getGroupBySn(groupArr[1]);
            if (group == null) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "集团不存在");
            } else if (!groupArr[0].equals(BeanUtil.getPropString(group, Group.NAME))) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "集团名称不正确");
            }
            Map superUser = groupService.getGroupUserSuperAdmin(BeanUtil.getPropString(group, DaoConstants.ID));
            Map<String, Object> payload = new HashMap<String, Object>();
            payload.put("group_sn", groupArr[1]);
            payload.put("group_name", groupArr[0]);
            payload.put("total", merchantInfos.size());
            List<Map> res = importGroupUserMerchantAuth(BeanUtil.getPropString(superUser, DaoConstants.ID), merchantInfos);
            payload.put("success", merchantInfos.size() - res.size());
            payload.put("fail", res.size());
            payload.put("fail_detail", res);
            Map user = (Map) HttpRequestUtil.getSession().getAttribute("osp_account");
            Map taskApplyLog = logService.createTaskApplyLog(CollectionUtil.hashMap(
                    TaskApplyLog.TYPE, 9,
                    TaskApplyLog.APPLY_SYSTEM, 2,
                    TaskApplyLog.APPLY_DATE, new java.sql.Date(new Date().getTime()),
                    TaskApplyLog.PAYLOAD, payload,
                    TaskApplyLog.USER_ID, BeanUtil.getPropString(user, "user_id")
            ));
            HttpRequestUtil.getSession().setAttribute(SESSION_IMPORT_GROUP_MERCHANT_AUTH_KEY, BeanUtil.getPropString(taskApplyLog, ConstantUtil.KEY_ID));
            batchAddMerchantInvoiceConfigStatus(group, merchantInfos);
        } catch (IOException e) {
            logger.error("", e);
            throw new UpayException(UpayException.CODE_IO_EXCEPTION, UpayException.getCodeDesc(UpayException.CODE_IO_EXCEPTION));
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    logger.error("", e);
                }
            }
        }
    }

    private static final String SESSION_IMPORT_GROUP_MERCHANT_AUTH_KEY = "importGroupMerchantAuth_task";

    @Override
    public Map getImportGroupMerchantAuthPayload(Map request) {
        String logId = (String) HttpRequestUtil.getSession().getAttribute(SESSION_IMPORT_GROUP_MERCHANT_AUTH_KEY);
        if (logId != null) {
            Map log = logService.getTaskApplyLog(logId);
            if (log != null) {
                return (Map) log.get(TaskApplyLog.PAYLOAD);
            }
        }
        return null;
    }

    @Override
    public Map getImportGroupMerchantAuthResult(Map request) {
        Map payload = getImportGroupMerchantAuthPayload(request);
        if (payload != null) {
            payload.remove("fail_detail");
            return payload;
        }
        return null;
    }

    @Override
    public void getImportGroupMerchantAuthFailDetail(Map params, HttpServletResponse response) throws IOException {
        Map payload = getImportGroupMerchantAuthPayload(params);
        if (payload == null) {
            return;
        }
        String groupSn = (String) payload.get("group_sn");
        List<Map> list = (List<Map>) payload.get("fail_detail");
        HSSFWorkbook workbook = buildImportAuthFailDetail(list);
        String fileName = groupSn + "-importFail.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }

    private HSSFWorkbook buildImportAuthFailDetail(List<Map> list) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("导入失败数据");
        SheetUtil sheetUtil = new SheetUtil(sheet);

        List<String> columnArr = Arrays.asList(
                "index", "errmsg"
        );
        Map nameMap = CollectionUtil.hashMap("index", "失败行", "errmsg", "失败原因");
        List headers = new ArrayList();
        for (String column : columnArr) {
            headers.add(nameMap.get(column));
        }
        sheetUtil.appendRow(headers);
        for (Map map : list) {
            List values = new ArrayList();
            for (String column : columnArr) {
                if ("index".equals(column)) {
                    values.add(BeanUtil.getPropInt(map, column) + 1);
                } else {
                    values.add(BeanUtil.getProperty(map, column));
                }
            }
            sheetUtil.appendRow(values);
        }
        return workbook;
    }

    private String toString(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    @Override
    public void deleteGroupUserMerchantAuth(Map request) {
        String id = BeanUtil.getPropString(request, ConstantUtil.KEY_ID);


        Map groupUserMerchantAuthOld = groupService.getGroupUserMerchantAuth(id);
        groupService.deleteGroupUserMerchantAuth(id);


        //Map groupUserMerchantAuthNew = groupService.getGroupUserMerchantAuth(groupId);

        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, groupUserMerchantAuthOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, null,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "group",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_DEL,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "group_user_merchant_auth",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "group_id"
                )
        ));
        String merchantId = MapUtils.getString(groupUserMerchantAuthOld, ConstantUtil.KEY_MERCHANT_ID);
        String groupId = MapUtils.getString(groupUserMerchantAuthOld, "group_id");
        Map config = merchantInvoiceService.getConfigByMerchantId(merchantId);
        if (!CollectionUtils.isEmpty(config)) {
            String bindGroupId = MapUtils.getString(config, MerchantInvoiceConfig.GROUP_ID);
            int isBind = MapUtils.getIntValue(config, MerchantInvoiceConfig.IS_BIND, -1);
            if (isBind == MerchantInvoiceConfig.IS_BIND_TRUE && StringUtils.equals(bindGroupId, groupId)) {
                merchantInvoiceService.updateMerchantInvoiceConfig(CollectionUtil.hashMap(
                        ConstantUtil.KEY_ID, MapUtils.getString(config, ConstantUtil.KEY_ID),
                        MerchantInvoiceConfig.IS_BIND, MerchantInvoiceConfig.IS_BIND_FALSE
                ));
            }
        }
    }

    @Override
    public Map getGroupUserMerchantAuth(Map request) {
        return groupService.getGroupUserMerchantAuth(BeanUtil.getPropString(request, ConstantUtil.KEY_ID));
    }

    @Override
    public List<Map> getGroupUserMerchantAuths(Map request) {
        return groupService.getGroupUserMerchantAuths(request);
    }

    @Override
    public ListResult findGroupUserMerchantAuths(Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        return groupService.findGroupUserMerchantAuths(pageInfo, request);
    }

    private void sendMail(String[] tos, String subject, String content) throws MessagingException {
        SimpleMailMessage mailMessage = new SimpleMailMessage();
        MimeMessage mime = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mime, true, "utf-8");
        helper.setFrom(javaMailSender.getUsername());
        helper.setTo(tos);
        helper.setSubject(subject);
        helper.setText(content);
        javaMailSender.send(mime);
    }

    @Override
    public void exportGroups(Map params, HttpServletResponse response) throws IOException {
        long page = BeanUtil.getPropLong(params, ConstantUtil.KEY_PAGE, 1);
        long pageSize = BeanUtil.getPropLong(params, ConstantUtil.KEY_PAGESIZE, Integer.MAX_VALUE);
        params.put(ConstantUtil.KEY_PAGE, page);
        params.put(ConstantUtil.KEY_PAGESIZE, pageSize);
        params.put(ConstantUtil.KEY_ORDER_BY, new ArrayList<Map>() {{
            add(CollectionUtil.hashMap(KEY_ORDER_BY_FIELD, "ctime", KEY_ORDER_BY_ORDER, OrderBy.OrderType.DESC));
        }});
        params.put("manager_merchant_names", "yes");
        ListResult listResult = findGroups(params);
        HSSFWorkbook workbook = buildGroupsExcel(listResult.getRecords());
        String fileName = new SimpleDateFormat("yyyy-MM-dd_HHmmss").format(new Date()) + "exportGroups.xls";
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        workbook.write(response.getOutputStream());
    }

    private HSSFWorkbook buildGroupsExcel(List<Map> groups) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("集团数据");
        SheetUtil sheetUtil = new SheetUtil(sheet);

        List<String> columnArr = Arrays.asList(
                // "contact_phone",
                "username", "name", "alias", "sn", "manager_merchant_num", "contact_name", "contact_cellphone", "manager_merchant_names"
        );
        Map nameMap = CollectionUtil.hashMap("username", "集团账号", "name", "集团主体名称", "alias", "集团简称", "sn", "集团编号"
                // "contact_phone", "联系人固定电话",
                , "manager_merchant_num", "所辖商户数", "contact_name", "联系人姓名", "contact_cellphone", "联系人手机号码"
                , "manager_merchant_names", "所辖商户");
        List headers = new ArrayList();
        for (String column : columnArr) {
            headers.add(nameMap.get(column));
        }
        sheetUtil.appendRow(headers);
        for (Map group : groups) {
            List values = new ArrayList();
            for (String column : columnArr) {
                Object value = BeanUtil.getProperty(group, column);
                values.add(value);
            }
            sheetUtil.appendRow(values);
        }
        return workbook;
    }

    private Map addGroupMspRefundPermission(Map group) {
        String groupId = BeanUtil.getPropString(group, DaoConstants.ID);
        if (StringUtil.empty(groupId)) {
            throw new CoreInvalidParameterException("集团id不能为空");
        }
        Map mspRefundPermission = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, groupId
        );
        long authType = BeanUtil.getPropLong(mspRefundPermission, SpecialAuthWhitelist.OBJECT_TYPE);
        if (mspRefundPermission != null &&
                authType == SpecialAuthWhitelist.OBJECT_TYPE_GROUP) {
            group.put("permission_msp_refund", 1);
        } else {
            group.put("permission_msp_refund", 0);
        }
        return group;
    }

    public long getGroupMspRefundPermission(Map<String, Object> request) {
        String groupId = BeanUtil.getPropString(request, "group_id");
        if (StringUtil.empty(groupId)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "集团id不可为空");
        }
        //todo common
        Map permission = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, groupId
        );
        if (permission == null || permission.isEmpty()) {
            return 0;
        } else {
            return 1;
        }
    }

    @Override
    public void updateGroupMspRefundPermission(Map request) {
        String groupId = BeanUtil.getPropString(request, "group_id");
        Map group = groupService.getGroup(groupId);
        if (group == null) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "集团商户不存在");
        }
        String objectName = BeanUtil.getPropString(group, Group.NAME);

        //权限是否需要变动
        long permissionOld = getGroupMspRefundPermission(request);
        int permission = BeanUtil.getPropInt(request, "permission_msp_refund");
        if (!SpecialAuthWhitelist.MSP_REFUND_PERMISSIONS.contains(permission)) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "permission_msp_refund参数不支持!");
        }
        if (permission == permissionOld) {
            throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "无效或重复操作!");
        }

        Map mspRefundPermissionOld = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, groupId
        );
        if (mspRefundPermissionOld != null) {
            mspRefundPermissionOld.put("object_name", objectName);
        }
        String mspRefundPermissionId = BeanUtil.getPropString(mspRefundPermissionOld, DaoConstants.ID);
        if (SpecialAuthWhitelist.MSP_REFUND_PERMISSION_CLOSE == permission && !StringUtil.empty(mspRefundPermissionId)) {
            specialAuthWhitelistService.deleteSpecialAuthWhitelist(mspRefundPermissionId);
        } else if (SpecialAuthWhitelist.MSP_REFUND_PERMISSION_OPEN == permission) {
            specialAuthWhitelistService.createSpecialAuthWhitelist(
                    CollectionUtil.hashMap(
                            SpecialAuthWhitelist.AUTH_TYPE, SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND,
                            SpecialAuthWhitelist.OBJECT_ID, groupId,
                            SpecialAuthWhitelist.OBJECT_TYPE, SpecialAuthWhitelist.OBJECT_TYPE_GROUP
                    )
            );
        }
        Map mspRefundPermissionNew = specialAuthWhitelistService.getSpecialAuthWhitelistsByAuthTypeAndObjectId(
                SpecialAuthWhitelist.AUTH_TYPE_MSP_REFUND, groupId
        );
        if (mspRefundPermissionNew != null) {
            mspRefundPermissionNew.put("object_name", objectName);
        }


        //保存业务日志
        businessLogService.sendBusinessLog(CollectionUtil.hashMap(
                BusinessLogUtil.LOG_PARAM_CHANGE_BEFORE, mspRefundPermissionOld,
                BusinessLogUtil.LOG_PARAM_CHANGE_AFTER, mspRefundPermissionNew,
                BusinessLogUtil.LOG_PARAM_REQUEST, HttpRequestUtil.getBusinessogRequest(),
                BusinessLogUtil.LOG_PARAM_OP_PARAMS, CollectionUtil.hashMap(
                        BizOpLog.BUSINESS_OBJECT_CODE, "special_auth_whitelist",
                        BizOpLog.OP_TYPE, BizOpLog.OP_TYPE_MODIFY,
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_TABLENAME, "special_auth_whitelist",
                        BusinessLogUtil.LOG_PARAM_OP_PARAMS_OBJECT_ID, "object_id",
                        BizOpLog.REMARK, "",
                        BizOpLog.BUSINESS_FUNCTION_CODE, "1000057"
                )
        ));
    }


    private void batchAddMerchantInvoiceConfigStatus(Map group, List<Map> merchants) {
        String groupId = MapUtils.getString(group, ConstantUtil.KEY_ID);
        Map groupConfig = merchantInvoiceService.getConfigByMerchantId(groupId);
        if(CollectionUtils.isEmpty(groupConfig)){
            return;
        }
        for (Map merchant : merchants) {
            merchant = merchantService.getMerchantBySn(MapUtils.getString(merchant, "merchant_sn"));
            if(CollectionUtils.isEmpty(merchant)){
               continue;
            }
            String merchantId = MapUtils.getString(merchant, ConstantUtil.KEY_ID);
            int groupStatus = MapUtils.getIntValue(groupConfig, MerchantInvoiceConfig.STATUS, MerchantInvoiceConfig.STATUS_CLOSED);
            int canBind = groupStatus == MerchantInvoiceConfig.STATUS_OPEN ? MerchantInvoiceConfig.IS_BIND_TRUE : MerchantInvoiceConfig.IS_BIND_FALSE;
            merchantInvoiceService.enableMerchant(CollectionUtil.hashMap(
                    MerchantInvoiceConfig.MERCHANT_ID, MapUtils.getString(merchant, ConstantUtil.KEY_ID),
                    MerchantInvoiceConfig.MERCHANT_TYPE, MerchantInvoiceConfig.MERCHANT_TYPE_SINGLE,
                    MerchantInvoiceConfig.GROUP_ID, groupId,
                    MerchantInvoiceConfig.IS_BIND, canBind,
                    MerchantInvoiceConfig.CREATOR_ID, getUserIdAndName()[0],
                    MerchantInvoiceConfig.CREATOR_NAME, getUserIdAndName()[1]
            ));
            merchantInvoiceService.synGroupConfig(merchantId);
        }

    }

    private void updateMerchantInvoiceConfigStatus(Map group, int invoiceWhiteList) {
        String groupId = MapUtils.getString(group, ConstantUtil.KEY_ID);
        if (invoiceWhiteList == 1) {
            merchantInvoiceService.enableMerchant(CollectionUtil.hashMap(
                    MerchantInvoiceConfig.MERCHANT_ID, groupId,
                    MerchantInvoiceConfig.MERCHANT_TYPE, MerchantInvoiceConfig.MERCHANT_TYPE_GROUP,
                    MerchantInvoiceConfig.CREATOR_ID, getUserIdAndName()[0],
                    MerchantInvoiceConfig.CREATOR_NAME, getUserIdAndName()[1]
            ));
        } else {
            merchantInvoiceService.disableMerchant(CollectionUtil.hashMap(
                    MerchantInvoiceConfig.MERCHANT_ID, groupId,
                    MerchantInvoiceConfig.MERCHANT_TYPE, MerchantInvoiceConfig.MERCHANT_TYPE_GROUP

            ));
        }
    }

    private void addInvoiceWhiteListToParam(Map group) {
        String groupId = MapUtils.getString(group, ConstantUtil.KEY_ID);
        Map invoiceConfig = merchantInvoiceService.getConfigByMerchantId(groupId);
        if (MapUtils.getIntValue(invoiceConfig, MerchantInvoiceConfig.STATUS, MerchantInvoiceConfig.STATUS_CLOSED) == MerchantInvoiceConfig.STATUS_CLOSED) {
            group.put("invoice_white_list", 0);
        } else {
            group.put("invoice_white_list", 1);
        }
    }



    private String[] getUserIdAndName() {
        HttpSession session = HttpRequestUtil.getSession();
        Object accountId = session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        Object username = session.getAttribute(CommonLoginService.SESSION_USERNAME);

        return new String[]{String.valueOf(accountId), String.valueOf(username)};
    }
}
