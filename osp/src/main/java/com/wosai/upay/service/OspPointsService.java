package com.wosai.upay.service;

import com.wosai.reward.entity.*;
import com.wosai.upay.common.bean.*;
import com.wosai.validation.validator.PropNotEmpty;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 积分服务接口透传
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/7.
 */
public interface OspPointsService {

    /**
     * 导入Excel，批量创建积分账户。
     *
     * @param file |cellphone|
     * @return {total: 2, failures: [{cellphone: "***********", code: 50001, msg: ""}, ...]}
     */
    Map batchImportAccounts(MultipartFile file, HttpServletRequest request);

    /**
     * 导出批量创建积分账户失败账号名单
     *
     * @param params
     * @param request
     * @param response
     */
    void exportFailedAccounts(Map params, HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 导入excel，批量增加积分。
     *
     * @param file |cellphone|points|
     * @return {total: 2, failures: [{cellphone: "***********", points: "100", code: 50001, msg: ""}, ...]}
     */
    Map batchImportAccountsToAddPoints(MultipartFile file, AccountChangePointsRequest.Expired expired, HttpServletRequest request);

    /**
     * 导出批量增加积分失败账号名单
     *
     * @param params
     * @param request
     * @param response
     */
    void exportFailedBatchAddPointsAccounts(Map params, HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * @param request {"id": "string"}
     * @return
     */
    Rule findRule(Map request);

    /**
     * 分页查询积分规则
     *
     * @param request
     * @returns
     */
    Page<Rule> findRules(Map request);

    /**
     * 新增／修改积分规则
     *
     * @param request
     * @return
     */
    Rule saveRule(Map request);

    /**
     * 激活积分规则
     *
     * @param request
     * @return
     */
    Boolean enableRule(Map request);

    /**
     * 禁用积分规则
     *
     * @param request
     * @return
     */
    Boolean disableRule(Map request);

    /**
     * 删除积分规则
     *
     * @param request
     * @return
     */
    Boolean deleteRule(Map request);

    /**
     * 查询积分详情
     *
     * @param request
     * @return
     */
    PointsResponse findPointsByCellphone(Map request);

    /**
     * 分页检索积分流水
     *
     * @param request
     * @return
     */
    Page<HistoryResponse> findHistories(Map request);

    /**
     * 查询积分对象标签（所在地、行业、支付源、角色...）
     *
     * @param request
     * @return
     */
    Set findTags(Map request);

    Map getUserInfo( Map<String, Object> requestMap);

    ListResult filterTransaction(Map<String, Object> requestMap);

    Map getAllTransactionEvent();

}
