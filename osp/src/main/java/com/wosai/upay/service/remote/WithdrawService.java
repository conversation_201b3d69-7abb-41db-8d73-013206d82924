package com.wosai.upay.service.remote;

import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * Created by kay on 16/11/2.
 */
public interface WithdrawService {
    /**
     * 创建提现请求
     * @param withdraw
     * @return
     */
    Map createWithdraw(Map withdraw);
    /**
     * 查询提现记录
     * @param pageInfo
     * @param queryFilter storeId storeName
     * @return
     */
    ListResult getDrawList(PageInfo pageInfo, Map queryFilter);

    /**
     * 查询提现子记录
     * @parem channelId
     * @param pageInfo
     * @param queryFilter storeId storeName
     * @return
     */
    ListResult getAllChildrenDrawList(String channelId, PageInfo pageInfo, Map queryFilter);

    Map getWithdraw(String withdrawId);

    /**
     * 更新提取审核信息
     * @param drawInfo
     */
    void updateWithdraw(Map drawInfo);

    /**
     *  更新提取审核信息
     * @param drawInfo
     * @param msg
     */
    void updateWithdrawAndPush2MQ(Map drawInfo, Map msg);

    /**
     * 更新提提现审核信息状态
     * @param withdrawId 提现id
     * @param opCheckStatus 审核状态
     * @param remark 备注
     */
    void updateWithdrawOpCheckStatus(String withdrawId, Long opCheckStatus, String remark);

    /**
     * 返回所有批次号
     * @return
     */
    List queryBatchNo();

    /**
     * 通过批次号,分页查询提现记录
     * @param pageInfo
     * @param query
     * @return
     */
    ListResult queryByConditions(PageInfo pageInfo, Map query);

    /**
     * 分页查询批次操作
     * @param pageInfo
     * @param batchNo
     * @return
     */
    ListResult queryBatchOperation(PageInfo pageInfo, String batchNo);

    /**
     * 为所传ids对应的记录生成批次号,更新入库并返回批次号
     * @param ids
     */
    String generatorBatchNo(List<String> ids);

    /**
     * 通过批次号查询批次操作
     * @param batchNo
     * @return
     */
    Map getBatchOperationByBatchNo(String batchNo);

    /**
     * 提现通知
     * @param withdrawId
     * @return
     */
    Boolean afterWithdrawNotice(String withdrawId);
    /**
     * 更新提提现审核信息状态
     * @param withdrawId 提现id
     * @param opCheckStatus 审核状态
     * @param remark 备注
     */
    Map changeWithDrawStatus(String withdrawId, Long opCheckStatus, String remark,  String operator);
    /**
     * 查询提现记录2.0
     * @param pageInfo
     * @param queryFilter storeId storeName
     * @return
     */
    ListResult getWithdrawList(PageInfo pageInfo, Map queryFilter);
    /**
     * 修改提现备注信息,并记录日志
     * @param drawInfo
     */
    void updateWithdrawRemark(Map drawInfo, Map userInfo);

    /**
     *
     * @param request
     *  clearing_delayed 清算是否超时 0:没超时； 1：超时
     *  need_to_compensate 是否需要赔付 0：不需要； 1： 需要
     *  compensation_status 赔付状态 0：赔付未开始； 1：赔付进行中；2：赔付完成
     *  remark 备注
     *  compensation_amount 赔付金额， 单位：分
     *  operator OSP 账号 username
     */
    void changeCompensationStatus(Map request);
}
