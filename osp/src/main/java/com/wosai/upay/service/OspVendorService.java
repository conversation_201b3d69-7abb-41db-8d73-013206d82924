package com.wosai.upay.service;


import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.validation.PropNotEmpty;

import java.util.Map;

/**
 * 接口定义，参数校验规则定义在接口方法上，校验失败的错误提示可以支持i18n，具体做法是在src/main/resources/ValidationMessages.properties资源文件中定义错误提示的键值对，然后在这里引用错误提示键。
 * 本地化的资源文件加后缀，例如ValidationMessages_zh_CN.properties。
 *
 * <AUTHOR>
 */
public interface OspVendorService {

    /**
     * 创建Vendor.
     *
     * @param request
     */
    Map createVendor(Map<String, Object> request);

    /**
     * 给Vendor授权限.
     * // TODO  next sprint
     *
     * @param request
     */
    void setVendorAuth(Map<String, Object> request);

    /**
     * 获取Vendor的权限.
     * // TODO  next sprint
     *
     * @param request id                  UUID
     */
    Map getVendorAuth(Map<String, Object> request);

    /**
     * 根据vendorId删除Vendor.
     *
     * @param request id                  UUID
     */
    void deleteVendor(Map<String, Object> request);

    /**
     * 根据vendorSn删除Vendor.
     *
     * @param request sn
     */
    void deleteVendorBySn(Map<String, Object> request);

    /**
     * 修改Vendor.
     *
     * @param request
     */
    Map updateVendor(Map<String, Object> request);

    /**
     * 根据vendorId获取Vendor.
     *
     * @param request id                  UUID
     * @return
     */
    Map getVendor(Map<String, Object> request);

    /**
     * 根据vendorSn获取Vendor.
     *
     * @param request sn
     * @return
     */
    Map getVendorBySn(Map<String, Object> request);

    /**
     * 根据vendorId禁用Vendor（调用此接口后不可以增加商户、激活设备）.
     * // TODO  next sprint
     *
     * @param request id                  UUID
     * @return
     */
    void disableVendor(Map<String, Object> request);

    /**
     * 根据vendorId启用Vendor（禁用状态的服务商可重新启用）.
     * // TODO  next sprint
     *
     * @param request id                  UUID
     * @return
     */
    void enableVendor(Map<String, Object> request);

    /**
     * 根据vendorId关闭Vendor（必须服务商下所有商户都关闭后才可以关闭服务商）.
     * // TODO  next sprint
     *
     * @param request id                  UUID
     * @return
     */
    void closeVendor(Map<String, Object> request);

    /**
     * 分页查询Vendor.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                name                名称
     *                sn                  服务商可见的编号
     *                status
     *                contact_phone       联系固定电话号码
     *                contact_cellphone   联系移动电话号码
     *                deleted
     * @return
     */
    ListResult findVendors(Map<String, Object> request);

    /**
     * 重置服务商vendorKey
     *
     * @param request sn
     * @return
     */
    String resetAppKey(Map<String, Object> request);

    /**
     * 获取服务商vendorKey
     *
     * @param request sn
     * @return
     */
    String getAppKey(Map<String, Object> request);

    /**
     * 创建VendorApp.
     * // TODO  next sprint
     *
     * @param request
     */
    Map createVendorApp(Map<String, Object> request);

    /**
     * 根据vendorAppId删除VendorApp.
     * // TODO  next sprint
     *
     * @param request id                  UUID
     */
    void deleteVendorApp(Map<String, Object> request);

    /**
     * 修改VendorApp.
     * // TODO  next sprint
     *
     * @param request
     */
    Map updateVendorApp(Map<String, Object> request);

    /**
     * 根据vendorAppId获取VendorApp.
     * // TODO  next sprint
     *
     * @param request id                  UUID
     * @return
     */
    Map getVendorApp(Map<String, Object> request);

    /**
     * 分页查询VendorApp.
     * // TODO  next sprint
     *
     * @param request pageInfo {}
     *                queryFilter {
     *                vendor_id           服务商id
     *                appid               应用编号
     *                name                应用名称
     *                type                产品类型
     *                }
     * @return
     */
    ListResult findVendorApps(Map<String, Object> request);

    /**
     * 创建VendorConfig.
     *
     * @param request
     */
    Map createVendorConfig(Map<String, Object> request);

    /**
     * 修改VendorConfig.
     *
     * @param request
     */
    Map updateVendorConfig(Map<String, Object> request);

    /**
     * 根据vendorId获取VendorConfig.
     *
     * @param request vendor_id
     * @return
     */
    Map getVendorConfigByVendorId(Map<String, Object> request);

    /**
     * 分页查询VendorConfig.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                vendor_id
     *                deleted
     * @return
     */
    ListResult findVendorConfigs(Map<String, Object> request);

    /**
     * 创建VendorDeveloper.
     *
     * @param request
     */
    Map createVendorDeveloper(Map<String, Object> request);

    /**
     * 修改VendorDeveloper.
     *
     * @param request
     */
    Map updateVendorDeveloper(Map<String, Object> request);

    /**
     * 根据vendorId获取VendorDeveloper.
     *
     * @param request vendor_id
     * @return
     */
    Map getVendorDeveloperByVendorId(Map<String, Object> request);

    /**
     * 分页查询VendorDeveloper.
     *
     * @param request page
     *                page_size
     *                date_start
     *                date_end
     *                order_by: [{field: ,order: }, {field: ,order: }]
     *                vendor_id
     *                vendor_sn
     *                deleted
     * @return
     */
    ListResult findVendorDevelopers(Map<String, Object> request);


    Map resetPassword(Map<String, Object> request);

}
