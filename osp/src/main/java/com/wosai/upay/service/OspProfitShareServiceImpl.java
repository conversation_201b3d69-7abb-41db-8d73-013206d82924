package com.wosai.upay.service;

import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.googlecode.jsonrpc4j.ProxyUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.profit.sharing.constant.CommonConstant;
import com.wosai.profit.sharing.model.Model;
import com.wosai.profit.sharing.model.SharingConfig;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.service.remote.RemoteApplicationService;
import com.wosai.upay.service.remote.RemoteModelService;
import com.wosai.upay.service.remote.RemoteSharingConfigService;
import com.wosai.web.api.ListResult;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Service
public class OspProfitShareServiceImpl implements OspProfitShareService, InitializingBean {

    private RemoteApplicationService applicationService;

    private RemoteModelService modelService;

    private RemoteSharingConfigService sharingConfigService;


    @Value("${jsonrpc.profit-share}")
    private String profitShareUrl;

    @Override
    public ListResult getModelList(Map map) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put(Model.ID, BeanUtil.getPropString(map, Model.ID));
        conditions.put(Model.NAME, BeanUtil.getPropString(map, Model.NAME));
        conditions.put(Model.APPLICATION_ID, BeanUtil.getPropString(map, Model.APPLICATION_ID));
        if (map.containsKey(Model.TYPE)) {
            conditions.put(Model.TYPE, BeanUtil.getPropInt(map, Model.TYPE, -1));
        }
        conditions = filterCondition(conditions);
        return modelService.queryModels(conditions, getPagination(map));
    }

    @Override
    public Map getModelDetail(Map map) {
        Map<String, Object> modelMap = modelService.getModel(BeanUtil.getPropString(map, DaoConstants.ID));
        modelMap.put("application", applicationService.getApplication(BeanUtil.getPropString(modelMap, Model.APPLICATION_ID)));
        return modelMap;
    }

    @Override
    public Map<String,Object> getMerchantShareConfig(Map map) {
        String merchantId = BeanUtil.getPropString(map, SharingConfig.MERCHANT_ID);
        Map<String,Object> sharingConfig = sharingConfigService.getSharingConfigByMerchantId(merchantId);
        if(sharingConfig == null || sharingConfig.isEmpty()){
            sharingConfig = new HashMap<>();
            sharingConfig.put(SharingConfig.ID,merchantId);
            sharingConfig.put(SharingConfig.STATUS,SharingConfig.STATUS_DISABLED);
        }
        return sharingConfig;
    }

    @Override
    public Map<String, Object> updateMerchantShareConfig(Map map) {
        String merchantId = BeanUtil.getPropString(map, SharingConfig.MERCHANT_ID);
        SharingConfig sharingConfig = new SharingConfig();
        sharingConfig.setMerchantId(merchantId);
        if(map.containsKey(SharingConfig.STATUS)){
            sharingConfig.setStatus(BeanUtil.getPropInt(map,SharingConfig.STATUS));
        }
        if(map.containsKey(SharingConfig.MODEL_ID)){
            sharingConfig.setModelId(BeanUtil.getPropString(map,SharingConfig.MODEL_ID));
        }
        return sharingConfigService.updateSharingConfigByMerchantId(sharingConfig);
    }



    private Map<String,Object> getPagination(Map map) {
        Map<String,Object> pageInfo = new HashMap<>();
        pageInfo.put(CommonConstant.PAGE,BeanUtil.getPropInt(map, CommonConstant.PAGE, 1));
        pageInfo.put(CommonConstant.PAGE_SIZE,BeanUtil.getPropInt(map, CommonConstant.PAGE_SIZE, 10));
        if (map.containsKey("start_time")) {
            pageInfo.put("start_timestamp",BeanUtil.getPropLong(map, "start_time"));
        }
        if (map.containsKey("end_time")) {
            pageInfo.put("end_timestamp",BeanUtil.getPropLong(map, "end_time"));
        }
        pageInfo.put("order_by", Collections.singletonList(new HashMap<String, Object>() {{
            put("field", "ctime");
            put("order", "-1");
        }}));
        return pageInfo;
    }

    private Map<String,Object> filterCondition(Map<String,Object> condition){
        Map<String,Object> result = new HashMap<>();
        for (Map.Entry<String,Object> entry:condition.entrySet()){
            if(entry.getValue() !=null){
                result.put(entry.getKey(),entry.getValue());
            }
        }
        return result;
    }

    @Override
    public void afterPropertiesSet() {
        initApplicationClient();
        initModelClient();
        initRemoteSharingConfigClient();
    }

    private void initApplicationClient(){
        JsonRpcHttpClient client;
        try {
            client = new JsonRpcHttpClient(new URL(profitShareUrl + "rpc/application"));
        } catch (MalformedURLException e) {
            throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        applicationService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                RemoteApplicationService.class,
                client);
    }

    private void initModelClient(){
        JsonRpcHttpClient client;
        try {
            client = new JsonRpcHttpClient(new URL(profitShareUrl + "rpc/model"));
        } catch (MalformedURLException e) {
            throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        modelService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                RemoteModelService.class,
                client);
    }

    private void initRemoteSharingConfigClient(){
        JsonRpcHttpClient client;
        try {
            client = new JsonRpcHttpClient(new URL(profitShareUrl + "rpc/sharingConfig"));
        } catch (MalformedURLException e) {
            throw  new UpayException(UpayException.CODE_UNKNOWN_ERROR, "系统错误", e);
        }
        sharingConfigService = ProxyUtil.createClientProxy(
                getClass().getClassLoader(),
                RemoteSharingConfigService.class,
                client);
    }

}
