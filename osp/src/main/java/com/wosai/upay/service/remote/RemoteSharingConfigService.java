package com.wosai.upay.service.remote;

import com.wosai.profit.sharing.model.SharingConfig;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/8/21.
 */
public interface RemoteSharingConfigService {

    /**
     * 新建商户分账配置
     * @param sharingConfig
     *  merchant_id
     *  model_id
     * @return 商户分账配置
     */
    Map<String,Object> createSharingConfig(SharingConfig sharingConfig);

    /**
     * 修改商户分账配置
     * @param sharingConfig
     *  id
     *  merchant_id
     *  model_id
     * @return 商户分账配置
     */
    Map<String,Object> updateSharingConfig(SharingConfig sharingConfig);

    /**
     * 修改商户分账配置
     * @param sharingConfig
     *  id
     *  merchant_id
     *  model_id
     * @return 商户分账配置
     */
    Map<String,Object> updateSharingConfigByMerchantId(SharingConfig sharingConfig);

    /**
     * 获取商户分账配置信息
     * @param id
     * @return 商户分账配置
     */
    Map<String,Object> getSharingConfig(String id);

    /**
     * 根据商户id获取商户分账配置信息
     * @param merchantId 商户id
     * @return
     */
    Map<String,Object> getSharingConfigByMerchantId(String merchantId);
}
