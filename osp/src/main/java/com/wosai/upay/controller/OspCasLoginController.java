package com.wosai.upay.controller;

import com.wosai.janus.api.cas.model.LogoutRequest;
import com.wosai.upay.service.OspCasService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Controller
@RequestMapping
public class OspCasLoginController {

    @Autowired
    private OspCasService ospCasService;
    @Value("${osp.server.url}")
    private String ospServerUrl;

    @ResponseBody
    @RequestMapping(value = "/cas/login", method = RequestMethod.GET)
    public void loginRedirect(HttpServletResponse response) {
        ospCasService.login(response);
    }

    @ResponseBody
    @RequestMapping(value = "/cas/logout", method = RequestMethod.GET)
    public void logoutRedirect(HttpServletRequest request, HttpServletResponse response) {
        ospCasService.logout(request, response);
    }

    @RequestMapping(value = "/", method = RequestMethod.GET)
    public void loginCallback(HttpServletRequest request, HttpServletResponse response, @RequestParam(required = false) String ticket) {
        ospCasService.validate(request, response, ticket);
    }

    @ResponseBody
    @RequestMapping(value = "/redirect", method = RequestMethod.GET)
    public void redirect(HttpServletRequest request, HttpServletResponse response) throws IOException {
        ospCasService.redirect(request, response);
    }

    @ResponseStatus(value = HttpStatus.OK)
    @RequestMapping(value = "/cas/logout", method = RequestMethod.POST)
    public void logoutCallback(@RequestBody LogoutRequest logoutRequest) {
        if (logoutRequest != null
                && logoutRequest.getAttributes() != null
                && logoutRequest.getAttributes().getUid() != null) {
            ospCasService.invalidate(logoutRequest.getAttributes().getUid());
        }
    }

}
