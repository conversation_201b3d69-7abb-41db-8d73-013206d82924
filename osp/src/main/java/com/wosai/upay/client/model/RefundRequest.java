package com.wosai.upay.client.model;


import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR> Date: 2019-10-18 Time: 11:41
 */
public class RefundRequest {

    @JSONField(name = "terminal_sn")
    private String terminalSn;
    private String sn;
    @JSONField(name = "refund_request_no")
    private String refundRequestNo;
    @JSONField(name = "refund_amount")
    private String refundAmount;
    private String operator;
    private String reflect;

    public String getTerminalSn() {
        return terminalSn;
    }

    public void setTerminalSn(String terminalSn) {
        this.terminalSn = terminalSn;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getRefundRequestNo() {
        return refundRequestNo;
    }

    public void setRefundRequestNo(String refundRequestNo) {
        this.refundRequestNo = refundRequestNo;
    }

    public String getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getReflect() {
        return reflect;
    }

    public void setReflect(String reflect) {
        this.reflect = reflect;
    }
}
