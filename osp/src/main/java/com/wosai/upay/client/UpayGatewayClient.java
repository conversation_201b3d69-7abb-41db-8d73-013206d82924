package com.wosai.upay.client;

import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.client.model.ReconciliationRequest;
import com.wosai.upay.client.model.RefundRequest;
import com.wosai.upay.util.HttpClientUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> Date: 2019-10-17 Time: 11:06
 */
@Component
public class UpayGatewayClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpayGatewayClient.class);
    private static final String SLASH = "/";
    private static final String REFUND_PATH = "/upay/v2/refund";
    private static final String RECONCILIATION_PATH = "/upay/v2/fix";

    private static final String KEY_RESULT_CODE = "result_code";
    private static final String KEY_INNER_RESULT_CODE = "biz_response.result_code";
    private static final String SUCCESS_RESULT_CODE = "200";
    private static final String REFUND_SUCCESS_INNER_RESULT_CODE = "REFUND_SUCCESS";
    private static final String RECONCILIATION_SUCCESS_INNER_RESULT_CODE = "SUCCESS";


    @Value("${upay.gateway.server}")
    private String gatewayHost;

    public boolean refund(RefundRequest refundRequest) {

        String result;
        try {
            LOGGER.info("【调用支付网关退款接口】调用入参: {}", JSON.toJSONString(refundRequest));
            result = HttpClientUtil.getPostResponseString(buildPath(gatewayHost, REFUND_PATH)
                    , JSON.toJSONString(refundRequest));
            LOGGER.info("【调用支付网关退款接口】调用出参: {}", result);
        } catch (Exception e) {
            LOGGER.error("【调用支付网关退款接口】调用异常，异常栈: ", e);
            return false;
        }
        return buildResult(result, REFUND_SUCCESS_INNER_RESULT_CODE);
    }

    public boolean reconciliation(ReconciliationRequest reconciliationRequest) {

        String result;
        try {
            LOGGER.info("【调用支付网关勾兑接口】调用入参: {}", JSON.toJSONString(reconciliationRequest));
            result = HttpClientUtil.getPostResponseString(buildPath(gatewayHost, RECONCILIATION_PATH)
                    , JSON.toJSONString(reconciliationRequest));
            LOGGER.info("【调用支付网关勾兑接口】调用出参: {}", result);
        } catch (Exception e) {
            LOGGER.error("【调用支付网关勾兑接口】调用异常, 异常栈: ", e);
            return false;
        }
        return buildResult(result, RECONCILIATION_SUCCESS_INNER_RESULT_CODE);
    }


    private boolean buildResult(String result, String code) {
        if (StringUtils.isBlank(result)) {
            return false;
        }

        Map<String, Object> resultJson = JSON.parseObject(result);
        String resultCode = MapUtil.getString(resultJson, KEY_RESULT_CODE);
        String innerResultCode = (String) BeanUtil.getNestedProperty(resultJson, KEY_INNER_RESULT_CODE);
        return SUCCESS_RESULT_CODE.equals(resultCode) && code.equals(innerResultCode);
    }

    private String buildPath(String host, String path) {

        return StringUtils.removeEnd(host, SLASH) + path;
    }



}
