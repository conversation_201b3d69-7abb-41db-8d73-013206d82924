package com.wosai.upay.bean;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/1/25/025.
 */
public enum OpCheckStatus {
    REVIEW(1, "提现审核中"),
    REJECT(2, "提现驳回"),
    FREEZE(3, "提现冻结"),
    PASS_WAIT(4, "运营审核通过，待打款"),
    WAIT_RESULT(5, "等待打款结果"),
    SUCCESS(6, "打款成功"),
    FAIL(7, "打款失败"),
    HUMAN_PAY(8, "人工打款成功"),
    WITHDRAW_EXCEPTION(9, "运营审核通过，待打款"),
    UNKNOWN(100, "");

    private int code;
    private String desc;

    OpCheckStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static OpCheckStatus getStatus(int code) {
        for (OpCheckStatus status : OpCheckStatus.values()) {
            if (code == status.getCode()) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
