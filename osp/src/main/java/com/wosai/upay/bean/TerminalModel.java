package com.wosai.upay.bean;

import com.wosai.mpay.util.StringUtils;

/**
 * @Auther: hrx
 * @Date: 2019-06-21
 * @Description: 收款终端类型映射
 * @version: 1.0
 */
public enum TerminalModel {

    POS_ORDINARY("2016090200000007", "传统POS"),

    POS_LKL("2018031300000576", "拉卡拉"),

    POS_A920("2016081800000003", "A920"),

    POS_A910("2019010700001242", "A910"),

    POS_A930("2019010700001243", "A920"),

    POS_T1("2019010700001244", "T1"),

    POS_A8("2019010700001245", "A8"),

    POS_V1("2017020600000069", "智能POS/V1"),

    SWEEP_CODE_1("2016050300000001", "扫码王1"),

    SWEEP_CODE_ENHANCE("2017110200000406", "派派小盒|增强版"),

    SWEEP_CODE_QR68("2018033100000614", "QR68|百富"),

    SWEEP_CODE_QM500("2018050400000669", "QM500|联迪"),

    SWEEP_CODE_QM800("2018070400000806", "QM800|联迪"),

    SWEEP_CODE_ME50C("2018051400000698", "ME50C|新大陆"),

    SWEEP_CODE_C2("2018051000000691", "C2|华智融"),

    SWEEP_CODE_SL51("2018050700000676", "SL51|波普安创"),

    SWEEP_CODE_Q50("2018051400000699", "Q50|升腾"),

    SWEEP_CODE_81G("2018051400000700", "81G|云码智能"),

    SWEEP_CODE_QP33("2018051400000701", "QP33|新国都"),

    SWEEP_CODE_FR010("2018052200000725", "FR010|商米"),

    CASHIER_PLUG_PC("2017030600000083", "PC版插件"),

    CASHIER_PLUG_ANDRROID("2018041000000638", "安卓版插件"),

    BUFFET_ORDER_CODE("2018111100000001", "自助点餐码-餐台码"),

    JJZ_CODE("2019012900001333", "久久折"),

    QR_CODE("2019053000001597", "普通收款码"),

    MERCHANT_QR_CODE("2019053100001606", "收款码牌定制版"),

    IOT_CODE("2019053100001602", "收钱音箱收款码"),

    MAPAI_CODE("2019071800001840", "收款码牌"),

    PMMA_CODE("2019072500001880", "收款码PMMA"),

    ZHUOTIE_CODE("2019072500001878", "收款码桌贴"),

    TIEZHI_CODE("2019072500001879", "收款码贴纸"),

    APP("2016111100000033", "收钱吧APP");


    private String code;
    private String desc;

    TerminalModel(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String code) {
        if (StringUtils.isEmpty(code)) {
            return "其他";
        }
        for (TerminalModel model : TerminalModel.values()) {
            if (code.equals(model.getCode())) {
                return model.getDesc();
            }
        }
        return "其他";
    }
}
