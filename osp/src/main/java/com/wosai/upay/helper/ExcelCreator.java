package com.wosai.upay.helper;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.bean.OpCheckStatus;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.upay.Withdraw;
import com.wosai.upay.util.DateTimeUtil;
import com.wosai.upay.util.SheetUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by chenyu on 2018/1/25.
 */
@SuppressWarnings("unchecked")
public class ExcelCreator {

    /**
     * 提现记录导出
     *
     * @param withdraws
     * @return
     */
    public static HSSFWorkbook exportWithdrawsExcel(List<Map> withdraws,boolean isEncrypt) throws Exception {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("提现数据");
        SheetUtil sheetUtil = new SheetUtil(sheet);
        List<String> headerArr = Arrays.asList(
                "提现时间", "商户号", "商户名", "收单机构号",
                "收款人","开户银行", "收款账户", "提现状态",
                "提现金额","提现方式", "提现手续费", "备注","请求流水号","收单机构","打款成功时间"
        );
        sheetUtil.appendRow(headerArr);
        if(CollectionUtils.isEmpty(withdraws)){
            return workbook;
        }

        List<String> columnArr = Arrays.asList(
                ConstantUtil.KEY_CTIME, ConstantUtil.KEY_MERCHANT_SN, ConstantUtil.KEY_MERCHANT_NAME, "provider_mch_id",
                "card_holder","bank_name","card_no", "op_check_status",
                Withdraw.AMOUNT, Merchant.WITHDRAW_MODE,"service_charge", "remark", com.wosai.upay.clearance.model.Withdraw.ORDER_SN,
                com.wosai.upay.clearance.model.Withdraw.PROVIDER,
                com.wosai.upay.clearance.model.Withdraw.CLEARING_SUCCESS_TIME
        );

        for (Map withdraw : withdraws) {
            List values = new ArrayList();
            withdraw.put(ConstantUtil.KEY_CTIME, DateFormatUtils.format(MapUtils.getLongValue(withdraw, ConstantUtil.KEY_CTIME), "yyyy-MM-dd HH:mm:ss"));
            String cardNo = MapUtils.getString(withdraw, "card_no");
            int opCheckStatus = MapUtils.getIntValue(withdraw, "op_check_status");
            int type = MapUtils.getIntValue(withdraw, "type");
            int mode = MapUtils.getIntValue(withdraw, Merchant.WITHDRAW_MODE);
            if(isEncrypt){
                if (StringUtils.isNotBlank(cardNo) && cardNo.length() > 8) {
                    cardNo = cardNo.substring(0, 4) + getFixNumberString("*", cardNo.length() - 8) + cardNo.substring(cardNo.length() - 4, cardNo.length());
                }
            }
            withdraw.put("card_no", cardNo);
            String amount = new BigDecimal(MapUtils.getLongValue(withdraw, Withdraw.AMOUNT))
                    .divide(new BigDecimal("100")).toString();
            withdraw.put(Withdraw.AMOUNT, amount);
            withdraw.put("op_check_status", OpCheckStatus.getStatus(opCheckStatus).getDesc());
            //        mode type
            //标准提现  1  0
            //智能提现  2  0
            //D0 提现  1  1
            String withdrawMode = mode + "" + type;
            switch (withdrawMode) {
                case "10":
                    withdrawMode = "标准提现";
                    break;
                case "11":
                    withdrawMode = "D0提现";
                    break;
                case "20":
                    withdrawMode = "智能提现";
                    break;
                case "30":
                    withdrawMode = "强制结算";
                    break;
                default:
                    withdrawMode = "";
            }
            withdraw.put(Merchant.WITHDRAW_MODE, withdrawMode);
            int provider = BeanUtil.getPropInt(withdraw, com.wosai.upay.clearance.model.Withdraw.PROVIDER);
            if(provider == com.wosai.upay.clearance.model.Withdraw.PROVIDER_LAKALA){
                withdraw.put(com.wosai.upay.clearance.model.Withdraw.PROVIDER,"拉卡拉");
            }else if(provider == com.wosai.upay.clearance.model.Withdraw.PROVIDER_TL){
                withdraw.put(com.wosai.upay.clearance.model.Withdraw.PROVIDER,"通联");
            }else{
                withdraw.put(com.wosai.upay.clearance.model.Withdraw.PROVIDER,"");
            }
            long clearingSuccessTime = BeanUtil.getPropLong(withdraw,com.wosai.upay.clearance.model.Withdraw.CLEARING_SUCCESS_TIME,0);
            if(clearingSuccessTime == 0){
                withdraw.put(com.wosai.upay.clearance.model.Withdraw.CLEARING_SUCCESS_TIME,"");
            }else{
                withdraw.put(com.wosai.upay.clearance.model.Withdraw.CLEARING_SUCCESS_TIME, DateFormatUtils.format(clearingSuccessTime, "yyyy-MM-dd HH:mm:ss"));
            }
            for (String column : columnArr) {
                values.add(BeanUtil.getPropString(withdraw, column));
            }
            sheetUtil.appendRow(values);
        }
        return workbook;
    }

    private static String getFixNumberString(String str, int number) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < number; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
