package com.wosai.upay.helper;

import com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by chenyu on 2018/1/22.
 */
public class CommonUtil {

    public static Map<String, Map> convert(List<Map> data, String key) {
        Map<String, Map> result = new HashMap<>();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }

        for (Map map : data) {
            result.put(BeanUtil.getPropString(map, key), map);
        }
        return result;
    }

    public static List convertToList(Object object) {
        if (object instanceof Collection) {
            Collection collection = (Collection) object;
            return Lists.newArrayList(collection);
        }
        return Collections.EMPTY_LIST;
    }

    public static Map retain(Map map, List<String> retainColumns) {
        Map result = new HashMap();
        if (CollectionUtils.isEmpty(map) || CollectionUtils.isEmpty(retainColumns)) {
            return result;
        }
        for (String key : retainColumns) {
            if (map.containsKey(key)) {
                result.put(key, MapUtils.getObject(map, key));
            }
        }
        return result;
    }

    public static List<String> getContainKeys(Map data, List<String> keys) {
        if (CollectionUtils.isEmpty(data) || CollectionUtils.isEmpty(keys)) {
            return Collections.EMPTY_LIST;
        }
        List<String> result = new ArrayList<>();
        for (String key : keys) {
            if (data.containsKey(key)) {
                result.add(key);
            }
        }
        return result;
    }

    public static List<String> getValues(Map data, List<String> keys) {
        if (CollectionUtils.isEmpty(data) || CollectionUtils.isEmpty(keys)) {
            return Collections.EMPTY_LIST;
        }
        List<String> values = new ArrayList<>();
        for (String key : keys) {
            if (data.containsKey(key)) {
                values.add(MapUtils.getString(data, key));
            }
        }
        return values;
    }

    public static List getValues(List<Map> data, String key) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.EMPTY_LIST;
        }
        List result = new ArrayList();
        data.stream().forEach(item -> result.add(MapUtils.getObject(item, key)));
        return result;
    }

    public static List<Map> convertToListMap(Map<String,Map> object) {
        if(CollectionUtils.isEmpty(object)){
            return Collections.EMPTY_LIST;
        }
        List<Map> result = new ArrayList<>();
        for (String key:object.keySet()){
            result.add(object.get(key));
        }
        return result;
    }

    public static List<Map> sort(List<Map> data,String key) {
        if(CollectionUtils.isEmpty(data)){
            return data;
        }

        return data.stream()
                .sorted((o1, o2)-> MapUtils.getIntValue(o1,key)-MapUtils.getIntValue(o2,key))
                .collect(Collectors.toList());
    }

    public static String divide100(long amount) {
        return new BigDecimal(amount)
                .setScale(2, BigDecimal.ROUND_HALF_UP)
                .divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP)
                .toString();
    }
    public static boolean existBlankStr(String ...strings) {
       for (String string : strings) {
            if (StringUtils.isBlank(string)) {
                return true;
            }
        }
        return false;
    }


    /**
     *
     * 功能描述:  费率为0.00到2.00
     * @auther: lishuangqiang
     * @date: 2019/8/13
     * @param: [str]
     * @return: boolean
     */
    public static boolean isUpToStandard(String str) {
        Pattern pattern = Pattern.compile("^(?:[01]\\.\\d{0,2})|(?:2\\.00)$|[0-2]");
        Matcher match = pattern.matcher(str);
        return match.matches();
    }
}
