package com.wosai.upay.constant;

import com.wosai.upay.service.OspUserLoginService;

import java.util.Arrays;
import java.util.List;

public class WithdrawConstant {

    public static final String PROVIDER_MCH_ID = "provider_mch_id";

    public static final String BATCH_NO = "batch_no";

    public static final String CDATE = "cdate";

    public static final String AMOUNT = "amount";

    public static final String REMARK = "remark";

    public static final List<String> EXPORT_WITHDRAW_ROLE_LIST = Arrays.asList(
            OspUserLoginService.ROLE_WR, OspUserLoginService.ROLE_CUSTOMER_MANAGE, OspUserLoginService.ROLE_WITHDRAW_VERIFY);

    public static final List<String> BATCH_CUT_TRANSACTION_PARAMS_ROLE_LIST = Arrays.asList(OspUserLoginService.ROLE_CUSTOMER_MANAGE,
            OspUserLoginService.ROLE_COMMON_APPROVAL_CREATE,OspUserLoginService.ROLE_COMMON_APPROVAL_HANDLE,
            OspUserLoginService.ROLE_OPERATE_BUSINESS_SUPPORT_SPECIALIST,OspUserLoginService.ROLE_OPERATE_LIQUIDATION_COMMISSIONER,OspUserLoginService.ROLE_OPERATE_SYSTEM_ADMIN,
            OspUserLoginService.ROLE_RISK_MANAGEMEN_COMMISSIONER,OspUserLoginService.ROLE_RISK_MANAGEMEN_MANAGEMENT,OspUserLoginService.ROLE_PRODUCT_PAYMENT_BUSINESS);




}
