#core-business
jsonrpc.core-business.server=http://upay-v2-internal.shouqianba.com:9803/

#bank-info-service
jsonrpc.bank-info-service.server=http://bank-info-service.internal.shouqianba.com/

#merchant-audit-service
jsonrpc.merchant-audit.server=http://merchant-audit-service.internal.shouqianba.com/

#user-service
jsonrpc.user-service.server=http://user-service.internal.shouqianba.com/

jsonrpc.upay-wallet.server=http://upay-v2-internal.shouqianba.com:9802/

#app-backend-api
jsonrpc.app-backend-api.server=http://internal.backstage.shouqianba.com/

#app-gated-api
jsonrpc.app-gated-api.server=http://app-gated-internal.shouqianba.com/

#shouqianba-withdraw-service
jsonrpc.shouqianba-withdraw-service.server=http://withdraw-service.internal.shouqianba.com/

#business-log-service
jsonrpc.bizlog.server = http://business-log.internal.shouqianba.com/

#business-audit-api
jsonrpc.bizaudit.server = http://business-audit.internal.shouqianba.com/

#sales-system-backend-api
jsonrpc.sales-system-backend-api.server=http://sales-internal.shouqianba.com/

#shouqianba-merchant-api
jsonrpc.shouqianba-merchant-api.server=http://merchant.internal.shouqianba.com/

#sales-system-service-api
jsonrpc.sales-system-service-api.server=http://sales-service-internal.shouqianba.com/

#shouqianba-risk-service-api
jsonrpc.shouqianba-risk-service-api.server=http://internal.risk.shouqianba.com/

#app-push-service
jsonrpc.app-push-service.server=http://internal.app.push.shouqianba.com/

#upay-transaction-api
jsonrpc.upay-transaction-api-querry.server=http://upay-transaction-query.internal.shouqianba.com/
jsonrpc.upay-transaction-api-export.server=http://upay-transaction-export.internal.shouqianba.com/

#upay-side-api
jsonrpc.upay-side-api.server=http://upay-side.internal.shouqianba.com/

#alipay-authinto-api
jsonrpc.alipay-authinto-api.server=http://alipay.authinto.shouqianba.com/

#alipay-risk-api
jsonrpc.alipay-risk-api.server=http://internal.alipay-risk.shouqianba.com

#upay-finance-api
jsonrpc.upay-finance-api.server=http://finance-backend.internal.shouqianba.com/

#sp-user-service
jsonrpc.sp-user-service.server=http://sp-user-service-internal.shouqianba.com/

#janus
jsonrpc.cas-server.server=http://cas-internal.shouqianba.com/

#sms host\u77ED\u4FE1\u7F51\u5173\u53D1\u9001\u5730\u5740
sms.host=http://sms-api.wosai.cn:8200/sms/send

#backend.upay.server
backend.upay.server=http://backend-portal.shouqianba.com/

#upay.gateway.server
upay.gateway.server=http://upay-v2-internal.shouqianba.com:9801/

#jsonrpc.merchant-level.server
jsonrpc.merchant-level.server=http://merchant-level.internal.shouqianba.com/
jsonrpc.iot.server=http://shouqianba-iot-service-internal.shouqianba.com/

#remit-gateway
jsonrpc.remit.server=http://remit-gateway.internal.shouqianba.com/

#upay-swipe.server
jsonprc.upay-swipe-api.server=http://upay-swipe.internal.shouqianba.com/

# ip discern server
jsonrpc.sales-system-poi.server=http://sales-system-poi-internal.shouqianba.com/

#sales-system-push.server
jsonprc.sales-system-push.server=http://http://sales-push-internal.shouqianba.com/

#mail
mail.host=smtp.mxhichina.com
mail.port=465
mail.protocal=smtp
mail.username=<EMAIL>
mail.password=Test2018
mail.auth=true
mail.timeout=5000
mail.ssl.enable=true

#notification.mail.order.sendto
notification.mail.order.sendto=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

#redis
redis.url=r-bp1dd0926c175fe4.redis.rds.aliyuncs.com
redis.port=6379
redis.database=1
redis.password=UWME#nYmYikHIjFK
#cacheService\u7F13\u5B58\u6570\u636E\u7684\u8FC7\u671F\u65F6\u95F4\uFF0C\u5355\u4F4D\u6BEB\u79D2
redis.cacheService.expiredTime=86400
#cacheService\u7F13\u5B58\u6570\u636Ekey\u7684\u524D\u7F00
redis.cacheService.keyPrefix=mini_info

#cas config
cas.server.url=https://cas.shouqianba.com/
osp.server.url=https://spa-legacy-api.shouqianba.com
osp.client.url=https://spa-legacy.shouqianba.com

#osp oauth_login config
cronut_url=http://cronut.shouqianba.com/
osp.authorize_url=http://cronut.shouqianba.com/api/authorize
osp.token_url=http://cronut.shouqianba.com/api/access_token
osp.userInfo_url=http://cronut.shouqianba.com/api/get_info
#osp.client_id=295d1264-6772-4762-8868-7c0521b10060
osp.client_id=f2479854-9cf3-452d-a052-528e97ade18b
osp.response_type=code
#osp.redirect_uri=http://localhost:9204/osp/api/oauth/callBack
osp.redirect_uri=https://web-platforms-osp.shouqianba.com/api/oauth/callBack
osp.grant_type=authorization_code
osp.client_secret=osp_key

#oAuth\u767B\u5F55\u6210\u529F\u540E \u91CD\u5B9A\u5411\u5230\u4E3B\u9875
homeUrl=http://sp.shouqianba.com


#marketing-service
api.shouqianba.server = http://api.internal.shouqianba.com/

jsonrpc.upay-qrcode.server=http://upay-qrcode-internal.shouqianba.com/

##Tracing
bootstrap.servers=*************:9092,*************:9092,************:9092
spring.application.name=osp
samplerRate=1.0f

#kafka
kafka.bootstrap.servers=************:9092,************:9092,*************:9092,*************:9092,*************:9092
kafka.acks=1
kafka.retries=3
kafka.linger.ms=500
kafka.batch.size=5
kafka.max.block.ms=1000
kafka.registry.url=http://************:8081,http://************:8081,http://*************:8081,http://*************:8081,http://*************:8081

#aop notice config
aop.notice.product.id.risk=dcb167d5-873c-4718-8145-e40220e5c382
aop.notice.terminal.id.app=feebe00e-c4a4-11e9-846e-7cd30aeb7366

# reward-service
jsonrpc.reward.server=http://172.16.1.80/
jsonrpc.opr-point.server=http://opr-point.internal.shouqianba.com/

jsonrpc.notice-service.server=http://notice-service-single.internal.shouqianba.com

#opr-merchant-activity
jsonrpc.opr-merchant-activity.server=http://opr-merchant-activity.internal.shouqianba.com/
#merchant-card
jsonrpc.merchant-card.server=http://merchant-card.internal.shouqianba.com/
#opr-merchant-tool
jsonrpc.merchant-tool.server=http://opr-merchant-tool.internal.shouqianba.com/
#sales-system-profit-api
jsonrpc.profit-api.server=http://sales-profit-internal.shouqianba.com/
#wosai-preorder-risk
jsonrpc.preorder-risk.server=http://wosai-preorder-risk.internal.shouqianba.com/
#merchant-contract-api
jsonrpc.merchant-contract-api.server = http://merchant-contract-internal.shouqianba.com/

#clearance-service
jsonrpc.clearance-service-api.server = http://clearance-service.internal.shouqianba.com/

#upay-insurance-api
jsonrpc.upay-insurance-api.server=http://insurance-backend.internal.shouqianba.com/
#upay-activity-api
jsonrpc.upay-activity-api.server=http://upay-activity-internal.shouqianba.com/
#ecards-api.server
jsonrpc.ecards-api.server=http://ecards-internal.shouqianba.com/
#statistics_backend service
jsonrpc.statistics-backend.service=http://statistics-backend-internal.shouqianba.com/
jsonrpc.sales-addin.server=http://sales-addin-internal.shouqianba.com/
jsonrpc.app-config.server=http://app-config-service.internal.shouqianba.com/


#upay-loan-api
jsonrpc.upay-loan-api.server=http://loan-backend.internal.shouqianba.com/

#upay-credit-pay-api
jsonrpc.upay-credit-pay-api.server=http://credit-pay-backend.internal.shouqianba.com/

#opr-task-backend
jsonrpc.opr-task-backend.server=http://opr-task-backend.internal.shouqianba.com/

jsonrpc.merchant-contract-job.server =http://merchant-contract-job.internal.shouqianba.com

jsonrpc.terminal-sales.server=http://shouqianba-terminal-service.internal.shouqianba.com/

com.wosai.oss.static-bucket=wosai-statics
com.wosai.oss.static-base-url=https://statics.wosaimg.com
com.wosai.oss.internal=true
jsonrpc.merchant-bank.server=http://merchant-bank-service.internal.shouqianba.com/

#aop-backend-notification-api
jsonprc.aop-backend-notification-api.server=http://aop-backend-notification.internal.shouqianba.com/

#merchant-invoice-api
jsonrpc.merchant-invoice-api.server=http://merchant-invoice.internal.shouqianba.com/

#oss
oss.base-url=http://wosai-statics.oss-cn-hangzhou.aliyuncs.com/

jsonrpc.profit-share=http://profit-sharing.internal.shouqianba.com/

#upay-grayscale
jsonrpc.upay-grayscale.server=http://upay-grayscale.internal.shouqianba.com/

jsonrpc.sp-workflow-service.server=http://sp-workflow-service.internal.shouqianba.com

#trade-manage-service
jsonrpc.trade-manage-service.server=http://trade-manage-service.internal.shouqianba.com/

#merchant-management.server
jsonprc.merchant-management.server=http://merchant-management.internal.shouqianba.com