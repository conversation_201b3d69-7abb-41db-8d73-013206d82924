#core-business
jsonrpc.core-business.server=http://core-business.dev.shouqianba.com/

#bank-info-service
jsonrpc.bank-info-service.server=http://bank-info-service.test.shouqianba.com/

#merchant-audit-service
jsonrpc.merchant-audit.server=http://merchant-audit-service.test.shouqianba.com/

#user-service
jsonrpc.user-service.server=http://user-service.test.shouqianba.com/

#sp-user-service
jsonrpc.sp-user-service.server=http://sp-user-service.test.shouqianba.com/

#jsonrpc.core-business.server=http://localhost:11114/core-business
jsonrpc.upay-wallet.server=http://upay-wallet.dev.shouqianba.com/

#app-backend-api
jsonrpc.app-backend-api.server=http://app-backend-process-service.test.shouqianba.com/

#app-gated-api
jsonrpc.app-gated-api.server=http://app-gated-process-service.test.shouqianba.com/

#shouqianba-withdraw-service
jsonrpc.shouqianba-withdraw-service.server=http://shouqianba-withdraw-service.test.shouqianba.com/

#business-log-service
jsonrpc.bizlog.server = http://business-log.dev.shouqianba.com/

#business-audit-api
jsonrpc.bizaudit.server = http://business-audit.dev.shouqianba.com/

#sales-system-backend-api
jsonrpc.sales-system-backend-api.server=http://sales-system-backend.dev2.shouqianba.com/

#shouqianba-merchant-api
jsonrpc.shouqianba-merchant-api.server=http://merchant.test.shouqianba.com/

#sales-system-service-api
jsonrpc.sales-system-service-api.server=http://sales-system-service.dev2.shouqianba.com/

#shouqianba-risk-service-api
jsonrpc.shouqianba-risk-service-api.server=http://risk.dev2.shouqianba.com/

#app-push-service
jsonrpc.app-push-service.server=http://app-push-service.test.shouqianba.com/

#upay-transaction-api
jsonrpc.upay-transaction-api-querry.server=http://upay-transaction.dev.shouqianba.com/
jsonrpc.upay-transaction-api-export.server=http://upay-transaction.dev.shouqianba.com/

#upay-side-api
jsonrpc.upay-side-api.server=http://upay-side.dev.shouqianba.com/

#alipay-authinto-api
jsonrpc.alipay-authinto-api.server=http://alipay.authinto.test.shouqianba.com/

#alipay-risk-api
jsonrpc.alipay-risk-api.server=http://alipay-risk-service.test.shouqianba.com/

#upay-finance-api
jsonrpc.upay-finance-api.server=http://finance-backend.dev.shouqianba.com/

#sms host短信网关发送地址
sms.host=http://dev.wosai.cn:8210/sms/send

#upay.gateway.server
upay.gateway.server=http://upay.dev.shouqianba.com/

#jsonrpc.merchant-level.server
jsonrpc.merchant-level.server=http://merchant-level.test2.shouqianba.com/

#janus
jsonrpc.cas-server.server=http://cas.test.shouqianba.com/

#upay-swipe.server
jsonprc.upay-swipe-api.server=http://upay-swipe.test.shouqianba.com/

# ip discern server
jsonrpc.sales-system-poi.server=http://sales-system-poi.test.shouqianba.com/

#sales-system-push.server
jsonprc.sales-system-push.server=http://sales-system-push.test.shouqianba.com/

#mail
mail.host=smtp.mxhichina.com
mail.port=465
mail.protocal=smtp
mail.username=<EMAIL>
mail.password=Test2018
mail.auth=true
mail.timeout=5000
mail.ssl.enable=true

#notification.mail.order.sendto
notification.mail.order.sendto=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

#redis
redis.url=localhost
redis.port=6379
redis.database=2
redis.password=wosai1234
#cacheService缓存数据的过期时间，单位毫秒
redis.cacheService.expiredTime=86400
#cacheService缓存数据key的前缀
redis.cacheService.keyPrefix=mini_info


#backend.upay.server
backend.upay.server=http://backend.dev.shouqianba.com/

#cas config
cas.server.url=http://cas.dev.shouqianba.com/
osp.server.url=http://web-platforms-osp.dev.shouqianba.com
osp.client.url=http://sp.dev.shouqianba.com

#osp oauth_login config
cronut_url=http://cronut.dev.shouqianba.com/
osp.authorize_url=http://cronut.dev.shouqianba.com/api/authorize
osp.token_url=http://cronut.dev.shouqianba.com/api/access_token
osp.userInfo_url=http://cronut.dev.shouqianba.com/api/get_info
osp.client_id=61faf957-20e7-4db7-9beb-ac6f47b8942b
osp.response_type=code
osp.redirect_uri=http://web-platforms-osp.dev.shouqianba.com/api/oauth/callBack
osp.grant_type=authorization_code
osp.client_secret=osp_key

#oAuth登录成功后 重定向到主页
homeUrl=http://wangx.osp2.dev.shouqianba.com

jsonrpc.upay-qrcode.server = http://upay-qrcode.dev.shouqianba.com/

#marketing-service
api.shouqianba.server = http://api.dev.shouqianba.com/

##Tracing
bootstrap.servers=10.46.67.89:9092
spring.application.name=osp-dev
samplerRate=1.0f

#kafka
kafka.bootstrap.servers=116.62.107.71:9092
kafka.acks=1
kafka.retries=3
kafka.linger.ms=500
kafka.batch.size=5
kafka.max.block.ms=1000
kafka.registry.url=http://116.62.107.71:8081

#aop notice config
aop.notice.product.id.risk=dcb167d5-873c-4718-8145-e40220e5c382
aop.notice.terminal.id.app=dfee2ee5-b1e7-11e9-9807-7cd30ae435b2

# reward-service
jsonrpc.reward.server=http://reward-service.dev.shouqianba.com/
jsonrpc.opr-point.server=http://opr-point.test.shouqianba.com/

jsonrpc.notice-service.server=http://10.80.65.217:20098/
#opr-merchant-tool
jsonrpc.merchant-tool.server=http://opr-merchant-tool.dev.shouqianba.com/
#sales-system-profit-api
jsonrpc.profit-api.server=http://sales-system-profit.test.shouqianba.com/
#wosai-preorder-risk
jsonrpc.preorder-risk.server=http://wosai-preorder-risk.test.shouqianba.com/

#clearance-service
jsonrpc.clearance-service-api.server = http://clearance-service.test.shouqianba.com/

#upay-insurance-api
jsonrpc.upay-insurance-api.server=http://insurance-backend.test.shouqianba.com/
#upay-activity-api
jsonrpc.upay-activity-api.server=http://upay-activity.dev.shouqianba.com/
#ecards-api.server
jsonrpc.ecards-api.server=http://ecards.dev.shouqianba.com/
#statistics_backend service
jsonrpc.statistics-backend.service=http://statistics-backend.dev.shouqianba.com/
jsonrpc.sales-addin.server=http://sales-system-addin.test.shouqianba.com/
jsonrpc.app-config.server=http://app-config-service.test.shouqianba.com/


#upay-loan-api
jsonrpc.upay-loan-api.server=http://loan-backend.test2.shouqianba.com/

#upay-credit-pay-api
jsonrpc.upay-credit-pay-api.server=http://credit-pay-backend.test.shouqianba.com/

#opr-task-backend
jsonrpc.opr-task-backend.server=http://opr-task-backend.test.shouqianba.com/


jsonrpc.merchant-contract-job.server =http://merchant-contract-job.test.shouqianba.com

com.wosai.oss.static-bucket=wosai-statics
com.wosai.oss.static-base-url=https://statics.wosaimg.com
jsonrpc.merchant-bank.server=http://merchant-bank-service.test.shouqianba.com/

#aop-backend-notification-api
jsonprc.aop-backend-notification-api.server=http://aop-backend-notification.test.shouqianba.com/

#oss
oss.base-url=http://wosai-statics.oss-cn-hangzhou.aliyuncs.com/
#aop-backend-api
jsonprc.aop-backend-api.server=http://aop-backend.test.shouqianba.com/
jsonrpc.profit-share=http://profit-sharing.test.shouqianba.com/

#upay-grayscale
jsonrpc.upay-grayscale.server=http://upay-grayscale.test.shouqianba.com/

#trade-manage-service
jsonrpc.trade-manage-service.server=http://trade-manage-service.test.shouqianba.com/
