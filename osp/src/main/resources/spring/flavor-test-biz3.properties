#core-business
jsonrpc.core-business.server=http://core-business-biz.test.shouqianba.com/

#bank-info-service
jsonrpc.bank-info-service.server=http://bank-info-service.test.shouqianba.com/

#merchant-audit-service
jsonrpc.merchant-audit.server=http://merchant-audit-service.test.shouqianba.com/

#user-service
jsonrpc.user-service.server=http://user-service.test.shouqianba.com/

#sp-user-service
jsonrpc.sp-user-service.server=http://sp-user-service.test.shouqianba.com/

jsonrpc.upay-wallet.server=http://upay-wallet.test.shouqianba.com/

#app-backend-api
jsonrpc.app-backend-api.server=http://app-backend-process-service.test.shouqianba.com/

#app-gated-api
jsonrpc.app-gated-api.server=http://app-gated-process-service.test.shouqianba.com/

#shouqianba-withdraw-service
jsonrpc.shouqianba-withdraw-service.server=http://shouqianba-withdraw-service.test.shouqianba.com/

#business-log-service
jsonrpc.bizlog.server = http://business-log.test.shouqianba.com/

#sales-system-backend-api
jsonrpc.sales-system-backend-api.server=http://sales-system-backend.test.shouqianba.com/

#shouqianba-merchant-api
jsonrpc.shouqianba-merchant-api.server=http://merchant.test.shouqianba.com/

#sales-system-service-api
jsonrpc.sales-system-service-api.server=http://sales-system-service.test.shouqianba.com/

#shouqianba-risk-service-api
jsonrpc.shouqianba-risk-service-api.server=http://risk.test.shouqianba.com/

#app-push-service
jsonrpc.app-push-service.server=http://app-push-service.test.shouqianba.com/

#upay-transaction-api
jsonrpc.upay-transaction-api-querry.server=http://upay-transaction.test.shouqianba.com/
jsonrpc.upay-transaction-api-export.server=http://upay-transaction.test.shouqianba.com/

#upay-side-api
jsonrpc.upay-side-api.server=http://upay-side.test.shouqianba.com/

#upay-finance-api
jsonrpc.upay-finance-api.server=http://finance-backend.test.shouqianba.com/

#janus
jsonrpc.cas-server.server=http://cas.test.shouqianba.com/

#sms host短信网关发送地址
sms.host=http://sms-gateway.test.shouqianba.com/sms/send

#backend.upay.server
backend.upay.server=http://backend.test.shouqianba.com/

#upay.gateway.server
upay.gateway.server=http://upay.test.shouqianba.com/

#remit-gateway
jsonrpc.remit.server=http://remit-gateway.test.shouqianba.com/

#upay-swipe.server
jsonprc.upay-swipe-api.server=http://upay-swipe.test.shouqianba.com/

#sales-system-push.server
jsonprc.sales-system-push.server=http://sales-system-push.test.shouqianba.com/

#mail
mail.host=smtp.mxhichina.com
mail.port=465
mail.protocal=smtp
mail.username=<EMAIL>
mail.password=Test2018
mail.auth=true
mail.timeout=5000
mail.ssl.enable=true

#notification.mail.order.sendto
notification.mail.order.sendto=<EMAIL>

#redis
redis.url=***********
redis.port=6379
redis.database=2
redis.password=wosai1234
#cacheService缓存数据的过期时间，单位毫秒
redis.cacheService.expiredTime=86400
#cacheService缓存数据key的前缀
redis.cacheService.keyPrefix=mini_info

#cas config
cas.server.url=http://cas.test.shouqianba.com/
osp.server.url=http://spa-legacy-api.test.shouqianba.com
osp.client.url=http://spa-legacy.test.shouqianba.com

#osp oauth_login config
cronut_url=http://cronut.test.shouqianba.com/
osp.authorize_url=http://cronut.test.shouqianba.com/api/authorize
osp.token_url=http://cronut.test.shouqianba.com/api/access_token
osp.userInfo_url=http://cronut.test.shouqianba.com/api/get_info
osp.client_id=00009d47-7028-48b7-abdd-8251fc811111
osp.response_type=code
osp.redirect_uri=http://web-platforms-osp-biz2.test.shouqianba.com/api/oauth/callBack
osp.grant_type=authorization_code
osp.client_secret=osp_test_biz2

#oAuth登录成功后 重定向到主页
homeUrl=http://sp-biz2.test.shouqianba.com

jsonrpc.upay-qrcode.server = http://upay-qrcode.test.shouqianba.com/

#marketing-service
api.shouqianba.server = http://api.test.shouqianba.com/

##Tracing
bootstrap.servers=***********:9092
spring.application.name=osp-test
samplerRate=1.0f

#kafka
kafka.bootstrap.servers=116.62.107.71:9092
kafka.acks=1
kafka.retries=3
kafka.linger.ms=500
kafka.batch.size=5
kafka.max.block.ms=1000
kafka.registry.url=http://116.62.107.71:8081

# reward-service
jsonrpc.reward.server=http://reward-service.test.shouqianba.com/
jsonrpc.opr-point.server=http://opr-point.test.shouqianba.com/

jsonrpc.opr-merchant-activity.server=http://opr-merchant-activity.test.shouqianba.com/

jsonrpc.merchant-card.server=http://merchant-card.test.shouqianba.com/

jsonrpc.notice-service.server=http://121.40.32.245:20098
#sales-system-profit-api
jsonrpc.profit-api.server=http://sales-system-profit.test2.shouqianba.com/
#wosai-preorder-risk
jsonrpc.preorder-risk.server=http://wosai-preorder-risk.test.shouqianba.com/
#merchant-contract-api
jsonrpc.merchant-contract-api.server = http://merchant-contract.test.shouqianba.com/
#clearance-service
jsonrpc.clearance-service-api.server = http://clearance-service.test.shouqianba.com/
#upay-insurance-api
jsonrpc.upay-insurance-api.server=http://insurance-backend.test.shouqianba.com/

jsonrpc.sales-addin.server=http://sales-system-addin.test.shouqianba.com/
jsonrpc.app-config.server=http://app-config-service.test.shouqianba.com/
#upay-loan-api
jsonrpc.upay-loan-api.server=http://loan-backend.test2.shouqianba.com/

#upay-credit-pay-api
jsonrpc.upay-credit-pay-api.server=http://credit-pay-backend.test.shouqianba.com/

#opr-task-backend
jsonrpc.opr-task-backend.server=http://opr-task-backend.test.shouqianba.com/

jsonrpc.merchant-contract-job.server =http://merchant-contract-job.test.shouqianba.com

com.wosai.oss.static-bucket=wosai-statics
com.wosai.oss.static-base-url=https://statics.wosaimg.com
jsonrpc.merchant-bank.server=http://merchant-bank-service.test.shouqianba.com/
jsonprc.aop-backend-notification-api.server=http://aop-backend-notification.test.shouqianba.com/
jsonprc.aop-backend-api.server=http://aop-backend.test.shouqianba.com/
jsonrpc.profit-share=http://profit-sharing.test.shouqianba.com/

#upay-grayscale
jsonrpc.upay-grayscale.server=http://upay-grayscale.test.shouqianba.com/

#trade-manage-service
jsonrpc.trade-manage-service.server=http://trade-manage-service.test.shouqianba.com/
