<?xml version="1.0" encoding="UTF-8"?>
<!--
Data and Service layers
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jpa="http://www.springframework.org/schema/data/jpa"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
			   http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa.xsd
			   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
			   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">
    <bean class="com.wosai.upay.common.helper.UpayMethodValidationPostProcessor">
        <property name="validatedAnnotationType" value="com.wosai.upay.helper.CorePlatformsValidated"/>
    </bean>
    <context:component-scan base-package="com.wosai.upay.service, com.wosai.upay.client"/>

    <aop:aspectj-autoproxy/>
    <bean class="com.fasterxml.jackson.databind.ObjectMapper"/>
    <bean class="com.wosai.upay.service.CoreUserServiceImpl"/>
    <bean id="springContextHolder" class="com.wosai.upay.common.util.SpringContextHolder" lazy-init="false"/>
    <context:property-placeholder location="classpath:spring/flavor-${shouqianba.flavor:default}.properties"/>
    <import resource="redis-config.xml"/>
    <import resource="classpath:META-INF/sdk.xml"/>
    <!-- 接入监控服务 -->
    <import resource="classpath*:/wosai-tracing.xml"/>
    <bean id="logAspect" class="com.wosai.upay.aop.LogAspect"/>
    <bean id="OssUpload" name="OssUpload" class="com.wosai.oss.OssUpload"/>
    <bean id="OssUrlEncrypt" name="OssUrlEncrypt" class="com.wosai.oss.OssUrlEncrypt"/>
    <bean id="OssStsClient" name="OssStsClient" class="com.wosai.oss.OssStsClient"/>
    <bean id="OssProperties" name="OssProperties" class="com.wosai.oss.configuration.OssProperties">
        <property name="staticBucket" value="${com.wosai.oss.static-bucket}"></property>
        <property name="staticBaseUrl" value="${com.wosai.oss.static-base-url}"></property>
        <property name="internal" value="${com.wosai.oss.internal}"></property>
    </bean>
    <!-- 配置oauth config-->
    <bean id="ospOauthService" class="com.wosai.upay.service.OspOauthServiceImpl">
        <constructor-arg name="authorize_url" value="${osp.authorize_url}"/>
        <constructor-arg name="token_url" value="${osp.token_url}"/>
        <constructor-arg name="userInfo_url" value="${osp.userInfo_url}"/>
        <constructor-arg name="client_id" value="${osp.client_id}"/>
        <constructor-arg name="client_secret" value="${osp.client_secret}"/>
        <constructor-arg name="redirect_uri" value="${osp.redirect_uri}"/>
        <constructor-arg name="response_type" value="${osp.response_type}"/>
        <constructor-arg name="grant_type" value="${osp.grant_type}"/>
        <constructor-arg name="homeUrl" value="${homeUrl}"/>
    </bean>
    <import resource="dynamic-beans.xml"/>
    <bean class="com.wosai.upay.service.AstHelperImpl"/>
    <bean class="com.wosai.upay.service.SmsSendServiceImpl">
        <property name="smsHost" value="${sms.host}"></property>
    </bean>
    <bean class="com.wosai.upay.service.OspOrderServiceImpl">
        <constructor-arg name="backendUpayUrl" value="${backend.upay.server}"></constructor-arg>
        <constructor-arg name="upayGatewayUrl" value="${upay.gateway.server}"/>
        <constructor-arg name="mailNotificationOrderSendto" value="${notification.mail.order.sendto}"/>
        <constructor-arg name="preOrderRiskUrl" value="${jsonrpc.preorder-risk.server}"/>
    </bean>
    <bean class="com.wosai.upay.service.OspWithdrawServiceImpl">
        <constructor-arg name="backendUpayUrl" value="${backend.upay.server}"></constructor-arg>
        <constructor-arg name="shouqianbaServer" value="${api.shouqianba.server}"/>
    </bean>
    <!-- remit gateway start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.remit.server}rpc/detail"></property>
        <property name="serviceInterface" value="com.wosai.upay.remit.service.RemitDetailService"/>
        <property name="serverName" value="remit-gateway-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.remit.server}rpc/order"></property>
        <property name="serviceInterface" value="com.wosai.upay.remit.service.RemitOrderService"/>
        <property name="serverName" value="remit-gateway-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.remit.server}rpc/batch"></property>
        <property name="serviceInterface" value="com.wosai.upay.remit.service.RemitBatchService"/>
        <property name="serverName" value="remit-gateway-service"/>
    </bean>
    <!-- remit-end start -->
    <bean class="com.wosai.upay.service.OspUserServiceImpl">
        <constructor-arg name="cronut_url" value="${cronut_url}"/>
        <constructor-arg name="client_id" value="${osp.client_id}"/>
    </bean>
    <bean class="com.wosai.upay.service.OspMerchantServiceImpl">
        <constructor-arg name="mailNotificationOrderSendto" value="${notification.mail.order.sendto}"/>
    </bean>
    <bean class="com.wosai.upay.helper.RecordLogUtil"/>
    <bean class="com.wosai.upay.service.CommonLoginServiceImpl"/>
    <bean class="com.wosai.upay.service.OspTransactionServiceImpl"/>
    <bean class="com.wosai.upay.service.OspWalletServiceImpl"/>
    <bean class="com.wosai.upay.service.OspTerminalServiceImpl">
        <constructor-arg name="upay_qrcode_url" value="${jsonrpc.upay-qrcode.server}"></constructor-arg>
    </bean>
    <bean class="com.wosai.upay.service.OspGroupServiceImpl"/>
    <bean class="com.wosai.upay.service.OspClearanceServiceImpl"/>
    <bean id="jsonRpcRequestListener" class="com.wosai.upay.helper.JsonRpcRequestListener">
        <property name="projectName" value="web-platforms-osp"></property>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/common"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.BusinssCommonService"/>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/mspRefundTerminal"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.MspRefundTerminalService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.notice-service.server}/rpc/notice"></property>
        <property name="serviceInterface" value="com.wosai.notice.service.NoticeService"></property>
        <property name="serverName" value="notice-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.app-backend-api.server}/rpc/staff"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IStaffService"></property>
        <property name="serverName" value="app-backend-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-wallet.server}rpc/wallet"></property>
        <property name="serviceInterface" value="com.wosai.upay.wallet.service.WalletService"></property>
        <property name="serverName" value="upay-wallet"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-wallet.server}rpc/merchantLimitAuthorization"></property>
        <property name="serviceInterface" value="com.wosai.upay.wallet.service.MerchantLimitAuthorizationService"></property>
        <property name="serverName" value="upay-wallet"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/vendor"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.VendorService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/solicitor"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.SolicitorService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/merchant"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MerchantService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/merchantGallery"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MerchantGalleryService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.StoreService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/terminal"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TerminalService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/application"></property>
        <property name="serviceInterface"
                  value="com.wosai.upay.merchant.audit.api.service.ApplicationService"></property>
        <property name="serverName" value="merchant-audit-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/message"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MessageService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/log"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.LogService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/rsaKey"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.RsaKeyService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/support"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.SupportService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean primary="true" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/tradeConfig"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TradeConfigService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean primary="false" id="tradeConfigServiceBatch" class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/tradeConfig"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TradeConfigService"></property>
        <property name="serverName" value="core-business"/>
        <property name="readTimeoutMillis" value="2000"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}rpc/withdraw"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.WithdrawService"></property>
        <property name="serverName" value="core-business"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}rpc/user"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.UserService"></property>
        <property name="serverName" value="user-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sp-user-service.server}rpc/user"></property>
        <property name="serviceInterface" value="com.wosai.sp.service.OspUserService"></property>
        <property name="serverName" value="osp-user-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}rpc/userToken"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.UserTokenService"></property>
        <property name="serverName" value="user-service"/>
    </bean>
    <bean class="com.wosai.upay.service.CommonServiceImpl"/>
    <bean class="com.wosai.upay.service.TranslateServiceImpl"/>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}rpc/industry"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.IndustryService"></property>
        <property name="serverName" value="bank-info-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}rpc/districts"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.DistrictsService"></property>
        <property name="serverName" value="bank-info-service"/>
    </bean>


    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-audit.server}rpc/merchantAudit"></property>
        <property name="serviceInterface" value="com.wosai.upay.merchant.audit.api.service.MerchantAuditService"/>
        <property name="serverName" value="merchant-audit-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}rpc/group"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.GroupService"/>
        <property name="serverName" value="user-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}rpc/bankinfo"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.BankInfoService"/>
        <property name="serverName" value="bank-info-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}rpc/withdrawbank"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.WithdrawBankService"/>
        <property name="serverName" value="bank-info-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}rpc/cardbin"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.CardBinService"/>
        <property name="serverName" value="bank-info-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bank-info-service.server}rpc/bankbiz"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.info.api.service.BankBizService"/>
        <property name="serverName" value="bank-info-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.user-service.server}/rpc/specialAuthWhitelist"></property>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.SpecialAuthWhitelistService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.core-business.server}/rpc/currencyFeerate"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.CurrencyFeerateService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.app-backend-api.server}rpc/bank"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IBankService"/>
        <property name="serverName" value="app-backend-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.app-backend-api.server}rpc/account"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IAccountService"></property>
        <property name="serverName" value="app-backend-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.app-backend-api.server}rpc/settings"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.ISettingsService"></property>
        <property name="serverName" value="app-backend-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.app-gated-api.server}rpc/appGatedRule"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IAppGatedRuleService"/>
        <property name="serverName" value="app-gated-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-withdraw-service.server}rpc/withdraw"></property>
        <property name="serviceInterface" value="com.wosai.shouqianba.withdrawservice.service.WithdrawService"/>
        <property name="serverName" value="shouqianba-withdraw-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-withdraw-service.server}rpc/withdrawConfig"></property>
        <property name="serviceInterface" value="com.wosai.shouqianba.withdrawservice.service.WithdrawConfigService"/>
        <property name="serverName" value="shouqianba-withdraw-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl"
                  value="${jsonrpc.shouqianba-withdraw-service.server}rpc/merchantWithdrawConfig"></property>
        <property name="serviceInterface"
                  value="com.wosai.shouqianba.withdrawservice.service.MerchantWithdrawConfigService"/>
        <property name="serverName" value="shouqianba-withdraw-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-withdraw-service.server}rpc/d1withdrawbatch"></property>
        <property name="serviceInterface" value="com.wosai.shouqianba.withdrawservice.service.D1WithdrawBatchService"/>
        <property name="serverName" value="shouqianba-withdraw-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-withdraw-service.server}rpc/withdrawNotice"></property>
        <property name="serviceInterface" value="com.wosai.shouqianba.withdrawservice.service.WithdrawNotice"/>
        <property name="serverName" value="shouqianba-withdraw-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-withdraw-service.server}rpc/compensation"></property>
        <property name="serviceInterface" value="com.wosai.shouqianba.withdrawservice.service.CompensationService"/>
        <property name="serverName" value="shouqianba-withdraw-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-level.server}rpc/merchantactivelevel"></property>
        <property name="serviceInterface" value="com.wosai.biz.merchantlevel.service.MerchantActiveLevelService"/>
        <property name="serverName" value="merchant-level"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-backend-api.server}rpc/organization"></property>
        <property name="serviceInterface" value="com.wosai.assistant.service.OrganizationRpcService"/>
        <property name="serverName" value="sales-system-backend"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-backend-api.server}rpc/merchant"></property>
        <property name="serviceInterface" value="com.wosai.assistant.service.MerchantRpcService"/>
        <property name="serverName" value="sales-system-backend"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-backend-api.server}rpc/user"></property>
        <property name="serviceInterface" value="com.wosai.assistant.service.UserRpcService"/>
        <property name="serverName" value="sales-system-backend"/>
    </bean>
    <!-- sales-system-service begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-service-api.server}rpc/organization"></property>
        <property name="serviceInterface" value="com.wosai.sales.core.service.OrganizationService"/>
        <property name="serverName" value="sales-system-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-service-api.server}rpc/user"></property>
        <property name="serviceInterface" value="com.wosai.sales.core.service.UserService"/>
        <property name="serverName" value="sales-system-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-service-api.server}rpc/risk"></property>
        <property name="serviceInterface" value="com.wosai.sales.core.service.RiskService"/>
        <property name="serverName" value="sales-system-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-service-api.server}rpc/merchant"></property>
        <property name="serviceInterface" value="com.wosai.sales.core.service.IMerchantService"/>
        <property name="serverName" value="sales-system-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-service-api.server}rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.sales.core.service.IStoreService"/>
        <property name="serverName" value="sales-system-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-service-api.server}rpc/keeper"></property>
        <property name="serviceInterface" value="com.wosai.sales.core.service.IKeeperService"/>
        <property name="serverName" value="sales-system-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-service-api.server}rpc/terminalLog"></property>
        <property name="serviceInterface" value="com.wosai.sales.core.service.TerminalLogService"/>
        <property name="serverName" value="sales-system-service"/>
    </bean>
    <!-- sales-system-service end -->
    <!-- shouqianba-risk-service begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-risk-service-api.server}rpc/riskconfig"></property>
        <property name="serviceInterface" value="com.wosai.risk.service.IRiskConfigService"/>
        <property name="serverName" value="shouqianba-risk-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-risk-service-api.server}rpc/riskquery"></property>
        <property name="serviceInterface" value="com.wosai.risk.service.IRiskQueryService"/>
        <property name="serverName" value="shouqianba-risk-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-risk-service-api.server}rpc/merchantlimit"></property>
        <property name="serviceInterface" value="com.wosai.risk.service.IRiskMerchantLimitService"/>
        <property name="serverName" value="shouqianba-risk-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-risk-service-api.server}rpc/riskactivity"></property>
        <property name="serviceInterface" value="com.wosai.risk.service.IRiskActivityService"/>
        <property name="serverName" value="shouqianba-risk-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-risk-service-api.server}rpc/facereview"></property>
        <property name="serviceInterface" value="com.wosai.risk.service.IRiskFaceReviewService"/>
        <property name="serverName" value="shouqianba-risk-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl"
                  value="${jsonrpc.shouqianba-risk-service-api.server}rpc/specialoperation"></property>
        <property name="serviceInterface" value="com.wosai.risk.service.IRiskSpecialOperationService"/>
        <property name="serverName" value="shouqianba-risk-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl"
                  value="${jsonrpc.shouqianba-risk-service-api.server}rpc/ocr"></property>
        <property name="serviceInterface" value="com.wosai.risk.service.IOcrService"/>
        <property name="serverName" value="shouqianba-risk-service"/>
        <property name="objectMapper">
            <bean class="com.wosai.upay.bean.IgnoreMapper" />
        </property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-risk-service-api.server}rpc/transLimit"></property>
        <property name="serviceInterface" value="com.wosai.risk.service.IRiskTransLimitService"></property>
        <property name="serverName" value="shouqianba-risk-service"/>
    </bean>
    <!-- shouqianba-risk-service end -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.app-push-service.server}rpc/pushMessage"></property>
        <property name="serviceInterface" value="com.wosai.app.push.api.service.IPushMessageService"/>
        <property name="serverName" value="app-push-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-withdraw-service.server}rpc/push"></property>
        <property name="serviceInterface" value="com.wosai.shouqianba.withdrawservice.service.PushService"/>
        <property name="serverName" value="shouqianba-withdraw-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-withdraw-service.server}rpc/closeSms"></property>
        <property name="serviceInterface"
                  value="com.wosai.shouqianba.withdrawservice.service.WithdrawCloseSmsNoticeService"/>
        <property name="serverName" value="shouqianba-withdraw-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-withdraw-service.server}rpc/noticeLog"></property>
        <property name="serviceInterface"
                  value="com.wosai.shouqianba.withdrawservice.service.WithdrawNoticeLogService"/>
        <property name="serverName" value="shouqianba-withdraw-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-withdraw-service.server}rpc/noticeRule"></property>
        <property name="serviceInterface" value="com.wosai.shouqianba.withdrawservice.service.NoticeRuleService"/>
        <property name="serverName" value="shouqianba-withdraw-service"/>
    </bean>
    <!-- businsess-log-->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizlog.server}/rpc/object"></property>
        <property name="serviceInterface" value="com.wosai.business.log.service.BizObjectService"/>
        <property name="serverName" value="business-log-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizlog.server}/rpc/objectColumn"></property>
        <property name="serviceInterface" value="com.wosai.business.log.service.BizObjectColumnService"/>
        <property name="serverName" value="business-log-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizlog.server}/rpc/function"></property>
        <property name="serviceInterface" value="com.wosai.business.log.service.BizFunctionService"/>
        <property name="serverName" value="business-log-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizlog.server}/rpc/opLog"></property>
        <property name="serviceInterface" value="com.wosai.business.log.service.BizOpLogService"/>
        <property name="serverName" value="business-log-service"/>
    </bean>

    <!-- business-audit -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizaudit.server}/rpc/replytemplate"></property>
        <property name="serviceInterface" value="com.wosai.business.audit.service.ReplyTemplateService"></property>
        <property name="serverName" value="business-audit-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizaudit.server}/rpc/customcolumns"></property>
        <property name="serviceInterface" value="com.wosai.business.audit.service.CustomColumnsService"></property>
        <property name="serverName" value="business-audit-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizaudit.server}/rpc/audit"></property>
        <property name="serviceInterface" value="com.wosai.business.audit.service.AuditService"></property>
        <property name="serverName" value="business-audit-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizaudit.server}/rpc/audititem"></property>
        <property name="serviceInterface" value="com.wosai.business.audit.service.AuditItemService"></property>
        <property name="serverName" value="business-audit-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizaudit.server}/rpc/auditimage"></property>
        <property name="serviceInterface" value="com.wosai.business.audit.service.AuditImageService"></property>
        <property name="serverName" value="business-audit-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizaudit.server}/rpc/cache"></property>
        <property name="serviceInterface" value="com.wosai.business.audit.service.CacheService"></property>
        <property name="serverName" value="business-audit-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.bizaudit.server}/rpc/auditrecord"></property>
        <property name="serviceInterface" value="com.wosai.business.audit.service.AuditRecordService"></property>
        <property name="serverName" value="business-audit-service"></property>
    </bean>
    <!-- business-audit-api end -->
    <!-- upay-finance-api begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/userinfo"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.UserInfoService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/useramount"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.UserAmountService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/incomerecord"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.IncomeRecordService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/withdrawrecord"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.WithdrawRecordService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/dailyrecord"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.DailyRecordService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/bank"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.BankService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/rule"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.RuleService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/purchaserecord"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.PurchaseRecordService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/push"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.PushService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/bankaccountrecord"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.BankAccountRecordService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-finance-api.server}/rpc/statementtask"></property>
        <property name="serviceInterface" value="com.wosai.bsm.financebackend.service.StatementTaskService"></property>
        <property name="serverName" value="finance-backend-service"></property>
    </bean>
    <!-- upay-finance-api end -->
    <!-- upay-transaction-api begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-querry.server}rpc/order"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.OrderService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-export.server}rpc/export"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.ExportService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-querry.server}rpc/transaction"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TransactionService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-transaction-api-export.server}rpc/statementTask"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TaskLogService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl"
                  value="${jsonrpc.upay-transaction-api-export.server}rpc/statementObjectConfig"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.StatementObjectConfigService"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl"
                  value="${jsonrpc.upay-transaction-api-querry.server}rpc/transaction_v2"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TransactionServiceV2"/>
        <property name="serverName" value="upay-transaction"/>
    </bean>
    <!-- upay-transaction-api end -->
    <!-- reward-service-api begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.opr-point.server}rpc/point"></property>
        <property name="serviceInterface" value="com.wosai.opr.point.service.PointService"/>
        <property name="serverName" value="opr-point-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.reward.server}rpc/points"></property>
        <property name="serviceInterface" value="com.wosai.reward.service.PointsService"/>
        <property name="serverName" value="reward-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.reward.server}rpc/accounts"></property>
        <property name="serviceInterface" value="com.wosai.reward.service.AccountService"/>
        <property name="serverName" value="reward-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.cas-server.server}rpc/account"></property>
        <property name="serviceInterface" value="com.wosai.janus.api.service.AccountService"/>
        <property name="serverName" value="janus"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.reward.server}rpc/rules"></property>
        <property name="serviceInterface" value="com.wosai.reward.service.RuleService"/>
        <property name="serverName" value="reward-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.reward.server}rpc/histories"></property>
        <property name="serviceInterface" value="com.wosai.reward.service.HistoryService"/>
        <property name="serverName" value="reward-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.reward.server}rpc/tags"></property>
        <property name="serviceInterface" value="com.wosai.reward.service.TagService"/>
        <property name="serverName" value="reward-service"/>
    </bean>
    <!-- reward-service-api end -->
    <!-- shouqianba-merchant-service begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl"
                  value="${jsonrpc.shouqianba-merchant-api.server}rpc/stock-merchant-audit"></property>
        <property name="serviceInterface" value="com.wosai.merchant.service.IStockMerchantAuditService"/>
        <property name="serverName" value="shouqianba-merchant-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-merchant-api.server}rpc/stock-merchant"></property>
        <property name="serviceInterface" value="com.wosai.merchant.service.IStockMerchantService"/>
        <property name="serverName" value="shouqianba-merchant-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-merchant-api.server}rpc/real-name-merchant"></property>
        <property name="serviceInterface" value="com.wosai.merchant.service.IMerchantRealNameAuthService"/>
        <property name="serverName" value="shouqianba-merchant-service"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl"
                  value="${jsonrpc.shouqianba-merchant-api.server}rpc/normal-merchant-audit"></property>
        <property name="serviceInterface" value="com.wosai.merchant.service.IMerchantAuditService"/>
        <property name="serverName" value="shouqianba-merchant-service"/>
    </bean>
<!--    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">-->
<!--        <property name="serviceUrl" value="${jsonrpc.shouqianba-merchant-api.server}rpc/merchantInvoice"></property>-->
<!--        <property name="serviceInterface" value="com.wosai.merchant.service.MerchantInvoiceService"></property>-->
<!--        <property name="serverName" value="shouqianba-merchant-service"></property>-->
<!--    </bean>-->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-merchant-api.server}rpc/exportTask"></property>
        <property name="serviceInterface" value="com.wosai.merchant.service.ExportTaskService"></property>
        <property name="serverName" value="shouqianba-merchant-service"></property>
    </bean>
    <!-- merchant-invoice-service -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-invoice-api.server}rpc/merchantInvoice"></property>
        <property name="serviceInterface" value="com.wosai.operation.service.MerchantInvoiceService"></property>
        <property name="serverName" value="merchant-invoice-service"></property>
    </bean>

<!--    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">-->
<!--        <property name="serviceUrl" value="${jsonrpc.merchant-invoice-api.server}rpc/exportTask"></property>-->
<!--        <property name="serviceInterface" value="com.wosai.operation.service.ExportTaskService"></property>-->
<!--        <property name="serverName" value="merchant-invoice-service"></property>-->
<!--    </bean>-->
    <!-- merchant-invoice-service end -->

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl"
                  value="${jsonrpc.shouqianba-merchant-api.server}/rpc/merchant-bank-account"></property>
        <property name="serviceInterface" value="com.wosai.merchant.service.IMerchantBankAccountService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.shouqianba-merchant-api.server}/rpc/merchant"></property>
        <property name="serviceInterface" value="com.wosai.merchant.service.IMerchantService"></property>
        <property name="requestListener" ref="jsonRpcRequestListener"/>
    </bean>
    <!-- shouqianba-merchant-service end -->



    <!-- upay-side begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-side-api.server}rpc/rule"></property>
        <property name="serviceInterface" value="com.wosai.upay.side.service.GeneralRuleService"/>
        <property name="serverName" value="upay-side"/>
    </bean>
    <!-- upay-side end -->
    <!-- opr-merchant-activity-service start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.opr-merchant-activity.server}/rpc/activity"></property>
        <property name="serviceInterface" value="com.wosai.operation.activity.service.ActivityService"/>
        <property name="serverName" value="opr-merchant-activity"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.opr-merchant-activity.server}/rpc/attend"></property>
        <property name="serviceInterface" value="com.wosai.operation.activity.service.ActivityAttendService"/>
        <property name="serverName" value="opr-merchant-activity"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.opr-merchant-activity.server}/rpc/whitelist"></property>
        <property name="serviceInterface" value="com.wosai.operation.activity.service.WhitelistService"/>
        <property name="serverName" value="opr-merchant-activity"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-card.server}/rpc/card"></property>
        <property name="serviceInterface" value="com.wosai.operation.merchant.card.service.CardService"/>
        <property name="serverName" value="merchant-card"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-tool.server}/rpc/tool/merchantAudit"></property>
        <property name="serviceInterface" value="com.wosai.operation.tool.service.ToolMerchantAuditService"/>
        <property name="serverName" value="opr-merchant-tool"/>
    </bean>
    <!-- opr-merchant-activity-service end -->
    <!-- alipay-authinto-service start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.alipay-authinto-api.server}/rpc/alipaystore"></property>
        <property name="serviceInterface" value="com.wosai.pub.alipay.authinto.service.AlipayStoreService"/>
        <property name="serverName" value="alipay-authinto"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.alipay-authinto-api.server}/rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.pub.alipay.authinto.service.StoreService"/>
        <property name="serverName" value="alipay-authinto"/>
    </bean>
    <!--  alipay-authinto-service end -->
    <!-- alipay-risk-service start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.alipay-risk-api.server}/rpc/healthPoint"></property>
        <property name="serviceInterface" value="com.wosai.alipay.risk.service.HealthPointService"></property>
        <property name="serverName" value="alipay-risk-service"></property>
    </bean>
    <!-- alipay-risk-service-end -->
    <!--  sales-system-profit start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.profit-api.server}rpc/flow"></property>
        <property name="serviceInterface" value="com.wosai.sales.profit.service.WorkflowService"/>
        <property name="serverName" value="sales-system-profit"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.profit-api.server}rpc/profit"></property>
        <property name="serviceInterface" value="com.wosai.sales.profit.service.ProfitService"/>
        <property name="serverName" value="sales-system-profit"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.profit-api.server}rpc/profit_detail"></property>
        <property name="serviceInterface" value="com.wosai.sales.profit.service.ProfitDetailService"/>
        <property name="serverName" value="sales-system-profit"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.profit-api.server}rpc/reward_punish"></property>
        <property name="serviceInterface" value="com.wosai.sales.profit.service.RewardPunishTaskService"/>
        <property name="serverName" value="sales-system-profit"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.profit-api.server}rpc/org_contract"></property>
        <property name="serviceInterface" value="com.wosai.sales.profit.service.OrgContractService"/>
        <property name="serverName" value="sales-system-profit"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.profit-api.server}rpc/attachment"></property>
        <property name="serviceInterface" value="com.wosai.sales.profit.service.AttachmentService"/>
        <property name="serverName" value="sales-system-profit"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.profit-api.server}rpc/profit_type"></property>
        <property name="serviceInterface" value="com.wosai.sales.profit.service.ProfitTypeService"/>
        <property name="serverName" value="sales-system-profit"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.profit-api.server}rpc/remit"></property>
        <property name="serviceInterface" value="com.wosai.sales.profit.service.RemitService"/>
        <property name="serverName" value="sales-system-profit"/>
    </bean>
    <!-- sales-system-profit end -->
    <!-- merchant-contract begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-contract-api.server}rpc/providerTradeParams"></property>
        <property name="serviceInterface" value="com.wosai.upay.merchant.contract.service.ProviderTradeParamsService"/>
        <property name="serverName" value="merchant-contract"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-contract-api.server}rpc/lakalawanma"></property>
        <property name="serviceInterface" value="com.wosai.upay.merchant.contract.service.LakalaWanmaService"/>
        <property name="serverName" value="merchant-contract"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-contract-api.server}rpc/wft"></property>
        <property name="serviceInterface" value="com.wosai.upay.merchant.contract.service.WftService"/>
        <property name="serverName" value="merchant-contract"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-contract-api.server}rpc/lakala"></property>
        <property name="serviceInterface" value="com.wosai.upay.merchant.contract.service.LakalaService"/>
        <property name="serverName" value="merchant-contract"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-contract-api.server}rpc/support"></property>
        <property name="serviceInterface" value="com.wosai.upay.merchant.contract.service.SupportService"/>
        <property name="serverName" value="merchant-contract"/>
    </bean>
    <!-- merchant-contract end -->
    <!-- clearance-service start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.clearance-service-api.server}rpc/clearance"></property>
        <property name="serviceInterface" value="com.wosai.upay.clearance.service.ClearanceService"/>
        <property name="serverName" value="merchant-contract"/>
    </bean>
    <!-- clearance-service end -->
    <!-- insurance begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-insurance-api.server}rpc/policy"></property>
        <property name="serviceInterface" value="com.wosai.bsm.insurancebackend.service.PolicyService"></property>
        <property name="serverName" value="insurance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-insurance-api.server}rpc/company"></property>
        <property name="serviceInterface" value="com.wosai.bsm.insurancebackend.service.CompanyService"></property>
        <property name="serverName" value="insurance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-insurance-api.server}rpc/product"></property>
        <property name="serviceInterface" value="com.wosai.bsm.insurancebackend.service.ProductService"></property>
        <property name="serverName" value="insurance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-insurance-api.server}rpc/user"></property>
        <property name="serviceInterface" value="com.wosai.bsm.insurancebackend.service.UserService"></property>
        <property name="serverName" value="insurance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-insurance-api.server}rpc/userTaskCoverageDetail"></property>
        <property name="serviceInterface"
                  value="com.wosai.bsm.insurancebackend.service.UserTaskCoverageDetailService"></property>
        <property name="serverName" value="insurance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-insurance-api.server}rpc/freePolicyDailyReport"></property>
        <property name="serviceInterface"
                  value="com.wosai.bsm.insurancebackend.service.FreePolicyDailyReportService"></property>
        <property name="serverName" value="insurance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-insurance-api.server}rpc/reportOverall"></property>
        <property name="serviceInterface"
                  value="com.wosai.bsm.insurancebackend.service.ReportOverallService"></property>
        <property name="serverName" value="insurance-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-insurance-api.server}/rpc/rule"></property>
        <property name="serviceInterface" value="com.wosai.bsm.insurancebackend.service.RuleService"></property>
        <property name="serverName" value="insurance-backend-service"></property>
    </bean>
    <!-- insurance end -->
    <!-- upay-activity begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-activity-api.server}rpc/activity"></property>
        <property name="serviceInterface" value="com.wosai.upay.activity.service.ActivityService"></property>
        <property name="serverName" value="upay-activity"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-activity-api.server}rpc/activitywhitelist"></property>
        <property name="serviceInterface" value="com.wosai.upay.activity.service.ActivityWhiteListService"></property>
        <property name="serverName" value="upay-activity"></property>
    </bean>
    <!-- upay-activity end -->
    <!-- ecards begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.ecards-api.server}rpc/card"></property>
        <property name="serviceInterface" value="com.wosai.pub.ecards.service.CardService"></property>
        <property name="serverName" value="ecards"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.ecards-api.server}rpc/usagerecord"></property>
        <property name="serviceInterface" value="com.wosai.pub.ecards.service.UsageRecordService"></property>
        <property name="serverName" value="ecards"></property>
    </bean>
    <!-- ecards end -->
    <!-- statistics-backend start-->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.statistics-backend.service}rpc/redPacketStat"></property>
        <property name="serviceInterface"
                  value="com.wosai.upay.statisticsbackend.service.RedPacketStatService"></property>
        <!--<property name="requestListener" ref="jsonRpcRequestListener"/>-->
        <property name="serverName" value="statistics-backend"/>
    </bean>
    <!-- statistics-backend end-->
    <!-- loan start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-loan-api.server}/rpc/loan"></property>
        <property name="serviceInterface" value="com.wosai.bsm.loanbackend.service.LoanService"></property>
        <property name="serverName" value="loan-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-loan-api.server}/rpc/whitelist"></property>
        <property name="serviceInterface" value="com.wosai.bsm.loanbackend.service.ProviderWhitelistService"></property>
        <property name="serverName" value="loan-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-loan-api.server}/rpc/userinfo"></property>
        <property name="serviceInterface" value="com.wosai.bsm.loanbackend.service.UserInfoService"></property>
        <property name="serverName" value="loan-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-loan-api.server}/rpc/merchantinfo"></property>
        <property name="serviceInterface" value="com.wosai.bsm.loanbackend.service.MerchantInfoService"></property>
        <property name="serverName" value="loan-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-loan-api.server}/rpc/applyrecord"></property>
        <property name="serviceInterface" value="com.wosai.bsm.loanbackend.service.ApplyRecordService"></property>
        <property name="serverName" value="loan-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-loan-api.server}/rpc/refundrecord"></property>
        <property name="serviceInterface" value="com.wosai.bsm.loanbackend.service.RefundRecordService"></property>
        <property name="serverName" value="loan-backend-service"></property>
    </bean>
    <!-- loan end -->
    <!-- credit-pay-backend start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-credit-pay-api.server}/rpc/userInfo"></property>
        <property name="serviceInterface" value="com.wosai.bsm.creditpaybackend.service.UserInfoService"></property>
        <property name="serverName" value="credit-pay-backend-service"></property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-credit-pay-api.server}/rpc/bdGoodsInfo"></property>
        <property name="serviceInterface" value="com.wosai.bsm.creditpaybackend.service.BdGoodsInfoService"></property>
        <property name="serverName" value="credit-pay-backend-service"></property>
    </bean>
    <!-- credit-pay-backend end -->
    <!-- opr-task-backend start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.opr-task-backend.server}/rpc/backend/task"></property>
        <property name="serviceInterface" value="com.wosai.operation.task.service.TaskService"></property>
        <property name="serverName" value="opr-task-backend-service"></property>
    </bean>
    <!-- opr-task-backend end -->
    <!-- iot start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.iot.server}rpc/iot"></property>
        <property name="serviceInterface" value="com.wosai.iot.api.rpc.SmartCloudSoundService"/>
        <property name="serverName" value="iot"/>
        <property name="objectMapper">
            <bean class="com.wosai.upay.bean.IgnoreMapper" />
        </property>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.iot.server}rpc/deviceQrcode"></property>
        <property name="serviceInterface" value="com.wosai.iot.api.sp.service.DeviceQrcodeService"/>
        <property name="serverName" value="iot"/>
    </bean>
    <!-- iot end -->
    <!-- sales-addin start-->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-addin.server}rpc/fileTask"></property>
        <property name="serviceInterface" value="com.wosai.sales.addin.service.FileTaskService"></property>
        <!--<property name="requestListener" ref="jsonRpcRequestListener"/>-->
        <property name="serverName" value="sales-addin"/>
    </bean>

    <!-- merchant-contract-job begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-contract-job.server}/rpc/contractWeixin"></property>
        <property name="serviceInterface" value="com.wosai.upay.job.service.ContractWeixinService"/>
        <property name="serverName" value="merchant-contract-job"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-contract-job.server}/rpc/wxBlackList"></property>
        <property name="serviceInterface" value="com.wosai.upay.job.service.WeixinBlackListService"/>
        <property name="serverName" value="merchant-contract-job"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-contract-job.server}/rpc/merchantProviderParams"></property>
        <property name="serviceInterface" value="com.wosai.upay.job.service.MerchantProviderParamsService"/>
        <property name="serverName" value="merchant-contract-job"/>
    </bean>
    <!-- merchant-contract-job end -->
    <!-- sales-addin end-->

    <!-- app-config start-->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153"
          p:serviceUrl="${jsonrpc.app-config.server}rpc/push"
          p:serviceInterface="com.wosai.app.service.PushConfigService"
          p:serverName="app-config"/>
    <!-- app-config end-->

    <!-- terminal-sales start-->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.terminal-sales.server}rpc/terminalOrder"></property>
        <property name="serviceInterface" value="com.wosai.sales.terminal.service.TerminalOrderService"></property>
        <!--<property name="requestListener" ref="jsonRpcRequestListener"/>-->
        <property name="serverName" value="shouqianba-terminal-sales"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.terminal-sales.server}rpc/terminalSalesConfig"></property>
        <property name="serviceInterface" value="com.wosai.sales.terminal.service.TerminalSalesConfigService"></property>
        <!--<property name="requestListener" ref="jsonRpcRequestListener"/>-->
        <property name="serverName" value="shouqianba-terminal-sales"/>
    </bean>
    <!-- terminal-sales end-->
    <!-- merchant_bank_service start -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-bank.server}rpc/merchantBank"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.service.BankService"></property>
        <property name="serverName" value="merchant-bank-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.merchant-bank.server}rpc/bank_business_license"></property>
        <property name="serviceInterface" value="com.wosai.upay.bank.service.BankBusinessLicenseService"></property>
        <property name="serverName" value="merchant-bank-service"/>
    </bean>

    <!-- merchant_bank_service end -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonprc.aop-backend-notification-api.server}/rpc/notice"></property>
        <property name="serviceInterface" value="com.wosai.aop.backend.notification.service.NoticeService"></property>
        <property name="serverName" value="aop-notice-service"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonprc.upay-swipe-api.server}rpc/swipe"></property>
        <property name="serviceInterface" value="com.wosai.upay.swipe.service.SwipeServices"></property>
        <property name="serverName" value="upay-swipe"/>
    </bean>

    <!--ip转换-->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sales-system-poi.server}rpc/ipquery"></property>
        <property name="serviceInterface" value="com.wosai.sales.service.IpLocationQueryService"></property>
        <property name="serverName" value="sales-system-poi"/>
    </bean>

    <!--sales-system-push-->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonprc.sales-system-push.server}rpc/mail"></property>
        <property name="serviceInterface" value="com.wosai.sales.push.service.MailService"></property>
        <property name="serverName" value="sales-system-push"/>
    </bean>

    <!-- upay-grayscale begin -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.upay-grayscale.server}rpc/merchantGray"></property>
        <property name="serviceInterface" value="com.wosai.service.IMerchantGrayService"/>
        <property name="serverName" value="upay-grayscale"/>
    </bean>

    <!-- sp-workflow-service -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.sp-workflow-service.server}/rpc/audit"></property>
        <property name="serviceInterface" value="com.shouqianba.workflow.service.AuditService"></property>
        <property name="serverName" value="sales-system-push"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonrpc.trade-manage-service.server}rpc/switch"></property>
        <property name="serviceInterface" value="com.wosai.trade.service.SwitchService"></property>
        <property name="serverName" value="manage-service"/>
    </bean>

    <!-- merchant-management-service -->
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153">
        <property name="serviceUrl" value="${jsonprc.merchant-management.server}/rpc/merchantDisable"></property>
        <property name="serviceInterface" value="com.wosai.sp.merchant.management.service.MerchantDisableService"></property>
        <property name="serverName" value="manage-service"/>
    </bean>

    <!-- 定义文件上传 -->
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="104857600"/>
        <property name="defaultEncoding" value="UTF-8"/>
    </bean>
    <bean class="org.springframework.mail.javamail.JavaMailSenderImpl">
        <property name="host" value="${mail.host}"></property>
        <property name="port" value="${mail.port}"></property>
        <property name="protocol" value="${mail.protocal}"></property>
        <property name="username" value="${mail.username}"></property>
        <property name="password" value="${mail.password}"></property>
        <property name="javaMailProperties">
            <props>
                <prop key="mail.smtp.auth">${mail.auth}</prop>
                <prop key="mail.smtp.timeout">${mail.timeout}</prop>
                <prop key="mail.smtp.ssl.enable">${mail.ssl.enable}</prop>
            </props>
        </property>
    </bean>
    <!--<import resource="kafka-config.xml" />-->
</beans>