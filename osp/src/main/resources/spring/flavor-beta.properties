#core-business
jsonrpc.core-business.server=http://core-business.core.svc.cluster.local/

#bank-info-service
jsonrpc.bank-info-service.server=http://bank-info-service.core.svc.cluster.local/

#merchant-audit-service
jsonrpc.merchant-audit.server=http://merchant-audit-service.osp.svc.cluster.local/

#user-service
jsonrpc.user-service.server=http://user-service.core.svc.cluster.local/

jsonrpc.upay-wallet.server=http://upay-wallet.pay.svc.cluster.local/

#app-backend-api
jsonrpc.app-backend-api.server=http://app-backend-service.core.svc.cluster.local/

#app-gated-api
jsonrpc.app-gated-api.server=http://app-gated-service.core.svc.cluster.local/

#shouqianba-withdraw-service
jsonrpc.shouqianba-withdraw-service.server=http://shouqianba-withdraw-service.pay.svc.cluster.local/

#business-log-service
jsonrpc.bizlog.server = http://business-log.osp.svc.cluster.local/

#business-audit-api
jsonrpc.bizaudit.server = http://business-audit.osp.svc.cluster.local/

#sales-system-backend-api
jsonrpc.sales-system-backend-api.server=http://sales-system-backend.beta.iwosai.com/

#shouqianba-merchant-api
jsonrpc.shouqianba-merchant-api.server=http://shouqianba-merchant-service.osp.svc.cluster.local/

#sales-system-service-api
jsonrpc.sales-system-service-api.server=http://sales-system-service.crm.svc.cluster.local/

#shouqianba-risk-service-api
jsonrpc.shouqianba-risk-service-api.server=http://shouqianba-risk-service.osp.svc.cluster.local/

#app-push-service
jsonrpc.app-push-service.server=http://app-push-service.ter.svc.cluster.local/

#upay-transaction-api
jsonrpc.upay-transaction-api-querry.server=http://upay-transaction.core.svc.cluster.local/
jsonrpc.upay-transaction-api-export.server=http://upay-transaction.core.svc.cluster.local/

#upay-side-api
jsonrpc.upay-side-api.server=http://upay-side.pay.svc.cluster.local/

#alipay-authinto-api
jsonrpc.alipay-authinto-api.server=http://alipay-authinto.pay.svc.cluster.local/

#alipay-risk-api
jsonrpc.alipay-risk-api.server=http://alipay-risk.osp.svc.cluster.local/

#upay-finance-api
jsonrpc.upay-finance-api.server=http://finance-backend.fin.svc.cluster.local/

#sp-user-service
jsonrpc.sp-user-service.server=http://sp-user-service.osp.svc.cluster.local/

#janus
jsonrpc.cas-server.server=http://janus-cas.beta.iwosai.com/

#sms host短信网关发送地址
sms.host=http://sms-gateway.core.svc.cluster.local/sms/send

#backend.upay.server
backend.upay.server=http://backend-upay.pay.svc.cluster.local/

#upay.gateway.server
upay.gateway.server=http://upay-gateway.pay.svc.cluster.local/

#jsonrpc.merchant-level.server
jsonrpc.merchant-level.server=http://merchant-level.osp.svc.cluster.local/
jsonrpc.iot.server=http://shouqianba-iot-service.core.svc.cluster.local/

#remit-gateway
jsonrpc.remit.server=http://remit-gateway.osp.svc.cluster.local/

#upay-swipe.server
jsonprc.upay-swipe-api.server=http://upay-swipe.pay.svc.cluster.local/

# ip discern server
jsonrpc.sales-system-poi.server=http://sales-system-poi.test.shouqianba.com/

#sales-system-push.server
jsonprc.sales-system-push.server=http://sales-system-push.test.shouqianba.com/

#merchant-management.server
jsonprc.merchant-management.server=http://merchant-management.osp.svc.cluster.local

#mail
mail.host=smtp.mxhichina.com
mail.port=465
mail.protocal=smtp
mail.username=<EMAIL>
mail.password=Test2018
mail.auth=true
mail.timeout=5000
mail.ssl.enable=true

#notification.mail.order.sendto
notification.mail.order.sendto=<EMAIL>

#redis
redis.url=redis-beta.base.svc.cluster.local
redis.port=6379
redis.database=2
redis.password=roFXzHwXPY3RnI%5
#cacheService缓存数据的过期时间，单位毫秒
redis.cacheService.expiredTime=86400
#cacheService缓存数据key的前缀
redis.cacheService.keyPrefix=mini_info

#cas config
cas.server.url=http://janus-cas.beta.iwosai.com/
osp.server.url=http://web-platforms-osp.beta.iwosai.com
osp.client.url=http://shouqianba-osp-portal.beta.iwosai.com

#osp oauth_login config
cronut_url=http://cronut.beta.shouqianba.com/
osp.authorize_url=http://cronut.beta.shouqianba.com/api/authorize
osp.token_url=http://cronut.beta.shouqianba.com/api/access_token
osp.userInfo_url=http://cronut.beta.shouqianba.com/api/get_info
osp.client_id=9d8ed4b1-9dca-11e7-8f4f-a0d3c1fd2c04
osp.response_type=code
osp.redirect_uri=http://web-platforms-osp.beta.iwosai.com/api/oauth/callBack
osp.grant_type=authorization_code
osp.client_secret=osp_beta

#oAuth登录成功后 重定向到主页
#oAuth\u767B\u5F55\u6210\u529F\u540E \u91CD\u5B9A\u5411\u5230\u4E3B\u9875
homeUrl=http://shouqianba-osp-portal.beta.iwosai.com

#marketing-service
api.shouqianba.server = http://app-edge.beta.iwosai.com/

jsonrpc.upay-qrcode.server = http://upay-qrcode.core.svc.cluster.local/

##Tracing
bootstrap.servers=kafka-beta1.base:9092,kafka-beta2.base:9092,kafka-beta3.base:9092
spring.application.name=osp-beta
samplerRate=1.0f

#kafka
kafka.bootstrap.servers=116.62.107.71:9092
kafka.acks=1
kafka.retries=3
kafka.linger.ms=500
kafka.batch.size=5
kafka.max.block.ms=1000
kafka.registry.url=http://116.62.107.71:8081

#aop notice config
aop.notice.product.id.risk=dcb167d5-873c-4718-8145-e40220e5c382
aop.notice.terminal.id.app=dfee2ee5-b1e7-11e9-9807-7cd30ae435b2

# reward-service
jsonrpc.reward.server=http://reward-service-core.opr.svc.cluster.local/
jsonrpc.opr-point.server=http://opr-point.opr.svc.cluster.local/

jsonrpc.notice-service.server=http://notice-service.core.svc.cluster.local/

#opr-merchant-activity
jsonrpc.opr-merchant-activity.server=http://opr-merchant-activity.opr.svc.cluster.local/
#merchant-card
jsonrpc.merchant-card.server=http://merchant-card.opr.svc.cluster.local/
#opr-merchant-tool
jsonrpc.merchant-tool.server=http://opr-merchant-tool.opr.svc.cluster.local/
#sales-system-profit-api
jsonrpc.profit-api.server=http://sales-system-profit.crm.svc.cluster.local/
#wosai-preorder-risk
jsonrpc.preorder-risk.server=http://wosai-preorder-risk.msp.svc.cluster.local/
#merchant-contract-api
jsonrpc.merchant-contract-api.server = http://merchant-contract.core.svc.cluster.local/

#clearance-service
jsonrpc.clearance-service-api.server = http://clearance-service.pay.svc.cluster.local/

#upay-insurance-api
jsonrpc.upay-insurance-api.server=http://insurance-backend.fin.svc.cluster.local/
#upay-activity-api
jsonrpc.upay-activity-api.server=http://upay-activity.mk.svc.cluster.local/
#ecards-api.server
jsonrpc.ecards-api.server=http://ecards.mk.svc.cluster.local/
#statistics_backend service
jsonrpc.statistics-backend.service=http://statistics-backend.bsm.svc.cluster.local/

jsonrpc.sales-addin.server=http://sales-system-addin.crm.svc.cluster.local/

jsonrpc.app-config.server=http://app-config-service.core.svc.cluster.local/

#upay-loan-api
jsonrpc.upay-loan-api.server=http://loan-backend.loan.svc.cluster.local/

#upay-credit-pay-api
jsonrpc.upay-credit-pay-api.server=http://credit-pay-backend.fin.svc.cluster.local/

#opr-task-backend
jsonrpc.opr-task-backend.server=http://opr-task-backend.opr.svc.cluster.local/

jsonrpc.merchant-contract-job.server =http://merchant-contract-job.core.svc.cluster.local/

jsonrpc.terminal-sales.server=http://shouqianba-terminal-service.crm.svc.cluster.local/

com.wosai.oss.static-bucket=wosai-statics
com.wosai.oss.static-base-url=https://statics.wosaimg.com
com.wosai.oss.internal=false

jsonrpc.merchant-bank.server=http://merchant-bank-service.core.svc.cluster.local/

#aop-backend-notification-api
jsonprc.aop-backend-notification-api.server=http://aop-backend-notification.opr/

#merchant-invoice-api
jsonrpc.merchant-invoice-api.server=http://merchant-invoice.osp.svc.cluster.local/

#oss
oss.base-url=https://wosai-statics.oss-cn-hangzhou.aliyuncs.com/

jsonrpc.profit-share=http://profit-sharing.pay/

#upay-grayscale
jsonrpc.upay-grayscale.server=http://upay-grayscale.pay/

jsonrpc.sp-workflow-service.server=http://sp-workflow-service.osp.svc.cluster.local/

#trade-manage-service
jsonrpc.trade-manage-service.server=http://trade-manage.core.svc.cluster.local/