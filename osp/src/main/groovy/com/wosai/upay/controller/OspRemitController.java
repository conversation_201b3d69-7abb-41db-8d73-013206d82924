package com.wosai.upay.controller;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.helper.FileUtil;
import com.wosai.upay.helper.SheetUtil;
import com.wosai.upay.helper.StringUtil;
import com.wosai.upay.remit.RemitConsts;
import com.wosai.upay.remit.enums.*;
import com.wosai.upay.remit.model.RemitBatch;
import com.wosai.upay.remit.model.RemitOrder;
import com.wosai.upay.remit.service.RemitBatchService;
import com.wosai.upay.remit.service.RemitDetailService;
import com.wosai.upay.remit.service.RemitOrderService;
import com.wosai.upay.service.CommonLoginService;
import com.wosai.upay.service.OssFileUploader;
import com.wosai.upay.util.HttpRequestUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: yinchengjian
 * @Description:
 * @Date: 2018/6/23
 * @Modified By:
 */
@SuppressWarnings("unchecked")
@Controller
@RequestMapping("/api/remit")
public class OspRemitController extends ExceptionHandlerController {


    private static final String OSP_REMIT_BASE_DIR = "osp/remit_order/";

    private static final String ACCOUNT_TYPE_NAME = "account_type_name";

    private static final String ORDER_TYPE_NAME = "order_type_name";

    private static final String STATUS_NAME = "status_name";

    private static final String PROVIDER_NAME = "provider_name";

    private static final String FLOW_TYPE = "flow_type";

    private static final String ENDPOINT_URL = "http://wosai-statics.oss-cn-hangzhou.aliyuncs.com/";

    private static Map downLoadMap = CollectionUtil.hashMap(
            RemitOrder.REQUEST_TYPE , "业务类型" ,
            RemitOrder.REQUEST_FLOW_NO , "业务方订单号" ,

            RemitOrder.BANK_CARD_NO , "收款账号" ,
            RemitOrder.BANK_HOLDER_NAME , "收款账户名" ,
            RemitOrder.BANK_CODE , "收款银行代码" ,
            RemitOrder.BANK_UNIQUE_CODE , "联行行号" ,
            RemitOrder.BANK_UNIQUE_NAME , "联行行名" ,
            RemitOrder.ABSTRACT , "摘要" ,
            RemitOrder.AMOUNT , "付款金额 (元)" ,
            RemitOrder.STATUS , "状态" ,
            RemitOrder.PROVIDER , "付款通道" ,
            RemitOrder.MESSAGE , "通道应答描述" ,
            RemitOrder.PROVIDER_MERCHANT_SN , "付款通道商户号" ,
            ORDER_TYPE_NAME , "交易类型" ,
            RemitOrder.CURRENCY , "币种" ,
            RemitOrder.ACCOUNT_TYPE , "账户类型" ,
            RemitOrder.PAY_SUBMIT_DATE , "交易时间" ,
            DaoConstants.CTIME , "请求付款时间"
    );

    @Autowired
    private RemitBatchService remitBatchService;

    @Autowired
    private RemitOrderService remitOrderService;

    @Autowired
    private  RemitDetailService remitDetailService;

    @Autowired
    private OssFileUploader ossFileUploader;

    /**
     * 1.1 财务打款报表列表
     */
    @RequestMapping(produces = "application/json", value = "/getBatchsWithCount")
    public Map getBatchsWithCount(@RequestBody Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        Map queryFilter = CollectionUtil.hashMap(
                RemitBatch.PAY_SEND_DATE, BeanUtil.getPropString(request, RemitBatch.PAY_SEND_DATE)
        );
        ListResult result = remitBatchService.findBatchsWithCount(pageInfo , queryFilter);
        for (Map map : result.getRecords()) {
            map.put(PROVIDER_NAME , RemitProvider.LAKALA.getDesc());
            map.put(STATUS_NAME , RemitBatchStatus.getDescByValue(MapUtils.getString(map , RemitBatch.STATUS)));
        }
        return astHelper.handleResult(log , result);
    }

    /**
     * 1.2 确认打款
     */
    @RequestMapping(produces = "application/json", value = "/confirmRemitBatch")
    public Map confirmRemitBatch(@RequestBody Map request) {
        request = getUserIdAndName(request);
        return astHelper.handleResult(log , remitBatchService.confirmRemitBatch(request).get(RemitConsts.SUCCESS));
    }

    /**
     * 2.1 付款订单管理列表
     */
    @RequestMapping(produces = "application/json", value = "/findRemitOrders")
    public Map findRemitOrders(@RequestBody Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        Map queryFilter = CollectionUtil.hashMap(
                RemitOrder.PAY_SUBMIT_DATE, BeanUtil.getPropString(request, RemitOrder.PAY_SUBMIT_DATE),
                RemitOrder.STATUS, BeanUtil.getPropString(request, RemitOrder.STATUS),
                RemitOrder.PLATFORM, BeanUtil.getPropString(request, RemitOrder.PLATFORM),
                RemitOrder.PROVIDER, BeanUtil.getPropString(request, RemitOrder.PROVIDER),
                RemitOrder.REQUEST_FLOW_NO, BeanUtil.getPropString(request, RemitOrder.REQUEST_FLOW_NO),
                RemitOrder.BANK_CARD_NO, BeanUtil.getPropString(request, RemitOrder.BANK_CARD_NO),
                RemitOrder.BANK_HOLDER_NAME, BeanUtil.getPropString(request, RemitOrder.BANK_HOLDER_NAME),
                RemitConsts.PAY_SUBMIT_DATE_START, BeanUtil.getPropString(request, RemitConsts.PAY_SUBMIT_DATE_START),
                RemitConsts.PAY_SUBMIT_DATE_END, BeanUtil.getPropString(request, RemitConsts.PAY_SUBMIT_DATE_END),
                RemitConsts.ORDERBY, BeanUtil.getPropString(request, RemitConsts.ORDERBY),
                RemitOrder.EXTRA,BeanUtil.getProperty(request,RemitOrder.EXTRA)
        );
        ListResult result = remitOrderService.findRemitOrders(pageInfo, queryFilter);
        for (Map map : result.getRecords()) {
            map.put(PROVIDER_NAME, RemitProvider.LAKALA.getDesc());
            map.put(STATUS_NAME, RemitOrderStatus.getDescByValue(MapUtils.getString(map, RemitOrder.STATUS)));
        }
        Long amount = remitOrderService.getRemitOrdersAmount(pageInfo, queryFilter);
        return astHelper.handleResult(log, CollectionUtil.hashMap(
                "amount", amount, "total", result.getTotal(), "records", result.getRecords()));
    }

    /**
     * 2.2 更改状态
     */
    @RequestMapping(produces = "application/json", value = "/updateRemitOrderStatus")
    public Map updateRemitOrderStatus(@RequestBody Map request) {
        request = getUserIdAndName(request);
        return astHelper.handleResult(log , remitOrderService.updateRemitOrder(request).get(RemitConsts.SUCCESS));
    }


    /**
     * 2.3 代付详情页面
     */
    @RequestMapping(produces = "application/json", value = "/getRemitOrderDetail")
    public Map getRemitOrderDetail(@RequestBody Map request) {
        String orderId = BeanUtil.getPropString(request, DaoConstants.ID);
        if (StringUtils.isEmpty(orderId)) {
            throw  new UpayException(UpayException.CODE_INVALID_PARAMETER, "id不能为空");
        }
        Map orderMap = remitOrderService.getRemitOrder(orderId);
        orderMap.put(PROVIDER_NAME , RemitProvider.LAKALA.getDesc());
        orderMap.put(STATUS_NAME ,RemitOrderStatus.getDescByValue(MapUtils.getString(orderMap , RemitOrder.STATUS)));
        orderMap.put(ACCOUNT_TYPE_NAME , RemitOrderAccountType.getDescByValue(MapUtils.getIntValue(orderMap , RemitOrder.ACCOUNT_TYPE)));
        orderMap.put(ORDER_TYPE_NAME , "代付");
        List<Map<String, Object>> detailLists = remitDetailService.getRemitDetails(orderId , OrderBy.OrderType.ASC);
        for (Map<String, Object> map : detailLists) {
            map.put(FLOW_TYPE, "代付");
            map.put(RemitOrder.AMOUNT , MapUtils.getString(orderMap , RemitOrder.AMOUNT));
        }
        return astHelper.handleResult(log , CollectionUtil.hashMap(
                "order", orderMap , "detail" , detailLists));
    }

    /**
     * 2.4 下载查询结果
     */
    @RequestMapping(produces = "application/json", value = "/downloadRemitOrders")
    public Map downloadRemitOrders(@RequestBody Map request)  throws IOException {
        //待打款
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        Map queryFilter = CollectionUtil.hashMap(
                RemitOrder.PAY_SUBMIT_DATE, BeanUtil.getPropString(request, RemitOrder.PAY_SUBMIT_DATE) ,
                RemitOrder.STATUS, BeanUtil.getPropString(request, RemitOrder.STATUS) ,
                RemitOrder.PLATFORM, BeanUtil.getPropString(request, RemitOrder.PLATFORM) ,
                RemitOrder.PROVIDER, BeanUtil.getPropString(request, RemitOrder.PROVIDER) ,
                RemitConsts.PAY_SUBMIT_DATE_START , BeanUtil.getPropString(request, RemitConsts.PAY_SUBMIT_DATE_START) ,
                RemitConsts.PAY_SUBMIT_DATE_END , BeanUtil.getPropString(request, RemitConsts.PAY_SUBMIT_DATE_END)

        );
        ListResult data = remitOrderService.findRemitOrders(pageInfo , queryFilter);

        long total = data.getTotal();
        int pageSize = 65530;
        long pageCount = total % pageSize == 0 ? total / pageSize : total / pageSize + 1;

        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("打款订单");
        SheetUtil sheetUtil = new SheetUtil(sheet);
        sheetUtil.appendRow(CollectionUtil.listOf(downLoadMap.values()));

        for (int i = 0; i < pageCount; i++) {
            pageInfo.setPage(i + 1);
            pageInfo.setPageSize(pageSize);
            List<Map> orders = getData(pageInfo , queryFilter);

            sheetUtil.appendRows(CollectionUtil.listOf(downLoadMap.keySet()) , orders);
        }

        String fileName = "remit_" + System.currentTimeMillis();
        String fileExt = ".xlsx";
        String fullName = fileName + fileExt;
        String ossKey = OSP_REMIT_BASE_DIR + fullName;
        String ossUrl = ENDPOINT_URL + ossKey;
        File file = File.createTempFile(fileName, fileExt);
        FileUtil.excelToFile(workbook, file.getAbsolutePath());

        FileInputStream inputStream = null;
        try {
            inputStream = new FileInputStream(file);
            ossFileUploader.uploadStaticsFile(ossKey, inputStream, file.length());
//            OSSClient client = new OSSClient("http://oss.aliyuncs.com", "LTAIgdvAVQ7FnGIO", "Da99zhuALMOrvIQquyGe7q36gEmuJR");
//            ObjectMetadata objectMeta = new ObjectMetadata();
//            objectMeta.setContentLength(file.length());
//            client.putObject("wosai-statics", ossKey, inputStream, objectMeta);
//            inputStream.close();
            return astHelper.handleResult(log, ossUrl);
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    /**
     * 2-5 应用方列表接口
     */
    @RequestMapping(produces = "application/json", value = "/getRemitOrderPlatforms")
    public Map getRemitOrderPlatforms(@RequestBody Map request)  throws IOException {
        JSONObject jsonObject = remitOrderService.getRemitSupportPlatforms();
        Map<String,String> map=new HashMap<>();
        for (String key:jsonObject.keySet()) {
            map.put(key , jsonObject.getString(key));
        }
        return astHelper.handleResult(log, map);
    }

    /**
     * 3.1 代付手续费报表
     */
    @RequestMapping(produces = "application/json", value = "/getRemitOrderFee")
    public Map getRemitOrderFee(@RequestBody Map request) {
        PageInfo pageInfo = PageInfoUtil.extractPageInfo(request);
        try {
            List<Map<String, Object>> list = remitOrderService.getRemitOrderFee(pageInfo, request);
            for (Map<String, Object> map : list) {
                map.put(PROVIDER_NAME, RemitProvider.LAKALA.getDesc());
            }
            return astHelper.handleResult(log, list);
        } catch (Exception e) {
            return astHelper.handleException(log, e);
        }
    }


    private List<Map> getData(PageInfo pageInfo , Map queryFilter) {
        ListResult data = remitOrderService.findRemitOrders(pageInfo , queryFilter);
        if (null == data || CollectionUtils.isEmpty(data.getRecords())) {
            return Collections.EMPTY_LIST;
        }
        ObjectMapper mapper = new ObjectMapper();
        for (Map map : data.getRecords()) {
            map.put(RemitOrder.STATUS , RemitOrderStatus.getDescByValue(MapUtils.getString(map , RemitOrder.STATUS)));
            map.put(DaoConstants.CTIME , DateFormatUtils.format(MapUtils.getLongValue(map, DaoConstants.CTIME), "yyyy-MM-dd hh:mm:ss"));
            map.put(RemitOrder.PAY_SUBMIT_DATE , DateFormatUtils.format(MapUtils.getLongValue(map, RemitOrder.PAY_SUBMIT_DATE), "yyyy-MM-dd hh:mm:ss"));
            map.put(RemitOrder.AMOUNT , MapUtils.getLongValue(map , RemitOrder.AMOUNT) / 100);
            map.put(ORDER_TYPE_NAME , "代付");
            map.put(RemitOrder.CURRENCY , RemitOrderCurrency.getDescByValue(MapUtils.getString(map , RemitOrder.CURRENCY)));
            map.put(RemitOrder.ACCOUNT_TYPE , RemitOrderAccountType.getDescByValue(MapUtils.getIntValue(map , RemitOrder.ACCOUNT_TYPE)));
            map.put(RemitOrder.PROVIDER , RemitProvider.getDescByValue(MapUtils.getIntValue(map , RemitOrder.PROVIDER)));
        }

        return data.getRecords();
    }

    private Map getUserIdAndName(Map map) {
        HttpSession session = HttpRequestUtil.getSession();
        Object accountId = session.getAttribute(CommonLoginService.SESSION_ACCOUNT_ID);
        Object username = session.getAttribute(CommonLoginService.SESSION_USERNAME);
        map.put(RemitOrder.OPERATOR, accountId);
        map.put(RemitConsts.OPERATOR_NAME, username);
        return map;
    }
}
