package com.wosai.upay.controller

import com.wosai.data.util.CollectionUtil
import com.wosai.merchant.Bean.MerchantInvoiceOrder
import com.wosai.operation.service.MerchantInvoiceService
import com.wosai.reward.entity.AccountChangePointsRequest
import com.wosai.upay.common.exception.CommonException
import com.wosai.upay.exception.UpayException
import com.wosai.upay.helper.CommonUtil
import com.wosai.upay.rest.ast.ASTHelper
import com.wosai.upay.service.BatchMerchantInfoService
import com.wosai.upay.service.OspAlipayService
import com.wosai.upay.service.OspCardsService
import com.wosai.upay.service.OspClearanceService
import com.wosai.upay.service.OspFinanceService
import com.wosai.upay.service.OspGroupService
import com.wosai.upay.service.OspInsuranceService
import com.wosai.upay.service.OspLoanService
import com.wosai.upay.service.OspPointsService
import com.wosai.upay.service.OspWeixinBlackListService
import com.wosai.upay.service.OspWithdrawService
import com.wosai.upay.util.ExcelUtil
import jodd.util.StringUtil
import org.apache.commons.collections.MapUtils
import org.apache.commons.lang.StringUtils
import org.apache.poi.ss.usermodel.Workbook
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.util.CollectionUtils
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.multipart.MultipartFile

import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse
import java.util.concurrent.Executors
import java.util.concurrent.ThreadPoolExecutor

/**
 * yaming.rong.
 */
@Controller
@RequestMapping("/api/import")
class ImportController {

    private Logger log = LoggerFactory.getLogger(ImportController.class);
    @Autowired
    private ASTHelper astHelper;
    @Autowired
    OspGroupService ospGroupService
    @Autowired
    private OspWithdrawService ospWithdrawService;
    @Autowired
    private OspFinanceService ospFinanceService;
    @Autowired
    OspPointsService ospPointsService;
    @Autowired
    private OspCardsService ospCardsService
    @Autowired
    private OspClearanceService ospClearanceService;
    @Autowired
    private OspInsuranceService insuranceService;
    @Autowired
    private OspLoanService loanService;

    @Autowired
    private OspAlipayService ospAlipayService

    @Autowired
    private OspWeixinBlackListService ospWeixinBlackListService;
    @Autowired
    private MerchantInvoiceService merchantInvoiceService;
    @Autowired
    private BatchMerchantInfoService batchMerchantInfoService;
    private ThreadPoolExecutor importExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(2);


    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importGroupMerchantAuth")
    @ResponseBody
    public Map importGroupMerchantAuth(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        long start = System.currentTimeMillis()
        try {
            astHelper.logMethod(log, "importGroupMerchantAuth", [file.getOriginalFilename()])
            def data = ospGroupService.importGroupMerchantAuth(file)
            return astHelper.handleResult(log, data)
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        } finally {
            long end = System.currentTimeMillis()
            astHelper.logMethodTime(log, "importGroupMerchantAuth", start, end)
        }
    }

    @RequestMapping(produces = "application/octet-stream", value = "/getImportGroupMerchantAuthFailDetail")
    @ResponseBody
    public void getImportGroupMerchantAuthFailDetail(@RequestParam Map params, HttpServletResponse response) {
        ospGroupService.getImportGroupMerchantAuthFailDetail(params, response);
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importDrawRealTime")
    @ResponseBody
    public Map importDrawRealTime(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        try {
            return astHelper.handleResult(log, ospWithdrawService.batchImportDrawRealTime(file, request.getParameter("notice_code"), request.getParameter("notice_sub_code")));
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importDeleteMerchantD0DisableReason")
    @ResponseBody
    public Map importDeleteMerchantD0DisableReason(@RequestParam("file") MultipartFile file) {
        try {
            return astHelper.handleResult(log, ospWithdrawService.batchDeleteMerchantD0DisableReason(file))
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }


    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importBatchWhitelist")
    @ResponseBody
    public Map importBatchWhitelist(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        try {
            return astHelper.handleResult(log, ospFinanceService.batchImportWhitelist(file));
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    /**
     * 批量开关余额提现页面理财转入入口
     * @param file
     * @param request
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/batchOpenOrCloseFinancePurchaseInWalletWithdraw")
    @ResponseBody
    public Map batchOpenOrCloseFinancePurchaseInWalletWithdraw(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        try {
            return astHelper.handleResult(log, ospFinanceService.batchOpenOrCloseFinancePurchaseInWalletWithdraw(file, request.getParameter("openOrClose")));
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importInsureWhitelist")
    @ResponseBody
    public Map importInsureWhitelist(@RequestParam("file") MultipartFile file, String tag) {
        try {
            return astHelper.handleResult(log, insuranceService.importInsureWhitelist(file, tag));
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/setLoanBatch")
    @ResponseBody
    public Map setLoanBatch(@RequestParam("file") MultipartFile file, String batch) {
        try {
            return astHelper.handleResult(log, loanService.setBatch(file, batch));
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/loanBatchChangeProvider")
    @ResponseBody
    public Map loanBatchChangeProvider(@RequestParam("file") MultipartFile file, @RequestParam Map params) {
        try {
            return astHelper.handleResult(log, loanService.batchChangeProvider(file, params));
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importDrawRealTimeConfig")
    @ResponseBody
    public Map importDrawRealTimeConfig(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        try {
            return astHelper.handleResult(log, ospWithdrawService.importDrawRealTimeConfig(file));
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(produces = "application/octet-stream", value = "/getImportDrawRealTimeFailDetail")
    @ResponseBody
    public void getImportDrawRealTimeFailDetail(@RequestParam Map params, HttpServletResponse response) {
        ospWithdrawService.getImportDrawRealTimeLog(params, response);
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importForceClearMerchants")
    @ResponseBody
    public Map importForceClearMerchants(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        try {
            return astHelper.handleResult(log, ospWithdrawService.batchImportForceClearMerchants(file));
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(produces = "application/octet-stream", value = "/getImportForceClearMerchantsDetail")
    @ResponseBody
    public void getImportForceClearMerchantsDetail(@RequestParam Map params, HttpServletResponse response) {
        ospWithdrawService.getImportForceClearMerchantsDetail(params, response);
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importGetHealthPoint")
    @ResponseBody
    public Map importGetHealthPoint(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        try {
            return astHelper.handleResult(log, ospAlipayService.importGetHealthPoint(file));
            //return astHelper.handleResult(log, ospWithdrawService.batchImportForceClearMerchants(file));
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(produces = "application/octet-stream", value = "/getImportGetHealthPoint")
    @ResponseBody
    public void getImportGetHealthPoint(@RequestParam Map params, HttpServletResponse response) {
        ospAlipayService.getImportGetHealthPoint(params, response);
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/batchImportAccountsToAddPoints")
    @ResponseBody
    Map importPointsAccountCellphonesToAddPoints(
            @RequestParam("file") MultipartFile file,
            @RequestParam("expired") String expired,
            HttpServletRequest request) {
        long start = System.currentTimeMillis()
        try {
            astHelper.logMethod(log, "batchImportAccountsToAddPoints", [file.getOriginalFilename(), expired])
            AccountChangePointsRequest.Expired exp
            for (AccountChangePointsRequest.Expired temp : AccountChangePointsRequest.Expired.values()) {
                if (temp.name() == expired) {
                    exp = temp
                    break
                }
            }
            if (exp) {
                def data = ospPointsService.batchImportAccountsToAddPoints(file, exp, request)
                return astHelper.handleResult(log, data)
            }
            return astHelper.handleResult(log, null)
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        } finally {
            long end = System.currentTimeMillis()
            astHelper.logMethodTime(log, "batchImportAccountsToAddPoints", start, end)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importCardsWhitelist")
    @ResponseBody
    public Map importCardsWhitelist(@RequestParam("file") MultipartFile file, @RequestParam Map params) {
        try {
            return astHelper.handleResult(log, ospCardsService.importCardsWhitelist(file, params))
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/checkCardsWhitelist")
    @ResponseBody
    public Map checkCardsWhitelist(@RequestParam("file") MultipartFile file) {
        try {
            return astHelper.handleResult(log, ospCardsService.checkCardsWhitelist(file))
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/batchImportCardsWhitelist")
    @ResponseBody
    public Map batchImportCardsWhitelist(@RequestParam("file") MultipartFile file, @RequestParam Map params) {
        try {
            return astHelper.handleResult(log, ospCardsService.batchImportCardsWhitelist(file, params));
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importD1WithdrawWhiteList")
    @ResponseBody
    Map importPriorMerchantWhiteList(@RequestParam("file") MultipartFile file) {
        try {
            return astHelper.handleResult(log, ospWithdrawService.importPriorMerchantWhiteList(file))
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importWithdrawBatchUpdateList")
    @ResponseBody
    Map importWithdrawBatchUpdateList(@RequestParam("file") MultipartFile file) {
        try {
            return astHelper.handleResult(log, ospClearanceService.batchUpdateWithdrawStatus(file))
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, consumes = "multipart/form-data", produces = "application/json")
    @ResponseBody
    Map uploadWithPOI(@RequestPart(value = "file") MultipartFile file,
                      @RequestParam(value = "watermark_flag", defaultValue = "false") boolean watermarkFlag,
                      @RequestParam(value = "ocr_type", required = false) String ocrType,
                      @RequestParam(defaultValue = "0") double longitude,
                      @RequestParam(defaultValue = "0") double latitude,
                      @RequestParam(value = "is_tolerant", defaultValue = "false") boolean isTolerant,
                      @RequestParam(required = false) String v) {
        try {

        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importWeixinBlackList")
    @ResponseBody
    Map importWeixinBlackList(@RequestParam("file") MultipartFile file) {
        //ospWeixinBlackListService.uploadBlackListExcel(file)
        try {
            return astHelper.handleResult(log, ospWeixinBlackListService.uploadBlackListExcel(file))
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importMerchantInvoiceExpressInfo")
    @ResponseBody
    Map importMerchantInvoiceExpressInfo(@RequestParam("file") MultipartFile file) {
        Workbook workbook = ExcelUtil.getExcelFromFile(file);
        List<Map> rows = ExcelUtil.getListFromSheet(workbook.getSheetAt(0), CollectionUtil.hashMap(
                "商户号", MerchantInvoiceOrder.MERCHANT_SN,
                "开票单号", MerchantInvoiceOrder.SN,
                "快递单号", MerchantInvoiceOrder.EXPRESS_NO,
                "快递公司", MerchantInvoiceOrder.EXPRESS_NAME
        ));

        if (CollectionUtils.isEmpty(rows)) {
            return astHelper.handleException(log, new UpayException(UpayException.CODE_INVALID_PARAMETER, "excel内容不能为空!"))
        }
        importExecutor.execute(new Runnable() {
            @Override
            void run() {
                try {
                    for (Map row : rows) {
                        String sn = MapUtils.getString(row, MerchantInvoiceOrder.SN);
                        String expressNo = MapUtils.getString(row, MerchantInvoiceOrder.EXPRESS_NO);
                        String expressName = MapUtils.getString(row, MerchantInvoiceOrder.EXPRESS_NAME);
                        if (CommonUtil.existBlankStr(sn, expressNo, expressName)) {
                            log.error("存在空数据,跳过更新物流信息");
                            continue;
                        }
                        merchantInvoiceService.updateOrder(CollectionUtil.hashMap(
                                MerchantInvoiceOrder.SN, sn,
                                MerchantInvoiceOrder.EXPRESS_NO, expressNo,
                                MerchantInvoiceOrder.EXPRESS_NAME, expressName
                        ))
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        })
        return astHelper.handleResult(log, null)
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/importToFindMerchantInfo")
    @ResponseBody
    Map importToFindMerchantInfo(@RequestParam("file") MultipartFile file, @RequestParam(value = "typeCode") Integer typeCode) {
        try {
            Long startTime = System.currentTimeMillis()
            log.info("开始时间：{}", startTime)
            Map result = astHelper.handleResult(log, batchMerchantInfoService.findMerchantInfo(file, typeCode))
            Long endTime = System.currentTimeMillis()
            log.info("结束时间：{}", endTime)
            return result
        } catch (Exception e) {
            return astHelper.handleException(log, e)
        }
    }

}
