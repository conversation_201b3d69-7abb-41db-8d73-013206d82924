package com.wosai.upay.controller;


import com.google.common.collect.Maps;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.StringUtil;
import com.wosai.notice.service.NoticeService;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.helper.ResponseHelper;
import com.wosai.upay.service.OssFileUploader;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Predicate;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * @create 2017-09-21 下午5:46
 */
@Controller
@RequestMapping("/api/notice")
public class NoticeController {

    public static final String IMAGES_OSS_INTERNAL = "http://wosai-images.oss-cn-hangzhou-internal.aliyuncs.com/";

    public static final String IMAGES_OSS_OUT = "http://images.wosaimg.com/";

    public static final String NOTICE_TO_ALL = "all";

    private static final Logger logger = LoggerFactory.getLogger(NoticeController.class);
    public static final String ROLES = "roles";

    @Value("${aop.notice.product.id.risk}")
    private String aopRiskNoticeProductID;

    @Value("${aop.notice.terminal.id.app}")
    private String appId;

    @Autowired
    OssFileUploader ossFileUploader;

    @Autowired
    private NoticeService noticeService;

    @Autowired
    private com.wosai.aop.backend.notification.service.NoticeService aopNoticeService;


    @RequestMapping("/query")
    public Map queryNotices(@RequestBody Map request) {
        Map response = ResponseHelper.getResponse(ResponseHelper.SUCCESS_CODE, ResponseHelper.SUCCESS_MSG);
        Integer page = null;
        Integer page_size = null;

        if (request != null) {
            page_size = (Integer) request.get("page_size");
            page = (Integer) request.get("page");
        }
        PageInfo pageInfo = new PageInfo(page, page_size);

        ListResult listResult = noticeService.queryNoticeList(pageInfo, null);
        ResponseHelper.addRecords(response, listResult.getRecords(), listResult.getTotal());

        return response;
    }

    /**
     *
     * @param request
     * @return
     */
    @RequestMapping("/add")
    public Map createNotice(@RequestBody Map request) {
        // 获取公告请求对象
        Map notice = (Map) request.get("notice");

        // 将角色list转换为使用逗号连接的字符串。
        // super_admin=老板, admin=店长, cashier=收营员
        List<String> roleList = (List<String>) notice.get(ROLES);
        notice.put(ROLES, listToString(roleList));

        boolean newRiskNotice = WosaiMapUtils.getBoolean(notice, "new_notice_risk", false);
        notice.remove("new_notice_risk");

        // 推送目标：全量和定向
        String target = WosaiMapUtils.getString(request, "target", NOTICE_TO_ALL);
        if (NOTICE_TO_ALL.equals(target)) {
            noticeService.createNoticeToAll(notice);
        } else {
            noticeService.createNotice(notice);
        }

        List<String> noticeWay = (List<String>)notice.get("notice_way");

        if (noticeWay.contains("notice") && newRiskNotice) {
            String title;
            String content;
            Map req = Maps.newHashMap();

            if (NOTICE_TO_ALL.equals(target)) {
                title = WosaiMapUtils.getString(notice, "title", "");
                content = WosaiMapUtils.getString(notice, "content", "");

                //按全量标签投放
                req.put("deliver_type", 0);
                req.put("merchant_dmp", "全量商户");
            } else {
                title = WosaiMapUtils.getString(notice, "notice_title", "");
                content = WosaiMapUtils.getString(notice, "notice_content", "");

                //按名单投放
                req.put("deliver_type", 1);
                req.put("whitelist_url", WosaiMapUtils.getString(notice, "excel_download_url", ""));
            }

            req.put("product_id", aopRiskNoticeProductID);
            //纯文字模版
            req.put("type", 2);
            req.put("title",title);
            req.put("sub_title", "");

            Map terminalConfig = MapUtil.hashMap(
                    "has_jump", 1,
                    "jump_type", 1,
                    "content", content,
                    "terminal_id", appId //"feebe00e-c4a4-11e9-846e-7cd30aeb7366"//生产环境收钱吧APP客户端ID

            );
            req.put("terminal_configs", Collections.singletonList(terminalConfig));
//            //需要跳转
//            req.put("has_jump", 1);
//            //跳转图文
//            req.put("jump_type", 1);
//            req.put("content", content);

            req.put("merchant_role", listToString(roleList));
            //iOS&android
            req.put("os_type", 3);

            long sendTime = WosaiMapUtils.getLong(notice, "effective_time", 0L);

            if (sendTime == 0L) {
                req.put("send_type", 1);
                req.put("send_time", System.currentTimeMillis());
            } else {
                req.put("send_type", 0);
                req.put("send_time", sendTime);
            }

            req.put("creator", WosaiMapUtils.getString(notice, "creator", ""));
            logger.info("new request for notification");
//            aopNoticeService.addNoticePublication(req);
        }
        return ResponseHelper.getResponse(ResponseHelper.SUCCESS_CODE, ResponseHelper.SUCCESS_MSG);
    }

    @RequestMapping("/update")
    public Map updateNotice(@RequestBody Map request) {
        Map notice = (Map) request.get("notice");
        noticeService.updateNotice(notice);
        return ResponseHelper.getResponse(ResponseHelper.SUCCESS_CODE, ResponseHelper.SUCCESS_MSG);
    }

    @RequestMapping("/get")
    public Map getNotice(@RequestBody Map<String, String> request) {
        if (request == null && WosaiStringUtils.isEmpty(request.get("id"))) {
            return ResponseHelper.getResponse(ResponseHelper.ERROR_CODE, ResponseHelper.ERROR__MSG);
        }
        Map map = noticeService.getNotice(request.get("id"));
        Map response = ResponseHelper.getResponse(ResponseHelper.SUCCESS_CODE, ResponseHelper.SUCCESS_MSG);
        ResponseHelper.addRecord(response, map);
        return response;
    }

    @RequestMapping("/send")
    public Map sendNotice(@RequestBody Map<String, String> request) {
        if (request == null && WosaiStringUtils.isEmpty(request.get("id"))) {
            return ResponseHelper.getResponse(ResponseHelper.ERROR_CODE, ResponseHelper.ERROR__MSG);
        }
        noticeService.sendNotice(request.get("id"));
        return ResponseHelper.getResponse(ResponseHelper.SUCCESS_CODE, ResponseHelper.SUCCESS_MSG);
    }

    @RequestMapping("/stop")
    public Map stopNotice(@RequestBody Map<String, String> request) {
        if (request == null && WosaiStringUtils.isEmpty(request.get("id"))) {
            return ResponseHelper.getResponse(ResponseHelper.ERROR_CODE, ResponseHelper.ERROR__MSG);
        }
        noticeService.stopNotice(request.get("id"));
        return ResponseHelper.getResponse(ResponseHelper.SUCCESS_CODE, ResponseHelper.SUCCESS_MSG);
    }

    @RequestMapping("/offshelve")
    public Map offshelveNotice(@RequestBody Map<String, String> request) {
        if (request == null && WosaiStringUtils.isEmpty(request.get("id"))) {
            return ResponseHelper.getResponse(ResponseHelper.ERROR_CODE, ResponseHelper.ERROR__MSG);
        }
        noticeService.offshelveNotice(request.get("id"));
        return ResponseHelper.getResponse(ResponseHelper.SUCCESS_CODE, ResponseHelper.SUCCESS_MSG);
    }

    @RequestMapping("/list")
    public Map getNoticeList(@RequestBody Map request) {
        if (request == null) {
            return ResponseHelper.getResponse(ResponseHelper.ERROR_CODE, ResponseHelper.SUCCESS_MSG);
        }
        Map noticeList = noticeService.getNoticeList(request);
        Map response = ResponseHelper.getResponse(ResponseHelper.SUCCESS_CODE, ResponseHelper.SUCCESS_MSG);
        if (noticeList == null) {
            ResponseHelper.addRecords(response, null, 0);
            return response;
        }
        List<Map> list = (List<Map>) noticeList.get("list");
        long total = (long) noticeList.get("total");
        ResponseHelper.addRecords(response, list, total);
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/uploadMerchants")
    @ResponseBody
    public Map uploadMerchantConfig(
            @RequestParam("file") MultipartFile file) throws IOException {
        Map response = ResponseHelper.getResponse(ResponseHelper.SUCCESS_CODE, ResponseHelper.SUCCESS_MSG);
        try (
                BufferedReader buffer = new BufferedReader(new InputStreamReader(file.getInputStream()))
        ) {
            String originalFileName = file.getOriginalFilename();
            int lastIndex = originalFileName.lastIndexOf(".");
            if (lastIndex == -1) {
                throw new UpayException(UpayException.CODE_INVALID_PARAMETER, "文件格式不正确");
            }

            //得到上传商户号文件行数
            long count = buffer.lines()
                    //java8和spring3有冲突，用非lambda实现
                    .filter(new Predicate() {
                        //过滤
                        @Override
                        public boolean test(Object o) {
                            String item = String.valueOf(o);
                            //去除空行和null值
                            if (WosaiStringUtils.isEmpty(item)) {
                                return false;
                            }
                            //去除无效字符
                            String[] arr = item.trim().split(",");
                            if (arr.length == 0) {
                                return false;
                            }
                            //去除无效商户号(非数字)
                            return StringUtils.isNumeric(arr[0].trim());
                        }
                    })
                    .distinct()
                    .count();


            // 生成oss文件名
            UUID uuid = UUID.randomUUID();
            String filename = uuid.toString();
            String pathname = filename.substring(0, 2);
            String type = originalFileName.substring(lastIndex + 1, originalFileName.length()).toLowerCase();
            String fullName = pathname + "/" + filename + "." + type;

            // 上传oss
            ossFileUploader.uploadImage(fullName, file.getInputStream(), file.getSize());

            Map res = Maps.newHashMap();
            res.put("excel_url", IMAGES_OSS_INTERNAL + fullName);
            res.put("total", count);
            ResponseHelper.addRecord(response, res);
        } catch (Exception e) {
            response = ResponseHelper.getResponse(ResponseHelper.ERROR_CODE, e.getMessage());
        }
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json", value = "/uploadImage")
    @ResponseBody
    public void uploadImage(@RequestParam("file") MultipartFile file, HttpServletResponse response) throws IOException {

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        IOUtils.copy(file.getInputStream(), baos);

        byte[] content = baos.toByteArray();

        // calculate path
        String digest = toSHA1(content);
        String dirName = digest.substring(0, 2);
        String fullName = dirName + "/" + digest.substring(2) + "." + "jpg";
        //上传oss
        ByteArrayInputStream bais = new ByteArrayInputStream(content);
        ossFileUploader.uploadIfNotExists(OssFileUploader.IMAGE_BUCKET_NAME, fullName, bais, content.length);
        PrintWriter writer = response.getWriter();
        writer.print(IMAGES_OSS_OUT + fullName);
        writer.flush();
        writer.close();
        return;
    }


    private static String toSHA1(byte[] convertme) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("SHA-1");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return byteArrayToHexString(md.digest(convertme));
    }

    private static String byteArrayToHexString(byte[] b) {
        String result = "";
        for (int i = 0; i < b.length; i++) {
            result +=
                    Integer.toString((b[i] & 0xff) + 0x100, 16).substring(1);
        }
        return result;
    }

    private String listToString(List<String> list) {
        StringBuilder string = new StringBuilder();
        if (WosaiCollectionUtils.isNotEmpty(list)) {
            for (String role : list) {
                string.append(role).append(",");
            }
            int length = string.length();
            string = string.delete(length - 1, length);
        }
        return string.toString();
    }


}
