<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://java.sun.com/xml/ns/javaee"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         id="WebApp_ID" version="2.5">

  <display-name>Spring MVC</display-name>
  <description>Spring MVC web application</description>


  <!--
      - Location of the XML file that defines the root application context.
      - Applied by ContextLoaderServlet.
  -->
  <context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>classpath:spring/business-config.xml, classpath:spring/tools-config.xml, classpath:spring/application-security.xml, classpath:spring/kafka-config.xml</param-value>
  </context-param>

  <listener>
    <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
  </listener>

  <!--
      - Servlet that dispatches request to registered handlers (Controller implementations).
  -->
  <servlet>
    <servlet-name>dispatcher</servlet-name>
    <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
    <init-param>
      <param-name>contextConfigLocation</param-name>
      <param-value>classpath:spring/mvc-core-config.xml</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
  </servlet>

  <servlet-mapping>
    <servlet-name>dispatcher</servlet-name>
    <url-pattern>/</url-pattern>
  </servlet-mapping>


  <!-- used so we can use forms of method type 'PUT' and 'DELETE'
       see here: http://static.springsource.org/spring/docs/current/spring-framework-reference/html/view.html#rest-method-conversion
  -->
  <filter>
    <filter-name>httpMethodFilter</filter-name>
    <filter-class>org.springframework.web.filter.HiddenHttpMethodFilter</filter-class>
  </filter>

  <filter-mapping>
    <filter-name>httpMethodFilter</filter-name>
    <servlet-name>dispatcher</servlet-name>
  </filter-mapping>

  <filter>
    <filter-name>crossDomainFilter</filter-name>
    <filter-class>com.wosai.upay.filter.CrossDomainFilter</filter-class>
  </filter>

  <filter-mapping>
    <filter-name>crossDomainFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>

  	<!-- Character Encoding filter -->
	<filter>
		<filter-name>encodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>encodingFilter</filter-name>
		<url-pattern>/api/import/*</url-pattern>
	</filter-mapping>




  <filter>
    <filter-name>loginCheckerFilter</filter-name>
    <filter-class>com.wosai.upay.filter.LoginCheckerFilter</filter-class>
    <init-param>
      <param-name>sessionKey</param-name>
      <param-value>osp_account</param-value>
    </init-param>
    <init-param>
      <param-name>notCheckUrls</param-name>
      <param-value>isAppidInWeixinBlackList,isLicenseNameInWeixinBlackList,login,sendSmsCode,logout,Info,getExceptionCodesAndDesc,api/oauth/login/in,api/oauth/callBack,cas,redirect</param-value>
    </init-param>
  </filter>



  <filter-mapping>
    <filter-name>loginCheckerFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>


  <!--<filter>-->
    <!--<filter-name>WithdrawAuthFilter</filter-name>-->
    <!--<filter-class>com.wosai.upay.filter.WithdrawAuthFilter</filter-class>-->
    <!--<init-param>-->
      <!--<param-name>sessionKey</param-name>-->
      <!--<param-value>osp_account</param-value>-->
    <!--</init-param>-->
    <!--<init-param>-->
      <!--<param-name>checkUrls</param-name>-->
      <!--<param-value>/withdraw/changeWithDrawStatus, /withdraw/batchChangeWithDrawStatus,/withdraw/batchWithdrawVerifyOfLakala</param-value>-->
    <!--</init-param>-->
  <!--</filter>-->

  <!--<filter-mapping>-->
    <!--<filter-name>WithdrawAuthFilter</filter-name>-->
    <!--<url-pattern>/*</url-pattern>-->
  <!--</filter-mapping>-->


  <filter>
    <filter-name>customMDCServletFilter</filter-name>
    <filter-class>com.wosai.upay.filter.CustomMDCServletFilter</filter-class>
    <init-param>
      <param-name>sessionKeyCustomKeyPairs</param-name>
      <param-value>osp_username:osp_username</param-value>
    </init-param>
  </filter>

  <filter-mapping>
    <filter-name>customMDCServletFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>

</web-app>
