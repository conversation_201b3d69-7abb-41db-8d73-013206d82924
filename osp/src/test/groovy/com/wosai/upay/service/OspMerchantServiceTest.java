package com.wosai.upay.service;

import com.wosai.risk.bean.RiskMerchantLimitBean;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.util.ParamConstantUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: huangwenbin
 * @description:
 * @date: 2018/7/5 13:10
 * @version: *******
 * @modified by: huangwenbin
 */
@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class OspMerchantServiceTest {
    @Autowired
    private OspMerchantService ospMerchantService;

    private String MERCHANT_ID = "172f519a-dcbb-4990-9e18-4f9c03262a62";
    private String MERCHANT_SN = "21680002803681";

    @Test
    public void updateMerchantTradeValidateParamsTest() {
        Map validateParams = new HashMap();
        validateParams.put(ConstantUtil.KEY_MERCHANT_ID, MERCHANT_ID);
        validateParams.put(ParamConstantUtil.MERCHANT_SN, MERCHANT_SN);
        validateParams.put(ParamConstantUtil.ALIPAY_LIMIT, 300);
        validateParams.put(ParamConstantUtil.WECHAT_PAY_LIMIT, 700);
        ospMerchantService.updateMerchantTradeValidateParams(validateParams);
    }

    @Test
    public void getMerchantTradeValidateParams() {
        Map validateParams = new HashMap();
        validateParams.put(ConstantUtil.KEY_MERCHANT_ID, MERCHANT_ID);
        Map merchantTradeValidateParams = ospMerchantService.getMerchantTradeValidateParams(validateParams);
        Assert.assertNotNull(merchantTradeValidateParams);
    }
}