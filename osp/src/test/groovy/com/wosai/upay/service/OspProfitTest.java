package com.wosai.upay.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by huang<PERSON>yi on 2017/12/27/027.
 */
@ContextConfiguration(locations = {"classpath:spring/business-config.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
public class OspProfitTest {
//    @Autowired
//    private OspProfitService ospProfitService;
//    @Test
//    public void Test() throws Exception {
//        Map<String ,Object> request=new HashMap<>();
//        request.put("profit_order_id",9);
////        request.put("profit_order_sn",1);
//        request.put("operator_id",9);
//        request.put("amount",20);
//        request.put("remark","分润账单金额修改");
//        request.put("operator_name","huangxiyi");
//        System.out.println(ospProfitService.modifyProfit(request));
//    }
}
