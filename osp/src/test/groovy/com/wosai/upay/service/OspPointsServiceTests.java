package com.wosai.upay.service;

import com.wosai.app.backend.api.consts.ExceptionConstants;
import com.wosai.app.backend.api.service.IAccountService;
import com.wosai.reward.entity.*;
import com.wosai.reward.entity.enums.Event;
import com.wosai.reward.entity.enums.TagGroup;
import com.wosai.reward.service.*;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.exception.UpayException;
import com.wosai.upay.util.SheetUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.internal.util.collections.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.*;
import java.util.*;

import static org.mockito.Mockito.*;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.*;

/**
 * Created by xuyuanxiang on 2017/8/10.
 */
public class OspPointsServiceTests {
    @Mock
    private AccountService accountService;
    @Mock
    private PointsService pointsService;
    @Mock
    private RuleService ruleService;
    @Mock
    private HistoryService historyService;
    @Mock
    private TagService tagService;
    @Mock
    private IAccountService iAccountService;
    private OspPointsService ospPointsService;
    private String mockOspUsername = "***********";
    private Map<String, Object> request;
    private MockHttpSession session;
    private MockHttpServletRequest httpServletRequest;


    @Autowired
    private OspRiskService ospRiskService;
    @Before
    public void beforeEach() {
        MockitoAnnotations.initMocks(this);
        httpServletRequest = new MockHttpServletRequest();
        session = new MockHttpSession();
        Map<String, Object> user = new HashMap<>();
        user.put(Account.USERNAME, mockOspUsername);
        httpServletRequest.setSession(session);
        session.setAttribute("osp_account", user);
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(httpServletRequest));
//        ospPointsService = new OspPointsServiceImpl(accountService, pointsService, ruleService, historyService, tagService, iAccountService);
        request = new HashMap<>();
    }

    public void testOspRiskService(){

    }

    @Test
    public void testFindHistories() {
        long startTime = DateUtils.addDays(new Date(), -3).getTime();
        long endTime = new Date().getTime();
        String operatorId = UUID.randomUUID().toString();
        Map<String, Object> params = new HashMap<>();
        params.put("cellphone", "***********");
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> user = new HashMap<>();
        List<Map<String, Object>> data = new ArrayList<>();
        data.add(user);
        user.put("id", operatorId);
        result.put("data", data);
        result.put("code", ExceptionConstants.SUCC);
        when(iAccountService.getAccountByCriteria(params)).thenReturn(result);
        HistoryQueryCriteria criteria = HistoryQueryCriteria.builder()
                .startTimestamp(startTime)
                .endTimestamp(endTime)
                .keyword("积分增加")
                .keyword("额外积分")
                .referenceId(operatorId)
                .build();
        List<HistoryResponse> historyResponses = new ArrayList<>();
        historyResponses.add(
                HistoryResponse.builder()
                        .event(Event.EVENT_INCOME_CHECKOUT)
                        .origins(100L)
                        .timestamp(System.currentTimeMillis())
                        .originTimestamp(System.currentTimeMillis())
                        .points(10L)
                        .pointsSum(110L)
                        .referenceId(operatorId)
                        .build()
        );
        when(historyService.findAll(criteria, 1, 10)).thenReturn(
                new Page<>(historyResponses, 2, 11, 1, 10)
        );

        request.put("cellphone", "***********");
        request.put("startTimestamp", startTime);
        request.put("endTimestamp", endTime);
        request.put("page", 1);
        request.put("size", 10);
        List<String> keywords = new ArrayList<>();
        keywords.add("积分增加");
        keywords.add("额外积分");
        request.put("keywords", keywords);
        Page<HistoryResponse> historyResponsePage = ospPointsService.findHistories(request);
        assertThat(historyResponsePage, notNullValue());
    }

    @Test
    public void testFindHistoriesAndReturnContentIsNull() {
        String operatorId = UUID.randomUUID().toString();
        Map<String, Object> params = new HashMap<>();
        params.put("cellphone", "***********");
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> user = new HashMap<>();
        List<Map<String, Object>> data = new ArrayList<>();
        data.add(user);
        user.put("id", operatorId);
        result.put("data", data);
        result.put("code", ExceptionConstants.SUCC);
        when(iAccountService.getAccountByCriteria(params)).thenReturn(result);
        HistoryQueryCriteria criteria = HistoryQueryCriteria.builder()
                .startTimestamp(0)
                .endTimestamp(0)
                .referenceId(operatorId)
                .build();
        when(historyService.findAll(criteria, 1, 10)).thenReturn(null);

        request.put("cellphone", "***********");
        request.put("startTimestamp", 0);
        request.put("endTimestamp", 0);
        request.put("page", 0);
        request.put("size", 10);
        request.put("keywords", "");
        Page<HistoryResponse> historyResponsePage = ospPointsService.findHistories(request);
        assertThat(historyResponsePage, nullValue());
    }

    @Test
    public void testFindPointsByCellphone() {
        String operatorId = UUID.randomUUID().toString();
        Map<String, Object> params = new HashMap<>();
        params.put("cellphone", "***********");
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> user = new HashMap<>();
        List<Map<String, Object>> data = new ArrayList<>();
        data.add(user);
        user.put("id", operatorId);
        result.put("data", data);
        result.put("code", ExceptionConstants.SUCC);
        when(iAccountService.getAccountByCriteria(params)).thenReturn(result);
        PointsResponse pointsResponse = new PointsResponse();
        pointsResponse.setReferenceId(operatorId);
        pointsResponse.setTotalAvailablePoints(1200L);
        pointsResponse.setDailyRealTimePoints(210L);
        pointsResponse.setYearlyExpiredPoints(20L);
        when(pointsService.findByReferenceId(operatorId)).thenReturn(pointsResponse);

        request.put("cellphone", "***********");
        PointsResponse response = ospPointsService.findPointsByCellphone(request);
        assertThat(response, equalTo(pointsResponse));
    }

    @Test
    public void testSaveRule() {
        Date expiredDate = DateUtils.addDays(new Date(), 30);
        Date effectiveDate = new Date();
        Date startRegisterDate = DateUtils.addYears(new Date(), -1);
        Date endRegisterDate = DateUtils.addYears(new Date(), 1);
        String accpetRefernceId1 = UUID.randomUUID().toString();
        String accpetRefernceId2 = UUID.randomUUID().toString();
        String rejectReferenceId1 = UUID.randomUUID().toString();
        String rejectReferenceId2 = UUID.randomUUID().toString();
        when(
                ruleService.save(
                        Rule.builder()
                                .name("测试规则")
                                .description("描述规则")
                                .createdBy(mockOspUsername)
                                .lastModifiedBy(mockOspUsername)
                                .points(10L)
                                .pointsUpperLimit(1000L)
                                .deleted(false)
                                .enabled(true)
                                .expiredTime(expiredDate.getTime())
                                .effectiveTime(effectiveDate.getTime())
                                .constraints(
                                        ReferenceConstraints.builder()
                                                .startRegisterTime(startRegisterDate.getTime())
                                                .endRegisterTime(endRegisterDate.getTime())
                                                .acceptReference(accpetRefernceId1)
                                                .acceptReference(accpetRefernceId2)
                                                .rejectReference(rejectReferenceId1)
                                                .rejectReference(rejectReferenceId2)
                                                .build()
                                )
                                .acceptEvent(Event.EVENT_INCOME_CHECKOUT)
                                .acceptReferenceTag(
                                        ReferenceTag.builder()
                                                .groupName(TagGroup.city)
                                                .name("上海市")
                                                .build()
                                )
                                .acceptReferenceTag(
                                        ReferenceTag.builder()
                                                .groupName(TagGroup.city)
                                                .name("苏州市")
                                                .build()
                                )
                                .acceptReferenceTag(
                                        ReferenceTag.builder()
                                                .groupName(TagGroup.payway)
                                                .name("1")
                                                .build()
                                )
                                .acceptReferenceTag(
                                        ReferenceTag.builder()
                                                .groupName(TagGroup.payway)
                                                .name("3")
                                                .build()
                                )
                                .rewardThreshold(
                                        RuleRewardThreshold.builder()
                                                .points(100L)
                                                .referenceField(RuleRewardThreshold.ReferenceField.ORIGINS_SUM)
                                                .value(10000L)
                                                .build()
                                )
                                .rewardThreshold(
                                        RuleRewardThreshold.builder()
                                                .points(20L)
                                                .referenceField(RuleRewardThreshold.ReferenceField.EVENT_COUNT)
                                                .value(1)
                                                .build()
                                )
                                .build()
                )
        ).thenReturn(
                Rule.builder()
                        .id(1L)
                        .name("测试规则")
                        .description("描述规则")
                        .createdBy(mockOspUsername)
                        .createdDate(new Date())
                        .lastModifiedDate(new Date())
                        .lastModifiedBy(mockOspUsername)
                        .points(10L)
                        .pointsUpperLimit(1000L)
                        .deleted(false)
                        .enabled(true)
                        .expiredTime(expiredDate.getTime())
                        .effectiveTime(effectiveDate.getTime())
                        .constraints(
                                ReferenceConstraints.builder()
                                        .id(UUID.randomUUID().toString())
                                        .startRegisterTime(startRegisterDate.getTime())
                                        .endRegisterTime(endRegisterDate.getTime())
                                        .acceptReference(accpetRefernceId1)
                                        .acceptReference(accpetRefernceId2)
                                        .rejectReference(rejectReferenceId1)
                                        .rejectReference(rejectReferenceId2)
                                        .build()
                        )
                        .acceptEvent(Event.EVENT_INCOME_CHECKOUT)
                        .acceptReferenceTag(
                                ReferenceTag.builder()
                                        .groupName(TagGroup.city)
                                        .name("上海市")
                                        .build()
                        )
                        .acceptReferenceTag(
                                ReferenceTag.builder()
                                        .groupName(TagGroup.city)
                                        .name("苏州市")
                                        .build()
                        )
                        .acceptReferenceTag(
                                ReferenceTag.builder()
                                        .groupName(TagGroup.payway)
                                        .name("1")
                                        .build()
                        )
                        .acceptReferenceTag(
                                ReferenceTag.builder()
                                        .groupName(TagGroup.payway)
                                        .name("3")
                                        .build()
                        )
                        .rewardThreshold(
                                RuleRewardThreshold.builder()
                                        .id(UUID.randomUUID().toString())
                                        .points(100L)
                                        .referenceField(RuleRewardThreshold.ReferenceField.ORIGINS_SUM)
                                        .value(10000L)
                                        .build()
                        )
                        .rewardThreshold(
                                RuleRewardThreshold.builder()
                                        .id(UUID.randomUUID().toString())
                                        .points(20L)
                                        .referenceField(RuleRewardThreshold.ReferenceField.EVENT_COUNT)
                                        .value(1)
                                        .build()
                        )
                        .version(0L)
                        .build()
        );

        request.put("name", "测试规则");
        request.put("description", "描述规则");
        request.put("points", 10L);
        request.put("pointsUpperLimit", 1000L);
        request.put("deleted", false);
        request.put("enabled", true);
        request.put("effectiveTime", effectiveDate.getTime());
        request.put("expiredTime", expiredDate.getTime());
        Map<String, Object> constraints = new HashMap<>();
        constraints.put("startRegisterTime", startRegisterDate.getTime());
        constraints.put("endRegisterTime", endRegisterDate.getTime());
        constraints.put("acceptReferences", Arrays.asList(accpetRefernceId1, accpetRefernceId2));
        constraints.put("rejectReferences", Arrays.asList(rejectReferenceId1, rejectReferenceId2));
        request.put("constraints", constraints);
        request.put("acceptEvents", Collections.singletonList("EVENT_INCOME_CHECKOUT"));
        Map<String, Object> acceptReferenceTags = new HashMap<>();
        List<String> cities = new ArrayList<>();
        List<String> payways = new ArrayList<>();
        cities.add("上海市");
        cities.add("苏州市");
        payways.add("1");
        payways.add("3");
        acceptReferenceTags.put("city", cities);
        acceptReferenceTags.put("payway", payways);
        request.put("acceptReferenceTags", acceptReferenceTags);
        List<Map<String, Object>> rewardThresholds = new ArrayList<>();
        Map<String, Object> threshold1 = new HashMap<>();
        threshold1.put("points", 100L);
        threshold1.put("value", 10000L);
        threshold1.put("referenceField", "ORIGINS_SUM");
        Map<String, Object> threshold2 = new HashMap<>();
        threshold2.put("points", 20L);
        threshold2.put("value", 1L);
        threshold2.put("referenceField", "EVENT_COUNT");
        rewardThresholds.add(threshold1);
        rewardThresholds.add(threshold2);
        request.put("rewardThresholds", rewardThresholds);
        Rule rule = ospPointsService.saveRule(request);
        assertThat(rule, notNullValue());
        assertThat(rule.getId(), equalTo(1L));
        assertThat(rule.getCreatedBy(), equalTo(mockOspUsername));
        assertThat(rule.getCreatedDate(), notNullValue());
        assertThat(rule.getLastModifiedDate(), notNullValue());
    }

    @Test
    public void testFindRules() {
        Rule rule1 = Rule.builder()
                .id(1L)
                .name("测试规则1")
                .description("测试规则一描述")
                .build();
        Rule rule2 = Rule.builder()
                .id(2L)
                .name("测试规则2")
                .description("测试规则二描述")
                .build();
        when(
                ruleService.findAll(
                        RuleQueryCriteria.builder()
                                .tag(TagGroup.city, Collections.singleton("苏州市"))
                                .tag(TagGroup.payway, Collections.singleton("1"))
                                .build(),
                        0,
                        20
                )
        ).thenReturn(
                new Page<>(
                        Arrays.asList(rule1, rule2),
                        1,
                        2,
                        0,
                        20
                )
        );
        request.put("city", Collections.singletonList("苏州市"));
        request.put("payway", Collections.singletonList("1"));
        Page<Rule> rulePage = ospPointsService.findRules(request);
        List<Rule> rules = rulePage.getContent();
        assertThat(rules.size(), equalTo(2));
        assertThat(rules.get(0), equalTo(rule1));
        assertThat(rules.get(1), equalTo(rule2));
    }

    @Test
    public void testFindRulesAndResponseNull() {
        request.put("city", null);
        request.put("industry", null);
        request.put("payway", null);
        Page<Rule> rulePage = ospPointsService.findRules(request);
        assertThat(rulePage.getContent().size(), is(0));
        assertThat(rulePage.getPage(), is(0));
        assertThat(rulePage.getSize(), is(20));
        assertThat(rulePage.getTotalElements(), is(0L));
        assertThat(rulePage.getTotalPages(), is(0));
    }

    private String randomCellphone() {
        return StringUtils.rightPad("1", 11, new Random().nextInt(999999999) + "");
    }

    private void mockAppBackend(String cellphone, String operatorId, String merchantId) {
        Map<String, Object> params = new HashMap<>();
        params.put("cellphone", cellphone);
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> data = null;
        if (StringUtils.isNotEmpty(operatorId) && StringUtils.isNotEmpty(merchantId)) {
            data = new ArrayList<>();
            Map<String, Object> user = new HashMap<>();
            user.put("id", operatorId);
            user.put("merchant_id", merchantId);
            data.add(user);
        }
        response.put("code", ExceptionConstants.SUCC);
        response.put("data", data);
        when(iAccountService.getAccountByCriteria(params)).thenReturn(response);
    }

    private void put(List<Map<String, Object>> expected, String cellphone, int code, String msg) {
        Map<String, Object> details = new HashMap<>();
        details.put("code", code);
        details.put("msg", msg);
        details.put("cellphone", cellphone);
        expected.add(details);
    }

    private void put(List<Map<String, Object>> expected, String cellphone, String points, int code, String msg) {
        Map<String, Object> details = new HashMap<>();
        details.put("code", code);
        details.put("msg", msg);
        details.put("cellphone", cellphone);
        details.put("points", points);
        expected.add(details);
    }

    @Test
    public void testBatchCreate() throws IOException {
        // 创建成功
        String cellphone = randomCellphone();
        String operatorId = UUID.randomUUID().toString();
        String merchantId = UUID.randomUUID().toString();
        // 非法手机号
        String illegalCellphone = "232112";
        // app-backend-service不存在的账号
        String nonExistCellphone = randomCellphone();
        // 已经开通积分账户的账号
        String existOperatorId = UUID.randomUUID().toString();
        String existMerchantId = UUID.randomUUID().toString();
        String existCellphone = randomCellphone();
        while (nonExistCellphone.equals(cellphone)) {
            nonExistCellphone = randomCellphone();
        }
        while (existCellphone.equals(cellphone) || existCellphone.equals(nonExistCellphone)) {
            existCellphone = randomCellphone();
        }

        mockAppBackend(cellphone, operatorId, merchantId);
        mockAppBackend(nonExistCellphone, null, null);
        mockAppBackend(existCellphone, existOperatorId, existMerchantId);
        when(
                accountService.batchCreate(
                        Arrays.asList(
                                AccountRequest.builder()
                                        .referenceId(operatorId)
                                        .remark(merchantId)
                                        .build()
                                , AccountRequest.builder()
                                        .referenceId(existOperatorId)
                                        .remark(existMerchantId)
                                        .build()
                        )
                )
        ).thenReturn(
                Arrays.asList(
                        AccountResponse.builder()
                                .request(
                                        AccountRequest.builder()
                                                .referenceId(operatorId)
                                                .remark(merchantId)
                                                .build()
                                )
                                .code(200)
                                .msg("开户成功！")
                                .build()
                        , AccountResponse.builder()
                                .request(
                                        AccountRequest.builder()
                                                .referenceId(existOperatorId)
                                                .remark(existMerchantId)
                                                .build()
                                )
                                .code(304)
                                .msg("开户失败，账户已激活到新积分，系统未对该账户做任何修改！")
                                .build()
                )
        );

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet();
        SheetUtil sheetUtil = new SheetUtil(sheet);
        sheetUtil.appendRow(Collections.singletonList(cellphone));
        sheetUtil.appendRow(Collections.singletonList(illegalCellphone));
        sheetUtil.appendRow(Collections.singletonList(nonExistCellphone));
        sheetUtil.appendRow(Collections.singletonList(existCellphone));
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        MockMultipartFile multipartFile = new MockMultipartFile(
                "file",
                "batchCreate4Test.xls",
                "multipart/form-data",
                new ByteArrayInputStream(outputStream.toByteArray())
        );

        Map<String, Object> expected = new HashMap<>();
        expected.put("total", 4);
        List<Map<String, Object>> failures = new ArrayList<>();
        put(failures, illegalCellphone, UpayException.CODE_INVALID_PARAMETER, "无效的手机号格式！");
        put(failures, nonExistCellphone, UpayException.CODE_INVALID_PARAMETER, "手机号对应账号不存在！");
        put(failures, existCellphone, UpayException.CODE_INVALID_PARAMETER, "该手机号对应积分账户已开通，系统未对该账户做任何修改！");
        expected.put("failures", failures);
        assertThat(ospPointsService.batchImportAccounts(multipartFile, httpServletRequest), Matchers.<Object>equalTo(expected));
    }

    @Test
    public void testFindTags() {
        when(tagService.findByGroup(TagGroup.city)).thenReturn(Sets.newSet("上海市", "苏州市"));
        request.put("group", "city");
        Set<String> tags = ospPointsService.findTags(request);
        assertThat(tags.size(), equalTo(2));
        assertThat(tags, contains("上海市", "苏州市"));
    }

    @Test
    public void testExport() throws IOException {
        List<Map<String, Object>> failed = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("cellphone", randomCellphone());
            item.put("msg", "手机号对应账号不存在！");
            failed.add(item);
        }
        session.setAttribute("batchImportPointsAccountsFailedResults", failed);
        Map<String, Object> params = new HashMap<>();
        params.put("originFilename", "batchCreate.xls");
        MockHttpServletResponse httpServletResponse = new MockHttpServletResponse();
        ospPointsService.exportFailedAccounts(params, httpServletRequest, httpServletResponse);
    }

    @Test
    public void testBatchAddPoints() throws IOException {
        String cellphone = "***********";
        String operatorId = UUID.randomUUID().toString();
        String merchantId = UUID.randomUUID().toString();
        long points = 43L;
        mockAppBackend(cellphone, operatorId, merchantId);
        String cellphone4TestIllegal = "12221";
        long points4TestIllegal = 0L;
        String cellphone4TestNonExistent = "***********";
        long points4TestNonExistent = 95L;
        mockAppBackend(cellphone4TestNonExistent, null, null);
        String cellphone4TestUsed = "***********";
        String operatorId4TestUsed = UUID.randomUUID().toString();
        String merchantId4TestUsed = UUID.randomUUID().toString();
        long points4TestUsed = 110L;
        mockAppBackend(cellphone4TestUsed, operatorId4TestUsed, merchantId4TestUsed);
        String cellphone4TestError = "***********";
        String operatorId4TestError = UUID.randomUUID().toString();
        String merchantId4TestError = UUID.randomUUID().toString();
        long points4TestError = 28L;
        mockAppBackend(cellphone4TestError, operatorId4TestError, merchantId4TestError);
        String cellphone4TestNotCreated = "***********";
        String operatorId4TestNotCreated = UUID.randomUUID().toString();
        String merchantId4TestNotCreated = UUID.randomUUID().toString();
        long points4TestNotCreated = 39L;
        mockAppBackend(cellphone4TestNotCreated, operatorId4TestNotCreated, merchantId4TestNotCreated);

        when(
                accountService
                        .batchAddPoints(ArgumentMatchers.<AccountChangePointsRequest>anySet(), anyLong(), eq(mockOspUsername))
        ).thenReturn(Arrays.asList(
                AccountChangePointsResponse.builder()
                        .request(
                                AccountChangePointsRequest.builder()
                                        .expired(AccountChangePointsRequest.Expired.LAST_MOMENT_OF_NEXT_YEAR)
                                        .points(points)
                                        .referenceId(operatorId)
                                        .remark(merchantId)
                                        .build()
                        )
                        .code(200)
                        .msg("积分增加成功！")
                        .build()
                , AccountChangePointsResponse.builder()
                        .request(
                                AccountChangePointsRequest.builder()
                                        .expired(AccountChangePointsRequest.Expired.LAST_MOMENT_OF_NEXT_YEAR)
                                        .points(points4TestUsed)
                                        .referenceId(operatorId4TestUsed)
                                        .remark(merchantId4TestUsed)
                                        .build()
                        )
                        .code(304)
                        .msg("积分增加失败，在相同时间：yyyy-MM-dd HH:mm:ss 已为该账户增加过：XX 积分。请确认当前提交是否为重复提交。")
                        .build()
                , AccountChangePointsResponse.builder()
                        .request(
                                AccountChangePointsRequest.builder()
                                        .expired(AccountChangePointsRequest.Expired.LAST_MOMENT_OF_NEXT_YEAR)
                                        .points(points4TestError)
                                        .referenceId(operatorId4TestError)
                                        .remark(merchantId4TestError)
                                        .build()
                        )
                        .code(500)
                        .msg("积分增加失败，修改积分账户失败，请重试！失败原因：OOXX。")
                        .build()
                , AccountChangePointsResponse.builder()
                        .request(
                                AccountChangePointsRequest.builder()
                                        .expired(AccountChangePointsRequest.Expired.LAST_MOMENT_OF_NEXT_YEAR)
                                        .points(points4TestNotCreated)
                                        .referenceId(operatorId4TestNotCreated)
                                        .remark(merchantId4TestNotCreated)
                                        .build()
                        )
                        .code(404)
                        .msg("积分增加失败，账户未激活到新积分！")
                        .build()
        ));

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet();
        SheetUtil sheetUtil = new SheetUtil(sheet);
        sheetUtil.appendRow(Arrays.asList(cellphone, points));
        sheetUtil.appendRow(Arrays.asList(cellphone4TestIllegal, points));
        sheetUtil.appendRow(Arrays.asList(cellphone));
        sheetUtil.appendRow(Arrays.asList(cellphone, "dsf"));
        sheetUtil.appendRow(Arrays.asList(cellphone, points4TestIllegal));
        sheetUtil.appendRow(Arrays.asList(cellphone4TestNonExistent, points4TestNonExistent));
        sheetUtil.appendRow(Arrays.asList(cellphone4TestUsed, points4TestUsed));
        sheetUtil.appendRow(Arrays.asList(cellphone4TestError, points4TestError));
        sheetUtil.appendRow(Arrays.asList(cellphone4TestNotCreated, points4TestNotCreated));
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        MockMultipartFile multipartFile = new MockMultipartFile(
                "file",
                "batchAddPoints4Test.xls",
                "multipart/form-data",
                new ByteArrayInputStream(outputStream.toByteArray())
        );
        Map result = ospPointsService.batchImportAccountsToAddPoints(
                multipartFile,
                AccountChangePointsRequest.Expired.LAST_MOMENT_OF_NEXT_YEAR,
                httpServletRequest
        );
        Map<String, Object> expected = new HashMap<>();
        expected.put("total", 9);
        List<Map<String, Object>> failures = new ArrayList<>();
        put(failures, cellphone4TestIllegal, points + "", UpayException.CODE_INVALID_PARAMETER, "无效的手机号格式！");
        put(failures, cellphone, null, UpayException.CODE_INVALID_PARAMETER, "第二列所加积分数值不能为空！");
        put(failures, cellphone, "dsf", UpayException.CODE_INVALID_PARAMETER, "第二列所加积分数值必须为数字！");
        put(failures, cellphone, points4TestIllegal + "", UpayException.CODE_INVALID_PARAMETER, "第二列所加积分数值不能小于1！");
        put(failures, cellphone4TestNonExistent, points4TestNonExistent + "", UpayException.CODE_INVALID_PARAMETER, "手机号对应账号不存在！");
        put(failures, cellphone4TestUsed, points4TestUsed + "", UpayException.CODE_INVALID_PARAMETER, "积分增加失败，在相同时间：yyyy-MM-dd HH:mm:ss 已为该账户增加过：XX 积分。请确认当前提交是否为重复提交。");
        put(failures, cellphone4TestError, points4TestError + "", UpayException.CODE_INVALID_PARAMETER, "积分增加失败，修改积分账户失败，请重试！失败原因：OOXX。");
        put(failures, cellphone4TestNotCreated, points4TestNotCreated + "", UpayException.CODE_INVALID_PARAMETER, "积分增加失败，账户未激活到新积分！");
        expected.put("failures", failures);
        assertThat(result, Matchers.<Object>equalTo(expected));
    }

    @Test
    public void testExportBatchAddPointsFailed() throws IOException {
        List<Map<String, Object>> failed = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("cellphone", randomCellphone());
            item.put("msg", "手机号对应账号不存在！");
            item.put("points", "100");
            failed.add(item);
        }
        session.setAttribute("batchAddPointsAccountsFailedResults", failed);
        Map<String, Object> params = new HashMap<>();
        params.put("originFilename", "batchAddPoints.xls");
        MockHttpServletResponse httpServletResponse = new MockHttpServletResponse();
        ospPointsService.exportFailedBatchAddPointsAccounts(params, httpServletRequest, httpServletResponse);
    }

    @Test
    public void testDeleteRule() {
        when(ruleService.delete(1L)).thenReturn(true);
        request.put("id", 1L);
        assertThat(ospPointsService.deleteRule(request), is(true));
    }
}
